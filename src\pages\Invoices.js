import React, { useState, useEffect } from 'react';
import axios from 'axios';
import Header from '../components/Header';
import Sidebar from '../components/Sidebar';
import Footer from '../components/Footer';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';

const Invoices = () => {
    const [invoices, setInvoices] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [selectedInvoice, setSelectedInvoice] = useState(null);
    const [showModal, setShowModal] = useState(false);
    const [filterStatus, setFilterStatus] = useState('all');
    const [filterMonth, setFilterMonth] = useState('all');

    // Sayfanın yüklenmesi
    useEffect(() => {
        const fetchInvoices = async () => {
            try {
                setLoading(true);
                setError(null);
                
                // Gerçek API çağrısı - user'ın fatura geçmişi
                const user = JSON.parse(localStorage.getItem('user'));
                const userId = user?.musteri_ID || 1;
                
                const response = await axios.get(`/api/payments/history/${userId}`);
                
                // API yanıtını invoice formatına çevir
                const invoicesData = response.data.map(payment => ({
                    id: payment.id,
                    invoiceNo: `FAT-${payment.date.split('-')[0]}-${payment.id.toString().padStart(3, '0')}`,
                    date: payment.date,
                    dueDate: new Date(new Date(payment.date).getTime() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                    amount: payment.amountEUR,
                    tax: payment.amountEUR * 0.18,
                    total: payment.amountTRY,
                    status: payment.status === 'completed' ? 'paid' : 'pending',
                    description: payment.packageName,
                    serviceType: 'MGZ24 Hizmet',
                    paymentDate: payment.status === 'completed' ? payment.date : null,
                    paymentMethod: 'Kredi Kartı'
                }));
                
                setInvoices(invoicesData);
                setLoading(false);
            } catch (error) {
                console.error('Fatura verileri alınırken hata:', error);
                setError('Fatura verileri yüklenirken hata oluştu: ' + error.message);
                setLoading(false);
            }
        };

        fetchInvoices();
    }, []);

    // Fatura detayını göster
    const handleShowInvoice = (invoice) => {
        setSelectedInvoice(invoice);
        setShowModal(true);
    };

    // Fatura durum rengini al
    const getStatusColor = (status) => {
        switch (status) {
            case 'paid':
                return 'success';
            case 'pending':
                return 'warning';
            case 'overdue':
                return 'danger';
            default:
                return 'secondary';
        }
    };

    // Fatura durum metnini al
    const getStatusText = (status) => {
        switch (status) {
            case 'paid':
                return 'Ödendi';
            case 'pending':
                return 'Bekliyor';
            case 'overdue':
                return 'Vadesi Geçti';
            default:
                return 'Bilinmiyor';
        }
    };

    // Filtrelenmiş faturaları al
    const getFilteredInvoices = () => {
        return invoices.filter(invoice => {
            const statusMatch = filterStatus === 'all' || invoice.status === filterStatus;
            const monthMatch = filterMonth === 'all' || invoice.date.substring(0, 7) === filterMonth;
            return statusMatch && monthMatch;
        });
    };

    // Toplamları hesapla
    const calculateTotals = () => {
        const filtered = getFilteredInvoices();
        return {
            total: filtered.reduce((sum, invoice) => sum + invoice.total, 0),
            paid: filtered.filter(inv => inv.status === 'paid').reduce((sum, invoice) => sum + invoice.total, 0),
            pending: filtered.filter(inv => inv.status === 'pending').reduce((sum, invoice) => sum + invoice.total, 0),
            overdue: filtered.filter(inv => inv.status === 'overdue').reduce((sum, invoice) => sum + invoice.total, 0)
        };
    };

    const totals = calculateTotals();

    return (
        <>
            <Header />
            <div className="container-fluid">
                <div className="row">
                    <Sidebar />

                    {/* main sütun */}
                    <main className="col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white">
                        <div className="pt-3 pb-1 mt-3 mb-3 border-bottom">
                            <h1 className="h4 text-dark">Faturalarım</h1>
                        </div>

                        {loading ? (
                            <LoadingSpinner 
                                size="lg" 
                                variant="primary" 
                                message="Fatura verileri yükleniyor..." 
                                centered={true}
                            />
                        ) : error ? (
                            <ErrorMessage 
                                message={error} 
                                variant="danger"
                                title="Veri Yükleme Hatası"
                                dismissible={true}
                                onDismiss={() => setError('')}
                            >
                                <button className="btn btn-primary btn-sm mt-2" onClick={() => window.location.reload()}>
                                    Yeniden Dene
                                </button>
                            </ErrorMessage>
                        ) : (
                            <div className="row">
                                <div className="col-12">
                                    {/* Özet Kartları */}
                                    <div className="row mb-4">
                                        <div className="col-md-3 col-sm-6 mb-3">
                                            <div className="card border-primary">
                                                <div className="card-body text-center">
                                                    <h5 className="card-title text-primary">Toplam Tutar</h5>
                                                    <h4 className="text-primary">{totals.total.toFixed(2)} ₺</h4>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="col-md-3 col-sm-6 mb-3">
                                            <div className="card border-success">
                                                <div className="card-body text-center">
                                                    <h5 className="card-title text-success">Ödenen</h5>
                                                    <h4 className="text-success">{totals.paid.toFixed(2)} ₺</h4>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="col-md-3 col-sm-6 mb-3">
                                            <div className="card border-warning">
                                                <div className="card-body text-center">
                                                    <h5 className="card-title text-warning">Bekleyen</h5>
                                                    <h4 className="text-warning">{totals.pending.toFixed(2)} ₺</h4>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="col-md-3 col-sm-6 mb-3">
                                            <div className="card border-danger">
                                                <div className="card-body text-center">
                                                    <h5 className="card-title text-danger">Vadesi Geçen</h5>
                                                    <h4 className="text-danger">{totals.overdue.toFixed(2)} ₺</h4>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Filtreler */}
                                    <div className="card mb-4">
                                        <div className="card-header">
                                            <h5 className="mb-0">Filtreler</h5>
                                        </div>
                                        <div className="card-body">
                                            <div className="row">
                                                <div className="col-md-4">
                                                    <label htmlFor="statusFilter" className="form-label">Durum</label>
                                                    <select
                                                        id="statusFilter"
                                                        className="form-select"
                                                        value={filterStatus}
                                                        onChange={(e) => setFilterStatus(e.target.value)}
                                                    >
                                                        <option value="all">Tümü</option>
                                                        <option value="paid">Ödendi</option>
                                                        <option value="pending">Bekliyor</option>
                                                        <option value="overdue">Vadesi Geçti</option>
                                                    </select>
                                                </div>
                                                <div className="col-md-4">
                                                    <label htmlFor="monthFilter" className="form-label">Ay</label>
                                                    <select
                                                        id="monthFilter"
                                                        className="form-select"
                                                        value={filterMonth}
                                                        onChange={(e) => setFilterMonth(e.target.value)}
                                                    >
                                                        <option value="all">Tümü</option>
                                                        <option value="2024-03">Mart 2024</option>
                                                        <option value="2024-02">Şubat 2024</option>
                                                        <option value="2024-01">Ocak 2024</option>
                                                        <option value="2023-12">Aralık 2023</option>
                                                    </select>
                                                </div>
                                                <div className="col-md-4 d-flex align-items-end">
                                                    <button 
                                                        className="btn btn-secondary"
                                                        onClick={() => {
                                                            setFilterStatus('all');
                                                            setFilterMonth('all');
                                                        }}
                                                    >
                                                        Filtreleri Temizle
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Fatura Listesi */}
                                    <div className="card border-tertiary-subtle">
                                        <div className="card-header bg-light">
                                            <h5 className="mb-0">
                                                <i className="fas fa-file-invoice me-2"></i>
                                                Fatura Listesi
                                                <span className="badge bg-primary ms-2">{getFilteredInvoices().length}</span>
                                            </h5>
                                        </div>
                                        <div className="card-body p-0">
                                            {getFilteredInvoices().length === 0 ? (
                                                <div className="p-4 text-center">
                                                    <ErrorMessage 
                                                        message="Seçilen kriterlere uygun fatura bulunamadı."
                                                        variant="info"
                                                        showIcon={true}
                                                    />
                                                </div>
                                            ) : (
                                                <div className="table-responsive">
                                                    <table className="table table-hover table-striped mb-0">
                                                        <thead className="table-dark">
                                                            <tr>
                                                                <th>Fatura No</th>
                                                                <th>Tarih</th>
                                                                <th>Vade Tarihi</th>
                                                                <th>Tutar</th>
                                                                <th>KDV</th>
                                                                <th>Toplam</th>
                                                                <th>Durum</th>
                                                                <th>Hizmet Türü</th>
                                                                <th>İşlemler</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            {getFilteredInvoices().map((invoice) => (
                                                                <tr key={invoice.id}>
                                                                    <td>
                                                                        <strong>{invoice.invoiceNo}</strong>
                                                                    </td>
                                                                    <td>{invoice.date}</td>
                                                                    <td>{invoice.dueDate}</td>
                                                                    <td>{invoice.amount.toFixed(2)} ₺</td>
                                                                    <td>{invoice.tax.toFixed(2)} ₺</td>
                                                                    <td><strong>{invoice.total.toFixed(2)} ₺</strong></td>
                                                                    <td>
                                                                        <span className={`badge bg-${getStatusColor(invoice.status)}`}>
                                                                            {getStatusText(invoice.status)}
                                                                        </span>
                                                                    </td>
                                                                    <td>{invoice.serviceType}</td>
                                                                    <td>
                                                                        <div className="btn-group" role="group">
                                                                            <button
                                                                                className="btn btn-sm btn-outline-primary"
                                                                                onClick={() => handleShowInvoice(invoice)}
                                                                                title="Detayları Göster"
                                                                            >
                                                                                <i className="fas fa-eye"></i>
                                                                            </button>
                                                                            <button
                                                                                className="btn btn-sm btn-outline-success"
                                                                                title="PDF İndir"
                                                                            >
                                                                                <i className="fas fa-download"></i>
                                                                            </button>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                            ))}
                                                        </tbody>
                                                    </table>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Fatura Detay Modal */}
                        {showModal && selectedInvoice && (
                            <div className="modal fade show d-block" tabIndex="-1" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
                                <div className="modal-dialog modal-lg">
                                    <div className="modal-content">
                                        <div className="modal-header">
                                            <h5 className="modal-title">Fatura Detayı - {selectedInvoice.invoiceNo}</h5>
                                            <button 
                                                type="button" 
                                                className="btn-close" 
                                                onClick={() => setShowModal(false)}
                                            ></button>
                                        </div>
                                        <div className="modal-body">
                                            <div className="row">
                                                <div className="col-md-6">
                                                    <h6>Fatura Bilgileri</h6>
                                                    <p><strong>Fatura No:</strong> {selectedInvoice.invoiceNo}</p>
                                                    <p><strong>Tarih:</strong> {selectedInvoice.date}</p>
                                                    <p><strong>Vade Tarihi:</strong> {selectedInvoice.dueDate}</p>
                                                    <p><strong>Durum:</strong> 
                                                        <span className={`badge bg-${getStatusColor(selectedInvoice.status)} ms-2`}>
                                                            {getStatusText(selectedInvoice.status)}
                                                        </span>
                                                    </p>
                                                </div>
                                                <div className="col-md-6">
                                                    <h6>Ödeme Bilgileri</h6>
                                                    {selectedInvoice.status === 'paid' ? (
                                                        <>
                                                            <p><strong>Ödeme Tarihi:</strong> {selectedInvoice.paymentDate}</p>
                                                            <p><strong>Ödeme Yöntemi:</strong> {selectedInvoice.paymentMethod}</p>
                                                        </>
                                                    ) : (
                                                        <p className="text-muted">Henüz ödeme yapılmamış</p>
                                                    )}
                                                </div>
                                            </div>
                                            <hr />
                                            <h6>Hizmet Detayları</h6>
                                            <p><strong>Hizmet Türü:</strong> {selectedInvoice.serviceType}</p>
                                            <p><strong>Açıklama:</strong> {selectedInvoice.description}</p>
                                            <hr />
                                            <div className="row">
                                                <div className="col-md-8">
                                                    <h6>Tutar Detayları</h6>
                                                </div>
                                                <div className="col-md-4 text-end">
                                                    <p><strong>Tutar:</strong> {selectedInvoice.amount.toFixed(2)} ₺</p>
                                                    <p><strong>KDV (%18):</strong> {selectedInvoice.tax.toFixed(2)} ₺</p>
                                                    <hr />
                                                    <h5><strong>Toplam:</strong> {selectedInvoice.total.toFixed(2)} ₺</h5>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="modal-footer">
                                            <button type="button" className="btn btn-success">
                                                <i className="fas fa-download me-2"></i>PDF İndir
                                            </button>
                                            <button 
                                                type="button" 
                                                className="btn btn-secondary" 
                                                onClick={() => setShowModal(false)}
                                            >
                                                Kapat
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                    </main>
                    
                    <Footer />
                </div>
            </div>
        </>
    );
};

export default Invoices;