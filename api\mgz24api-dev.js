import express from 'express';
import cors from 'cors';
import mysql from 'mysql2/promise';

// Veritabanı bağlantısı
const pool = mysql.createPool({
    host: 'localhost',
    user: 'root',
    password: '',
    database: 'mgz24db',
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
});

const app = express();
const port = 3001;

// Middleware
app.use(cors({
    origin: ['https://ffl21.fun', 'http://localhost:3000', 'https://mgz24.com', 'https://www.mgz24.com'],
    credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

// Test endpoint
app.get('/api/cihaz/:cihazID', async (req, res) => {
    try {
        const { cihazID } = req.params;
        
        res.json({
            success: true,
            data: {
                cihazID: cihazID,
                message: 'Test data from local development server',
                timestamp: new Date().toISOString()
            }
        });
    } catch (error) {
        console.error('Error:', error);
        res.status(500).json({
            success: false,
            message: 'Development server error'
        });
    }
});

// HTTP sunucusunu başlat
app.listen(port, () => {
    console.log(`Development API server running on http://localhost:${port}`);
    console.log(`Test endpoint: http://localhost:${port}/api/cihaz/55968376`);
});