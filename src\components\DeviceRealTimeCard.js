import React, { useState, useEffect, useCallback } from 'react';
import { fetchRealTimeDeviceData, startRealTimePolling } from '../services/deviceService';
import notificationService from '../services/notificationService';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faThermometerHalf,
  faEye,
  faBatteryFull,
  faBatteryHalf,
  faBatteryEmpty,
  faMapMarkerAlt,
  faSync,
  faExclamationTriangle,
  faCheckCircle,
  faTimesCircle
} from '@fortawesome/free-solid-svg-icons';

const DeviceRealTimeCard = ({ cihazID, onAlertTriggered }) => {
  const [deviceData, setDeviceData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(null);
  const [isPolling, setIsPolling] = useState(false);
  const [dataSource, setDataSource] = useState(null);

  // Pil seviyesi için ikon seçimi
  const getBatteryIcon = (level) => {
    if (level > 50) return faBatteryFull;
    if (level > 20) return faBatteryHalf;
    return faBatteryEmpty;
  };

  // Pil seviyesi için renk seçimi
  const getBatteryColor = (level) => {
    if (level > 50) return 'text-success';
    if (level > 20) return 'text-warning';
    return 'text-danger';
  };

  // Sıcaklık için renk seçimi
  const getTemperatureColor = (temp) => {
    if (temp > 8) return 'text-danger';
    if (temp > 5) return 'text-warning';
    if (temp < -2) return 'text-info';
    return 'text-success';
  };

  // Nem için renk seçimi
  const getHumidityColor = (humidity) => {
    if (humidity > 80) return 'text-danger';
    if (humidity > 60) return 'text-warning';
    return 'text-success';
  };

  // Alarm kontrolü - notification service kullanarak
  const checkAlerts = useCallback((data) => {
    // Notification service ile sensör verilerini kontrol et
    notificationService.checkSensorData(cihazID, {
      sicaklik: data.sicaklik,
      nem: data.nem,
      pil_seviyesi: data.pil_seviyesi,
      kapi_durumu: data.kapi_durumu,
      kapi_acik_kalma_suresi: data.kapi_acik_kalma_suresi,
      son_guncelleme: data.son_guncelleme || new Date().toISOString()
    });

    // Eski alert sistemi de korunuyor (callback için)
    const alerts = [];

    if (data.sicaklik > 8) {
      alerts.push({
        type: 'temperature',
        message: `Sıcaklık kritik seviyede: ${data.sicaklik}°C`,
        severity: 'danger'
      });
    }

    if (data.pil_seviyesi < 20) {
      alerts.push({
        type: 'battery',
        message: `Pil seviyesi düşük: %${data.pil_seviyesi}`,
        severity: 'warning'
      });
    }

    if (data.nem > 80) {
      alerts.push({
        type: 'humidity',
        message: `Nem seviyesi yüksek: %${data.nem}`,
        severity: 'warning'
      });
    }

    if (alerts.length > 0 && onAlertTriggered) {
      onAlertTriggered(cihazID, alerts);
    }

    return alerts;
  }, [cihazID, onAlertTriggered]);

  // Veri çekme fonksiyonu
  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await fetchRealTimeDeviceData(cihazID);

      if (result.success) {
        setDeviceData(result.data.data);
        setDataSource(result.data.source);
        setLastUpdate(new Date());

        // Alarm kontrolü
        checkAlerts(result.data.data);
      } else {
        setError(result.message || 'Veri alınamadı');
      }
    } catch (err) {
      console.error('Device data fetch error:', err);
      setError('Veri çekme hatası');
    } finally {
      setLoading(false);
    }
  }, [cihazID, checkAlerts]);

  // Polling başlat/durdur
  const togglePolling = useCallback(() => {
    if (isPolling) {
      setIsPolling(false);
    } else {
      setIsPolling(true);
      const stopPolling = startRealTimePolling(cihazID, (data) => {
        setDeviceData(data.data);
        setDataSource(data.source);
        setLastUpdate(new Date());
        checkAlerts(data.data);
      }, 5000);

      // Cleanup fonksiyonu
      return () => {
        stopPolling();
        setIsPolling(false);
      };
    }
  }, [cihazID, isPolling, checkAlerts]);

  // Component mount edildiğinde veri çek
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (isPolling) {
        setIsPolling(false);
      }
    };
  }, [isPolling]);

  if (loading && !deviceData) {
    return (
      <div className="card device-realtime-card">
        <div className="card-body">
          <div className="d-flex align-items-center justify-content-center" style={{ height: '200px' }}>
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Yükleniyor...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error && !deviceData) {
    return (
      <div className="card device-realtime-card border-danger">
        <div className="card-body">
          <div className="alert alert-danger d-flex align-items-center">
            <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
            <div>
              <strong>Hata!</strong> {error}
            </div>
          </div>
          <button
            className="btn btn-outline-primary"
            onClick={fetchData}
            disabled={loading}
          >
            <FontAwesomeIcon icon={faSync} className="me-2" />
            Yeniden Dene
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="card device-realtime-card">
      <div className="card-header d-flex justify-content-between align-items-center">
        <h6 className="mb-0">
          <FontAwesomeIcon icon={faSync} className="me-2" />
          Gerçek Zamanlı Veriler - {cihazID}
        </h6>
        <div className="d-flex align-items-center">
          {/* Veri kaynağı göstergesi */}
          <span className={`badge me-2 ${dataSource === 'external_api' ? 'bg-success' : 'bg-warning'}`}>
            {dataSource === 'external_api' ? 'Canlı' : 'Demo'}
          </span>

          {/* Polling toggle butonu */}
          <button
            className={`btn btn-sm ${isPolling ? 'btn-danger' : 'btn-success'}`}
            onClick={togglePolling}
            title={isPolling ? 'Otomatik güncellemeyi durdur' : 'Otomatik güncellemeyi başlat'}
          >
            <FontAwesomeIcon icon={isPolling ? faTimesCircle : faCheckCircle} />
          </button>

          {/* Manuel yenileme butonu */}
          <button
            className="btn btn-sm btn-outline-primary ms-2"
            onClick={fetchData}
            disabled={loading}
            title="Manuel yenile"
          >
            <FontAwesomeIcon icon={faSync} className={loading ? 'fa-spin' : ''} />
          </button>
        </div>
      </div>

      <div className="card-body">
        {deviceData && (
          <div className="sensor-data-container">
            {/* Sıcaklık */}
            <div className="sensor-item">
              <div className="d-flex align-items-center">
                <FontAwesomeIcon
                  icon={faThermometerHalf}
                  className={`me-2 ${getTemperatureColor(parseFloat(deviceData.sicaklik))}`}
                />
                <div>
                  <small className="text-muted">Sıcaklık</small>
                  <div className={`fw-bold ${getTemperatureColor(parseFloat(deviceData.sicaklik))}`}>
                    {deviceData.sicaklik}°C
                  </div>
                </div>
              </div>
            </div>

            {/* Nem */}
            <div className="sensor-item">
              <div className="d-flex align-items-center">
                <FontAwesomeIcon
                  icon={faEye}
                  className={`me-2 ${getHumidityColor(parseFloat(deviceData.nem))}`}
                />
                <div>
                  <small className="text-muted">Nem</small>
                  <div className={`fw-bold ${getHumidityColor(parseFloat(deviceData.nem))}`}>
                    %{deviceData.nem}
                  </div>
                </div>
              </div>
            </div>

            {/* Pil Seviyesi */}
            <div className="sensor-item">
              <div className="d-flex align-items-center">
                <FontAwesomeIcon
                  icon={getBatteryIcon(parseFloat(deviceData.pil_seviyesi))}
                  className={`me-2 ${getBatteryColor(parseFloat(deviceData.pil_seviyesi))}`}
                />
                <div>
                  <small className="text-muted">Pil</small>
                  <div className={`fw-bold ${getBatteryColor(parseFloat(deviceData.pil_seviyesi))}`}>
                    %{deviceData.pil_seviyesi}
                  </div>
                </div>
              </div>
            </div>

            {/* Işık */}
            <div className="sensor-item">
              <div className="d-flex align-items-center">
                <FontAwesomeIcon
                  icon={faEye}
                  className="me-2 text-info"
                />
                <div>
                  <small className="text-muted">Işık</small>
                  <div className="fw-bold text-info">
                    {deviceData.isik}
                  </div>
                </div>
              </div>
            </div>

            {/* Konum */}
            <div className="sensor-item">
              <div className="d-flex align-items-center">
                <FontAwesomeIcon
                  icon={faMapMarkerAlt}
                  className="me-2 text-primary"
                />
                <div>
                  <small className="text-muted">Konum</small>
                  <div className="fw-bold text-primary">
                    {deviceData.enlem}, {deviceData.boylam}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Durum ve Son Güncelleme */}
        <div className="row mt-3 pt-3 border-top">
          <div className="col-md-6">
            <small className="text-muted">Durum: </small>
            <span className={`badge ${deviceData?.durum === 'aktif' ? 'bg-success' : 'bg-secondary'}`}>
              {deviceData?.durum || 'Bilinmiyor'}
            </span>
          </div>
          <div className="col-md-6 text-md-end">
            <small className="text-muted">
              Son Güncelleme: {lastUpdate ? lastUpdate.toLocaleTimeString('tr-TR') : 'Bilinmiyor'}
            </small>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeviceRealTimeCard;