-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sevkiyatGecmis tablosu için cihaz_kodu sütununun karakter setini ve collation'ını düzenleme
ALTER TABLE `sevkiyatGecmis` 
MODIFY COLUMN `cihaz_kodu` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL;

-- <PERSON><PERSON><PERSON> fk_sevkiyatGecmis_cihaz constraint'i varsa önce onu silme
ALTER TABLE `sevkiyatGecmis` 
DROP CONSTRAINT IF EXISTS `fk_sevkiyatGecmis_cihaz`;

-- <PERSON><PERSON><PERSON> karakter seti ve collation ile foreign key constraint'i ekleme
ALTER TABLE `sevkiyatGecmis` 
ADD CONSTRAINT `fk_sevkiyatGecmis_cihaz` 
FOREIGN KEY (`cihaz_kodu`) REFERENCES `cihazBilgi` (`cihaz_kodu`) 
ON DELETE RESTRICT ON UPDATE RESTRICT; 