"use strict";(self.webpackChunkmgz24=self.webpackChunkmgz24||[]).push([[511],{421:(e,a,s)=>{s.d(a,{A:()=>c});s(5043);var l=s(9002),i=s(3910),n=s(7929),t=s(579);const c=()=>{const e=(0,l.zy)(),a="admin"===(()=>{try{var e,a;const s=JSON.parse(localStorage.getItem("user"));return(null===s||void 0===s||null===(e=s.user)||void 0===e?void 0:e.role)||(null===s||void 0===s||null===(a=s.user)||void 0===a?void 0:a.gorev)||"user"}catch(s){return"user"}})();return(0,t.jsx)("div",{className:"sidebar col-md-4 col-lg-2 p-0 bg-light-subtle",children:(0,t.jsxs)("div",{className:"offcanvas-md offcanvas-end",tabIndex:"-1",id:"yanMenu","aria-labelledby":"yanMenu",children:[(0,t.jsxs)("div",{className:"offcanvas-header",children:[(0,t.jsx)("h5",{className:"offcanvas-title",id:"yanMenu",children:"MGZ24 Gold"}),(0,t.jsx)("button",{type:"button",className:"btn-close","data-bs-dismiss":"offcanvas","data-bs-target":"#yanMenu","aria-label":"Kapat"})]}),(0,t.jsxs)("div",{className:"offcanvas-body d-md-flex flex-column p-0 pt-lg-3 overflow-y-auto",children:[(0,t.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase",children:"Sevkiyat \u0130\u015flemleri"}),(0,t.jsxs)("ul",{className:"nav nav-pills flex-column",children:[(0,t.jsx)("li",{className:"nav-item",children:(0,t.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/"===e.pathname?"active":""),to:"/",children:[(0,t.jsx)(i.g,{icon:n.msb}),"Aktif Sevkiyatlar"]})}),(0,t.jsx)("li",{className:"nav-item",children:(0,t.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/add"===e.pathname?"active":""),to:"/add",children:[(0,t.jsx)(i.g,{icon:n.E5r}),"Yeni Sevkiyat Olu\u015ftur"]})}),(0,t.jsx)("li",{className:"nav-item",children:(0,t.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/inactive-devices"===e.pathname?"active":""),to:"/inactive-devices",children:[(0,t.jsx)(i.g,{icon:n.fH7}),"\u0130naktif Cihazlar"]})}),(0,t.jsx)("li",{className:"nav-item",children:(0,t.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/history"===e.pathname?"active":""),to:"/history",children:[(0,t.jsx)(i.g,{icon:n.o9H}),"Ge\xe7mi\u015f Sevkiyatlar"]})})]}),(0,t.jsx)("hr",{className:"my-3"}),(0,t.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase",children:"Sistem Y\xf6netimi"}),(0,t.jsxs)("ul",{className:"nav nav-pills flex-column",children:[a&&(0,t.jsx)("li",{className:"nav-item",children:(0,t.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/devices"===e.pathname?"active":""),to:"/devices",children:[(0,t.jsx)(i.g,{icon:n.ArK}),"Cihaz Y\xf6netimi"]})}),(0,t.jsx)("li",{className:"nav-item",children:(0,t.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/notifications"===e.pathname?"active":""),to:"/notifications",children:[(0,t.jsx)(i.g,{icon:n.z$e}),"Bildirimler"]})})]}),(0,t.jsx)("hr",{className:"my-3"}),(0,t.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase",children:"Kullan\u0131c\u0131 Ayarlar\u0131"}),(0,t.jsxs)("ul",{className:"nav nav-pills flex-column",children:[(0,t.jsx)("li",{className:"nav-item",children:(0,t.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/profile"===e.pathname?"active":""),to:"/profile",children:[(0,t.jsx)(i.g,{icon:n.X46}),"Kullan\u0131c\u0131 Profilim"]})}),(0,t.jsx)("li",{className:"nav-item",children:(0,t.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/viewers"===e.pathname?"active":""),to:"/viewers",children:[(0,t.jsx)(i.g,{icon:n.yLE}),"\u0130zleyici \u0130\u015flemleri"]})})]}),(0,t.jsx)("hr",{className:"my-3"}),(0,t.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase",children:"\xd6deme ve Yap\u0131land\u0131rma"}),(0,t.jsxs)("ul",{className:"nav nav-pills flex-column",children:[(0,t.jsx)("li",{className:"nav-item",children:(0,t.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/payment"===e.pathname?"active":""),to:"/payment",children:[(0,t.jsx)(i.g,{icon:n.$O8}),"\xd6deme Yap"]})}),(0,t.jsx)("li",{className:"nav-item",children:(0,t.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/invoices"===e.pathname?"active":""),to:"/invoices",children:[(0,t.jsx)(i.g,{icon:n.bLf}),"Faturalar\u0131m"]})})]}),(0,t.jsx)("hr",{className:"my-3"}),(0,t.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-body-secondary text-uppercase",children:"Kredi Durumu"})]})]})})}},834:(e,a,s)=>{s.d(a,{A:()=>i});s(5043);var l=s(579);const i=()=>(0,l.jsx)("footer",{className:"py-5 border-top",children:(0,l.jsx)("p",{className:"text-center",children:"\xa92025 Inkatech \xd6l\xe7\xfcm Sistemleri"})})},1899:(e,a,s)=>{s.d(a,{A:()=>d});var l=s(5043),i=s(9002),n=s(3910),t=s(7929);var c=s(4713),r=s(579);const d=()=>{const e=(0,i.Zp)(),[a,s]=(0,l.useState)("Misafir"),[d,o]=(0,l.useState)(!0);(0,l.useEffect)((()=>{(async()=>{try{var e,a,l,i;const t=JSON.parse(localStorage.getItem("user")),r=(null===t||void 0===t||null===(e=t.user)||void 0===e?void 0:e.musteri_ID)||(null===t||void 0===t||null===(a=t.user)||void 0===a?void 0:a.id),d=(null===t||void 0===t||null===(l=t.user)||void 0===l?void 0:l.name)||(null===t||void 0===t||null===(i=t.user)||void 0===i?void 0:i.musteri_adi);if(!r)return console.warn("Oturum bilgisi bulunamad\u0131"),s("Misafir"),void o(!1);if(d)return s(d),void o(!1);try{const e=await c.Qj.getKullanici(r);e&&e.musteri_adi&&(s(e.musteri_adi),null!==t&&void 0!==t&&t.user&&(t.user.name=e.musteri_adi,localStorage.setItem("user",JSON.stringify(t))))}catch(n){console.warn("API'den kullan\u0131c\u0131 bilgisi al\u0131namad\u0131, localStorage kullan\u0131l\u0131yor"),s(d||"Kullan\u0131c\u0131")}}catch(t){console.error("Kullan\u0131c\u0131 bilgileri al\u0131n\u0131rken hata:",t),s("Kullan\u0131c\u0131")}finally{o(!1)}})()}),[]);return(0,r.jsxs)("header",{className:"navbar sticky-top bg-darkblue flex-md-nowrap p-0 shadow","data-bs-theme":"dark",children:[(0,r.jsx)(i.N_,{className:"navbar-brand col-md-4 col-lg-2 me-0 px-3 py-2 fs-6 text-white",to:"/",children:(0,r.jsx)("img",{src:"data:image/png;base64,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",alt:"MGZ24 Logo",height:"40"})}),(0,r.jsxs)("ul",{className:"navbar-nav flex-row align-items-center",children:[(0,r.jsx)("li",{className:"nav-item text-nowrap d-none d-md-block me-3",children:(0,r.jsxs)("span",{className:"text-white",children:[(0,r.jsx)(n.g,{icon:t.X46,className:"me-2"}),d?"Y\xfckleniyor...":a]})}),(0,r.jsx)("li",{className:"nav-item text-nowrap px-1",children:(0,r.jsx)("button",{onClick:()=>{localStorage.removeItem("user"),e("/login")},className:"nav-link px-3","data-bs-toggle":"tooltip","data-bs-placement":"left","data-bs-title":"\xc7\u0131k\u0131\u015f yap!",children:(0,r.jsx)(n.g,{icon:t.yBu})})}),(0,r.jsx)("li",{className:"nav-item text-nowrap d-md-none",children:(0,r.jsx)("button",{className:"nav-link px-3",type:"button","data-bs-toggle":"offcanvas","data-bs-target":"#yanMenu","aria-controls":"yanMenu","aria-expanded":"false","aria-label":"menu a\xe7/kapa",children:(0,r.jsx)(n.g,{icon:t.ckx})})})]})]})}},9511:(e,a,s)=>{s.r(a),s.d(a,{default:()=>x});var l=s(9379),i=s(5043),n=s(9002),t=s(3910),c=s(7929),r=s(1899),d=s(421),o=s(834),m=s(9144),h=s(5492),u=s(579);const x=()=>{const[e,a]=(0,i.useState)([]),[s,x]=(0,i.useState)([]),[v,A]=(0,i.useState)(!0),[b,g]=(0,i.useState)(null),[j,N]=(0,i.useState)("all"),[p,y]=(0,i.useState)([]),[f,k]=(0,i.useState)(!1);(0,i.useEffect)((()=>{z()}),[]),(0,i.useEffect)((()=>{C()}),[e,j]);const z=async()=>{try{A(!0),g(null);const e=[{id:1,type:"temperature",severity:"high",title:"Y\xfcksek S\u0131cakl\u0131k Alarm\u0131",message:"Cihaz MGZ-001 s\u0131cakl\u0131k de\u011feri 12\xb0C (Normal: 2-8\xb0C)",deviceId:"MGZ-001",shipmentId:"SVK-2024-001",timestamp:new Date(Date.now()-3e5),read:!1,location:"Ankara - \u0130stanbul Yolu",resolved:!1},{id:2,type:"battery",severity:"medium",title:"D\xfc\u015f\xfck Batarya",message:"Cihaz MGZ-002 batarya seviyesi %15",deviceId:"MGZ-002",shipmentId:"SVK-2024-002",timestamp:new Date(Date.now()-18e5),read:!1,location:"\u0130zmir - Manisa Yolu",resolved:!1},{id:3,type:"humidity",severity:"low",title:"Nem Seviyesi Uyar\u0131s\u0131",message:"Cihaz MGZ-003 nem seviyesi %85 (Normal: 40-60%)",deviceId:"MGZ-003",shipmentId:"SVK-2024-003",timestamp:new Date(Date.now()-72e5),read:!0,location:"Bursa - Eski\u015fehir Yolu",resolved:!0},{id:4,type:"door",severity:"high",title:"Kap\u0131 A\xe7\u0131lmas\u0131",message:"Cihaz MGZ-004 kap\u0131 sens\xf6r\xfc tetiklendi",deviceId:"MGZ-004",shipmentId:"SVK-2024-004",timestamp:new Date(Date.now()-144e5),read:!0,location:"Adana - Mersin Yolu",resolved:!1},{id:5,type:"connection",severity:"medium",title:"Ba\u011flant\u0131 Kayb\u0131",message:"Cihaz MGZ-005 ile ba\u011flant\u0131 kesildi",deviceId:"MGZ-005",shipmentId:"SVK-2024-005",timestamp:new Date(Date.now()-216e5),read:!0,location:"Antalya - Konya Yolu",resolved:!0}];a(e),A(!1)}catch(b){console.error("Bildirim verileri al\u0131n\u0131rken hata:",b),g("Bildirim verileri y\xfcklenirken hata olu\u015ftu: "+b.message),A(!1)}},C=()=>{let a=[...e];switch(j){case"unread":a=a.filter((e=>!e.read));break;case"high":a=a.filter((e=>"high"===e.severity));break;case"medium":a=a.filter((e=>"medium"===e.severity));break;case"low":a=a.filter((e=>"low"===e.severity));break;case"resolved":a=a.filter((e=>e.resolved));break;case"unresolved":a=a.filter((e=>!e.resolved))}x(a)},M=e=>{const a=new Date-e,s=Math.floor(a/6e4),l=Math.floor(a/36e5),i=Math.floor(a/864e5);return s<60?"".concat(s," dakika \xf6nce"):l<24?"".concat(l," saat \xf6nce"):"".concat(i," g\xfcn \xf6nce")},O=e=>{switch(e){case"temperature":return c.Rog;case"battery":return c.CfV;case"humidity":return c.yzd;case"door":return c.uQF;case"connection":return c.BwJ;default:return c.z$e}},S=e=>{switch(e){case"high":return(0,u.jsx)("span",{className:"badge bg-danger",children:"Y\xfcksek"});case"medium":return(0,u.jsx)("span",{className:"badge bg-warning",children:"Orta"});case"low":return(0,u.jsx)("span",{className:"badge bg-info",children:"D\xfc\u015f\xfck"});default:return(0,u.jsx)("span",{className:"badge bg-secondary",children:"Bilinmiyor"})}},Z=a=>{switch(a){case"unread":return e.filter((e=>!e.read)).length;case"high":return e.filter((e=>"high"===e.severity)).length;case"medium":return e.filter((e=>"medium"===e.severity)).length;case"low":return e.filter((e=>"low"===e.severity)).length;case"resolved":return e.filter((e=>e.resolved)).length;case"unresolved":return e.filter((e=>!e.resolved)).length;default:return e.length}};return(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(r.A,{}),(0,u.jsx)("div",{className:"container-fluid",children:(0,u.jsxs)("div",{className:"row",children:[(0,u.jsx)(d.A,{}),(0,u.jsxs)("main",{className:"col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white",children:[(0,u.jsxs)("div",{className:"d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom",children:[(0,u.jsxs)("h1",{className:"h2",children:[(0,u.jsx)(t.g,{icon:c.z$e,className:"me-2"}),"Bildirimler",e.filter((e=>!e.read)).length>0&&(0,u.jsx)("span",{className:"badge bg-danger ms-2",children:e.filter((e=>!e.read)).length})]}),(0,u.jsxs)("button",{className:"btn btn-outline-secondary",onClick:()=>k(!f),children:[(0,u.jsx)(t.g,{icon:c.dB,className:"me-2"}),"Ayarlar"]})]}),v?(0,u.jsx)(m.A,{size:"lg",variant:"primary",message:"Bildirimler y\xfckleniyor...",centered:!0}):b?(0,u.jsx)(h.A,{message:b,variant:"danger",title:"Veri Y\xfckleme Hatas\u0131",dismissible:!0,onDismiss:()=>g(""),children:(0,u.jsx)("button",{className:"btn btn-primary btn-sm mt-2",onClick:z,children:"Yeniden Dene"})}):(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)("div",{className:"card mb-4",children:(0,u.jsxs)("div",{className:"card-body",children:[(0,u.jsxs)("div",{className:"row g-2",children:[(0,u.jsx)("div",{className:"col-auto",children:(0,u.jsxs)("button",{className:"btn btn-sm ".concat("all"===j?"btn-primary":"btn-outline-primary"),onClick:()=>N("all"),children:["T\xfcm\xfc (",Z("all"),")"]})}),(0,u.jsx)("div",{className:"col-auto",children:(0,u.jsxs)("button",{className:"btn btn-sm ".concat("unread"===j?"btn-primary":"btn-outline-primary"),onClick:()=>N("unread"),children:["Okunmam\u0131\u015f (",Z("unread"),")"]})}),(0,u.jsx)("div",{className:"col-auto",children:(0,u.jsxs)("button",{className:"btn btn-sm ".concat("high"===j?"btn-danger":"btn-outline-danger"),onClick:()=>N("high"),children:["Y\xfcksek (",Z("high"),")"]})}),(0,u.jsx)("div",{className:"col-auto",children:(0,u.jsxs)("button",{className:"btn btn-sm ".concat("medium"===j?"btn-warning":"btn-outline-warning"),onClick:()=>N("medium"),children:["Orta (",Z("medium"),")"]})}),(0,u.jsx)("div",{className:"col-auto",children:(0,u.jsxs)("button",{className:"btn btn-sm ".concat("low"===j?"btn-info":"btn-outline-info"),onClick:()=>N("low"),children:["D\xfc\u015f\xfck (",Z("low"),")"]})}),(0,u.jsx)("div",{className:"col-auto",children:(0,u.jsxs)("button",{className:"btn btn-sm ".concat("unresolved"===j?"btn-warning":"btn-outline-warning"),onClick:()=>N("unresolved"),children:["\xc7\xf6z\xfclmemi\u015f (",Z("unresolved"),")"]})})]}),p.length>0&&(0,u.jsxs)("div",{className:"mt-3 d-flex gap-2",children:[(0,u.jsxs)("button",{className:"btn btn-sm btn-outline-success",onClick:()=>{a(e.map((e=>p.includes(e.id)?(0,l.A)((0,l.A)({},e),{},{read:!0}):e))),y([])},children:[(0,u.jsx)(t.g,{icon:c.e68,className:"me-1"}),"Okundu \u0130\u015faretle (",p.length,")"]}),(0,u.jsxs)("button",{className:"btn btn-sm btn-outline-danger",onClick:()=>{a(e.filter((e=>!p.includes(e.id)))),y([])},children:[(0,u.jsx)(t.g,{icon:c.yLS,className:"me-1"}),"Sil (",p.length,")"]})]})]})}),(0,u.jsx)("div",{className:"card",children:(0,u.jsx)("div",{className:"card-body p-0",children:0===s.length?(0,u.jsxs)("div",{className:"text-center py-5",children:[(0,u.jsx)(t.g,{icon:c.z$e,className:"fa-3x text-muted mb-3"}),(0,u.jsx)("h5",{className:"text-muted",children:"Bildirim Bulunamad\u0131"}),(0,u.jsx)("p",{className:"text-muted",children:"Se\xe7ili filtreye uygun bildirim bulunamad\u0131."})]}):(0,u.jsx)("div",{className:"list-group list-group-flush",children:s.map((s=>(0,u.jsx)("div",{className:"list-group-item ".concat(s.read?"":"list-group-item-light"),children:(0,u.jsxs)("div",{className:"d-flex align-items-start",children:[(0,u.jsx)("div",{className:"me-3",children:(0,u.jsx)("input",{type:"checkbox",className:"form-check-input",checked:p.includes(s.id),onChange:()=>{return e=s.id,void y((a=>a.includes(e)?a.filter((a=>a!==e)):[...a,e]));var e}})}),(0,u.jsx)("div",{className:"me-3",children:(0,u.jsx)("div",{className:"notification-icon ".concat(s.severity),children:(0,u.jsx)(t.g,{icon:O(s.type),className:"text-".concat("high"===s.severity?"danger":"medium"===s.severity?"warning":"info")})})}),(0,u.jsxs)("div",{className:"flex-grow-1",children:[(0,u.jsxs)("div",{className:"d-flex justify-content-between align-items-start",children:[(0,u.jsxs)("div",{children:[(0,u.jsxs)("h6",{className:"mb-1",children:[s.title,!s.read&&(0,u.jsx)("span",{className:"badge bg-primary ms-2",children:"Yeni"}),s.resolved&&(0,u.jsx)("span",{className:"badge bg-success ms-2",children:"\xc7\xf6z\xfcld\xfc"})]}),(0,u.jsx)("p",{className:"mb-1",children:s.message}),(0,u.jsxs)("div",{className:"small text-muted",children:[(0,u.jsx)(t.g,{icon:c.Pcr,className:"me-1"}),s.location,(0,u.jsx)("span",{className:"mx-2",children:"\u2022"}),(0,u.jsx)(t.g,{icon:c.BEE,className:"me-1"}),M(s.timestamp)]})]}),(0,u.jsx)("div",{className:"text-end",children:S(s.severity)})]}),(0,u.jsx)("div",{className:"mt-2",children:(0,u.jsxs)("div",{className:"btn-group btn-group-sm",children:[(0,u.jsxs)(n.N_,{to:"/view/".concat(s.shipmentId),className:"btn btn-outline-primary",children:[(0,u.jsx)(t.g,{icon:c.pS3,className:"me-1"}),"Detay"]}),!s.read&&(0,u.jsxs)("button",{className:"btn btn-outline-success",onClick:()=>{return i=s.id,void a(e.map((e=>e.id===i?(0,l.A)((0,l.A)({},e),{},{read:!0}):e)));var i},children:[(0,u.jsx)(t.g,{icon:c.e68,className:"me-1"}),"Okundu"]}),!s.resolved&&(0,u.jsxs)("button",{className:"btn btn-outline-warning",onClick:()=>{return i=s.id,void a(e.map((e=>e.id===i?(0,l.A)((0,l.A)({},e),{},{resolved:!0}):e)));var i},children:[(0,u.jsx)(t.g,{icon:c.e68,className:"me-1"}),"\xc7\xf6z\xfcld\xfc"]}),(0,u.jsx)("button",{className:"btn btn-outline-danger",onClick:()=>{return l=s.id,void a(e.filter((e=>e.id!==l)));var l},children:(0,u.jsx)(t.g,{icon:c.yLS})})]})})]})]})},s.id)))})})})]})]}),(0,u.jsx)(o.A,{})]})}),f&&(0,u.jsx)("div",{className:"modal show d-block",tabIndex:"-1",style:{backgroundColor:"rgba(0,0,0,0.5)"},children:(0,u.jsx)("div",{className:"modal-dialog",children:(0,u.jsxs)("div",{className:"modal-content",children:[(0,u.jsxs)("div",{className:"modal-header",children:[(0,u.jsx)("h5",{className:"modal-title",children:"Bildirim Ayarlar\u0131"}),(0,u.jsx)("button",{type:"button",className:"btn-close",onClick:()=>k(!1)})]}),(0,u.jsxs)("div",{className:"modal-body",children:[(0,u.jsxs)("div",{className:"form-check",children:[(0,u.jsx)("input",{className:"form-check-input",type:"checkbox",id:"emailNotifications",defaultChecked:!0}),(0,u.jsx)("label",{className:"form-check-label",htmlFor:"emailNotifications",children:"E-posta bildirimleri"})]}),(0,u.jsxs)("div",{className:"form-check",children:[(0,u.jsx)("input",{className:"form-check-input",type:"checkbox",id:"smsNotifications"}),(0,u.jsx)("label",{className:"form-check-label",htmlFor:"smsNotifications",children:"SMS bildirimleri"})]}),(0,u.jsxs)("div",{className:"form-check",children:[(0,u.jsx)("input",{className:"form-check-input",type:"checkbox",id:"pushNotifications",defaultChecked:!0}),(0,u.jsx)("label",{className:"form-check-label",htmlFor:"pushNotifications",children:"Push bildirimleri"})]}),(0,u.jsx)("hr",{}),(0,u.jsx)("h6",{children:"Bildirim T\xfcrleri"}),(0,u.jsxs)("div",{className:"form-check",children:[(0,u.jsx)("input",{className:"form-check-input",type:"checkbox",id:"tempAlerts",defaultChecked:!0}),(0,u.jsx)("label",{className:"form-check-label",htmlFor:"tempAlerts",children:"S\u0131cakl\u0131k alarmlar\u0131"})]}),(0,u.jsxs)("div",{className:"form-check",children:[(0,u.jsx)("input",{className:"form-check-input",type:"checkbox",id:"batteryAlerts",defaultChecked:!0}),(0,u.jsx)("label",{className:"form-check-label",htmlFor:"batteryAlerts",children:"Batarya alarmlar\u0131"})]}),(0,u.jsxs)("div",{className:"form-check",children:[(0,u.jsx)("input",{className:"form-check-input",type:"checkbox",id:"doorAlerts",defaultChecked:!0}),(0,u.jsx)("label",{className:"form-check-label",htmlFor:"doorAlerts",children:"Kap\u0131 alarmlar\u0131"})]})]}),(0,u.jsxs)("div",{className:"modal-footer",children:[(0,u.jsx)("button",{type:"button",className:"btn btn-secondary",onClick:()=>k(!1),children:"\u0130ptal"}),(0,u.jsx)("button",{type:"button",className:"btn btn-primary",onClick:()=>k(!1),children:"Kaydet"})]})]})})})]})}}}]);
//# sourceMappingURL=511.14e4010b.chunk.js.map