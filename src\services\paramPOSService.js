/**
 * Param POS API Service
 * Context7 referansı: /stripe-samples/accept-a-payment - API service patterns
 * Param POS API entegrasyonu için adapte edilmiştir
 */

class ParamPOSService {
  constructor() {
    this.apiUrl = process.env.REACT_APP_PARAM_API_URL || '/api/param-pos';
    this.testMode = process.env.NODE_ENV !== 'production';
    this.merchantId = process.env.REACT_APP_PARAM_MERCHANT_ID || 'demo_merchant';
    this.terminalId = process.env.REACT_APP_PARAM_TERMINAL_ID || 'demo_terminal';
  }

  /**
   * API isteği gönder
   * Context7 referansı: Stripe fetch API patterns
   */
  async makeRequest(endpoint, options = {}) {
    const url = `${this.apiUrl}${endpoint}`;

    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Merchant-ID': this.merchantId,
        'X-Terminal-ID': this.terminalId,
        ...options.headers
      },
      ...options
    };

    try {
      const response = await fetch(url, defaultOptions);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Param POS API Error:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Hata yönetimi
   */
  handleError(error) {
    const errorMap = {
      'INVALID_CARD_NUMBER': 'Geçersiz kart numarası',
      'EXPIRED_CARD': 'Kart süresi dolmuş',
      'INSUFFICIENT_FUNDS': 'Yetersiz bakiye',
      'INVALID_CVV': 'Geçersiz güvenlik kodu',
      'CARD_BLOCKED': 'Kart blokeli',
      'TRANSACTION_DECLINED': 'İşlem reddedildi',
      'NETWORK_ERROR': 'Ağ bağlantı hatası',
      'MERCHANT_ERROR': 'Üye işyeri hatası',
      'SYSTEM_ERROR': 'Sistem hatası'
    };

    const message = errorMap[error.code] || error.message || 'Bilinmeyen hata';

    return {
      ...error,
      message,
      userFriendlyMessage: message
    };
  }

  /**
   * Ödeme intent'i oluştur
   * Context7 referansı: /stripe-samples/accept-a-payment - create-payment-intent
   */
  async createPaymentIntent(params) {
    const { amount, currency = 'TRY', packageInfo, metadata = {} } = params;

    const payload = {
      amount: Math.round(amount * 100), // Kuruş cinsine çevir
      currency: currency.toUpperCase(),
      merchantId: this.merchantId,
      terminalId: this.terminalId,
      packageInfo,
      metadata: {
        ...metadata,
        source: 'mgz24_web',
        timestamp: new Date().toISOString()
      },
      testMode: this.testMode
    };

    return await this.makeRequest('/create-payment-intent', {
      method: 'POST',
      body: JSON.stringify(payload)
    });
  }

  /**
   * Ödemeyi onayla
   * Context7 referansı: /stripe-samples/accept-a-payment - confirm payment
   */
  async confirmPayment(params) {
    const { clientSecret, paymentMethod } = params;

    const payload = {
      clientSecret,
      paymentMethod: {
        card: {
          number: paymentMethod.card.number,
          expiryMonth: paymentMethod.card.expiryMonth,
          expiryYear: paymentMethod.card.expiryYear,
          cvv: paymentMethod.card.cvv
        },
        billingDetails: {
          name: paymentMethod.billingDetails.name
        }
      },
      merchantId: this.merchantId,
      terminalId: this.terminalId
    };

    return await this.makeRequest('/confirm-payment', {
      method: 'POST',
      body: JSON.stringify(payload)
    });
  }

  /**
   * Ödeme durumunu sorgula
   */
  async getPaymentStatus(paymentIntentId) {
    return await this.makeRequest(`/payment-status/${paymentIntentId}`, {
      method: 'GET'
    });
  }

  /**
   * İade işlemi başlat
   */
  async refundPayment(params) {
    const { paymentIntentId, amount, reason = 'requested_by_customer' } = params;

    const payload = {
      paymentIntentId,
      amount: amount ? Math.round(amount * 100) : undefined, // Partial refund
      reason,
      merchantId: this.merchantId,
      terminalId: this.terminalId
    };

    return await this.makeRequest('/refund', {
      method: 'POST',
      body: JSON.stringify(payload)
    });
  }

  /**
   * 3D Secure sonucunu doğrula
   */
  async verify3DSecure(params) {
    const { paymentIntentId, authenticationResult } = params;

    const payload = {
      paymentIntentId,
      authenticationResult,
      merchantId: this.merchantId,
      terminalId: this.terminalId
    };

    return await this.makeRequest('/verify-3ds', {
      method: 'POST',
      body: JSON.stringify(payload)
    });
  }

  /**
   * Webhook imzasını doğrula
   */
  async verifyWebhookSignature(payload, signature, secret) {
    // Bu gerçek bir implementasyonda crypto library kullanılmalı
    // Şimdilik basit bir kontrol yapıyoruz
    const expectedSignature = await this.generateSignature(payload, secret);
    return signature === expectedSignature;
  }

  /**
   * İmza oluştur (webhook için)
   */
  async generateSignature(payload, secret) {
    try {
      // Browser ortamında Web Crypto API kullan
      if (typeof window !== 'undefined' && window.crypto && window.crypto.subtle) {
        // Secret'i Uint8Array'e çevir
        const encoder = new TextEncoder();
        const secretKey = await window.crypto.subtle.importKey(
          'raw',
          encoder.encode(secret),
          { name: 'HMAC', hash: 'SHA-256' },
          false,
          ['sign']
        );

        // Payload'u imzala
        const signature = await window.crypto.subtle.sign(
          'HMAC',
          secretKey,
          encoder.encode(payload)
        );

        // Hex string'e çevir
        return Array.from(new Uint8Array(signature))
          .map(b => b.toString(16).padStart(2, '0'))
          .join('');
      } else {
        // Node.js ortamında veya Web Crypto API desteklenmiyorsa fallback
        console.warn('Web Crypto API desteklenmiyor, basit hash kullanılıyor');
        // Basit bir hash alternatifi (gerçek production'da kullanılmamalı)
        let hash = 0;
        const str = payload + secret;
        for (let i = 0; i < str.length; i++) {
          const char = str.charCodeAt(i);
          hash = ((hash << 5) - hash) + char;
          hash = hash & hash; // 32bit integer'a çevir
        }
        return Math.abs(hash).toString(16);
      }
    } catch (error) {
      console.error('İmza oluşturulurken hata:', error);
      // Fallback hash
      let hash = 0;
      const str = payload + secret;
      for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
      }
      return Math.abs(hash).toString(16);
    }
  }

  /**
   * Test kart numaralarını getir
   */
  getTestCards() {
    return {
      success: {
        number: '****************',
        cvv: '000',
        expiryMonth: '12',
        expiryYear: '25',
        name: 'TEST KART'
      },
      declined: {
        number: '****************',
        cvv: '000',
        expiryMonth: '12',
        expiryYear: '25',
        name: 'REDDEDILEN KART'
      },
      insufficientFunds: {
        number: '****************',
        cvv: '000',
        expiryMonth: '12',
        expiryYear: '25',
        name: 'YETERSIZ BAKIYE'
      },
      threeDSecure: {
        number: '****************',
        cvv: '000',
        expiryMonth: '12',
        expiryYear: '25',
        name: '3D SECURE TEST'
      }
    };
  }

  /**
   * Üye işyeri bilgilerini getir
   */
  async getMerchantInfo() {
    return await this.makeRequest('/merchant-info', {
      method: 'GET'
    });
  }

  /**
   * İşlem geçmişini getir
   */
  async getTransactionHistory(params = {}) {
    const { page = 1, limit = 20, startDate, endDate, status } = params;

    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...(startDate && { startDate }),
      ...(endDate && { endDate }),
      ...(status && { status })
    });

    return await this.makeRequest(`/transactions?${queryParams}`, {
      method: 'GET'
    });
  }

  /**
   * Günlük rapor getir
   */
  async getDailyReport(date = new Date()) {
    const dateStr = date.toISOString().split('T')[0]; // YYYY-MM-DD format

    return await this.makeRequest(`/reports/daily/${dateStr}`, {
      method: 'GET'
    });
  }

  /**
   * Taksit seçeneklerini getir
   */
  async getInstallmentOptions(amount, cardBin) {
    const payload = {
      amount: Math.round(amount * 100),
      cardBin: cardBin.substring(0, 6), // İlk 6 hane
      merchantId: this.merchantId
    };

    return await this.makeRequest('/installment-options', {
      method: 'POST',
      body: JSON.stringify(payload)
    });
  }
}

// Singleton instance
export const paramPOSService = new ParamPOSService();
export default paramPOSService;