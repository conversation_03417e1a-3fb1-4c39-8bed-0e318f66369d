{"version": 3, "file": "static/js/364.3f9cac72.chunk.js", "mappings": "gKASA,MAwGA,EAxGgBA,KACZ,MAAMC,GAAWC,EAAAA,EAAAA,MAaXC,EAAuB,UAVTC,MAChB,IAAK,IAADC,EAAAC,EACA,MAAMC,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C,OAAW,OAAJJ,QAAI,IAAJA,GAAU,QAANF,EAAJE,EAAMA,YAAI,IAAAF,OAAN,EAAJA,EAAYO,QAAY,OAAJL,QAAI,IAAJA,GAAU,QAAND,EAAJC,EAAMA,YAAI,IAAAD,OAAN,EAAJA,EAAYO,QAAS,MACpD,CAAE,MAAAC,GACE,MAAO,MACX,GAGaV,GAGjB,OACIW,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC1DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6BAA6BG,SAAS,KAAKC,GAAG,UAAU,kBAAgB,UAASH,SAAA,EAC5FC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,EAC7BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,kBAAkBI,GAAG,UAASH,SAAC,gBAC7CF,EAAAA,EAAAA,KAAA,UAAQM,KAAK,SAASL,UAAU,YAAY,kBAAgB,YAAY,iBAAe,WAAW,aAAW,cAEjHE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mEAAkEC,SAAA,EAC7EF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,kCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,MAAtBtB,EAASuB,SAAmB,SAAW,IAAMC,GAAG,IAAGR,SAAA,EAC5GF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMC,EAAAA,MAAW,0BAG1Cb,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,SAAtBtB,EAASuB,SAAsB,SAAW,IAAMC,GAAG,OAAMR,SAAA,EAClHF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAME,EAAAA,MAAgB,mCAG/Cd,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,sBAAtBtB,EAASuB,SAAmC,SAAW,IAAMC,GAAG,oBAAmBR,SAAA,EAC5IF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMG,EAAAA,MAAY,8BAG3Cf,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMI,EAAAA,MAAmB,sCAKtDhB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wBACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,CAEpCd,IACGY,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMK,EAAAA,MAAa,0BAIhDjB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,mBAAtBtB,EAASuB,SAAgC,SAAW,IAAMC,GAAG,iBAAgBR,SAAA,EACtIF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMM,EAAAA,MAAU,uBAK7ClB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,uCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMO,EAAAA,MAAU,qCAGzCnB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMQ,EAAAA,MAAgB,6CAKnDpB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMS,EAAAA,MAAgB,qBAG/CrB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,cAAtBtB,EAASuB,SAA2B,SAAW,IAAMC,GAAG,YAAWR,SAAA,EAC5HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMU,EAAAA,MAAe,4BAKlDtB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wBAG1I,C,sDC3Gd,MAQA,EAReqB,KAEPvB,EAAAA,EAAAA,KAAA,UAAQC,UAAU,kBAAiBC,UAC/BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,cAAaC,SAAC,iD,qGCEvC,MA6GA,EA7GesB,KACX,MAAMC,GAAWC,EAAAA,EAAAA,OACVC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,YAClCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAGvCG,EAAAA,EAAAA,YAAU,KACgBC,WAClB,IAAK,IAADC,EAAAC,EAAAC,EAAAC,EAEA,MAAMC,EAAa7C,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C2C,GAAmB,OAAVD,QAAU,IAAVA,GAAgB,QAANJ,EAAVI,EAAY9C,YAAI,IAAA0C,OAAN,EAAVA,EAAkBM,cAAwB,OAAVF,QAAU,IAAVA,GAAgB,QAANH,EAAVG,EAAY9C,YAAI,IAAA2C,OAAN,EAAVA,EAAkB9B,IAC3DsB,GAAqB,OAAVW,QAAU,IAAVA,GAAgB,QAANF,EAAVE,EAAY9C,YAAI,IAAA4C,OAAN,EAAVA,EAAkBK,QAAkB,OAAVH,QAAU,IAAVA,GAAgB,QAAND,EAAVC,EAAY9C,YAAI,IAAA6C,OAAN,EAAVA,EAAkBK,aAE7D,IAAKH,EAID,OAHAI,QAAQC,KAAK,kCACbhB,EAAY,gBACZG,GAAW,GAKf,GAAIJ,EAGA,OAFAC,EAAYD,QACZI,GAAW,GAKf,IACI,MAAMc,QAAiBC,EAAAA,GAAiBC,aAAaR,GAGjDM,GAAYA,EAASH,cACrBd,EAAYiB,EAASH,aAGP,OAAVJ,QAAU,IAAVA,GAAAA,EAAY9C,OACZ8C,EAAW9C,KAAKiD,KAAOI,EAASH,YAChC/C,aAAaqD,QAAQ,OAAQvD,KAAKwD,UAAUX,KAGxD,CAAE,MAAOY,GACLP,QAAQC,KAAK,gGAEbhB,EAAYD,GAAY,sBAC5B,CACJ,CAAE,MAAOwB,GACLR,QAAQQ,MAAM,0DAAuCA,GAErDvB,EAAY,sBAChB,CAAC,QACGG,GAAW,EACf,GAGJqB,EAAe,GAChB,IAWH,OACIjD,EAAAA,EAAAA,MAAA,UAAQF,UAAU,0DAA0D,gBAAc,OAAMC,SAAA,EAC5FF,EAAAA,EAAAA,KAACO,EAAAA,GAAI,CAACN,UAAU,gEAAgES,GAAG,IAAGR,UAClFF,EAAAA,EAAAA,KAAA,OAAKqD,I,60RAAeC,IAAI,aAAaC,OAAO,UAEhDpD,EAAAA,EAAAA,MAAA,MAAIF,UAAU,yCAAwCC,SAAA,EAClDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8CAA6CC,UACvDC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,aAAYC,SAAA,EACxBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMO,EAAAA,IAAQlB,UAAU,SACxC6B,EAAU,mBAAkBH,QAGrC3B,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4BAA2BC,UACrCF,EAAAA,EAAAA,KAAA,UACIwD,QAtBCC,KAEjB9D,aAAa+D,WAAW,QAGxBjC,EAAS,SAAS,EAkBFxB,UAAU,gBACV,iBAAe,UACf,oBAAkB,OAClB,gBAAc,+BAAYC,UAE1BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM+C,EAAAA,WAG/B3D,EAAAA,EAAAA,KAAA,MAAIC,UAAU,iCAAgCC,UAC1CF,EAAAA,EAAAA,KAAA,UACIC,UAAU,gBACVK,KAAK,SACL,iBAAe,YACf,iBAAe,WACf,gBAAc,UACd,gBAAc,QACd,aAAW,kBAAcJ,UAEzBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMgD,EAAAA,eAI9B,C,0ICxGjB,MA6SA,EA7SgBC,KACZ,MAAOC,EAASC,IAAclC,EAAAA,EAAAA,UAAS,KAChCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCsB,EAAOa,IAAYnC,EAAAA,EAAAA,UAAS,OAC5BoC,EAAaC,IAAkBrC,EAAAA,EAAAA,WAAS,IACxCsC,EAAWC,IAAgBvC,EAAAA,EAAAA,UAAS,CACvCY,KAAM,GACN4B,MAAO,GACPC,MAAO,GACPC,YAAa,UAIjBvC,EAAAA,EAAAA,YAAU,KACeC,WACjB,IACIF,GAAW,GACXiC,EAAS,MAGT,MAKMQ,SALiBC,EAAAA,EAAMC,IAAI,oBAAqB,CAClDC,OAAQ,CAAE9E,KAAM,aAIS+E,KAAKC,KAAIrF,IAAI,CACtCa,GAAIb,EAAKgD,WACTC,KAAMjD,EAAKkD,YACX2B,MAAO7E,EAAK6E,MACZC,MAAO9E,EAAKsF,IACZP,YAAa/E,EAAKM,MAClBiF,OAAQ,SACRC,UAAW,IAAIC,KAAKzF,EAAK0F,kBAAkBC,mBAAmB,SAC9DC,WAAY,IAAIH,KAAKzF,EAAK6F,mBAAmBC,eAAe,aAGhEvB,EAAWS,GACXzC,GAAW,EACf,CAAE,MAAOoB,GACLR,QAAQQ,MAAM,mDAAqCA,GACnDa,EAAS,2DAAgDb,EAAMoC,SAC/DxD,GAAW,EACf,GAGJyD,EAAc,GACf,IA2CH,OACIrF,EAAAA,EAAAA,MAAAsF,EAAAA,SAAA,CAAAvF,SAAA,EACIF,EAAAA,EAAAA,KAACwB,EAAAA,EAAM,KACPxB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAChBF,EAAAA,EAAAA,KAACf,EAAAA,EAAO,KAGRkB,EAAAA,EAAAA,MAAA,QAAMF,UAAU,iFAAgFC,SAAA,EAC5FC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sFAAqFC,SAAA,EAChGF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,eAAcC,SAAC,uCAC7BF,EAAAA,EAAAA,KAAA,UACIC,UAAU,yBACVuD,QAASA,IAAMU,GAAgBD,GAAa/D,SAE3C+D,EAAc,aAAU,+BAIhCnC,GACG9B,EAAAA,EAAAA,KAAC0F,EAAAA,EAAc,CACXC,KAAK,KACLC,QAAQ,UACRL,QAAQ,0CACRM,UAAU,IAEd1C,GACAnD,EAAAA,EAAAA,KAAC8F,EAAAA,EAAY,CACTP,QAASpC,EACTyC,QAAQ,SACRG,MAAM,8BACNC,aAAa,EACbC,UAAWA,IAAMjC,EAAS,IAAI9D,UAE9BF,EAAAA,EAAAA,KAAA,UAAQC,UAAU,8BAA8BuD,QAASA,IAAM0C,OAAOhH,SAASiH,SAASjG,SAAC,oBAK7FF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,MAAKC,UAChBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,SAAQC,SAAA,CAElB+D,IACG9D,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2BAA0BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAAmCC,UAC9CF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,OAAMC,SAAC,+BAEzBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACtBC,EAAAA,EAAAA,MAAA,QAAMiG,SAxFzBC,IAIrB,GAHAA,EAAEC,kBAGGnC,EAAU1B,OAAS0B,EAAUE,MAE9B,YADAL,EAAS,yCAKb,MAAMuC,GAAaC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,CACfnG,GAAIyD,EAAQ2C,OAAS,GAClBtC,GAAS,IACZY,OAAQ,SACRC,WAAW,IAAIC,MAAOyB,cAAcC,MAAM,KAAK,GAC/CvB,WAAY,MAGhBrB,EAAW,IAAID,EAASyC,IACxBnC,EAAa,CAAE3B,KAAM,GAAI4B,MAAO,GAAIC,MAAO,GAAIC,YAAa,SAC5DL,GAAe,GACfF,EAAS,KAAK,EAmE0D9D,SAAA,EAC5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAChBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,SAAO4G,QAAQ,OAAO3G,UAAU,aAAYC,SAAC,gBAC7CF,EAAAA,EAAAA,KAAA,SACIM,KAAK,OACLL,UAAU,eACVI,GAAG,OACHwG,MAAO1C,EAAU1B,KACjBqE,SAAWT,GAAMjC,GAAYoC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAGrC,GAAS,IAAE1B,KAAM4D,EAAEU,OAAOF,SAC5DG,UAAQ,QAGhB7G,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,SAAO4G,QAAQ,QAAQ3G,UAAU,aAAYC,SAAC,aAC9CF,EAAAA,EAAAA,KAAA,SACIM,KAAK,QACLL,UAAU,eACVI,GAAG,QACHwG,MAAO1C,EAAUE,MACjByC,SAAWT,GAAMjC,GAAYoC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAGrC,GAAS,IAAEE,MAAOgC,EAAEU,OAAOF,SAC7DG,UAAQ,QAGhB7G,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,SAAO4G,QAAQ,QAAQ3G,UAAU,aAAYC,SAAC,aAC9CF,EAAAA,EAAAA,KAAA,SACIM,KAAK,MACLL,UAAU,eACVI,GAAG,QACHwG,MAAO1C,EAAUG,MACjBwC,SAAWT,GAAMjC,GAAYoC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAGrC,GAAS,IAAEG,MAAO+B,EAAEU,OAAOF,eAGrE1G,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,SAAO4G,QAAQ,cAAc3G,UAAU,aAAYC,SAAC,oBACpDC,EAAAA,EAAAA,MAAA,UACIF,UAAU,cACVI,GAAG,cACHwG,MAAO1C,EAAUI,YACjBuC,SAAWT,GAAMjC,GAAYoC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAGrC,GAAS,IAAEI,YAAa8B,EAAEU,OAAOF,SAAQ3G,SAAA,EAE3EF,EAAAA,EAAAA,KAAA,UAAQ6G,MAAM,OAAM3G,SAAC,iCACrBF,EAAAA,EAAAA,KAAA,UAAQ6G,MAAM,OAAM3G,SAAC,kBACrBF,EAAAA,EAAAA,KAAA,UAAQ6G,MAAM,QAAO3G,SAAC,0BAIlCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,eAAcC,SAAA,EACzBF,EAAAA,EAAAA,KAAA,UAAQM,KAAK,SAASL,UAAU,kBAAiBC,SAAC,wBAGlDF,EAAAA,EAAAA,KAAA,UACIM,KAAK,SACLL,UAAU,oBACVuD,QAASA,IAAMU,GAAe,GAAOhE,SACxC,2BAUrBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EACxCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uBAAsBC,UACjCC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,OAAMC,SAAA,EAChBF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sBAAwB,yBAErCD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,wBAAuBC,SAAE4D,EAAQ2C,eAGzDzG,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,SACN,IAAnB4D,EAAQ2C,QACLzG,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC5BF,EAAAA,EAAAA,KAAC8F,EAAAA,EAAY,CACTP,QAAQ,wGACRK,QAAQ,OACRqB,UAAU,OAIlBjH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,UAC7BC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,uCAAsCC,SAAA,EACnDF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,aAAYC,UACzBC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACIF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,cACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,WACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,aACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,WACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,WACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,oBACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,qBACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,6BAGZF,EAAAA,EAAAA,KAAA,SAAAE,SACK4D,EAAQe,KAAKqC,IACV/G,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACIF,EAAAA,EAAAA,KAAA,MAAAE,SAAKgH,EAAOzE,QACZzC,EAAAA,EAAAA,KAAA,MAAAE,SAAKgH,EAAO7C,SACZrE,EAAAA,EAAAA,KAAA,MAAAE,SAAKgH,EAAO5C,OAAS,OACrBtE,EAAAA,EAAAA,KAAA,MAAAE,UACIF,EAAAA,EAAAA,KAAA,QAAMC,UAAS,SAAAO,OACY,UAAvB0G,EAAO3C,YAA0B,YACV,SAAvB2C,EAAO3C,YAAyB,aAChC,WACDrE,SACyB,UAAvBgH,EAAO3C,YAA0B,cACV,SAAvB2C,EAAO3C,YAAyB,eAChC,4BAGTvE,EAAAA,EAAAA,KAAA,MAAAE,UACIF,EAAAA,EAAAA,KAAA,QAAMC,UAAS,SAAAO,OACO,WAAlB0G,EAAOnC,OAAsB,aAAe,gBAC7C7E,SACoB,WAAlBgH,EAAOnC,OAAsB,QAAU,aAGhD/E,EAAAA,EAAAA,KAAA,MAAAE,SAAKgH,EAAOlC,aACZhF,EAAAA,EAAAA,KAAA,MAAAE,SAAKgH,EAAO9B,cACZpF,EAAAA,EAAAA,KAAA,MAAAE,UACIC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAYJ,KAAK,QAAOK,SAAA,EACnCF,EAAAA,EAAAA,KAAA,UACIC,UAAS,cAAAO,OACa,WAAlB0G,EAAOnC,OAAsB,cAAgB,eAEjDvB,QAASA,KAAM2D,OA1L/D9G,EA0LkF6G,EAAO7G,QAzLjH0D,EAAWD,EAAQe,KAAIqC,GACnBA,EAAO7G,KAAOA,GAAEmG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACLU,GAAM,IAAEnC,OAA0B,WAAlBmC,EAAOnC,OAAsB,WAAa,WAC/DmC,KAJc7G,KA0L6F,EAC7C0F,MAAyB,WAAlBmB,EAAOnC,OAAsB,mBAAgB,mBAAc7E,SAE/C,WAAlBgH,EAAOnC,OAAsB,QAAU,WAE5C/E,EAAAA,EAAAA,KAAA,UACIC,UAAU,wBACVuD,QAASA,KAAM4D,OAxM/D/G,EAwMkF6G,EAAO7G,QAvM7G6F,OAAOmB,QAAQ,2DACftD,EAAWD,EAAQwD,QAAOJ,GAAUA,EAAO7G,KAAOA,MAF9BA,KAwM6F,EAC7C0F,MAAM,MAAK7F,SACd,eAvCJgH,EAAO7G,2BAyD5DL,EAAAA,EAAAA,KAACuB,EAAAA,EAAM,WAGhB,C", "sources": ["components/Sidebar.js", "components/Footer.js", "components/Header.js", "pages/Viewers.js"], "sourcesContent": ["import React from 'react';\r\nimport { Link, useLocation } from 'react-router-dom';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport {\r\n    faDolly, faFolderPlus, faAnchor, faFlag<PERSON>he<PERSON>ed,\r\n    faUser, faStreetView, faCreditCard, faFileLines,\r\n    faBell, faDesktop\r\n} from '@fortawesome/free-solid-svg-icons';\r\n\r\nconst Sidebar = () => {\r\n    const location = useLocation();\r\n    \r\n    // Get user role from localStorage\r\n    const getUserRole = () => {\r\n        try {\r\n            const user = JSON.parse(localStorage.getItem('user'));\r\n            return user?.user?.role || user?.user?.gorev || 'user';\r\n        } catch {\r\n            return 'user';\r\n        }\r\n    };\r\n    \r\n    const userRole = getUserRole();\r\n    const isAdmin = userRole === 'admin';\r\n\r\n    return (\r\n        <div className=\"sidebar col-md-4 col-lg-2 p-0 bg-light-subtle\">\r\n            <div className=\"offcanvas-md offcanvas-end\" tabIndex=\"-1\" id=\"yanMenu\" aria-labelledby=\"yanMenu\">\r\n                <div className=\"offcanvas-header\">\r\n                    <h5 className=\"offcanvas-title\" id=\"yanMenu\">MGZ24 Gold</h5>\r\n                    <button type=\"button\" className=\"btn-close\" data-bs-dismiss=\"offcanvas\" data-bs-target=\"#yanMenu\" aria-label=\"Kapat\"></button>\r\n                </div>\r\n                <div className=\"offcanvas-body d-md-flex flex-column p-0 pt-lg-3 overflow-y-auto\">\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Sevkiyat İşlemleri</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/' ? 'active' : ''}`} to=\"/\">\r\n                                <FontAwesomeIcon icon={faDolly} />Aktif Sevkiyatlar\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/add' ? 'active' : ''}`} to=\"/add\">\r\n                                <FontAwesomeIcon icon={faFolderPlus} />Yeni Sevkiyat Oluştur\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/inactive-devices' ? 'active' : ''}`} to=\"/inactive-devices\">\r\n                                <FontAwesomeIcon icon={faAnchor} />İnaktif Cihazlar\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/history' ? 'active' : ''}`} to=\"/history\">\r\n                                <FontAwesomeIcon icon={faFlagCheckered} />Geçmiş Sevkiyatlar\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Sistem Yönetimi</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        {/* Cihaz Yönetimi - Sadece admin kullanıcılarına göster */}\r\n                        {isAdmin && (\r\n                            <li className=\"nav-item\">\r\n                                <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/devices' ? 'active' : ''}`} to=\"/devices\">\r\n                                    <FontAwesomeIcon icon={faDesktop} />Cihaz Yönetimi\r\n                                </Link>\r\n                            </li>\r\n                        )}\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/notifications' ? 'active' : ''}`} to=\"/notifications\">\r\n                                <FontAwesomeIcon icon={faBell} />Bildirimler\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Kullanıcı Ayarları</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/profile' ? 'active' : ''}`} to=\"/profile\">\r\n                                <FontAwesomeIcon icon={faUser} />Kullanıcı Profilim\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/viewers' ? 'active' : ''}`} to=\"/viewers\">\r\n                                <FontAwesomeIcon icon={faStreetView} />İzleyici İşlemleri\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Ödeme ve Yapılandırma</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/payment' ? 'active' : ''}`} to=\"/payment\">\r\n                                <FontAwesomeIcon icon={faCreditCard} />Ödeme Yap\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/invoices' ? 'active' : ''}`} to=\"/invoices\">\r\n                                <FontAwesomeIcon icon={faFileLines} />Faturalarım\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-body-secondary text-uppercase\">Kredi Durumu</h6>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Sidebar;", "import React from 'react';\r\n\r\nconst Footer = () => {\r\n    return (\r\n        <footer className=\"py-5 border-top\">\r\n            <p className=\"text-center\">&copy;2025 Inkatech Ölçüm Sistemleri</p>\r\n        </footer>\r\n    );\r\n};\r\n\r\nexport default Footer; ", "import React, { useState, useEffect } from 'react';\r\nimport { Link, useNavigate } from 'react-router-dom';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { faRightFromBracket, faBars, faUser } from '@fortawesome/free-solid-svg-icons';\r\nimport logoDark from '../assets/img/logo.png';\r\nimport { kullaniciService } from '../api/dbService';\r\n\r\nconst Header = () => {\r\n    const navigate = useNavigate();\r\n    const [userName, setUserName] = useState('Misafir');\r\n    const [loading, setLoading] = useState(true);\r\n\r\n    // Kullanıcı bilgilerini veritabanından al\r\n    useEffect(() => {\r\n        const fetchUserData = async () => {\r\n            try {\r\n                // Local storage'dan kullanıcı bilgisini al\r\n                const storedUser = JSON.parse(localStorage.getItem('user'));\r\n                const userId = storedUser?.user?.musteri_ID || storedUser?.user?.id;\r\n                const userName = storedUser?.user?.name || storedUser?.user?.musteri_adi;\r\n\r\n                if (!userId) {\r\n                    console.warn('Oturum bilgisi bulunamadı');\r\n                    setUserName('Misafir');\r\n                    setLoading(false);\r\n                    return;\r\n                }\r\n\r\n                // Eğer localStorage'da isim varsa direkt kullan\r\n                if (userName) {\r\n                    setUserName(userName);\r\n                    setLoading(false);\r\n                    return;\r\n                }\r\n\r\n                // API'den kullanıcı bilgilerini getir (backend çalışıyorsa)\r\n                try {\r\n                    const userData = await kullaniciService.getKullanici(userId);\r\n\r\n                    // musteri_adi alanını kullan\r\n                    if (userData && userData.musteri_adi) {\r\n                        setUserName(userData.musteri_adi);\r\n\r\n                        // localStorage'ı güncelle\r\n                        if (storedUser?.user) {\r\n                            storedUser.user.name = userData.musteri_adi;\r\n                            localStorage.setItem('user', JSON.stringify(storedUser));\r\n                        }\r\n                    }\r\n                } catch (apiError) {\r\n                    console.warn('API\\'den kullanıcı bilgisi alınamadı, localStorage kullanılıyor');\r\n                    // API hatası durumunda localStorage'daki adı kullan\r\n                    setUserName(userName || 'Kullanıcı');\r\n                }\r\n            } catch (error) {\r\n                console.error('Kullanıcı bilgileri alınırken hata:', error);\r\n                // Hata durumunda varsayılan isim kullan\r\n                setUserName('Kullanıcı');\r\n            } finally {\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        fetchUserData();\r\n    }, []);\r\n\r\n    // Çıkış işlemi\r\n    const handleLogout = () => {\r\n        // Local storage'dan kullanıcı bilgilerini temizle\r\n        localStorage.removeItem('user');\r\n\r\n        // Login sayfasına yönlendir\r\n        navigate('/login');\r\n    };\r\n\r\n    return (\r\n        <header className=\"navbar sticky-top bg-darkblue flex-md-nowrap p-0 shadow\" data-bs-theme=\"dark\">\r\n            <Link className=\"navbar-brand col-md-4 col-lg-2 me-0 px-3 py-2 fs-6 text-white\" to=\"/\">\r\n                <img src={logoDark} alt=\"MGZ24 Logo\" height=\"40\" />\r\n            </Link>\r\n            <ul className=\"navbar-nav flex-row align-items-center\">\r\n                <li className=\"nav-item text-nowrap d-none d-md-block me-3\">\r\n                    <span className=\"text-white\">\r\n                        <FontAwesomeIcon icon={faUser} className=\"me-2\" />\r\n                        {loading ? 'Yükleniyor...' : userName}\r\n                    </span>\r\n                </li>\r\n                <li className=\"nav-item text-nowrap px-1\">\r\n                    <button\r\n                        onClick={handleLogout}\r\n                        className=\"nav-link px-3\"\r\n                        data-bs-toggle=\"tooltip\"\r\n                        data-bs-placement=\"left\"\r\n                        data-bs-title=\"Çıkış yap!\"\r\n                    >\r\n                        <FontAwesomeIcon icon={faRightFromBracket} />\r\n                    </button>\r\n                </li>\r\n                <li className=\"nav-item text-nowrap d-md-none\">\r\n                    <button\r\n                        className=\"nav-link px-3\"\r\n                        type=\"button\"\r\n                        data-bs-toggle=\"offcanvas\"\r\n                        data-bs-target=\"#yanMenu\"\r\n                        aria-controls=\"yanMenu\"\r\n                        aria-expanded=\"false\"\r\n                        aria-label=\"menu aç/kapa\"\r\n                    >\r\n                        <FontAwesomeIcon icon={faBars} />\r\n                    </button>\r\n                </li>\r\n            </ul>\r\n        </header>\r\n    );\r\n};\r\n\r\nexport default Header; ", "import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport Header from '../components/Header';\nimport Sidebar from '../components/Sidebar';\nimport Footer from '../components/Footer';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\n\nconst Viewers = () => {\n    const [viewers, setViewers] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState(null);\n    const [showAddForm, setShowAddForm] = useState(false);\n    const [newViewer, setNewViewer] = useState({\n        name: '',\n        email: '',\n        phone: '',\n        permissions: 'view'\n    });\n\n    // Sayfanın yüklenmesi\n    useEffect(() => {\n        const fetchViewers = async () => {\n            try {\n                setLoading(true);\n                setError(null);\n                \n                // Gerçek API çağrısı - viewers endpoint'i çağır\n                const response = await axios.get('/api/kullanicilar', {\n                    params: { role: 'viewer' }\n                });\n                \n                // API yanıtını viewers formatına çevir\n                const viewersData = response.data.map(user => ({\n                    id: user.musteri_ID,\n                    name: user.musteri_adi,\n                    email: user.email,\n                    phone: user.tel,\n                    permissions: user.gorev,\n                    status: 'active', // Backend'den gelen duruma göre ayarlanacak\n                    createdAt: new Date(user.olusturma_tarihi).toLocaleDateString('tr-TR'),\n                    lastAccess: new Date(user.guncelleme_tarihi).toLocaleString('tr-TR')\n                }));\n                \n                setViewers(viewersData);\n                setLoading(false);\n            } catch (error) {\n                console.error('İzleyici verileri alınırken hata:', error);\n                setError('İzleyici verileri yüklenirken hata oluştu: ' + error.message);\n                setLoading(false);\n            }\n        };\n\n        fetchViewers();\n    }, []);\n\n    // Yeni izleyici ekleme\n    const handleAddViewer = (e) => {\n        e.preventDefault();\n        \n        // Validasyon\n        if (!newViewer.name || !newViewer.email) {\n            setError('Ad ve email alanları zorunludur.');\n            return;\n        }\n\n        // Yeni izleyici ekle\n        const newViewerData = {\n            id: viewers.length + 1,\n            ...newViewer,\n            status: 'active',\n            createdAt: new Date().toISOString().split('T')[0],\n            lastAccess: '-'\n        };\n\n        setViewers([...viewers, newViewerData]);\n        setNewViewer({ name: '', email: '', phone: '', permissions: 'view' });\n        setShowAddForm(false);\n        setError(null);\n    };\n\n    // İzleyici silme\n    const handleDeleteViewer = (id) => {\n        if (window.confirm('Bu izleyiciyi silmek istediğinizden emin misiniz?')) {\n            setViewers(viewers.filter(viewer => viewer.id !== id));\n        }\n    };\n\n    // Durum değiştirme\n    const handleToggleStatus = (id) => {\n        setViewers(viewers.map(viewer => \n            viewer.id === id \n                ? { ...viewer, status: viewer.status === 'active' ? 'inactive' : 'active' }\n                : viewer\n        ));\n    };\n\n    return (\n        <>\n            <Header />\n            <div className=\"container-fluid\">\n                <div className=\"row\">\n                    <Sidebar />\n\n                    {/* main sütun */}\n                    <main className=\"col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white\">\n                        <div className=\"pt-3 pb-1 mt-3 mb-3 border-bottom d-flex justify-content-between align-items-center\">\n                            <h1 className=\"h4 text-dark\">İzleyici İşlemleri</h1>\n                            <button \n                                className=\"btn btn-primary btn-sm\"\n                                onClick={() => setShowAddForm(!showAddForm)}\n                            >\n                                {showAddForm ? 'İptal' : 'Yeni İzleyici Ekle'}\n                            </button>\n                        </div>\n\n                        {loading ? (\n                            <LoadingSpinner \n                                size=\"lg\" \n                                variant=\"primary\" \n                                message=\"İzleyici verileri yükleniyor...\" \n                                centered={true}\n                            />\n                        ) : error ? (\n                            <ErrorMessage \n                                message={error} \n                                variant=\"danger\"\n                                title=\"Veri Yükleme Hatası\"\n                                dismissible={true}\n                                onDismiss={() => setError('')}\n                            >\n                                <button className=\"btn btn-primary btn-sm mt-2\" onClick={() => window.location.reload()}>\n                                    Yeniden Dene\n                                </button>\n                            </ErrorMessage>\n                        ) : (\n                            <div className=\"row\">\n                                <div className=\"col-12\">\n                                    {/* Yeni İzleyici Ekleme Formu */}\n                                    {showAddForm && (\n                                        <div className=\"card border-primary mb-4\">\n                                            <div className=\"card-header bg-primary text-white\">\n                                                <h5 className=\"mb-0\">Yeni İzleyici Ekle</h5>\n                                            </div>\n                                            <div className=\"card-body\">\n                                                <form onSubmit={handleAddViewer}>\n                                                    <div className=\"row\">\n                                                        <div className=\"col-md-6 mb-3\">\n                                                            <label htmlFor=\"name\" className=\"form-label\">Ad Soyad *</label>\n                                                            <input\n                                                                type=\"text\"\n                                                                className=\"form-control\"\n                                                                id=\"name\"\n                                                                value={newViewer.name}\n                                                                onChange={(e) => setNewViewer({...newViewer, name: e.target.value})}\n                                                                required\n                                                            />\n                                                        </div>\n                                                        <div className=\"col-md-6 mb-3\">\n                                                            <label htmlFor=\"email\" className=\"form-label\">Email *</label>\n                                                            <input\n                                                                type=\"email\"\n                                                                className=\"form-control\"\n                                                                id=\"email\"\n                                                                value={newViewer.email}\n                                                                onChange={(e) => setNewViewer({...newViewer, email: e.target.value})}\n                                                                required\n                                                            />\n                                                        </div>\n                                                        <div className=\"col-md-6 mb-3\">\n                                                            <label htmlFor=\"phone\" className=\"form-label\">Telefon</label>\n                                                            <input\n                                                                type=\"tel\"\n                                                                className=\"form-control\"\n                                                                id=\"phone\"\n                                                                value={newViewer.phone}\n                                                                onChange={(e) => setNewViewer({...newViewer, phone: e.target.value})}\n                                                            />\n                                                        </div>\n                                                        <div className=\"col-md-6 mb-3\">\n                                                            <label htmlFor=\"permissions\" className=\"form-label\">Yetki Seviyesi</label>\n                                                            <select\n                                                                className=\"form-select\"\n                                                                id=\"permissions\"\n                                                                value={newViewer.permissions}\n                                                                onChange={(e) => setNewViewer({...newViewer, permissions: e.target.value})}\n                                                            >\n                                                                <option value=\"view\">Sadece Görüntüleme</option>\n                                                                <option value=\"edit\">Düzenleme</option>\n                                                                <option value=\"admin\">Yönetici</option>\n                                                            </select>\n                                                        </div>\n                                                    </div>\n                                                    <div className=\"d-flex gap-2\">\n                                                        <button type=\"submit\" className=\"btn btn-primary\">\n                                                            İzleyici Ekle\n                                                        </button>\n                                                        <button \n                                                            type=\"button\" \n                                                            className=\"btn btn-secondary\"\n                                                            onClick={() => setShowAddForm(false)}\n                                                        >\n                                                            İptal\n                                                        </button>\n                                                    </div>\n                                                </form>\n                                            </div>\n                                        </div>\n                                    )}\n\n                                    {/* İzleyici Listesi */}\n                                    <div className=\"card border-tertiary-subtle\">\n                                        <div className=\"card-header bg-light\">\n                                            <h5 className=\"mb-0\">\n                                                <i className=\"fas fa-users me-2\"></i>\n                                                İzleyici Listesi\n                                                <span className=\"badge bg-primary ms-2\">{viewers.length}</span>\n                                            </h5>\n                                        </div>\n                                        <div className=\"card-body p-0\">\n                                            {viewers.length === 0 ? (\n                                                <div className=\"p-4 text-center\">\n                                                    <ErrorMessage \n                                                        message=\"Henüz izleyici bulunmuyor. Yeni izleyici eklemek için yukarıdaki butonu kullanabilirsiniz.\"\n                                                        variant=\"info\"\n                                                        showIcon={true}\n                                                    />\n                                                </div>\n                                            ) : (\n                                                <div className=\"table-responsive\">\n                                                    <table className=\"table table-hover table-striped mb-0\">\n                                                        <thead className=\"table-dark\">\n                                                            <tr>\n                                                                <th>Ad Soyad</th>\n                                                                <th>Email</th>\n                                                                <th>Telefon</th>\n                                                                <th>Yetki</th>\n                                                                <th>Durum</th>\n                                                                <th>Eklenme Tarihi</th>\n                                                                <th>Son Erişim</th>\n                                                                <th>İşlemler</th>\n                                                            </tr>\n                                                        </thead>\n                                                        <tbody>\n                                                            {viewers.map((viewer) => (\n                                                                <tr key={viewer.id}>\n                                                                    <td>{viewer.name}</td>\n                                                                    <td>{viewer.email}</td>\n                                                                    <td>{viewer.phone || '-'}</td>\n                                                                    <td>\n                                                                        <span className={`badge ${\n                                                                            viewer.permissions === 'admin' ? 'bg-danger' :\n                                                                            viewer.permissions === 'edit' ? 'bg-warning' :\n                                                                            'bg-info'\n                                                                        }`}>\n                                                                            {viewer.permissions === 'admin' ? 'Yönetici' :\n                                                                             viewer.permissions === 'edit' ? 'Düzenleme' :\n                                                                             'Görüntüleme'}\n                                                                        </span>\n                                                                    </td>\n                                                                    <td>\n                                                                        <span className={`badge ${\n                                                                            viewer.status === 'active' ? 'bg-success' : 'bg-secondary'\n                                                                        }`}>\n                                                                            {viewer.status === 'active' ? 'Aktif' : 'Pasif'}\n                                                                        </span>\n                                                                    </td>\n                                                                    <td>{viewer.createdAt}</td>\n                                                                    <td>{viewer.lastAccess}</td>\n                                                                    <td>\n                                                                        <div className=\"btn-group\" role=\"group\">\n                                                                            <button\n                                                                                className={`btn btn-sm ${\n                                                                                    viewer.status === 'active' ? 'btn-warning' : 'btn-success'\n                                                                                }`}\n                                                                                onClick={() => handleToggleStatus(viewer.id)}\n                                                                                title={viewer.status === 'active' ? 'Pasifleştir' : 'Aktifleştir'}\n                                                                            >\n                                                                                {viewer.status === 'active' ? 'Pasif' : 'Aktif'}\n                                                                            </button>\n                                                                            <button\n                                                                                className=\"btn btn-sm btn-danger\"\n                                                                                onClick={() => handleDeleteViewer(viewer.id)}\n                                                                                title=\"Sil\"\n                                                                            >\n                                                                                Sil\n                                                                            </button>\n                                                                        </div>\n                                                                    </td>\n                                                                </tr>\n                                                            ))}\n                                                        </tbody>\n                                                    </table>\n                                                </div>\n                                            )}\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        )}\n                    </main>\n                    \n                    <Footer />\n                </div>\n            </div>\n        </>\n    );\n};\n\nexport default Viewers;"], "names": ["Sidebar", "location", "useLocation", "isAdmin", "getUserRole", "_user$user", "_user$user2", "user", "JSON", "parse", "localStorage", "getItem", "role", "gorev", "_unused", "_jsx", "className", "children", "_jsxs", "tabIndex", "id", "type", "Link", "concat", "pathname", "to", "FontAwesomeIcon", "icon", "faDolly", "faFolderPlus", "faAnchor", "faFlag<PERSON><PERSON><PERSON><PERSON>", "faDesktop", "faBell", "faUser", "faStreetView", "faCreditCard", "faFileLines", "Footer", "Header", "navigate", "useNavigate", "userName", "setUserName", "useState", "loading", "setLoading", "useEffect", "async", "_storedUser$user", "_storedUser$user2", "_storedUser$user3", "_storedUser$user4", "storedUser", "userId", "musteri_ID", "name", "musteri_adi", "console", "warn", "userData", "kullaniciService", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setItem", "stringify", "apiError", "error", "fetchUserData", "src", "alt", "height", "onClick", "handleLogout", "removeItem", "faRightFromBracket", "faBars", "Viewers", "viewers", "set<PERSON>ie<PERSON><PERSON>", "setError", "showAddForm", "setShowAddForm", "newViewer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email", "phone", "permissions", "viewersData", "axios", "get", "params", "data", "map", "tel", "status", "createdAt", "Date", "olusturma_tarihi", "toLocaleDateString", "lastAccess", "guncelleme_tarihi", "toLocaleString", "message", "fetchViewers", "_Fragment", "LoadingSpinner", "size", "variant", "centered", "ErrorMessage", "title", "dismissible", "on<PERSON><PERSON><PERSON>", "window", "reload", "onSubmit", "e", "preventDefault", "newViewerData", "_objectSpread", "length", "toISOString", "split", "htmlFor", "value", "onChange", "target", "required", "showIcon", "viewer", "handleToggleStatus", "handleDeleteViewer", "confirm", "filter"], "sourceRoot": ""}