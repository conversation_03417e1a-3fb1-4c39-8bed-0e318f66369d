#!/bin/bash

echo "MGZ24 API HTTPS Sunucusu Başlatılıyor..."
echo "========================================"

# SSL sertifika dosyalarının varlığını kontrol et
if [ ! -f "/etc/letsencrypt/live/ffl21.fun/privkey.pem" ]; then
    echo "HATA: SSL private key dosyası bulunamadı!"
    echo "Dosya yolu: /etc/letsencrypt/live/ffl21.fun/privkey.pem"
    exit 1
fi

if [ ! -f "/etc/letsencrypt/live/ffl21.fun/fullchain.pem" ]; then
    echo "HATA: SSL certificate dosyası bulunamadı!"
    echo "Dosya yolu: /etc/letsencrypt/live/ffl21.fun/fullchain.pem"
    exit 1
fi

echo "SSL sertifika dosyaları kontrol edildi ✓"

# Node.js modüllerini yükle
echo "Node.js modülleri yükleniyor..."
npm install

# Port 80 ve 443'ün kullanılabilir olduğunu kontrol et
if lsof -Pi :80 -sTCP:LISTEN -t >/dev/null ; then
    echo "UYARI: Port 80 zaten kullanımda. HTTP yönlendirme başlatılmayacak."
    HTTP_REDIRECT=false
else
    HTTP_REDIRECT=true
fi

if lsof -Pi :3001 -sTCP:LISTEN -t >/dev/null ; then
    echo "HATA: Port 3001 zaten kullanımda!"
    echo "Lütfen port 3001'i kullanan servisi durdurun."
    exit 1
fi

echo "Portlar kontrol edildi ✓"

# HTTPS sunucusunu başlat
echo "HTTPS sunucusu başlatılıyor..."
node mgz24api.js &

# HTTP yönlendirme sunucusunu başlat (eğer port 80 boşsa)
if [ "$HTTP_REDIRECT" = true ]; then
    echo "HTTP yönlendirme sunucusu başlatılıyor..."
    node http-redirect.js &
fi

echo ""
echo "========================================"
echo "MGZ24 API Sunucusu Başarıyla Başlatıldı!"
echo "========================================"
echo "HTTPS API: https://ffl21.fun:3001"
echo "Health Check: https://ffl21.fun:3001/health"
echo ""
echo "Sunucuyu durdurmak için: Ctrl+C"
echo ""

# Sunucuları arka planda çalıştır ve logları göster
wait 