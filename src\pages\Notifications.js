import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faBell, faExclamationTriangle, faThermometerHalf, faBatteryEmpty,
  faDroplet, faDoorOpen, faWifi, faCheck, faTrash, faFilter,
  faCalendarAlt, faMapMarkerAlt, faEye, faCog
} from '@fortawesome/free-solid-svg-icons';
import Header from '../components/Header';
import Sidebar from '../components/Sidebar';
import Footer from '../components/Footer';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';

const Notifications = () => {
  const [notifications, setNotifications] = useState([]);
  const [filteredNotifications, setFilteredNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filter, setFilter] = useState('all');
  const [selectedNotifications, setSelectedNotifications] = useState([]);
  const [showSettings, setShowSettings] = useState(false);

  useEffect(() => {
    fetchNotifications();
  }, []);

  useEffect(() => {
    filterNotifications();
  }, [notifications, filter]);

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      setError(null);

      // Demo veriler - gerçek API ile değiştirilecek
      const demoNotifications = [
        {
          id: 1,
          type: 'temperature',
          severity: 'high',
          title: 'Yüksek Sıcaklık Alarmı',
          message: 'Cihaz MGZ-001 sıcaklık değeri 12°C (Normal: 2-8°C)',
          deviceId: 'MGZ-001',
          shipmentId: 'SVK-2024-001',
          timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 dakika önce
          read: false,
          location: 'Ankara - İstanbul Yolu',
          resolved: false
        },
        {
          id: 2,
          type: 'battery',
          severity: 'medium',
          title: 'Düşük Batarya',
          message: 'Cihaz MGZ-002 batarya seviyesi %15',
          deviceId: 'MGZ-002',
          shipmentId: 'SVK-2024-002',
          timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 dakika önce
          read: false,
          location: 'İzmir - Manisa Yolu',
          resolved: false
        },
        {
          id: 3,
          type: 'humidity',
          severity: 'low',
          title: 'Nem Seviyesi Uyarısı',
          message: 'Cihaz MGZ-003 nem seviyesi %85 (Normal: 40-60%)',
          deviceId: 'MGZ-003',
          shipmentId: 'SVK-2024-003',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 saat önce
          read: true,
          location: 'Bursa - Eskişehir Yolu',
          resolved: true
        },
        {
          id: 4,
          type: 'door',
          severity: 'high',
          title: 'Kapı Açılması',
          message: 'Cihaz MGZ-004 kapı sensörü tetiklendi',
          deviceId: 'MGZ-004',
          shipmentId: 'SVK-2024-004',
          timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 saat önce
          read: true,
          location: 'Adana - Mersin Yolu',
          resolved: false
        },
        {
          id: 5,
          type: 'connection',
          severity: 'medium',
          title: 'Bağlantı Kaybı',
          message: 'Cihaz MGZ-005 ile bağlantı kesildi',
          deviceId: 'MGZ-005',
          shipmentId: 'SVK-2024-005',
          timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 saat önce
          read: true,
          location: 'Antalya - Konya Yolu',
          resolved: true
        }
      ];

      setNotifications(demoNotifications);
      setLoading(false);
    } catch (error) {
      console.error('Bildirim verileri alınırken hata:', error);
      setError('Bildirim verileri yüklenirken hata oluştu: ' + error.message);
      setLoading(false);
    }
  };

  const filterNotifications = () => {
    let filtered = [...notifications];

    switch (filter) {
      case 'unread':
        filtered = filtered.filter(n => !n.read);
        break;
      case 'high':
        filtered = filtered.filter(n => n.severity === 'high');
        break;
      case 'medium':
        filtered = filtered.filter(n => n.severity === 'medium');
        break;
      case 'low':
        filtered = filtered.filter(n => n.severity === 'low');
        break;
      case 'resolved':
        filtered = filtered.filter(n => n.resolved);
        break;
      case 'unresolved':
        filtered = filtered.filter(n => !n.resolved);
        break;
      default:
        break;
    }

    setFilteredNotifications(filtered);
  };

  const formatTimeAgo = (timestamp) => {
    const now = new Date();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 60) {
      return `${minutes} dakika önce`;
    } else if (hours < 24) {
      return `${hours} saat önce`;
    } else {
      return `${days} gün önce`;
    }
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'temperature':
        return faThermometerHalf;
      case 'battery':
        return faBatteryEmpty;
      case 'humidity':
        return faDroplet;
      case 'door':
        return faDoorOpen;
      case 'connection':
        return faWifi;
      default:
        return faBell;
    }
  };

  const getSeverityBadge = (severity) => {
    switch (severity) {
      case 'high':
        return <span className="badge bg-danger">Yüksek</span>;
      case 'medium':
        return <span className="badge bg-warning">Orta</span>;
      case 'low':
        return <span className="badge bg-info">Düşük</span>;
      default:
        return <span className="badge bg-secondary">Bilinmiyor</span>;
    }
  };

  const markAsRead = (id) => {
    setNotifications(notifications.map(n => 
      n.id === id ? { ...n, read: true } : n
    ));
  };

  const markAsResolved = (id) => {
    setNotifications(notifications.map(n => 
      n.id === id ? { ...n, resolved: true } : n
    ));
  };

  const deleteNotification = (id) => {
    setNotifications(notifications.filter(n => n.id !== id));
  };

  const toggleNotificationSelection = (id) => {
    setSelectedNotifications(prev => 
      prev.includes(id) 
        ? prev.filter(nId => nId !== id)
        : [...prev, id]
    );
  };

  const markSelectedAsRead = () => {
    setNotifications(notifications.map(n => 
      selectedNotifications.includes(n.id) ? { ...n, read: true } : n
    ));
    setSelectedNotifications([]);
  };

  const deleteSelected = () => {
    setNotifications(notifications.filter(n => !selectedNotifications.includes(n.id)));
    setSelectedNotifications([]);
  };

  const getFilterCount = (filterType) => {
    switch (filterType) {
      case 'unread':
        return notifications.filter(n => !n.read).length;
      case 'high':
        return notifications.filter(n => n.severity === 'high').length;
      case 'medium':
        return notifications.filter(n => n.severity === 'medium').length;
      case 'low':
        return notifications.filter(n => n.severity === 'low').length;
      case 'resolved':
        return notifications.filter(n => n.resolved).length;
      case 'unresolved':
        return notifications.filter(n => !n.resolved).length;
      default:
        return notifications.length;
    }
  };

  return (
    <>
      <Header />
      <div className="container-fluid">
        <div className="row">
          <Sidebar />
          
          <main className="col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white">
            <div className="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
              <h1 className="h2">
                <FontAwesomeIcon icon={faBell} className="me-2" />
                Bildirimler
                {notifications.filter(n => !n.read).length > 0 && (
                  <span className="badge bg-danger ms-2">
                    {notifications.filter(n => !n.read).length}
                  </span>
                )}
              </h1>
              <button
                className="btn btn-outline-secondary"
                onClick={() => setShowSettings(!showSettings)}
              >
                <FontAwesomeIcon icon={faCog} className="me-2" />
                Ayarlar
              </button>
            </div>

            {loading ? (
              <LoadingSpinner size="lg" variant="primary" message="Bildirimler yükleniyor..." centered={true} />
            ) : error ? (
              <ErrorMessage 
                message={error} 
                variant="danger"
                title="Veri Yükleme Hatası"
                dismissible={true}
                onDismiss={() => setError('')}
              >
                <button className="btn btn-primary btn-sm mt-2" onClick={fetchNotifications}>
                  Yeniden Dene
                </button>
              </ErrorMessage>
            ) : (
              <>
                {/* Filtreler */}
                <div className="card mb-4">
                  <div className="card-body">
                    <div className="row g-2">
                      <div className="col-auto">
                        <button
                          className={`btn btn-sm ${filter === 'all' ? 'btn-primary' : 'btn-outline-primary'}`}
                          onClick={() => setFilter('all')}
                        >
                          Tümü ({getFilterCount('all')})
                        </button>
                      </div>
                      <div className="col-auto">
                        <button
                          className={`btn btn-sm ${filter === 'unread' ? 'btn-primary' : 'btn-outline-primary'}`}
                          onClick={() => setFilter('unread')}
                        >
                          Okunmamış ({getFilterCount('unread')})
                        </button>
                      </div>
                      <div className="col-auto">
                        <button
                          className={`btn btn-sm ${filter === 'high' ? 'btn-danger' : 'btn-outline-danger'}`}
                          onClick={() => setFilter('high')}
                        >
                          Yüksek ({getFilterCount('high')})
                        </button>
                      </div>
                      <div className="col-auto">
                        <button
                          className={`btn btn-sm ${filter === 'medium' ? 'btn-warning' : 'btn-outline-warning'}`}
                          onClick={() => setFilter('medium')}
                        >
                          Orta ({getFilterCount('medium')})
                        </button>
                      </div>
                      <div className="col-auto">
                        <button
                          className={`btn btn-sm ${filter === 'low' ? 'btn-info' : 'btn-outline-info'}`}
                          onClick={() => setFilter('low')}
                        >
                          Düşük ({getFilterCount('low')})
                        </button>
                      </div>
                      <div className="col-auto">
                        <button
                          className={`btn btn-sm ${filter === 'unresolved' ? 'btn-warning' : 'btn-outline-warning'}`}
                          onClick={() => setFilter('unresolved')}
                        >
                          Çözülmemiş ({getFilterCount('unresolved')})
                        </button>
                      </div>
                    </div>
                    
                    {selectedNotifications.length > 0 && (
                      <div className="mt-3 d-flex gap-2">
                        <button className="btn btn-sm btn-outline-success" onClick={markSelectedAsRead}>
                          <FontAwesomeIcon icon={faCheck} className="me-1" />
                          Okundu İşaretle ({selectedNotifications.length})
                        </button>
                        <button className="btn btn-sm btn-outline-danger" onClick={deleteSelected}>
                          <FontAwesomeIcon icon={faTrash} className="me-1" />
                          Sil ({selectedNotifications.length})
                        </button>
                      </div>
                    )}
                  </div>
                </div>

                {/* Bildirim Listesi */}
                <div className="card">
                  <div className="card-body p-0">
                    {filteredNotifications.length === 0 ? (
                      <div className="text-center py-5">
                        <FontAwesomeIcon icon={faBell} className="fa-3x text-muted mb-3" />
                        <h5 className="text-muted">Bildirim Bulunamadı</h5>
                        <p className="text-muted">Seçili filtreye uygun bildirim bulunamadı.</p>
                      </div>
                    ) : (
                      <div className="list-group list-group-flush">
                        {filteredNotifications.map((notification) => (
                          <div
                            key={notification.id}
                            className={`list-group-item ${!notification.read ? 'list-group-item-light' : ''}`}
                          >
                            <div className="d-flex align-items-start">
                              <div className="me-3">
                                <input
                                  type="checkbox"
                                  className="form-check-input"
                                  checked={selectedNotifications.includes(notification.id)}
                                  onChange={() => toggleNotificationSelection(notification.id)}
                                />
                              </div>
                              
                              <div className="me-3">
                                <div className={`notification-icon ${notification.severity}`}>
                                  <FontAwesomeIcon 
                                    icon={getNotificationIcon(notification.type)} 
                                    className={`text-${notification.severity === 'high' ? 'danger' : 
                                      notification.severity === 'medium' ? 'warning' : 'info'}`}
                                  />
                                </div>
                              </div>
                              
                              <div className="flex-grow-1">
                                <div className="d-flex justify-content-between align-items-start">
                                  <div>
                                    <h6 className="mb-1">
                                      {notification.title}
                                      {!notification.read && (
                                        <span className="badge bg-primary ms-2">Yeni</span>
                                      )}
                                      {notification.resolved && (
                                        <span className="badge bg-success ms-2">Çözüldü</span>
                                      )}
                                    </h6>
                                    <p className="mb-1">{notification.message}</p>
                                    <div className="small text-muted">
                                      <FontAwesomeIcon icon={faMapMarkerAlt} className="me-1" />
                                      {notification.location}
                                      <span className="mx-2">•</span>
                                      <FontAwesomeIcon icon={faCalendarAlt} className="me-1" />
                                      {formatTimeAgo(notification.timestamp)}
                                    </div>
                                  </div>
                                  <div className="text-end">
                                    {getSeverityBadge(notification.severity)}
                                  </div>
                                </div>
                                
                                <div className="mt-2">
                                  <div className="btn-group btn-group-sm">
                                    <Link
                                      to={`/view/${notification.shipmentId}`}
                                      className="btn btn-outline-primary"
                                    >
                                      <FontAwesomeIcon icon={faEye} className="me-1" />
                                      Detay
                                    </Link>
                                    {!notification.read && (
                                      <button
                                        className="btn btn-outline-success"
                                        onClick={() => markAsRead(notification.id)}
                                      >
                                        <FontAwesomeIcon icon={faCheck} className="me-1" />
                                        Okundu
                                      </button>
                                    )}
                                    {!notification.resolved && (
                                      <button
                                        className="btn btn-outline-warning"
                                        onClick={() => markAsResolved(notification.id)}
                                      >
                                        <FontAwesomeIcon icon={faCheck} className="me-1" />
                                        Çözüldü
                                      </button>
                                    )}
                                    <button
                                      className="btn btn-outline-danger"
                                      onClick={() => deleteNotification(notification.id)}
                                    >
                                      <FontAwesomeIcon icon={faTrash} />
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}
          </main>
          
          <Footer />
        </div>
      </div>

      {/* Ayarlar Modalı */}
      {showSettings && (
        <div className="modal show d-block" tabIndex="-1" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Bildirim Ayarları</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => setShowSettings(false)}
                ></button>
              </div>
              <div className="modal-body">
                <div className="form-check">
                  <input className="form-check-input" type="checkbox" id="emailNotifications" defaultChecked />
                  <label className="form-check-label" htmlFor="emailNotifications">
                    E-posta bildirimleri
                  </label>
                </div>
                <div className="form-check">
                  <input className="form-check-input" type="checkbox" id="smsNotifications" />
                  <label className="form-check-label" htmlFor="smsNotifications">
                    SMS bildirimleri
                  </label>
                </div>
                <div className="form-check">
                  <input className="form-check-input" type="checkbox" id="pushNotifications" defaultChecked />
                  <label className="form-check-label" htmlFor="pushNotifications">
                    Push bildirimleri
                  </label>
                </div>
                <hr />
                <h6>Bildirim Türleri</h6>
                <div className="form-check">
                  <input className="form-check-input" type="checkbox" id="tempAlerts" defaultChecked />
                  <label className="form-check-label" htmlFor="tempAlerts">
                    Sıcaklık alarmları
                  </label>
                </div>
                <div className="form-check">
                  <input className="form-check-input" type="checkbox" id="batteryAlerts" defaultChecked />
                  <label className="form-check-label" htmlFor="batteryAlerts">
                    Batarya alarmları
                  </label>
                </div>
                <div className="form-check">
                  <input className="form-check-input" type="checkbox" id="doorAlerts" defaultChecked />
                  <label className="form-check-label" htmlFor="doorAlerts">
                    Kapı alarmları
                  </label>
                </div>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => setShowSettings(false)}
                >
                  İptal
                </button>
                <button
                  type="button"
                  className="btn btn-primary"
                  onClick={() => setShowSettings(false)}
                >
                  Kaydet
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Notifications;