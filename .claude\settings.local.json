{"permissions": {"allow": ["mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(npm install:*)", "Bash(node server.js)", "Bash(node:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(mv:*)", "Bash(BACKEND_PATH=backend node backend/apply_database_optimizations.js)", "Bash(grep:*)", "Bash(npm restart)", "<PERSON><PERSON>(mysql:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(ls:*)", "Bash(npm run:*)", "<PERSON><PERSON>(scp:*)", "<PERSON><PERSON>(claude mcp:*)", "Bash(npm start)", "Bash(rm:*)", "Bash(git checkout:*)", "Bash(find:*)", "WebFetch(domain:dev.paytr.com)", "Bash(kill:*)", "<PERSON><PERSON>(cat:*)"], "deny": []}}