import express from 'express';
import ExternalApiService from '../services/externalApiService.js';
import DeviceAssignmentService from '../services/deviceAssignmentService.js';
import RealTimePollingService from '../services/realTimePollingService.js';
import authMiddleware from '../middleware/auth.js';

const router = express.Router();

// Service instance'ları
const externalApiService = new ExternalApiService();
const deviceAssignmentService = new DeviceAssignmentService();
const pollingService = new RealTimePollingService();

// External API'den cihaz listesini getir
router.get('/devices', authMiddleware, async (req, res) => {
    try {
        const { limit = 50, page = 1 } = req.query;
        const userId = req.user.id;
        
        // Kullanıcının müşteri ID'sini al
        const musteriID = req.user.musteri_ID || req.user.id;
        
        // External API'den cihaz listesini getir
        const deviceList = await externalApiService.fetchDeviceList(limit, page);
        
        // Müşteriye ait cihazları filtrele
        const customerDevices = deviceList.cihazlar.filter(device => 
            device.simBilgisi.musteriID === musteriID
        );

        res.json({
            success: true,
            data: {
                devices: customerDevices,
                pagination: {
                    ...deviceList.pagination,
                    filteredCount: customerDevices.length
                }
            }
        });

    } catch (error) {
        console.error('Error fetching external devices:', error);
        res.status(500).json({
            success: false,
            message: 'Cihaz listesi alınırken hata oluştu: ' + error.message
        });
    }
});

// External API'den cihaz detayını getir
router.get('/devices/:deviceId', authMiddleware, async (req, res) => {
    try {
        const { deviceId } = req.params;
        const musteriID = req.user.musteri_ID || req.user.id;
        
        // Cihazın müşteriye ait olup olmadığını kontrol et
        const deviceDetail = await externalApiService.fetchDeviceDetail(deviceId);
        
        if (deviceDetail.simBilgisi && deviceDetail.simBilgisi.musteriID !== musteriID) {
            return res.status(403).json({
                success: false,
                message: 'Bu cihaza erişim yetkiniz yok'
            });
        }

        // Lokal database'i güncelle
        await externalApiService.syncDeviceToLocal(deviceId);

        res.json({
            success: true,
            data: deviceDetail
        });

    } catch (error) {
        console.error(`Error fetching device ${req.params.deviceId}:`, error);
        res.status(500).json({
            success: false,
            message: 'Cihaz detayı alınırken hata oluştu: ' + error.message
        });
    }
});

// External API'den cihaz durumunu getir
router.get('/devices/:deviceId/status', authMiddleware, async (req, res) => {
    try {
        const { deviceId } = req.params;
        const musteriID = req.user.musteri_ID || req.user.id;
        
        // Cihaz durumunu getir
        const deviceStatus = await externalApiService.fetchDeviceStatus(deviceId);
        
        // Yetki kontrolü (SIM bilgisi varsa)
        try {
            const simInfo = await externalApiService.fetchSimInfo(deviceId);
            if (simInfo.simBilgileri[0] && simInfo.simBilgileri[0].musteriID !== musteriID) {
                return res.status(403).json({
                    success: false,
                    message: 'Bu cihaza erişim yetkiniz yok'
                });
            }
        } catch (simError) {
            console.warn(`Could not fetch SIM info for device ${deviceId}:`, simError.message);
        }

        res.json({
            success: true,
            data: deviceStatus
        });

    } catch (error) {
        console.error(`Error fetching device status ${req.params.deviceId}:`, error);
        res.status(500).json({
            success: false,
            message: 'Cihaz durumu alınırken hata oluştu: ' + error.message
        });
    }
});

// External API'den cihaz geçmişini getir
router.get('/devices/:deviceId/history', authMiddleware, async (req, res) => {
    try {
        const { deviceId } = req.params;
        const { startDate, endDate, limit = 100 } = req.query;
        const musteriID = req.user.musteri_ID || req.user.id;
        
        // Yetki kontrolü
        const simInfo = await externalApiService.fetchSimInfo(deviceId);
        if (simInfo.simBilgileri[0] && simInfo.simBilgileri[0].musteriID !== musteriID) {
            return res.status(403).json({
                success: false,
                message: 'Bu cihaza erişim yetkiniz yok'
            });
        }

        // Cihaz geçmişini getir
        const deviceHistory = await externalApiService.fetchDeviceHistory(deviceId, startDate, endDate, limit);
        
        // Geçmiş verileri lokal database'e kaydet
        if (startDate && endDate) {
            await externalApiService.syncDeviceHistory(deviceId, startDate, endDate);
        }

        res.json({
            success: true,
            data: deviceHistory
        });

    } catch (error) {
        console.error(`Error fetching device history ${req.params.deviceId}:`, error);
        res.status(500).json({
            success: false,
            message: 'Cihaz geçmişi alınırken hata oluştu: ' + error.message
        });
    }
});

// Cihazı sevkiyata ata
router.post('/devices/:deviceId/assign', authMiddleware, async (req, res) => {
    try {
        const { deviceId } = req.params;
        const { sevkiyatId } = req.body;
        const userId = req.user.id;

        if (!sevkiyatId) {
            return res.status(400).json({
                success: false,
                message: 'Sevkiyat ID gerekli'
            });
        }

        // Cihazı sevkiyata ata
        const result = await deviceAssignmentService.assignDeviceToShipment(deviceId, sevkiyatId, userId);
        
        // Polling listesine ekle
        await pollingService.addDeviceToPolling(deviceId);

        res.json({
            success: true,
            data: result
        });

    } catch (error) {
        console.error(`Error assigning device ${req.params.deviceId}:`, error);
        res.status(500).json({
            success: false,
            message: 'Cihaz atama hatası: ' + error.message
        });
    }
});

// Cihazın sevkiyat atamasını kaldır
router.post('/devices/:deviceId/unassign', authMiddleware, async (req, res) => {
    try {
        const { deviceId } = req.params;
        const { sevkiyatId, reason = 'manual' } = req.body;
        const userId = req.user.id;

        if (!sevkiyatId) {
            return res.status(400).json({
                success: false,
                message: 'Sevkiyat ID gerekli'
            });
        }

        // Cihazın atamasını kaldır
        const result = await deviceAssignmentService.unassignDeviceFromShipment(deviceId, sevkiyatId, userId, reason);
        
        // Polling listesinden çıkar
        pollingService.removeDeviceFromPolling(deviceId);

        res.json({
            success: true,
            data: result
        });

    } catch (error) {
        console.error(`Error unassigning device ${req.params.deviceId}:`, error);
        res.status(500).json({
            success: false,
            message: 'Cihaz atama kaldırma hatası: ' + error.message
        });
    }
});

// Kullanılabilir cihazları getir
router.get('/available-devices', authMiddleware, async (req, res) => {
    try {
        const musteriID = req.user.musteri_ID || req.user.id;
        
        // Kullanılabilir cihazları getir
        const availableDevices = await deviceAssignmentService.getAvailableDevices(musteriID);

        res.json({
            success: true,
            data: {
                devices: availableDevices,
                stats: {
                    total: availableDevices.length,
                    available: availableDevices.filter(d => d.availability_status === 'available').length,
                    assigned: availableDevices.filter(d => d.availability_status === 'assigned').length,
                    gold: availableDevices.filter(d => d.GoldCihaz === 1).length
                }
            }
        });

    } catch (error) {
        console.error('Error fetching available devices:', error);
        res.status(500).json({
            success: false,
            message: 'Kullanılabilir cihazlar alınırken hata oluştu: ' + error.message
        });
    }
});

// Otomatik cihaz atama
router.post('/auto-assign', authMiddleware, async (req, res) => {
    try {
        const { sevkiyatId } = req.body;
        const userId = req.user.id;

        if (!sevkiyatId) {
            return res.status(400).json({
                success: false,
                message: 'Sevkiyat ID gerekli'
            });
        }

        // Otomatik cihaz atama
        const result = await deviceAssignmentService.autoAssignDevice(sevkiyatId, userId);
        
        // Polling listesine ekle
        await pollingService.addDeviceToPolling(result.deviceInfo.cihazID);

        res.json({
            success: true,
            data: result
        });

    } catch (error) {
        console.error(`Error auto-assigning device for shipment ${req.body.sevkiyatId}:`, error);
        res.status(500).json({
            success: false,
            message: 'Otomatik cihaz atama hatası: ' + error.message
        });
    }
});

// Cihaz atama geçmişini getir
router.get('/devices/:deviceId/assignment-history', authMiddleware, async (req, res) => {
    try {
        const { deviceId } = req.params;
        const { limit = 50 } = req.query;
        const musteriID = req.user.musteri_ID || req.user.id;
        
        // Yetki kontrolü
        const simInfo = await externalApiService.fetchSimInfo(deviceId);
        if (simInfo.simBilgileri[0] && simInfo.simBilgileri[0].musteriID !== musteriID) {
            return res.status(403).json({
                success: false,
                message: 'Bu cihaza erişim yetkiniz yok'
            });
        }

        // Atama geçmişini getir
        const history = await deviceAssignmentService.getDeviceAssignmentHistory(deviceId, limit);

        res.json({
            success: true,
            data: history
        });

    } catch (error) {
        console.error(`Error fetching assignment history for device ${req.params.deviceId}:`, error);
        res.status(500).json({
            success: false,
            message: 'Cihaz atama geçmişi alınırken hata oluştu: ' + error.message
        });
    }
});

// Atama istatistiklerini getir
router.get('/assignment-stats', authMiddleware, async (req, res) => {
    try {
        const musteriID = req.user.musteri_ID || req.user.id;
        
        // Atama istatistiklerini getir
        const stats = await deviceAssignmentService.getAssignmentStats(musteriID);

        res.json({
            success: true,
            data: stats
        });

    } catch (error) {
        console.error('Error fetching assignment stats:', error);
        res.status(500).json({
            success: false,
            message: 'Atama istatistikleri alınırken hata oluştu: ' + error.message
        });
    }
});

// Müşteri cihazlarını senkronize et
router.post('/sync-devices', authMiddleware, async (req, res) => {
    try {
        const musteriID = req.user.musteri_ID || req.user.id;
        
        // Müşteri cihazlarını senkronize et
        const result = await externalApiService.syncCustomerDevices(musteriID);

        res.json({
            success: true,
            data: result,
            message: `${result.synced}/${result.total} cihaz senkronize edildi`
        });

    } catch (error) {
        console.error('Error syncing customer devices:', error);
        res.status(500).json({
            success: false,
            message: 'Cihaz senkronizasyonu hatası: ' + error.message
        });
    }
});

// Polling durumunu getir
router.get('/polling-status', authMiddleware, async (req, res) => {
    try {
        const status = pollingService.getPollingStatus();

        res.json({
            success: true,
            data: status
        });

    } catch (error) {
        console.error('Error fetching polling status:', error);
        res.status(500).json({
            success: false,
            message: 'Polling durumu alınırken hata oluştu: ' + error.message
        });
    }
});

// Cihaz kodu ile cihaz bilgilerini sorgula (proxy endpoint)
router.get('/cihaz/:cihazKodu', authMiddleware, async (req, res) => {
    try {
        const { cihazKodu } = req.params;
        
        // External API'den cihaz detayını getir
        const deviceDetail = await externalApiService.fetchDeviceDetail(cihazKodu);
        
        res.json({
            success: true,
            data: deviceDetail
        });

    } catch (error) {
        console.error(`Error fetching device ${req.params.cihazKodu}:`, error);
        res.status(500).json({
            success: false,
            message: 'Cihaz bilgisi alınırken hata oluştu: ' + error.message
        });
    }
});

// Polling'i başlat/durdur (admin only)
router.post('/polling/:action', authMiddleware, async (req, res) => {
    try {
        const { action } = req.params;
        
        // Admin kontrolü
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Bu işlem için admin yetkisi gerekli'
            });
        }

        if (action === 'start') {
            await pollingService.startPolling();
            res.json({
                success: true,
                message: 'Polling servisi başlatıldı'
            });
        } else if (action === 'stop') {
            await pollingService.stopPolling();
            res.json({
                success: true,
                message: 'Polling servisi durduruldu'
            });
        } else {
            res.status(400).json({
                success: false,
                message: 'Geçersiz aksiyon. Kullanım: /polling/start veya /polling/stop'
            });
        }

    } catch (error) {
        console.error(`Error ${req.params.action} polling:`, error);
        res.status(500).json({
            success: false,
            message: `Polling ${req.params.action} hatası: ` + error.message
        });
    }
});

export default router;