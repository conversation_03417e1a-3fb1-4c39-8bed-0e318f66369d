"use strict";(self.webpackChunkmgz24=self.webpackChunkmgz24||[]).push([[936],{421:(e,a,n)=>{n.d(a,{A:()=>r});n(5043);var s=n(9002),t=n(3910),i=n(7929),l=n(579);const r=()=>{const e=(0,s.zy)(),a="admin"===(()=>{try{var e,a;const n=JSON.parse(localStorage.getItem("user"));return(null===n||void 0===n||null===(e=n.user)||void 0===e?void 0:e.role)||(null===n||void 0===n||null===(a=n.user)||void 0===a?void 0:a.gorev)||"user"}catch(n){return"user"}})();return(0,l.jsx)("div",{className:"sidebar col-md-4 col-lg-2 p-0 bg-light-subtle",children:(0,l.jsxs)("div",{className:"offcanvas-md offcanvas-end",tabIndex:"-1",id:"yanMenu","aria-labelledby":"yanMenu",children:[(0,l.jsxs)("div",{className:"offcanvas-header",children:[(0,l.jsx)("h5",{className:"offcanvas-title",id:"yanMenu",children:"MGZ24 Gold"}),(0,l.jsx)("button",{type:"button",className:"btn-close","data-bs-dismiss":"offcanvas","data-bs-target":"#yanMenu","aria-label":"Kapat"})]}),(0,l.jsxs)("div",{className:"offcanvas-body d-md-flex flex-column p-0 pt-lg-3 overflow-y-auto",children:[(0,l.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase",children:"Sevkiyat \u0130\u015flemleri"}),(0,l.jsxs)("ul",{className:"nav nav-pills flex-column",children:[(0,l.jsx)("li",{className:"nav-item",children:(0,l.jsxs)(s.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/"===e.pathname?"active":""),to:"/",children:[(0,l.jsx)(t.g,{icon:i.msb}),"Aktif Sevkiyatlar"]})}),(0,l.jsx)("li",{className:"nav-item",children:(0,l.jsxs)(s.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/add"===e.pathname?"active":""),to:"/add",children:[(0,l.jsx)(t.g,{icon:i.E5r}),"Yeni Sevkiyat Olu\u015ftur"]})}),(0,l.jsx)("li",{className:"nav-item",children:(0,l.jsxs)(s.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/inactive-devices"===e.pathname?"active":""),to:"/inactive-devices",children:[(0,l.jsx)(t.g,{icon:i.fH7}),"\u0130naktif Cihazlar"]})}),(0,l.jsx)("li",{className:"nav-item",children:(0,l.jsxs)(s.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/history"===e.pathname?"active":""),to:"/history",children:[(0,l.jsx)(t.g,{icon:i.o9H}),"Ge\xe7mi\u015f Sevkiyatlar"]})})]}),(0,l.jsx)("hr",{className:"my-3"}),(0,l.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase",children:"Sistem Y\xf6netimi"}),(0,l.jsxs)("ul",{className:"nav nav-pills flex-column",children:[a&&(0,l.jsx)("li",{className:"nav-item",children:(0,l.jsxs)(s.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/devices"===e.pathname?"active":""),to:"/devices",children:[(0,l.jsx)(t.g,{icon:i.ArK}),"Cihaz Y\xf6netimi"]})}),(0,l.jsx)("li",{className:"nav-item",children:(0,l.jsxs)(s.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/notifications"===e.pathname?"active":""),to:"/notifications",children:[(0,l.jsx)(t.g,{icon:i.z$e}),"Bildirimler"]})})]}),(0,l.jsx)("hr",{className:"my-3"}),(0,l.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase",children:"Kullan\u0131c\u0131 Ayarlar\u0131"}),(0,l.jsxs)("ul",{className:"nav nav-pills flex-column",children:[(0,l.jsx)("li",{className:"nav-item",children:(0,l.jsxs)(s.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/profile"===e.pathname?"active":""),to:"/profile",children:[(0,l.jsx)(t.g,{icon:i.X46}),"Kullan\u0131c\u0131 Profilim"]})}),(0,l.jsx)("li",{className:"nav-item",children:(0,l.jsxs)(s.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/viewers"===e.pathname?"active":""),to:"/viewers",children:[(0,l.jsx)(t.g,{icon:i.yLE}),"\u0130zleyici \u0130\u015flemleri"]})})]}),(0,l.jsx)("hr",{className:"my-3"}),(0,l.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase",children:"\xd6deme ve Yap\u0131land\u0131rma"}),(0,l.jsxs)("ul",{className:"nav nav-pills flex-column",children:[(0,l.jsx)("li",{className:"nav-item",children:(0,l.jsxs)(s.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/payment"===e.pathname?"active":""),to:"/payment",children:[(0,l.jsx)(t.g,{icon:i.$O8}),"\xd6deme Yap"]})}),(0,l.jsx)("li",{className:"nav-item",children:(0,l.jsxs)(s.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/invoices"===e.pathname?"active":""),to:"/invoices",children:[(0,l.jsx)(t.g,{icon:i.bLf}),"Faturalar\u0131m"]})})]}),(0,l.jsx)("hr",{className:"my-3"}),(0,l.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-body-secondary text-uppercase",children:"Kredi Durumu"})]})]})})}},834:(e,a,n)=>{n.d(a,{A:()=>t});n(5043);var s=n(579);const t=()=>(0,s.jsx)("footer",{className:"py-5 border-top",children:(0,s.jsx)("p",{className:"text-center",children:"\xa92025 Inkatech \xd6l\xe7\xfcm Sistemleri"})})},1899:(e,a,n)=>{n.d(a,{A:()=>d});var s=n(5043),t=n(9002),i=n(3910),l=n(7929);var r=n(4713),c=n(579);const d=()=>{const e=(0,t.Zp)(),[a,n]=(0,s.useState)("Misafir"),[d,o]=(0,s.useState)(!0);(0,s.useEffect)((()=>{(async()=>{try{var e,a,s,t;const l=JSON.parse(localStorage.getItem("user")),c=(null===l||void 0===l||null===(e=l.user)||void 0===e?void 0:e.musteri_ID)||(null===l||void 0===l||null===(a=l.user)||void 0===a?void 0:a.id),d=(null===l||void 0===l||null===(s=l.user)||void 0===s?void 0:s.name)||(null===l||void 0===l||null===(t=l.user)||void 0===t?void 0:t.musteri_adi);if(!c)return console.warn("Oturum bilgisi bulunamad\u0131"),n("Misafir"),void o(!1);if(d)return n(d),void o(!1);try{const e=await r.Qj.getKullanici(c);e&&e.musteri_adi&&(n(e.musteri_adi),null!==l&&void 0!==l&&l.user&&(l.user.name=e.musteri_adi,localStorage.setItem("user",JSON.stringify(l))))}catch(i){console.warn("API'den kullan\u0131c\u0131 bilgisi al\u0131namad\u0131, localStorage kullan\u0131l\u0131yor"),n(d||"Kullan\u0131c\u0131")}}catch(l){console.error("Kullan\u0131c\u0131 bilgileri al\u0131n\u0131rken hata:",l),n("Kullan\u0131c\u0131")}finally{o(!1)}})()}),[]);return(0,c.jsxs)("header",{className:"navbar sticky-top bg-darkblue flex-md-nowrap p-0 shadow","data-bs-theme":"dark",children:[(0,c.jsx)(t.N_,{className:"navbar-brand col-md-4 col-lg-2 me-0 px-3 py-2 fs-6 text-white",to:"/",children:(0,c.jsx)("img",{src:"data:image/png;base64,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",alt:"MGZ24 Logo",height:"40"})}),(0,c.jsxs)("ul",{className:"navbar-nav flex-row align-items-center",children:[(0,c.jsx)("li",{className:"nav-item text-nowrap d-none d-md-block me-3",children:(0,c.jsxs)("span",{className:"text-white",children:[(0,c.jsx)(i.g,{icon:l.X46,className:"me-2"}),d?"Y\xfckleniyor...":a]})}),(0,c.jsx)("li",{className:"nav-item text-nowrap px-1",children:(0,c.jsx)("button",{onClick:()=>{localStorage.removeItem("user"),e("/login")},className:"nav-link px-3","data-bs-toggle":"tooltip","data-bs-placement":"left","data-bs-title":"\xc7\u0131k\u0131\u015f yap!",children:(0,c.jsx)(i.g,{icon:l.yBu})})}),(0,c.jsx)("li",{className:"nav-item text-nowrap d-md-none",children:(0,c.jsx)("button",{className:"nav-link px-3",type:"button","data-bs-toggle":"offcanvas","data-bs-target":"#yanMenu","aria-controls":"yanMenu","aria-expanded":"false","aria-label":"menu a\xe7/kapa",children:(0,c.jsx)(i.g,{icon:l.ckx})})})]})]})}},9936:(e,a,n)=>{n.r(a),n.d(a,{default:()=>u});var s=n(5043),t=n(9002),i=n(1899),l=n(421),r=n(834),c=n(9144),d=n(5492),o=n(4713),m=n(579);const u=()=>{const[e,a]=(0,s.useState)([]),[n,u]=(0,s.useState)(!0),[A,v]=(0,s.useState)(null),[x]=(0,s.useState)({}),h=(0,t.Zp)();(0,s.useEffect)((()=>{(async()=>{try{var e,n,s,t;u(!0),v(null);let i=JSON.parse(localStorage.getItem("user")),l=(null===(e=i)||void 0===e||null===(n=e.user)||void 0===n?void 0:n.musteri_ID)||(null===(s=i)||void 0===s||null===(t=s.user)||void 0===t?void 0:t.id);if(!l){console.log("Oturum bilgisi bulunamad\u0131. Demo kullan\u0131c\u0131 olu\u015fturuluyor...");const e={user:{id:1,musteri_ID:1,name:"Demo Kullan\u0131c\u0131",email:"<EMAIL>",username:"demo",role:"user"}};localStorage.setItem("user",JSON.stringify(e)),i=e,l=1}const r=await o.eg.getSevkiyatlarByMusteriId(l),c=await Promise.all(r.map((async e=>{var a,n,s,t,i,l;let r=null;try{if(e.mgz24_kodu){const a=await o.eT.getCihazFromExternalAPI(e.mgz24_kodu);if(a.success&&a.data){const e=a.data.sonSensorler||a.data;var c,d;if(e)r={sicaklik:e.sicaklik,nem:e.nem,enlem:e.enlem||(null===(c=a.data.konum)||void 0===c?void 0:c.enlem),boylam:e.boylam||(null===(d=a.data.konum)||void 0===d?void 0:d.boylam),zaman:e.zaman||a.data.sonGuncelleme}}}}catch(A){console.warn("Sevkiyat ".concat(e.id," (MGZ24: ").concat(e.mgz24_kodu,") i\xe7in sens\xf6r verisi al\u0131namad\u0131:"),A)}let m="normal";if(null!==(a=r)&&void 0!==a&&a.sicaklik){const a=parseFloat(r.sicaklik),[n,s]=(e.sicaklik_araligi||"15-25\xb0C").replace("\xb0C","").split("-").map((e=>parseFloat(e.trim())));(a<n||a>s)&&(m="warning")}const u=e.cikis_lokasyon||"Lokasyon ".concat(e.cikis_lokasyon_id||""),v=e.varis_lokasyon||"Lokasyon ".concat(e.varis_lokasyon_id||""),x=e.nakliyeci||"Nakliyeci ".concat(e.nakliyeci_id||""),h=e.urun||"\xdcr\xfcn ".concat(e.urun_id||"");return{id:e.id,sevkiyatID:e.sevkiyat_ID||"-",mgzKodu:e.mgz24_kodu||"-",name:e.sevkiyat_adi||"\u0130simsiz Sevkiyat",plate:e.plaka_no||"-",from:u,to:v,carrier:x,product:h,orderNo:e.mgz24_kodu||"-",pallet:(null===(n=e.palet_sayisi)||void 0===n?void 0:n.toString())||"-",net:(null===(s=e.net_agirlik)||void 0===s?void 0:s.toString())||"-",gross:(null===(t=e.brut_agirlik)||void 0===t?void 0:t.toString())||"-",added:g(e.olusturma_zamani)||"-",location:r?"".concat(r.enlem,", ").concat(r.boylam):"Konum bilgisi yok",lastData:r?g(r.zaman,!0):"-",temperature:(null===(i=r)||void 0===i||null===(l=i.sicaklik)||void 0===l?void 0:l.toString())||"-",tempStatus:m}})));a(c),u(!1)}catch(A){var i,l,r;if(console.error("Sevkiyat verileri al\u0131n\u0131rken hata:",A),503===(null===(i=A.response)||void 0===i?void 0:i.status))v("Sunucu \u015fu anda hizmet veremiyor. L\xfctfen birka\xe7 dakika sonra tekrar deneyin.");else if(500===(null===(l=A.response)||void 0===l?void 0:l.status))v("Sunucu hatas\u0131 olu\u015ftu. L\xfctfen sistem y\xf6neticisine ba\u015fvurun.");else{if(404===(null===(r=A.response)||void 0===r?void 0:r.status))return a([]),void u(!1);if("ECONNABORTED"===A.code||A.message.includes("timeout"))v("Ba\u011flant\u0131 zaman a\u015f\u0131m\u0131na u\u011frad\u0131. L\xfctfen internet ba\u011flant\u0131n\u0131z\u0131 kontrol edin.");else if(A.message.includes("Network Error"))v("Sunucu ba\u011flant\u0131 hatas\u0131. L\xfctfen internet ba\u011flant\u0131n\u0131z\u0131 kontrol edin.");else{var c,d;v("Sevkiyat verileri y\xfcklenirken hata olu\u015ftu: "+((null===(c=A.response)||void 0===c||null===(d=c.data)||void 0===d?void 0:d.message)||A.message))}}u(!1)}})()}),[x,h]);const g=function(e){let a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e)return"";const n=new Date(e),s=n.getDate().toString().padStart(2,"0"),t=(n.getMonth()+1).toString().padStart(2,"0"),i=n.getFullYear();if(a){const e=n.getHours().toString().padStart(2,"0"),a=n.getMinutes().toString().padStart(2,"0");return"".concat(s,".").concat(t,".").concat(i," ").concat(e,":").concat(a)}return"".concat(s,".").concat(t,".").concat(i)};return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(i.A,{}),(0,m.jsx)("div",{className:"container-fluid",children:(0,m.jsxs)("div",{className:"row",children:[(0,m.jsx)(l.A,{}),(0,m.jsxs)("main",{className:"col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white",children:[(0,m.jsx)("div",{className:"pt-3 pb-1 mt-3 mb-3 border-bottom",children:(0,m.jsx)("h1",{className:"h4 text-dark",children:"Aktif Sevkiyatlar"})}),n?(0,m.jsx)(c.A,{size:"lg",variant:"primary",message:"Sevkiyat verileri y\xfckleniyor...",centered:!0}):A?(0,m.jsx)(d.A,{message:A,variant:"danger",title:"Veri Y\xfckleme Hatas\u0131",dismissible:!0,onDismiss:()=>v(""),children:(0,m.jsx)("button",{className:"btn btn-primary btn-sm mt-2",onClick:()=>window.location.reload(),children:"Yeniden Dene"})}):(0,m.jsx)("div",{className:"row",children:(0,m.jsx)("div",{className:"col-12",children:(0,m.jsx)("div",{className:"card border-tertiary-subtle px-3 py-3 mb-5",children:0===e.length?(0,m.jsxs)("div",{className:"text-center py-5",children:[(0,m.jsxs)("div",{className:"mb-4",children:[(0,m.jsx)("i",{className:"fas fa-shipping-fast fa-3x text-muted mb-3"}),(0,m.jsx)("h5",{className:"text-muted",children:"Aktif Sevkiyat Bulunamad\u0131"}),(0,m.jsx)("p",{className:"text-muted",children:"Hen\xfcz aktif sevkiyat\u0131n\u0131z bulunmamaktad\u0131r. Yeni bir sevkiyat olu\u015fturmak i\xe7in a\u015fa\u011f\u0131daki butonu kullanabilirsiniz."})]}),(0,m.jsxs)(t.N_,{to:"/add",className:"btn btn-primary",children:[(0,m.jsx)("i",{className:"fas fa-plus me-2"}),"Yeni Sevkiyat Olu\u015ftur"]})]}):(0,m.jsx)("div",{className:"table-responsive",style:{overflowX:"unset"},children:(0,m.jsxs)("table",{className:"table table-dark-subtle table-bordered table-hover table-striped table-sm",style:{fontSize:"0.85rem"},children:[(0,m.jsx)("thead",{className:"table-light",children:(0,m.jsxs)("tr",{children:[(0,m.jsx)("th",{children:"Sevkiyat ID"}),(0,m.jsx)("th",{children:"Sevkiyat Ad\u0131"}),(0,m.jsx)("th",{children:"Plaka No"}),(0,m.jsx)("th",{children:"Nereden"}),(0,m.jsx)("th",{children:"Nereye"}),(0,m.jsx)("th",{children:"Nakliyeci"}),(0,m.jsx)("th",{children:"\xdcr\xfcn"}),(0,m.jsx)("th",{children:"Sipari\u015f No"}),(0,m.jsx)("th",{children:"Palet"}),(0,m.jsx)("th",{children:"Net"}),(0,m.jsx)("th",{children:"Br\xfct"}),(0,m.jsx)("th",{children:"Eklenme"})]})}),(0,m.jsx)("tbody",{children:e.map(((e,a)=>(0,m.jsxs)("tr",{children:[(0,m.jsx)("td",{children:(0,m.jsxs)(t.N_,{to:"/view/".concat(e.id),children:[e.sevkiyatID,e.mgzKodu&&(0,m.jsx)("span",{className:"ms-1 badge bg-primary text-white",children:e.mgzKodu})]})}),(0,m.jsx)("td",{children:e.name}),(0,m.jsx)("td",{children:e.plate}),(0,m.jsx)("td",{children:e.from}),(0,m.jsx)("td",{children:e.to}),(0,m.jsx)("td",{children:e.carrier}),(0,m.jsx)("td",{children:e.product}),(0,m.jsx)("td",{children:e.orderNo}),(0,m.jsx)("td",{children:e.pallet}),(0,m.jsx)("td",{children:e.net}),(0,m.jsx)("td",{children:e.gross}),(0,m.jsx)("td",{children:e.added})]},a)))})]})})})})})]}),(0,m.jsx)(r.A,{})]})})]})}}}]);
//# sourceMappingURL=936.fbc9d7fb.chunk.js.map