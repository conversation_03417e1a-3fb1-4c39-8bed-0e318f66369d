{"version": 3, "file": "static/js/936.fbc9d7fb.chunk.js", "mappings": "gKASA,MAwGA,EAxGgBA,KACZ,MAAMC,GAAWC,EAAAA,EAAAA,MAaXC,EAAuB,UAVTC,MAChB,IAAK,IAADC,EAAAC,EACA,MAAMC,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C,OAAW,OAAJJ,QAAI,IAAJA,GAAU,QAANF,EAAJE,EAAMA,YAAI,IAAAF,OAAN,EAAJA,EAAYO,QAAY,OAAJL,QAAI,IAAJA,GAAU,QAAND,EAAJC,EAAMA,YAAI,IAAAD,OAAN,EAAJA,EAAYO,QAAS,MACpD,CAAE,MAAAC,GACE,MAAO,MACX,GAGaV,GAGjB,OACIW,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC1DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6BAA6BG,SAAS,KAAKC,GAAG,UAAU,kBAAgB,UAASH,SAAA,EAC5FC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,EAC7BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,kBAAkBI,GAAG,UAASH,SAAC,gBAC7CF,EAAAA,EAAAA,KAAA,UAAQM,KAAK,SAASL,UAAU,YAAY,kBAAgB,YAAY,iBAAe,WAAW,aAAW,cAEjHE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mEAAkEC,SAAA,EAC7EF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,kCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,MAAtBtB,EAASuB,SAAmB,SAAW,IAAMC,GAAG,IAAGR,SAAA,EAC5GF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMC,EAAAA,MAAW,0BAG1Cb,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,SAAtBtB,EAASuB,SAAsB,SAAW,IAAMC,GAAG,OAAMR,SAAA,EAClHF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAME,EAAAA,MAAgB,mCAG/Cd,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,sBAAtBtB,EAASuB,SAAmC,SAAW,IAAMC,GAAG,oBAAmBR,SAAA,EAC5IF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMG,EAAAA,MAAY,8BAG3Cf,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMI,EAAAA,MAAmB,sCAKtDhB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wBACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,CAEpCd,IACGY,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMK,EAAAA,MAAa,0BAIhDjB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,mBAAtBtB,EAASuB,SAAgC,SAAW,IAAMC,GAAG,iBAAgBR,SAAA,EACtIF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMM,EAAAA,MAAU,uBAK7ClB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,uCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMO,EAAAA,MAAU,qCAGzCnB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMQ,EAAAA,MAAgB,6CAKnDpB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMS,EAAAA,MAAgB,qBAG/CrB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,cAAtBtB,EAASuB,SAA2B,SAAW,IAAMC,GAAG,YAAWR,SAAA,EAC5HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMU,EAAAA,MAAe,4BAKlDtB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wBAG1I,C,sDC3Gd,MAQA,EAReqB,KAEPvB,EAAAA,EAAAA,KAAA,UAAQC,UAAU,kBAAiBC,UAC/BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,cAAaC,SAAC,iD,qGCEvC,MA6GA,EA7GesB,KACX,MAAMC,GAAWC,EAAAA,EAAAA,OACVC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,YAClCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAGvCG,EAAAA,EAAAA,YAAU,KACgBC,WAClB,IAAK,IAADC,EAAAC,EAAAC,EAAAC,EAEA,MAAMC,EAAa7C,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C2C,GAAmB,OAAVD,QAAU,IAAVA,GAAgB,QAANJ,EAAVI,EAAY9C,YAAI,IAAA0C,OAAN,EAAVA,EAAkBM,cAAwB,OAAVF,QAAU,IAAVA,GAAgB,QAANH,EAAVG,EAAY9C,YAAI,IAAA2C,OAAN,EAAVA,EAAkB9B,IAC3DsB,GAAqB,OAAVW,QAAU,IAAVA,GAAgB,QAANF,EAAVE,EAAY9C,YAAI,IAAA4C,OAAN,EAAVA,EAAkBK,QAAkB,OAAVH,QAAU,IAAVA,GAAgB,QAAND,EAAVC,EAAY9C,YAAI,IAAA6C,OAAN,EAAVA,EAAkBK,aAE7D,IAAKH,EAID,OAHAI,QAAQC,KAAK,kCACbhB,EAAY,gBACZG,GAAW,GAKf,GAAIJ,EAGA,OAFAC,EAAYD,QACZI,GAAW,GAKf,IACI,MAAMc,QAAiBC,EAAAA,GAAiBC,aAAaR,GAGjDM,GAAYA,EAASH,cACrBd,EAAYiB,EAASH,aAGP,OAAVJ,QAAU,IAAVA,GAAAA,EAAY9C,OACZ8C,EAAW9C,KAAKiD,KAAOI,EAASH,YAChC/C,aAAaqD,QAAQ,OAAQvD,KAAKwD,UAAUX,KAGxD,CAAE,MAAOY,GACLP,QAAQC,KAAK,gGAEbhB,EAAYD,GAAY,sBAC5B,CACJ,CAAE,MAAOwB,GACLR,QAAQQ,MAAM,0DAAuCA,GAErDvB,EAAY,sBAChB,CAAC,QACGG,GAAW,EACf,GAGJqB,EAAe,GAChB,IAWH,OACIjD,EAAAA,EAAAA,MAAA,UAAQF,UAAU,0DAA0D,gBAAc,OAAMC,SAAA,EAC5FF,EAAAA,EAAAA,KAACO,EAAAA,GAAI,CAACN,UAAU,gEAAgES,GAAG,IAAGR,UAClFF,EAAAA,EAAAA,KAAA,OAAKqD,I,60RAAeC,IAAI,aAAaC,OAAO,UAEhDpD,EAAAA,EAAAA,MAAA,MAAIF,UAAU,yCAAwCC,SAAA,EAClDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8CAA6CC,UACvDC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,aAAYC,SAAA,EACxBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMO,EAAAA,IAAQlB,UAAU,SACxC6B,EAAU,mBAAkBH,QAGrC3B,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4BAA2BC,UACrCF,EAAAA,EAAAA,KAAA,UACIwD,QAtBCC,KAEjB9D,aAAa+D,WAAW,QAGxBjC,EAAS,SAAS,EAkBFxB,UAAU,gBACV,iBAAe,UACf,oBAAkB,OAClB,gBAAc,+BAAYC,UAE1BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM+C,EAAAA,WAG/B3D,EAAAA,EAAAA,KAAA,MAAIC,UAAU,iCAAgCC,UAC1CF,EAAAA,EAAAA,KAAA,UACIC,UAAU,gBACVK,KAAK,SACL,iBAAe,YACf,iBAAe,WACf,gBAAc,UACd,gBAAc,QACd,aAAW,kBAAcJ,UAEzBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMgD,EAAAA,eAI9B,C,0ICvGjB,MAkRA,EAlRaC,KACT,MAAOC,EAAWC,IAAgBlC,EAAAA,EAAAA,UAAS,KACpCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCsB,EAAOa,IAAYnC,EAAAA,EAAAA,UAAS,OAC5BoC,IAAcpC,EAAAA,EAAAA,UAAS,CAAC,GACzBJ,GAAWC,EAAAA,EAAAA,OAGjBM,EAAAA,EAAAA,YAAU,KACYC,WACd,IAAK,IAADiC,EAAAhC,EAAAiC,EAAAC,EACArC,GAAW,GACXiC,EAAS,MAGT,IAAI1B,EAAa7C,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C2C,GAAmB,QAAV2B,EAAA5B,SAAU,IAAA4B,GAAM,QAANhC,EAAVgC,EAAY1E,YAAI,IAAA0C,OAAN,EAAVA,EAAkBM,cAAwB,QAAd2B,EAAI7B,SAAU,IAAA6B,GAAM,QAANC,EAAVD,EAAY3E,YAAI,IAAA4E,OAAN,EAAVA,EAAkB/D,IAG/D,IAAKkC,EAAQ,CACTI,QAAQ0B,IAAI,kFAGZ,MAAMC,EAAW,CACb9E,KAAM,CACFa,GAAI,EACJmC,WAAY,EACZC,KAAM,2BACN8B,MAAO,iBACPC,SAAU,OACV3E,KAAM,SAKdF,aAAaqD,QAAQ,OAAQvD,KAAKwD,UAAUqB,IAC5ChC,EAAagC,EACb/B,EAAS,CACb,CAGA,MAAMkC,QAAqBC,EAAAA,GAAgBC,0BAA0BpC,GAG/DqC,QAAsBC,QAAQC,IAAIL,EAAaM,KAAI9C,UAAiB,IAAD+C,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAErE,IAAIC,EAAa,KACjB,IACI,GAAIC,EAAKC,WAAY,CACjB,MAAMC,QAAiBC,EAAAA,GAAkBC,wBAAwBJ,EAAKC,YACtE,GAAIC,EAASG,SAAWH,EAASI,KAAM,CAEnC,MAAMC,EAAaL,EAASI,KAAKE,cAAgBN,EAASI,KACzC,IAADG,EAAAC,EAAhB,GAAIH,EACAR,EAAa,CACTY,SAAUJ,EAAWI,SACrBC,IAAKL,EAAWK,IAChBC,MAAON,EAAWM,QAA4B,QAAvBJ,EAAIP,EAASI,KAAKQ,aAAK,IAAAL,OAAA,EAAnBA,EAAqBI,OAChDE,OAAQR,EAAWQ,SAA6B,QAAvBL,EAAIR,EAASI,KAAKQ,aAAK,IAAAJ,OAAA,EAAnBA,EAAqBK,QAClDC,MAAOT,EAAWS,OAASd,EAASI,KAAKW,cAGrD,CACJ,CACJ,CAAE,MAAOrD,GACLR,QAAQC,KAAK,YAADpC,OAAa+E,EAAKlF,GAAE,aAAAG,OAAY+E,EAAKC,WAAU,mDAAmCrC,EAClG,CAGA,IAAIsD,EAAa,SACjB,GAAc,QAAdzB,EAAIM,SAAU,IAAAN,GAAVA,EAAYkB,SAAU,CACtB,MAAMQ,EAAOC,WAAWrB,EAAWY,WAC5BU,EAASC,IAAYtB,EAAKuB,kBAAoB,cAChDC,QAAQ,QAAM,IACdC,MAAM,KACNjC,KAAIkC,GAAKN,WAAWM,EAAEC,WAEvBR,EAAOE,GAAWF,EAAOG,KACzBJ,EAAa,UAErB,CAGA,MAAMU,EAAW5B,EAAK6B,gBAAc,YAAA5G,OAAgB+E,EAAK8B,mBAAqB,IACxEC,EAAS/B,EAAKgC,gBAAc,YAAA/G,OAAgB+E,EAAKiC,mBAAqB,IACtEC,EAAclC,EAAKmC,WAAS,aAAAlH,OAAiB+E,EAAKoC,cAAgB,IAClEC,EAAcrC,EAAKsC,MAAI,cAAArH,OAAY+E,EAAKuC,SAAW,IAEzD,MAAO,CACHzH,GAAIkF,EAAKlF,GACT0H,WAAYxC,EAAKyC,aAAe,IAChCC,QAAS1C,EAAKC,YAAc,IAC5B/C,KAAM8C,EAAK2C,cAAgB,wBAC3BC,MAAO5C,EAAK6C,UAAY,IACxBC,KAAMlB,EACNzG,GAAI4G,EACJgB,QAASb,EACTc,QAASX,EACTY,QAASjD,EAAKC,YAAc,IAC5BiD,QAAyB,QAAjBxD,EAAAM,EAAKmD,oBAAY,IAAAzD,OAAA,EAAjBA,EAAmB0D,aAAc,IACzCC,KAAqB,QAAhB1D,EAAAK,EAAKsD,mBAAW,IAAA3D,OAAA,EAAhBA,EAAkByD,aAAc,IACrCG,OAAwB,QAAjB3D,EAAAI,EAAKwD,oBAAY,IAAA5D,OAAA,EAAjBA,EAAmBwD,aAAc,IACxCK,MAAOC,EAAW1D,EAAK2D,mBAAqB,IAC5ChK,SAAUoG,EAAU,GAAA9E,OAAM8E,EAAWc,MAAK,MAAA5F,OAAK8E,EAAWgB,QAAW,oBACrE6C,SAAU7D,EAAa2D,EAAW3D,EAAWiB,OAAO,GAAQ,IAC5D6C,aAAuB,QAAVhE,EAAAE,SAAU,IAAAF,GAAU,QAAVC,EAAVD,EAAYc,gBAAQ,IAAAb,OAAV,EAAVA,EAAsBsD,aAAc,IACjDlC,WAAYA,EACf,KAGL1C,EAAaa,GACb7C,GAAW,EACf,CAAE,MAAOoB,GAAQ,IAADkG,EAAAC,EAAAC,EAIZ,GAHA5G,QAAQQ,MAAM,8CAAqCA,GAGpB,OAAb,QAAdkG,EAAAlG,EAAMsC,gBAAQ,IAAA4D,OAAA,EAAdA,EAAgBG,QAChBxF,EAAS,+FACN,GAA+B,OAAb,QAAdsF,EAAAnG,EAAMsC,gBAAQ,IAAA6D,OAAA,EAAdA,EAAgBE,QACvBxF,EAAS,uFACN,IAA+B,OAAb,QAAduF,EAAApG,EAAMsC,gBAAQ,IAAA8D,OAAA,EAAdA,EAAgBC,QAIvB,OAFAzF,EAAa,SACbhC,GAAW,GAER,GAAmB,iBAAfoB,EAAMsG,MAA2BtG,EAAMuG,QAAQC,SAAS,WAC/D3F,EAAS,4IACN,GAAIb,EAAMuG,QAAQC,SAAS,iBAC9B3F,EAAS,gHACN,CAAC,IAAD4F,EAAAC,EACH7F,EAAS,wDAA+D,QAAd4F,EAAAzG,EAAMsC,gBAAQ,IAAAmE,GAAM,QAANC,EAAdD,EAAgB/D,YAAI,IAAAgE,OAAN,EAAdA,EAAsBH,UAAWvG,EAAMuG,SACrG,EACA3H,GAAW,EACf,GAGJ+H,EAAW,GACZ,CAAC7F,EAAYxC,IAGhB,MAAMwH,EAAa,SAACc,GAAqC,IAAzBC,EAAWC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,IAAAA,UAAA,GACvC,IAAKF,EAAY,MAAO,GAExB,MAAMK,EAAO,IAAIC,KAAKN,GAChBO,EAAMF,EAAKG,UAAU5B,WAAW6B,SAAS,EAAG,KAC5CC,GAASL,EAAKM,WAAa,GAAG/B,WAAW6B,SAAS,EAAG,KACrDG,EAAOP,EAAKQ,cAElB,GAAIZ,EAAa,CACb,MAAMa,EAAQT,EAAKU,WAAWnC,WAAW6B,SAAS,EAAG,KAC/CO,EAAUX,EAAKY,aAAarC,WAAW6B,SAAS,EAAG,KACzD,MAAM,GAANhK,OAAU8J,EAAG,KAAA9J,OAAIiK,EAAK,KAAAjK,OAAImK,EAAI,KAAAnK,OAAIqK,EAAK,KAAArK,OAAIuK,EAC/C,CAEA,MAAM,GAANvK,OAAU8J,EAAG,KAAA9J,OAAIiK,EAAK,KAAAjK,OAAImK,EAC9B,EAEA,OACIxK,EAAAA,EAAAA,MAAA8K,EAAAA,SAAA,CAAA/K,SAAA,EACIF,EAAAA,EAAAA,KAACwB,EAAAA,EAAM,KACPxB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAChBF,EAAAA,EAAAA,KAACf,EAAAA,EAAO,KAGRkB,EAAAA,EAAAA,MAAA,QAAMF,UAAU,iFAAgFC,SAAA,EAC5FF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAAmCC,UAC9CF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,eAAcC,SAAC,wBAGhC4B,GACG9B,EAAAA,EAAAA,KAACkL,EAAAA,EAAc,CACXC,KAAK,KACLC,QAAQ,UACR1B,QAAQ,qCACR2B,UAAU,IAEdlI,GACAnD,EAAAA,EAAAA,KAACsL,EAAAA,EAAY,CACT5B,QAASvG,EACTiI,QAAQ,SACRG,MAAM,8BACNC,aAAa,EACbC,UAAWA,IAAMzH,EAAS,IAAI9D,UAE9BF,EAAAA,EAAAA,KAAA,UAAQC,UAAU,8BAA8BuD,QAASA,IAAMkI,OAAOxM,SAASyM,SAASzL,SAAC,oBAK7FF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,MAAKC,UAChBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,SAAQC,UAGnBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6CAA4CC,SACjC,IAArB4D,EAAUoG,QACP/J,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,EAC7BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACjBF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDACbD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,aAAYC,SAAC,oCAC3BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,aAAYC,SAAC,iKAK9BC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACG,GAAG,OAAOT,UAAU,kBAAiBC,SAAA,EACvCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBAAuB,oCAK5CD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAmB2L,MAAO,CAAEC,UAAW,SAAU3L,UAC5DC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,4EAA4E2L,MAAO,CAAEE,SAAU,WAAY5L,SAAA,EACxHF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,cAAaC,UAC1BC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACIF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,iBACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,uBACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,cACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,aACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,YACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,eACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,gBACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,qBACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,WACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,SACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,aACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,kBAGZF,EAAAA,EAAAA,KAAA,SAAAE,SACK4D,EAAUiB,KAAI,CAACgH,EAAUC,KACtB7L,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACIF,EAAAA,EAAAA,KAAA,MAAAE,UACIC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACG,GAAE,SAAAF,OAAWuL,EAAS1L,IAAKH,SAAA,CAC5B6L,EAAShE,WACTgE,EAAS9D,UACNjI,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mCAAkCC,SAC7C6L,EAAS9D,gBAK1BjI,EAAAA,EAAAA,KAAA,MAAAE,SAAK6L,EAAStJ,QACdzC,EAAAA,EAAAA,KAAA,MAAAE,SAAK6L,EAAS5D,SACdnI,EAAAA,EAAAA,KAAA,MAAAE,SAAK6L,EAAS1D,QACdrI,EAAAA,EAAAA,KAAA,MAAAE,SAAK6L,EAASrL,MACdV,EAAAA,EAAAA,KAAA,MAAAE,SAAK6L,EAASzD,WACdtI,EAAAA,EAAAA,KAAA,MAAAE,SAAK6L,EAASxD,WACdvI,EAAAA,EAAAA,KAAA,MAAAE,SAAK6L,EAASvD,WACdxI,EAAAA,EAAAA,KAAA,MAAAE,SAAK6L,EAAStD,UACdzI,EAAAA,EAAAA,KAAA,MAAAE,SAAK6L,EAASnD,OACd5I,EAAAA,EAAAA,KAAA,MAAAE,SAAK6L,EAASjD,SACd9I,EAAAA,EAAAA,KAAA,MAAAE,SAAK6L,EAAS/C,UArBTgD,sBAoCjDhM,EAAAA,EAAAA,KAACuB,EAAAA,EAAM,WAGhB,C", "sources": ["components/Sidebar.js", "components/Footer.js", "components/Header.js", "pages/Home.js"], "sourcesContent": ["import React from 'react';\r\nimport { Link, useLocation } from 'react-router-dom';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport {\r\n    faDolly, faFolderPlus, faAnchor, faFlag<PERSON>he<PERSON>ed,\r\n    faUser, faStreetView, faCreditCard, faFileLines,\r\n    faBell, faDesktop\r\n} from '@fortawesome/free-solid-svg-icons';\r\n\r\nconst Sidebar = () => {\r\n    const location = useLocation();\r\n    \r\n    // Get user role from localStorage\r\n    const getUserRole = () => {\r\n        try {\r\n            const user = JSON.parse(localStorage.getItem('user'));\r\n            return user?.user?.role || user?.user?.gorev || 'user';\r\n        } catch {\r\n            return 'user';\r\n        }\r\n    };\r\n    \r\n    const userRole = getUserRole();\r\n    const isAdmin = userRole === 'admin';\r\n\r\n    return (\r\n        <div className=\"sidebar col-md-4 col-lg-2 p-0 bg-light-subtle\">\r\n            <div className=\"offcanvas-md offcanvas-end\" tabIndex=\"-1\" id=\"yanMenu\" aria-labelledby=\"yanMenu\">\r\n                <div className=\"offcanvas-header\">\r\n                    <h5 className=\"offcanvas-title\" id=\"yanMenu\">MGZ24 Gold</h5>\r\n                    <button type=\"button\" className=\"btn-close\" data-bs-dismiss=\"offcanvas\" data-bs-target=\"#yanMenu\" aria-label=\"Kapat\"></button>\r\n                </div>\r\n                <div className=\"offcanvas-body d-md-flex flex-column p-0 pt-lg-3 overflow-y-auto\">\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Sevkiyat İşlemleri</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/' ? 'active' : ''}`} to=\"/\">\r\n                                <FontAwesomeIcon icon={faDolly} />Aktif Sevkiyatlar\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/add' ? 'active' : ''}`} to=\"/add\">\r\n                                <FontAwesomeIcon icon={faFolderPlus} />Yeni Sevkiyat Oluştur\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/inactive-devices' ? 'active' : ''}`} to=\"/inactive-devices\">\r\n                                <FontAwesomeIcon icon={faAnchor} />İnaktif Cihazlar\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/history' ? 'active' : ''}`} to=\"/history\">\r\n                                <FontAwesomeIcon icon={faFlagCheckered} />Geçmiş Sevkiyatlar\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Sistem Yönetimi</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        {/* Cihaz Yönetimi - Sadece admin kullanıcılarına göster */}\r\n                        {isAdmin && (\r\n                            <li className=\"nav-item\">\r\n                                <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/devices' ? 'active' : ''}`} to=\"/devices\">\r\n                                    <FontAwesomeIcon icon={faDesktop} />Cihaz Yönetimi\r\n                                </Link>\r\n                            </li>\r\n                        )}\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/notifications' ? 'active' : ''}`} to=\"/notifications\">\r\n                                <FontAwesomeIcon icon={faBell} />Bildirimler\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Kullanıcı Ayarları</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/profile' ? 'active' : ''}`} to=\"/profile\">\r\n                                <FontAwesomeIcon icon={faUser} />Kullanıcı Profilim\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/viewers' ? 'active' : ''}`} to=\"/viewers\">\r\n                                <FontAwesomeIcon icon={faStreetView} />İzleyici İşlemleri\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Ödeme ve Yapılandırma</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/payment' ? 'active' : ''}`} to=\"/payment\">\r\n                                <FontAwesomeIcon icon={faCreditCard} />Ödeme Yap\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/invoices' ? 'active' : ''}`} to=\"/invoices\">\r\n                                <FontAwesomeIcon icon={faFileLines} />Faturalarım\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-body-secondary text-uppercase\">Kredi Durumu</h6>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Sidebar;", "import React from 'react';\r\n\r\nconst Footer = () => {\r\n    return (\r\n        <footer className=\"py-5 border-top\">\r\n            <p className=\"text-center\">&copy;2025 Inkatech Ölçüm Sistemleri</p>\r\n        </footer>\r\n    );\r\n};\r\n\r\nexport default Footer; ", "import React, { useState, useEffect } from 'react';\r\nimport { Link, useNavigate } from 'react-router-dom';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { faRightFromBracket, faBars, faUser } from '@fortawesome/free-solid-svg-icons';\r\nimport logoDark from '../assets/img/logo.png';\r\nimport { kullaniciService } from '../api/dbService';\r\n\r\nconst Header = () => {\r\n    const navigate = useNavigate();\r\n    const [userName, setUserName] = useState('Misafir');\r\n    const [loading, setLoading] = useState(true);\r\n\r\n    // Kullanıcı bilgilerini veritabanından al\r\n    useEffect(() => {\r\n        const fetchUserData = async () => {\r\n            try {\r\n                // Local storage'dan kullanıcı bilgisini al\r\n                const storedUser = JSON.parse(localStorage.getItem('user'));\r\n                const userId = storedUser?.user?.musteri_ID || storedUser?.user?.id;\r\n                const userName = storedUser?.user?.name || storedUser?.user?.musteri_adi;\r\n\r\n                if (!userId) {\r\n                    console.warn('Oturum bilgisi bulunamadı');\r\n                    setUserName('Misafir');\r\n                    setLoading(false);\r\n                    return;\r\n                }\r\n\r\n                // Eğer localStorage'da isim varsa direkt kullan\r\n                if (userName) {\r\n                    setUserName(userName);\r\n                    setLoading(false);\r\n                    return;\r\n                }\r\n\r\n                // API'den kullanıcı bilgilerini getir (backend çalışıyorsa)\r\n                try {\r\n                    const userData = await kullaniciService.getKullanici(userId);\r\n\r\n                    // musteri_adi alanını kullan\r\n                    if (userData && userData.musteri_adi) {\r\n                        setUserName(userData.musteri_adi);\r\n\r\n                        // localStorage'ı güncelle\r\n                        if (storedUser?.user) {\r\n                            storedUser.user.name = userData.musteri_adi;\r\n                            localStorage.setItem('user', JSON.stringify(storedUser));\r\n                        }\r\n                    }\r\n                } catch (apiError) {\r\n                    console.warn('API\\'den kullanıcı bilgisi alınamadı, localStorage kullanılıyor');\r\n                    // API hatası durumunda localStorage'daki adı kullan\r\n                    setUserName(userName || 'Kullanıcı');\r\n                }\r\n            } catch (error) {\r\n                console.error('Kullanıcı bilgileri alınırken hata:', error);\r\n                // Hata durumunda varsayılan isim kullan\r\n                setUserName('Kullanıcı');\r\n            } finally {\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        fetchUserData();\r\n    }, []);\r\n\r\n    // Çıkış işlemi\r\n    const handleLogout = () => {\r\n        // Local storage'dan kullanıcı bilgilerini temizle\r\n        localStorage.removeItem('user');\r\n\r\n        // Login sayfasına yönlendir\r\n        navigate('/login');\r\n    };\r\n\r\n    return (\r\n        <header className=\"navbar sticky-top bg-darkblue flex-md-nowrap p-0 shadow\" data-bs-theme=\"dark\">\r\n            <Link className=\"navbar-brand col-md-4 col-lg-2 me-0 px-3 py-2 fs-6 text-white\" to=\"/\">\r\n                <img src={logoDark} alt=\"MGZ24 Logo\" height=\"40\" />\r\n            </Link>\r\n            <ul className=\"navbar-nav flex-row align-items-center\">\r\n                <li className=\"nav-item text-nowrap d-none d-md-block me-3\">\r\n                    <span className=\"text-white\">\r\n                        <FontAwesomeIcon icon={faUser} className=\"me-2\" />\r\n                        {loading ? 'Yükleniyor...' : userName}\r\n                    </span>\r\n                </li>\r\n                <li className=\"nav-item text-nowrap px-1\">\r\n                    <button\r\n                        onClick={handleLogout}\r\n                        className=\"nav-link px-3\"\r\n                        data-bs-toggle=\"tooltip\"\r\n                        data-bs-placement=\"left\"\r\n                        data-bs-title=\"Çıkış yap!\"\r\n                    >\r\n                        <FontAwesomeIcon icon={faRightFromBracket} />\r\n                    </button>\r\n                </li>\r\n                <li className=\"nav-item text-nowrap d-md-none\">\r\n                    <button\r\n                        className=\"nav-link px-3\"\r\n                        type=\"button\"\r\n                        data-bs-toggle=\"offcanvas\"\r\n                        data-bs-target=\"#yanMenu\"\r\n                        aria-controls=\"yanMenu\"\r\n                        aria-expanded=\"false\"\r\n                        aria-label=\"menu aç/kapa\"\r\n                    >\r\n                        <FontAwesomeIcon icon={faBars} />\r\n                    </button>\r\n                </li>\r\n            </ul>\r\n        </header>\r\n    );\r\n};\r\n\r\nexport default Header; ", "import React, { useState, useEffect } from 'react';\r\nimport { Link, useNavigate } from 'react-router-dom';\r\nimport Header from '../components/Header';\r\nimport Sidebar from '../components/Sidebar';\r\nimport Footer from '../components/Footer';\r\nimport LoadingSpinner from '../components/LoadingSpinner';\r\nimport ErrorMessage from '../components/ErrorMessage';\r\nimport { sevkiyatService, cihazBilgiService } from '../api/dbService';\r\n\r\nconst Home = () => {\r\n    const [shipments, setShipments] = useState([]);\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState(null);\r\n    const [masterData] = useState({});\r\n    const navigate = useNavigate();\r\n\r\n    // Verileri API'den getir\r\n    useEffect(() => {\r\n        const fetchData = async () => {\r\n            try {\r\n                setLoading(true);\r\n                setError(null);\r\n\r\n                // Local storage'dan kullanıcı bilgilerini al\r\n                let storedUser = JSON.parse(localStorage.getItem('user'));\r\n                let userId = storedUser?.user?.musteri_ID || storedUser?.user?.id;\r\n\r\n                // Kullanıcı bilgisi yoksa demo kullanıcı oluştur\r\n                if (!userId) {\r\n                    console.log('Oturum bilgisi bulunamadı. Demo kullanıcı oluşturuluyor...');\r\n\r\n                    // Demo kullanıcı oluştur\r\n                    const demoUser = {\r\n                        user: {\r\n                            id: 1,\r\n                            musteri_ID: 1,\r\n                            name: 'Demo Kullanıcı',\r\n                            email: '<EMAIL>',\r\n                            username: 'demo',\r\n                            role: 'user'\r\n                        }\r\n                    };\r\n\r\n                    // Demo kullanıcıyı localStorage'a kaydet\r\n                    localStorage.setItem('user', JSON.stringify(demoUser));\r\n                    storedUser = demoUser;\r\n                    userId = 1;\r\n                }\r\n\r\n                // Aktif kullanıcının sevkiyatlarını getir\r\n                const sevkiyatData = await sevkiyatService.getSevkiyatlarByMusteriId(userId);\r\n\r\n                // Veri dönüşümü\r\n                const processedData = await Promise.all(sevkiyatData.map(async (item) => {\r\n                    // Her sevkiyat için son sensör verilerini al - uzak API'den mgz24_kodu ile\r\n                    let lastSensor = null;\r\n                    try {\r\n                        if (item.mgz24_kodu) {\r\n                            const response = await cihazBilgiService.getCihazFromExternalAPI(item.mgz24_kodu);\r\n                            if (response.success && response.data) {\r\n                                // Uzak API'den gelen veri formatını düzenle\r\n                                const sensorData = response.data.sonSensorler || response.data;\r\n                                if (sensorData) {\r\n                                    lastSensor = {\r\n                                        sicaklik: sensorData.sicaklik,\r\n                                        nem: sensorData.nem,\r\n                                        enlem: sensorData.enlem || response.data.konum?.enlem,\r\n                                        boylam: sensorData.boylam || response.data.konum?.boylam,\r\n                                        zaman: sensorData.zaman || response.data.sonGuncelleme\r\n                                    };\r\n                                }\r\n                            }\r\n                        }\r\n                    } catch (error) {\r\n                        console.warn(`Sevkiyat ${item.id} (MGZ24: ${item.mgz24_kodu}) için sensör verisi alınamadı:`, error);\r\n                    }\r\n\r\n                    // Sıcaklık kontrolü ve durumu belirle\r\n                    let tempStatus = 'normal';\r\n                    if (lastSensor?.sicaklik) {\r\n                        const temp = parseFloat(lastSensor.sicaklik);\r\n                        const [minTemp, maxTemp] = (item.sicaklik_araligi || '15-25°C')\r\n                            .replace('°C', '')\r\n                            .split('-')\r\n                            .map(t => parseFloat(t.trim()));\r\n\r\n                        if (temp < minTemp || temp > maxTemp) {\r\n                            tempStatus = 'warning';\r\n                        }\r\n                    }\r\n\r\n                    // Doğrudan sevkiyat nesnesindeki metin alanlarını kullan\r\n                    const fromName = item.cikis_lokasyon || `Lokasyon ${item.cikis_lokasyon_id || ''}`;\r\n                    const toName = item.varis_lokasyon || `Lokasyon ${item.varis_lokasyon_id || ''}`;\r\n                    const carrierName = item.nakliyeci || `Nakliyeci ${item.nakliyeci_id || ''}`;\r\n                    const productName = item.urun || `Ürün ${item.urun_id || ''}`;\r\n\r\n                    return {\r\n                        id: item.id,\r\n                        sevkiyatID: item.sevkiyat_ID || '-',\r\n                        mgzKodu: item.mgz24_kodu || '-',\r\n                        name: item.sevkiyat_adi || 'İsimsiz Sevkiyat',\r\n                        plate: item.plaka_no || '-',\r\n                        from: fromName,\r\n                        to: toName,\r\n                        carrier: carrierName,\r\n                        product: productName,\r\n                        orderNo: item.mgz24_kodu || '-',\r\n                        pallet: item.palet_sayisi?.toString() || '-',\r\n                        net: item.net_agirlik?.toString() || '-',\r\n                        gross: item.brut_agirlik?.toString() || '-',\r\n                        added: formatDate(item.olusturma_zamani) || '-',\r\n                        location: lastSensor ? `${lastSensor.enlem}, ${lastSensor.boylam}` : 'Konum bilgisi yok',\r\n                        lastData: lastSensor ? formatDate(lastSensor.zaman, true) : '-',\r\n                        temperature: lastSensor?.sicaklik?.toString() || '-',\r\n                        tempStatus: tempStatus\r\n                    };\r\n                }));\r\n\r\n                setShipments(processedData);\r\n                setLoading(false);\r\n            } catch (error) {\r\n                console.error('Sevkiyat verileri alınırken hata:', error);\r\n                \r\n                // 503 veya 500 hataları için özel mesajlar\r\n                if (error.response?.status === 503) {\r\n                    setError('Sunucu şu anda hizmet veremiyor. Lütfen birkaç dakika sonra tekrar deneyin.');\r\n                } else if (error.response?.status === 500) {\r\n                    setError('Sunucu hatası oluştu. Lütfen sistem yöneticisine başvurun.');\r\n                } else if (error.response?.status === 404) {\r\n                    // 404 durumunda boş liste göster, hata gösterme\r\n                    setShipments([]);\r\n                    setLoading(false);\r\n                    return;\r\n                } else if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {\r\n                    setError('Bağlantı zaman aşımına uğradı. Lütfen internet bağlantınızı kontrol edin.');\r\n                } else if (error.message.includes('Network Error')) {\r\n                    setError('Sunucu bağlantı hatası. Lütfen internet bağlantınızı kontrol edin.');\r\n                } else {\r\n                    setError('Sevkiyat verileri yüklenirken hata oluştu: ' + (error.response?.data?.message || error.message));\r\n                }\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        fetchData();\r\n    }, [masterData, navigate]);\r\n\r\n    // Tarih formatla\r\n    const formatDate = (dateString, includeTime = false) => {\r\n        if (!dateString) return '';\r\n\r\n        const date = new Date(dateString);\r\n        const day = date.getDate().toString().padStart(2, '0');\r\n        const month = (date.getMonth() + 1).toString().padStart(2, '0');\r\n        const year = date.getFullYear();\r\n\r\n        if (includeTime) {\r\n            const hours = date.getHours().toString().padStart(2, '0');\r\n            const minutes = date.getMinutes().toString().padStart(2, '0');\r\n            return `${day}.${month}.${year} ${hours}:${minutes}`;\r\n        }\r\n\r\n        return `${day}.${month}.${year}`;\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <Header />\r\n            <div className=\"container-fluid\">\r\n                <div className=\"row\">\r\n                    <Sidebar />\r\n\r\n                    {/* main sütun */}\r\n                    <main className=\"col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white\">\r\n                        <div className=\"pt-3 pb-1 mt-3 mb-3 border-bottom\">\r\n                            <h1 className=\"h4 text-dark\">Aktif Sevkiyatlar</h1>\r\n                        </div>\r\n\r\n                        {loading ? (\r\n                            <LoadingSpinner \r\n                                size=\"lg\" \r\n                                variant=\"primary\" \r\n                                message=\"Sevkiyat verileri yükleniyor...\" \r\n                                centered={true}\r\n                            />\r\n                        ) : error ? (\r\n                            <ErrorMessage \r\n                                message={error} \r\n                                variant=\"danger\"\r\n                                title=\"Veri Yükleme Hatası\"\r\n                                dismissible={true}\r\n                                onDismiss={() => setError('')}\r\n                            >\r\n                                <button className=\"btn btn-primary btn-sm mt-2\" onClick={() => window.location.reload()}>\r\n                                    Yeniden Dene\r\n                                </button>\r\n                            </ErrorMessage>\r\n                        ) : (\r\n                            <div className=\"row\">\r\n                                <div className=\"col-12\">\r\n\r\n                                    {/* aktif cihaz tablo */}\r\n                                    <div className=\"card border-tertiary-subtle px-3 py-3 mb-5\">\r\n                                        {shipments.length === 0 ? (\r\n                                            <div className=\"text-center py-5\">\r\n                                                <div className=\"mb-4\">\r\n                                                    <i className=\"fas fa-shipping-fast fa-3x text-muted mb-3\"></i>\r\n                                                    <h5 className=\"text-muted\">Aktif Sevkiyat Bulunamadı</h5>\r\n                                                    <p className=\"text-muted\">\r\n                                                        Henüz aktif sevkiyatınız bulunmamaktadır. \r\n                                                        Yeni bir sevkiyat oluşturmak için aşağıdaki butonu kullanabilirsiniz.\r\n                                                    </p>\r\n                                                </div>\r\n                                                <Link to=\"/add\" className=\"btn btn-primary\">\r\n                                                    <i className=\"fas fa-plus me-2\"></i>\r\n                                                    Yeni Sevkiyat Oluştur\r\n                                                </Link>\r\n                                            </div>\r\n                                        ) : (\r\n                                            <div className=\"table-responsive\" style={{ overflowX: 'unset' }}>\r\n                                                <table className=\"table table-dark-subtle table-bordered table-hover table-striped table-sm\" style={{ fontSize: '0.85rem' }}>\r\n                                                    <thead className=\"table-light\">\r\n                                                        <tr>\r\n                                                            <th>Sevkiyat ID</th>\r\n                                                            <th>Sevkiyat Adı</th>\r\n                                                            <th>Plaka No</th>\r\n                                                            <th>Nereden</th>\r\n                                                            <th>Nereye</th>\r\n                                                            <th>Nakliyeci</th>\r\n                                                            <th>Ürün</th>\r\n                                                            <th>Sipariş No</th>\r\n                                                            <th>Palet</th>\r\n                                                            <th>Net</th>\r\n                                                            <th>Brüt</th>\r\n                                                            <th>Eklenme</th>\r\n                                                        </tr>\r\n                                                    </thead>\r\n                                                    <tbody>\r\n                                                        {shipments.map((shipment, index) => (\r\n                                                            <tr key={index}>\r\n                                                                <td>\r\n                                                                    <Link to={`/view/${shipment.id}`}>\r\n                                                                        {shipment.sevkiyatID}\r\n                                                                        {shipment.mgzKodu &&\r\n                                                                            <span className=\"ms-1 badge bg-primary text-white\">\r\n                                                                                {shipment.mgzKodu}\r\n                                                                            </span>\r\n                                                                        }\r\n                                                                    </Link>\r\n                                                                </td>\r\n                                                                <td>{shipment.name}</td>\r\n                                                                <td>{shipment.plate}</td>\r\n                                                                <td>{shipment.from}</td>\r\n                                                                <td>{shipment.to}</td>\r\n                                                                <td>{shipment.carrier}</td>\r\n                                                                <td>{shipment.product}</td>\r\n                                                                <td>{shipment.orderNo}</td>\r\n                                                                <td>{shipment.pallet}</td>\r\n                                                                <td>{shipment.net}</td>\r\n                                                                <td>{shipment.gross}</td>\r\n                                                                <td>{shipment.added}</td>\r\n                                                            </tr>\r\n                                                        ))}\r\n                                                    </tbody>\r\n                                                </table>\r\n                                            </div>\r\n                                        )}\r\n                                    </div>\r\n                                    {/* /aktif cihaz tablo */}\r\n                                </div>\r\n                            </div>\r\n                        )}\r\n                    </main>\r\n                    {/* /main sütun */}\r\n\r\n                    <Footer />\r\n                </div>\r\n            </div>\r\n        </>\r\n    );\r\n};\r\n\r\nexport default Home; "], "names": ["Sidebar", "location", "useLocation", "isAdmin", "getUserRole", "_user$user", "_user$user2", "user", "JSON", "parse", "localStorage", "getItem", "role", "gorev", "_unused", "_jsx", "className", "children", "_jsxs", "tabIndex", "id", "type", "Link", "concat", "pathname", "to", "FontAwesomeIcon", "icon", "faDolly", "faFolderPlus", "faAnchor", "faFlag<PERSON><PERSON><PERSON><PERSON>", "faDesktop", "faBell", "faUser", "faStreetView", "faCreditCard", "faFileLines", "Footer", "Header", "navigate", "useNavigate", "userName", "setUserName", "useState", "loading", "setLoading", "useEffect", "async", "_storedUser$user", "_storedUser$user2", "_storedUser$user3", "_storedUser$user4", "storedUser", "userId", "musteri_ID", "name", "musteri_adi", "console", "warn", "userData", "kullaniciService", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setItem", "stringify", "apiError", "error", "fetchUserData", "src", "alt", "height", "onClick", "handleLogout", "removeItem", "faRightFromBracket", "faBars", "Home", "shipments", "setShipments", "setError", "masterData", "_storedUser", "_storedUser2", "_storedUser2$user", "log", "demoUser", "email", "username", "sevkiyatData", "sevkiyatService", "getSevkiyatlarByMusteriId", "processedData", "Promise", "all", "map", "_lastSensor", "_item$palet_sayisi", "_item$net_agirlik", "_item$brut_agirlik", "_lastSensor2", "_lastSensor2$sicaklik", "lastSensor", "item", "mgz24_kodu", "response", "cihazBilgiService", "getCihazFromExternalAPI", "success", "data", "sensorData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_response$data$konum", "_response$data$konum2", "sicaklik", "nem", "enlem", "konum", "boy<PERSON>", "zaman", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tempStatus", "temp", "parseFloat", "minTemp", "maxTemp", "sicaklik_araligi", "replace", "split", "t", "trim", "fromName", "cikis_lokasyon", "cikis_lokasyon_id", "to<PERSON>ame", "varis_lokasyon", "varis_lokasyon_id", "carrierName", "<PERSON><PERSON><PERSON><PERSON>", "nakliyeci_id", "productName", "urun", "urun_id", "sevkiyatID", "sevkiyat_ID", "mgzKodu", "sevkiyat_adi", "plate", "plaka_no", "from", "carrier", "product", "orderNo", "pallet", "palet_sayisi", "toString", "net", "net_agirlik", "gross", "brut_agirlik", "added", "formatDate", "olusturma_zamani", "lastData", "temperature", "_error$response", "_error$response2", "_error$response3", "status", "code", "message", "includes", "_error$response4", "_error$response4$data", "fetchData", "dateString", "includeTime", "arguments", "length", "undefined", "date", "Date", "day", "getDate", "padStart", "month", "getMonth", "year", "getFullYear", "hours", "getHours", "minutes", "getMinutes", "_Fragment", "LoadingSpinner", "size", "variant", "centered", "ErrorMessage", "title", "dismissible", "on<PERSON><PERSON><PERSON>", "window", "reload", "style", "overflowX", "fontSize", "shipment", "index"], "sourceRoot": ""}