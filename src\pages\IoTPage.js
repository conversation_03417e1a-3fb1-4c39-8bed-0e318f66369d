import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import Header from '../components/Header';
import Sidebar from '../components/Sidebar';
import Footer from '../components/Footer';
import IoTDashboard from '../components/IoTDashboard';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faRefresh, faDownload, faCog } from '@fortawesome/free-solid-svg-icons';

/**
 * IoT Dashboard sayfası - Detaylı sensör verileri ve analitik
 * Context7 referansı: /recharts/recharts - Complete dashboard implementation
 */
const IoTPage = () => {
  const { sevkiyatId } = useParams();
  const [refreshKey, setRefreshKey] = useState(0);
  const [autoRefresh, setAutoRefresh] = useState(true);

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  const handleExportData = () => {
    // Veri export işlemi için placeholder
    alert('Veri export özelliği yakında eklenecek');
  };

  const handleSettings = () => {
    // Dashboard ayarları için placeholder
    alert('Dashboard ayarları yakında eklenecek');
  };

  return (
    <>
      <Header />
      <div className="container-fluid">
        <div className="row">
          <Sidebar />

          {/* Ana içerik */}
          <main className="col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white">
            <div className="pt-3 pb-1 mt-3 mb-3 border-bottom">
              <div className="d-flex justify-content-between align-items-center">
                <h1 className="h4 text-dark">
                  <FontAwesomeIcon icon="microchip" className="me-2" />
                  IoT Sensör Dashboard
                  {sevkiyatId && (
                    <small className="text-muted ms-2">
                      Sevkiyat #{sevkiyatId}
                    </small>
                  )}
                </h1>

                {/* Dashboard kontrolleri */}
                <div className="btn-group">
                  <button
                    className="btn btn-outline-primary btn-sm"
                    onClick={handleRefresh}
                    title="Verileri Yenile"
                  >
                    <FontAwesomeIcon icon={faRefresh} className="me-1" />
                    Yenile
                  </button>
                  
                  <button
                    className="btn btn-outline-secondary btn-sm"
                    onClick={handleExportData}
                    title="Verileri Dışa Aktar"
                  >
                    <FontAwesomeIcon icon={faDownload} className="me-1" />
                    Dışa Aktar
                  </button>
                  
                  <button
                    className="btn btn-outline-info btn-sm"
                    onClick={handleSettings}
                    title="Dashboard Ayarları"
                  >
                    <FontAwesomeIcon icon={faCog} className="me-1" />
                    Ayarlar
                  </button>
                </div>
              </div>

              {/* Otomatik yenileme toggle */}
              <div className="mt-2">
                <div className="form-check form-switch">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    id="autoRefreshSwitch"
                    checked={autoRefresh}
                    onChange={(e) => setAutoRefresh(e.target.checked)}
                  />
                  <label className="form-check-label" htmlFor="autoRefreshSwitch">
                    <small className="text-muted">
                      Otomatik yenileme (30 saniye)
                    </small>
                  </label>
                </div>
              </div>
            </div>

            {/* IoT Dashboard bileşeni */}
            <div className="row">
              <div className="col-12">
                <IoTDashboard 
                  key={refreshKey}
                  sevkiyatId={sevkiyatId}
                  autoRefresh={autoRefresh}
                  onRefresh={handleRefresh}
                />
              </div>
            </div>

            {/* Alt bilgi */}
            <div className="row mt-4">
              <div className="col-12">
                <div className="card bg-light">
                  <div className="card-body">
                    <h6 className="card-title">Dashboard Bilgileri</h6>
                    <div className="row">
                      <div className="col-md-3">
                        <strong>Veri Kaynağı:</strong><br />
                        <small className="text-muted">MGZ24 IoT Sensörleri</small>
                      </div>
                      <div className="col-md-3">
                        <strong>Güncelleme Sıklığı:</strong><br />
                        <small className="text-muted">30 saniye</small>
                      </div>
                      <div className="col-md-3">
                        <strong>Veri Saklama:</strong><br />
                        <small className="text-muted">Son 24 saat</small>
                      </div>
                      <div className="col-md-3">
                        <strong>Grafik Türleri:</strong><br />
                        <small className="text-muted">Çizgi, Bar, Gauge</small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </main>

          <Footer />
        </div>
      </div>
    </>
  );
};

export default IoTPage;