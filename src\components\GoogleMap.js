import React, { useState, useEffect, useRef, useCallback } from 'react';
import { GoogleMap, useJsApi<PERSON>oader, <PERSON><PERSON>, InfoWindow } from '@react-google-maps/api';

// Google Maps API için stil ve ayarlar
const mapContainerStyle = {
    width: '100%',
    height: '100%'
};

// Google Maps API için seçenekler
const options = {
    mapTypeId: 'roadmap',
    disableDefaultUI: false,
    zoomControl: true,
    mapTypeControl: true,
    scaleControl: true,
    streetViewControl: false,
    rotateControl: false,
    clickableIcons: true,
    fullscreenControl: true,
    gestureHandling: 'greedy',
    minZoom: 2,
    maxZoom: 20,
    restriction: {
        latLngBounds: {
            north: 85,
            south: -85,
            west: -180,
            east: 180
        }
    }
};

// Google Maps API için kütüphaneleri statik bir değişken olarak tanımla
const libraries = ['marker'];

// Ana harita bileşeni
const GoogleMapComponent = ({ shipments = [] }) => {
    const [selectedMarker, setSelectedMarker] = useState(null);
    const [mapCenter, setMapCenter] = useState({ lat: 39.9334, lng: 32.8597 }); // Ankara merkez
    const [zoom, setZoom] = useState(6);
    const mapRef = useRef(null);

    // Google Maps API'yi yükle
    const { isLoaded, loadError } = useJsApiLoader({
        id: 'google-map-script',
        googleMapsApiKey: 'AIzaSyA2cfEmiPMyvcGfRiCyB9khWrccCgqpxKs',
        libraries: libraries
    });

    // Harita referansını kaydet
    const onMapLoad = useCallback((map) => {
        mapRef.current = map;

        // Smooth zoom için özel ayarlar
        if (map) {
            // Zoom değişikliğini izle
            map.addListener('zoom_changed', () => {
                setZoom(map.getZoom());
            });
        }
    }, []);

    // Sevkiyatların konumlarına göre harita merkezini hesapla
    useEffect(() => {
        if (shipments.length > 0) {
            const validLocations = shipments
                .map(shipment => {
                    if (shipment.location && shipment.location !== 'Konum bilgisi yok') {
                        const [lat, lng] = shipment.location.split(', ').map(coord => parseFloat(coord.trim()));
                        if (!isNaN(lat) && !isNaN(lng)) {
                            return { lat, lng };
                        }
                    }
                    return null;
                })
                .filter(location => location !== null);

            if (validLocations.length > 0) {
                // Tüm konumların ortalamasını al
                const avgLat = validLocations.reduce((sum, loc) => sum + loc.lat, 0) / validLocations.length;
                const avgLng = validLocations.reduce((sum, loc) => sum + loc.lng, 0) / validLocations.length;

                setMapCenter({ lat: avgLat, lng: avgLng });
            }
        }
    }, [shipments]);

    // Marker'a tıklandığında InfoWindow'u aç/kapat
    const handleMarkerClick = useCallback((marker) => {
        setSelectedMarker(selectedMarker?.id === marker.id ? null : marker);
    }, [selectedMarker]);

    // Haritaya tıklandığında InfoWindow'u kapat
    const handleMapClick = useCallback(() => {
        setSelectedMarker(null);
    }, []);

    // Özel zoom butonları
    const handleZoomIn = () => {
        if (mapRef.current) {
            const currentZoom = mapRef.current.getZoom();
            mapRef.current.setZoom(Math.min(currentZoom + 0.5, 20));
        }
    };

    const handleZoomOut = () => {
        if (mapRef.current) {
            const currentZoom = mapRef.current.getZoom();
            mapRef.current.setZoom(Math.max(currentZoom - 0.5, 2));
        }
    };

    // Sıcaklık durumuna göre marker rengi belirle
    const getMarkerColor = (tempStatus) => {
        switch (tempStatus) {
            case 'warning':
                return 'red'; // Kırmızı
            case 'normal':
                return 'green'; // Yeşil
            default:
                return 'blue'; // Mavi
        }
    };

    // Sevkiyat konumlarını marker'lara dönüştür
    const markers = shipments
        .map(shipment => {
            if (shipment.location && shipment.location !== 'Konum bilgisi yok') {
                const [lat, lng] = shipment.location.split(', ').map(coord => parseFloat(coord.trim()));
                if (!isNaN(lat) && !isNaN(lng)) {
                    return {
                        ...shipment,
                        position: { lat, lng }
                    };
                }
            }
            return null;
        })
        .filter(marker => marker !== null);

    // API yüklenme hatası
    if (loadError) {
        return <div className="alert alert-danger">Google Maps yüklenemedi. Lütfen daha sonra tekrar deneyin.</div>;
    }

    // API yüklenene kadar bekle
    if (!isLoaded) {
        return <div className="d-flex justify-content-center my-5">
            <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Harita yükleniyor...</span>
            </div>
            <span className="ms-2">Harita yükleniyor...</span>
        </div>;
    }

    return (
        <div style={{ height: '50vh', width: '100%', minHeight: '400px', position: 'relative' }}>
            {/* Özel zoom butonları */}
            <div style={{ position: 'absolute', top: 16, right: 16, zIndex: 2, display: 'flex', flexDirection: 'column', gap: 8 }}>
                <button onClick={handleZoomIn} style={{ width: 36, height: 36, fontSize: 24, borderRadius: 8, border: '1px solid #ccc', background: '#fff', cursor: 'pointer' }}>+</button>
                <button onClick={handleZoomOut} style={{ width: 36, height: 36, fontSize: 24, borderRadius: 8, border: '1px solid #ccc', background: '#fff', cursor: 'pointer' }}>-</button>
            </div>

            <GoogleMap
                mapContainerStyle={mapContainerStyle}
                center={mapCenter}
                zoom={zoom}
                options={options}
                onClick={handleMapClick}
                onLoad={onMapLoad}
            >
                {markers.map((marker) => (
                    <Marker
                        key={marker.id}
                        position={marker.position}
                        onClick={() => handleMarkerClick(marker)}
                        title={`${marker.name} - ${marker.sevkiyatID}`}
                        animation={window.google?.maps?.Animation?.DROP}
                        icon={{
                            path: window.google?.maps?.SymbolPath?.CIRCLE,
                            fillColor: getMarkerColor(marker.tempStatus),
                            fillOpacity: 0.8,
                            strokeWeight: 2,
                            strokeColor: '#ffffff',
                            scale: 8
                        }}
                    >
                        {selectedMarker?.id === marker.id && (
                            <InfoWindow
                                position={marker.position}
                                onCloseClick={() => setSelectedMarker(null)}
                            >
                                <div style={{ maxWidth: '300px', padding: '10px' }}>
                                    <h6 className="mb-2 text-primary">
                                        <strong>{marker.name}</strong>
                                    </h6>
                                    <div className="row g-1">
                                        <div className="col-12">
                                            <small><strong>Sevkiyat ID:</strong> {marker.sevkiyatID}</small>
                                        </div>
                                        <div className="col-12">
                                            <small><strong>Plaka:</strong> {marker.plate}</small>
                                        </div>
                                        <div className="col-12">
                                            <small><strong>Güzergah:</strong> {marker.from} → {marker.to}</small>
                                        </div>
                                        <div className="col-12">
                                            <small><strong>Nakliyeci:</strong> {marker.carrier}</small>
                                        </div>
                                        <div className="col-12">
                                            <small><strong>Ürün:</strong> {marker.product}</small>
                                        </div>
                                        <div className="col-12">
                                            <small><strong>Sıcaklık:</strong>
                                                <span className={`ms-1 ${marker.tempStatus === 'warning' ? 'text-danger fw-bold' : 'text-success'}`}>
                                                    {marker.temperature}°C
                                                </span>
                                            </small>
                                        </div>
                                        <div className="col-12">
                                            <small><strong>Son Güncelleme:</strong> {marker.lastData}</small>
                                        </div>
                                        <div className="col-12 mt-2">
                                            <a
                                                href={`/view/${marker.id}`}
                                                className="btn btn-primary btn-sm"
                                                style={{ fontSize: '0.75rem' }}
                                            >
                                                Detayları Görüntüle
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </InfoWindow>
                        )}
                    </Marker>
                ))}
            </GoogleMap>

            {/* Harita altında bilgi */}
            <div className="mt-2">
                <div className="d-flex justify-content-between align-items-center mb-2">
                    <div className="d-flex gap-3">
                        <div className="d-flex align-items-center">
                            <div
                                style={{
                                    width: '12px',
                                    height: '12px',
                                    borderRadius: '50%',
                                    backgroundColor: 'green',
                                    border: '2px solid #ffffff',
                                    marginRight: '5px'
                                }}
                            ></div>
                            <small>Normal Sıcaklık</small>
                        </div>
                        <div className="d-flex align-items-center">
                            <div
                                style={{
                                    width: '12px',
                                    height: '12px',
                                    borderRadius: '50%',
                                    backgroundColor: 'red',
                                    border: '2px solid #ffffff',
                                    marginRight: '5px'
                                }}
                            ></div>
                            <small>Sıcaklık Uyarısı</small>
                        </div>
                        <div className="d-flex align-items-center">
                            <div
                                style={{
                                    width: '12px',
                                    height: '12px',
                                    borderRadius: '50%',
                                    backgroundColor: 'blue',
                                    border: '2px solid #ffffff',
                                    marginRight: '5px'
                                }}
                            ></div>
                            <small>Veri Yok</small>
                        </div>
                    </div>
                    <small className="text-muted">
                        Marker'a tıklayarak detayları görüntüleyebilirsiniz
                    </small>
                </div>
                <div className="text-center">
                    <small className="text-muted">
                        <i className="fas fa-info-circle me-1"></i>
                        Haritayı büyütmek/küçültmek için: Mouse tekerleği, + - butonları veya çift tıklama kullanabilirsiniz
                    </small>
                </div>
            </div>
        </div>
    );
};

export default GoogleMapComponent;
