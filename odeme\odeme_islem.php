<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Veritabanı bağlantısını dahil et
require_once $_SERVER['DOCUMENT_ROOT'] . '/inc/db.php';
global $mysqli;

// API bilgileri
$client_code = "58437";
$guid = "D8AE7D3C-31B4-453A-8E53-49F0A8B68927";
$client_user = '**********';
$client_pass = '0D004378D1733A78';
$hataURL = 'https://dev.param.com.tr/en';
$basariliURL = 'https://ffl21.fun/data/odeme/basarili.php';
$wsdl_url = 'https://posws.param.com.tr/turkpos.ws/service_turkpos_prod.asmx?wsdl';

try {
    // SOAP istemcisi oluşturma
    $client = new SoapClient($wsdl_url, ['trace' => 1, 'exceptions' => 1]);
} catch (SoapFault $fault) {
    die("SOAP istemcisi oluşturulamadı: " . $fault->getMessage());
}

// 3D doğrulama öncesi işlemler
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($_POST['UCD_MD'])) {
    // Formdan gelen verileri al ve sanitize et
    $kk_sahibi = filter_input(INPUT_POST, 'kk_sahibi', FILTER_SANITIZE_SPECIAL_CHARS);
    $kk_no = filter_input(INPUT_POST, 'kk_no', FILTER_SANITIZE_SPECIAL_CHARS);
    $kk_sk_ay = filter_input(INPUT_POST, 'kk_sk_ay', FILTER_SANITIZE_SPECIAL_CHARS);
    $kk_sk_yil = filter_input(INPUT_POST, 'kk_sk_yil', FILTER_SANITIZE_SPECIAL_CHARS);
    $kk_cvc = filter_input(INPUT_POST, 'kk_cvc', FILTER_SANITIZE_SPECIAL_CHARS);
    $kk_sahibi_gsm = '5551236541';  // Örnek GSM numarası
    $siparis_id = uniqid('ORDER_'); //Benzersiz Sipariş ID
    $siparis_aciklama = "GZC24Test"; // Sipariş açıklaması
    $taksit = 1;  // Tek çekim için 1

    // Tutarları al ve formatla
    $islem_tutar_raw = str_replace(',', '.', filter_input(INPUT_POST, 'islem_tutar', FILTER_SANITIZE_SPECIAL_CHARS));
    $toplam_tutar_raw = str_replace(',', '.', filter_input(INPUT_POST, 'toplam_tutar', FILTER_SANITIZE_SPECIAL_CHARS));
    $islem_tutar = number_format(floatval($islem_tutar_raw), 2, ',', '');
    $toplam_tutar = number_format(floatval($toplam_tutar_raw), 2, ',', '');

    // Hash oluştur
    $islemGuvenlikStr = $client_code . $guid . $taksit . $islem_tutar . $toplam_tutar . $siparis_id;
    $hash = base64_encode(sha1($islemGuvenlikStr, true));

    if (empty($hash)) {
        die("Hash oluşturulamadı.");
    }

    try {
        // UCD parametreleri
        $ucdParams = array(
            'G' => array(
                'CLIENT_CODE' => $client_code,
                'CLIENT_USERNAME' => $client_user,
                'CLIENT_PASSWORD' => $client_pass,
            ),
            'GUID' => $guid,
            'KK_Sahibi' => $kk_sahibi,
            'KK_No' => $kk_no,
            'KK_SK_Ay' => $kk_sk_ay,
            'KK_SK_Yil' => $kk_sk_yil,
            'KK_CVC' => $kk_cvc,
            'KK_Sahibi_GSM' => $kk_sahibi_gsm,
            'Hata_URL' => $hataURL,
            'Basarili_URL' => $basariliURL,
            'Siparis_ID' => $siparis_id,
            'Siparis_Aciklama' => $siparis_aciklama,
            'Taksit' => $taksit,
            'Islem_Tutar' => $islem_tutar,
            'Toplam_Tutar' => $toplam_tutar,
            'Islem_Hash' => $hash,
            'Islem_Guvenlik_Tip' => '3D',
            'Islem_ID' => $siparis_id,
            'IPAdr' => '127.0.0.1',
            'Ref_URL' => 'https://dev.param.com.tr/tr',
        );

        // UCD işlemi başlat
        $ucdResponse = $client->TP_WMD_UCD($ucdParams);

        // Yanıt kontrolü
        if (!isset($ucdResponse->TP_WMD_UCDResult)) {
            throw new Exception("UCD yanıtı geçersiz.");
        }

        $ucdResult = $ucdResponse->TP_WMD_UCDResult;

        // Sonuç kontrolü
        if ($ucdResult->Sonuc <= 0) {
            throw new Exception("UCD işlemi başarısız: " . $ucdResult->Sonuc_Str);
        }

        // 3D işlemi için HTML içeriğini kontrol et
        if (empty($ucdResult->UCD_HTML)) {
            throw new Exception("UCD_HTML bulunamadı.");
        }

        // Kullanıcıyı 3D doğrulama sayfasına yönlendir
        echo $ucdResult->UCD_HTML;
        exit();

    } catch (Exception $e) {
        echo "Hata oluştu: " . $e->getMessage();
        error_log("UCD Hatası: " . $e->getMessage());
    }
}

// 3D doğrulama sonrası geri dönüş
if (isset($_POST['md']) || isset($_POST['orderId'])) {
    // Parametrelerin kontrolü
    $ucd_md = $_POST['UCD_MD'] ?? '';
    $response = $_POST['Response'] ?? '';
    $mdStatus = $_POST['mdStatus'] ?? '';
    $orderId = $_POST['orderId'] ?? '';
    $islemGUID = $_POST['islemGUID'] ?? '';
    $islemHash = $_POST['islemHash'] ?? '';

    // Log kaydı
    error_log("Yönlendirme öncesi parametreler: " . print_r($_POST, true));

    // JavaScript ile yönlendirme
    echo "<script>
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '$basariliURL';
        
        var params = " . json_encode($_POST) . ";
        for (var key in params) {
            if (params.hasOwnProperty(key)) {
                var input = document.createElement('input');
                input.type = 'hidden';
                input.name = key;
                input.value = params[key];
                form.appendChild(input);
            }
        }
        
        document.body.appendChild(form);
        form.submit();
    </script>";
    exit();
}
?>
