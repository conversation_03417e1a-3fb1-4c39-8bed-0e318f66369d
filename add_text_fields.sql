-- Sevkiyatlar tablosuna metin tabanlı alan ekle<PERSON>eri
ALTER TABLE sevkiyatlar
ADD COLUMN cikis_lokasyon VARCHAR(100) NULL DEFAULT NULL COMMENT '<PERSON><PERSON>k<PERSON><PERSON> lokasyonu metni',
ADD COLUMN varis_lokasyon VARCHAR(100) NULL DEFAULT NULL COMMENT 'Varış lokasyonu metni',
ADD COLUMN nakliyeci VARCHAR(100) NULL DEFAULT NULL COMMENT 'Nak<PERSON><PERSON>ci metni',
ADD COLUMN urun VARCHAR(100) NULL DEFAULT NULL COMMENT '<PERSON><PERSON><PERSON><PERSON> metni';

-- Mevcut kayıtlar için metin alanlarını varsayılan değerlerle doldur
UPDATE sevkiyatlar s
SET 
    s.cikis_lokasyon = 'Çıkış Lokasyonu',
    s.varis_lokasyon = 'Varış Lokasyonu',
    s.nakliyeci = 'Nak<PERSON>yeci',
    s.urun = 'Ürün'
WHERE 
    s.cikis_lokasyon IS NULL 
    OR s.varis_lokasyon IS NULL 
    OR s.nakliyeci IS NULL 
    OR s.urun IS NULL; 