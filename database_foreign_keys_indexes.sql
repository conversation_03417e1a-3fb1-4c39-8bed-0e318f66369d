-- ===================================================================
-- MGZ24 Veritabanı Foreign Key Constraints ve Performance Index'leri
-- ===================================================================

-- Foreign Key'leri eklemeden önce, mevcut invalid data olup olmadığını kontrol et
-- Foreign key errors'ını önlemek için

USE mgz24db;

-- 1. FOREIGN KEY CONSTRAINTS EKLEME
-- ===================================================================

-- sevkiyatlar tablosundaki foreign key'ler
-- Not: sevkiyatlar tablosunda cikis_lokasyon_id, varis_lokasyon_id, nakliyeci_id, urun_id alanları var

-- Lokasyonlar ile sevkiyatlar arasındaki ilişki
ALTER TABLE sevkiyatlar 
ADD CONSTRAINT fk_sevkiyatlar_cikis_lokasyon 
FOREIGN KEY (cikis_lokasyon_id) REFERENCES lokasyonlar(id) 
ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE sevkiyatlar 
ADD CONSTRAINT fk_sevkiyatlar_varis_lokasyon 
FOREIGN KEY (varis_lokasyon_id) REFERENCES lokasyonlar(id) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- Taşıyıcılar ile sevkiyatlar arasındaki ilişki
ALTER TABLE sevkiyatlar 
ADD CONSTRAINT fk_sevkiyatlar_nakliyeci 
FOREIGN KEY (nakliyeci_id) REFERENCES tasiyicilar(id) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- Ürünler ile sevkiyatlar arasındaki ilişki
ALTER TABLE sevkiyatlar 
ADD CONSTRAINT fk_sevkiyatlar_urun 
FOREIGN KEY (urun_id) REFERENCES urunler(id) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- cihazBilgi tablosundaki foreign key'ler
-- Sevkiyatlar ile cihaz bilgileri arasındaki ilişki
ALTER TABLE cihazBilgi 
ADD CONSTRAINT fk_cihazBilgi_sevkiyat 
FOREIGN KEY (sevkiyat_id) REFERENCES sevkiyatlar(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- sevkiyatGecmis tablosundaki foreign key'ler (eğer tabloda sevkiyat_ID ve musteri_ID varsa)
-- sevkiyatGecmis.sevkiyat_ID -> sevkiyatlar.id
ALTER TABLE sevkiyatGecmis 
ADD CONSTRAINT fk_sevkiyatGecmis_sevkiyat 
FOREIGN KEY (sevkiyat_ID) REFERENCES sevkiyatlar(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- sevkiyatGecmis.musteri_ID -> kullanicilar.musteri_ID
ALTER TABLE sevkiyatGecmis 
ADD CONSTRAINT fk_sevkiyatGecmis_musteri 
FOREIGN KEY (musteri_ID) REFERENCES kullanicilar(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- 2. PERFORMANCE INDEX'LERİ EKLEME
-- ===================================================================

-- cihazBilgi tablosu için performance index'leri
-- Sevkiyat ID ve zaman'a göre sık sorgu yapıldığı için composite index
CREATE INDEX idx_cihazBilgi_sevkiyat_zaman 
ON cihazBilgi(sevkiyat_id, zaman DESC);

-- Cihaz kodu'na göre hızlı arama için index (zaten unique var ama performance için)
CREATE INDEX idx_cihazBilgi_cihaz_kodu 
ON cihazBilgi(cihaz_kodu);

-- Okuma tipi'ne göre filtreleme için index
CREATE INDEX idx_cihazBilgi_okuma_tipi 
ON cihazBilgi(okuma_tipi);

-- Durum'a göre filtreleme için index (aktif/pasif cihazlar)
CREATE INDEX idx_cihazBilgi_durum 
ON cihazBilgi(durum);

-- Sıcaklık verisi sorguları için index
CREATE INDEX idx_cihazBilgi_sicaklik 
ON cihazBilgi(sicaklik);

-- sevkiyatlar tablosu için performance index'leri
-- Müşteri ID ve oluşturma zamanı'na göre composite index (en çok kullanılan sorgu)
CREATE INDEX idx_sevkiyatlar_musteri_olusturma 
ON sevkiyatlar(musteri_ID, olusturma_zamani DESC);

-- Durum'a göre filtreleme için index
CREATE INDEX idx_sevkiyatlar_durum 
ON sevkiyatlar(durum);

-- Plaka no'ya göre arama için index
CREATE INDEX idx_sevkiyatlar_plaka 
ON sevkiyatlar(plaka_no);

-- MGZ24 kodu'na göre arama için index
CREATE INDEX idx_sevkiyatlar_mgz24_kodu 
ON sevkiyatlar(mgz24_kodu);

-- kullanicilar tablosu için performance index'leri
-- Email'e göre login sorguları için index (zaten unique var ama belirtmek için)
CREATE INDEX idx_kullanicilar_email 
ON kullanicilar(email);

-- Role/görev'e göre filtreleme için index
CREATE INDEX idx_kullanicilar_gorev 
ON kullanicilar(gorev);

-- sevkiyatGecmis tablosu için performance index'leri
-- Müşteri ID'ye göre sorgular için
CREATE INDEX idx_sevkiyatGecmis_musteri 
ON sevkiyatGecmis(musteri_ID);

-- Cihaz kodu'na göre sorgular için
CREATE INDEX idx_sevkiyatGecmis_cihaz_kodu 
ON sevkiyatGecmis(cihaz_kodu);

-- Başlangıç zamanı'na göre sıralama için
CREATE INDEX idx_sevkiyatGecmis_baslangic_zamani 
ON sevkiyatGecmis(baslangic_zamani DESC);

-- Durum'a göre filtreleme için
CREATE INDEX idx_sevkiyatGecmis_durum 
ON sevkiyatGecmis(durum);

-- lokasyonlar tablosu için performance index'leri
-- Koordinat bazlı arama için (harita sorguları)
CREATE INDEX idx_lokasyonlar_koordinatlar 
ON lokasyonlar(latitude, longitude);

-- Şehir'e göre filtreleme için
CREATE INDEX idx_lokasyonlar_city 
ON lokasyonlar(city);

-- Tür'e göre filtreleme için  
CREATE INDEX idx_lokasyonlar_type 
ON lokasyonlar(type);

-- tasiyicilar tablosu için performance index'leri
-- İsim'e göre arama için
CREATE INDEX idx_tasiyicilar_name 
ON tasiyicilar(name);

-- urunler tablosu için performance index'leri
-- Kategori'ye göre filtreleme için
CREATE INDEX idx_urunler_category 
ON urunler(category);

-- İsim'e göre arama için
CREATE INDEX idx_urunler_name 
ON urunler(name);

-- 3. COMPOSITE INDEX'LER (Gelişmiş Sorgular İçin)
-- ===================================================================

-- Sevkiyat durum raporları için composite index
CREATE INDEX idx_sevkiyatlar_musteri_durum_tarih 
ON sevkiyatlar(musteri_ID, durum, olusturma_zamani DESC);

-- Cihaz sensör verisi analizi için composite index
CREATE INDEX idx_cihazBilgi_sevkiyat_tip_zaman 
ON cihazBilgi(sevkiyat_id, okuma_tipi, zaman DESC);

-- Sıcaklık alarm sorguları için composite index
CREATE INDEX idx_cihazBilgi_sevkiyat_sicaklik_zaman 
ON cihazBilgi(sevkiyat_id, sicaklik, zaman DESC);

-- 4. FULL TEXT INDEX'LER (Arama İçin)
-- ===================================================================

-- Sevkiyat açıklaması ve adı'nda full text arama için
ALTER TABLE sevkiyatlar ADD FULLTEXT(sevkiyat_adi);

-- Lokasyon isimlerinde full text arama için
ALTER TABLE lokasyonlar ADD FULLTEXT(name, address);

-- Müşteri firma adlarında full text arama için
ALTER TABLE kullanicilar ADD FULLTEXT(musteri_adi, firma);

-- 5. INDEX DURUMUNU KONTROL ETME SORGUSU
-- ===================================================================

-- Bu script çalıştırıldıktan sonra index'leri kontrol etmek için:
/*
SHOW INDEX FROM sevkiyatlar;
SHOW INDEX FROM cihazBilgi;
SHOW INDEX FROM kullanicilar;
SHOW INDEX FROM sevkiyatGecmis;
SHOW INDEX FROM lokasyonlar;
SHOW INDEX FROM tasiyicilar;
SHOW INDEX FROM urunler;
*/

-- 6. FOREIGN KEY DURUMUNU KONTROL ETME SORGUSU
-- ===================================================================

-- Foreign key constraint'leri kontrol etmek için:
/*
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE REFERENCED_TABLE_SCHEMA = 'mgz24db'
AND REFERENCED_TABLE_NAME IS NOT NULL;
*/

-- Script tamamlandı
SELECT 'Foreign Key Constraints ve Performance Index\'leri başarıyla eklendi!' as Status;