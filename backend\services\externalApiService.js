import axios from 'axios';
import mysql from 'mysql2/promise';
import { Pool } from 'mysql2';

class ExternalApiService {
    constructor() {
        this.externalApiUrl = process.env.EXTERNAL_API_URL || 'https://ffl21.fun:3001';
        this.pool = mysql.createPool({
            host: process.env.DB_HOST || 'localhost',
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || '',
            database: process.env.DB_NAME || 'mgz24db',
            waitForConnections: true,
            connectionLimit: 10,
            queueLimit: 0,
            connectTimeout: 10000
        });
    }

    // External API'den cihaz listesini getir
    async fetchDeviceList(limit = 100, page = 1) {
        try {
            const response = await axios.get(`${this.externalApiUrl}/api/cihazlar`, {
                params: { limit, page },
                timeout: 10000,
                httpsAgent: new (require('https').Agent)({
                    rejectUnauthorized: false
                })
            });

            if (response.data.success) {
                return response.data.data;
            }
            throw new Error('External API error: ' + response.data.message);
        } catch (error) {
            console.error('External API fetch error:', error);
            throw error;
        }
    }

    // External API'den belirli cihazın detayını getir
    async fetchDeviceDetail(cihazID) {
        try {
            const response = await axios.get(`${this.externalApiUrl}/api/cihaz/${cihazID}`, {
                timeout: 10000
            });

            if (response.data.success) {
                return response.data.data;
            }
            throw new Error('External API error: ' + response.data.message);
        } catch (error) {
            console.error(`External API fetch error for device ${cihazID}:`, error);
            throw error;
        }
    }

    // External API'den cihaz durumunu getir
    async fetchDeviceStatus(cihazID) {
        try {
            const response = await axios.get(`${this.externalApiUrl}/api/cihaz/${cihazID}/status`, {
                timeout: 10000,
                httpsAgent: new (require('https').Agent)({
                    rejectUnauthorized: false
                })
            });

            if (response.data.success) {
                return response.data.data;
            }
            throw new Error('External API error: ' + response.data.message);
        } catch (error) {
            console.error(`External API status fetch error for device ${cihazID}:`, error);
            throw error;
        }
    }

    // External API'den cihaz geçmişini getir
    async fetchDeviceHistory(cihazID, startDate, endDate, limit = 100) {
        try {
            const response = await axios.get(`${this.externalApiUrl}/api/cihaz/${cihazID}/history`, {
                params: {
                    baslangicTarihi: startDate,
                    bitisTarihi: endDate,
                    limit
                },
                timeout: 30000
            });

            if (response.data.success) {
                return response.data.data;
            }
            throw new Error('External API error: ' + response.data.message);
        } catch (error) {
            console.error(`External API history fetch error for device ${cihazID}:`, error);
            throw error;
        }
    }

    // External API'den SIM bilgilerini getir
    async fetchSimInfo(cihazID) {
        try {
            const response = await axios.get(`${this.externalApiUrl}/api/sim/${cihazID}`, {
                timeout: 10000
            });

            if (response.data.success) {
                return response.data.data;
            }
            throw new Error('External API error: ' + response.data.message);
        } catch (error) {
            console.error(`External API SIM fetch error for device ${cihazID}:`, error);
            throw error;
        }
    }

    // Cihaz bilgilerini lokal database'e senkronize et
    async syncDeviceToLocal(cihazID) {
        const connection = await this.pool.getConnection();
        
        try {
            await connection.beginTransaction();

            // External API'den cihaz detayını getir
            const deviceData = await this.fetchDeviceDetail(cihazID);
            
            // Cihaz bilgilerini lokal database'e kaydet/güncelle
            const cihazQuery = `
                INSERT INTO cihazBilgi (
                    cihaz_kodu, sicaklik, nem, basinc, isik, pil_seviyesi,
                    enlem, boylam, ip_adres, operator, ulke, zaman, son_kontrol,
                    durum, aktif
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), 'aktif', 1)
                ON DUPLICATE KEY UPDATE
                    sicaklik = VALUES(sicaklik),
                    nem = VALUES(nem),
                    basinc = VALUES(basinc),
                    isik = VALUES(isik),
                    pil_seviyesi = VALUES(pil_seviyesi),
                    enlem = VALUES(enlem),
                    boylam = VALUES(boylam),
                    ip_adres = VALUES(ip_adres),
                    operator = VALUES(operator),
                    ulke = VALUES(ulke),
                    zaman = NOW(),
                    son_kontrol = NOW()
            `;

            await connection.execute(cihazQuery, [
                deviceData.cihazID,
                deviceData.sonSensorler.sicaklik,
                deviceData.sonSensorler.nem,
                deviceData.sonSensorler.basinc,
                deviceData.sonSensorler.isik,
                deviceData.sonSensorler.pil,
                deviceData.sonKonum.enlem,
                deviceData.sonKonum.boylam,
                deviceData.baglantiBilgisi.ipAdres,
                deviceData.baglantiBilgisi.operator,
                deviceData.baglantiBilgisi.ulke
            ]);

            // SIM bilgilerini kaydet/güncelle
            if (deviceData.simBilgisi) {
                const simQuery = `
                    INSERT INTO cihazID (
                        CihazID, ICCID, MusteriID, GoldCihaz, KullanimBitti,
                        KalanSure, BaslamaTarihi, BitisTarihi, SistemeEklemeTarihi,
                        Notlar, Notlaradmin
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ON DUPLICATE KEY UPDATE
                        ICCID = VALUES(ICCID),
                        MusteriID = VALUES(MusteriID),
                        GoldCihaz = VALUES(GoldCihaz),
                        KullanimBitti = VALUES(KullanimBitti),
                        KalanSure = VALUES(KalanSure),
                        BaslamaTarihi = VALUES(BaslamaTarihi),
                        BitisTarihi = VALUES(BitisTarihi),
                        Notlar = VALUES(Notlar),
                        Notlaradmin = VALUES(Notlaradmin)
                `;

                await connection.execute(simQuery, [
                    deviceData.cihazID,
                    deviceData.simBilgisi.iccid,
                    deviceData.simBilgisi.musteriID,
                    deviceData.simBilgisi.goldCihaz,
                    deviceData.simBilgisi.kullanimBitti,
                    deviceData.simBilgisi.kalanSure,
                    deviceData.simBilgisi.baslamaTarihi,
                    deviceData.simBilgisi.bitisTarihi,
                    deviceData.simBilgisi.sistemeEklemeTarihi,
                    deviceData.simBilgisi.simNotlar,
                    deviceData.simBilgisi.adminNotlar
                ]);
            }

            await connection.commit();
            console.log(`Device ${cihazID} synchronized successfully`);
            return true;

        } catch (error) {
            await connection.rollback();
            console.error(`Error syncing device ${cihazID}:`, error);
            throw error;
        } finally {
            connection.release();
        }
    }

    // Cihaz geçmişini lokal database'e senkronize et
    async syncDeviceHistory(cihazID, startDate, endDate) {
        const connection = await this.pool.getConnection();
        
        try {
            await connection.beginTransaction();

            // External API'den geçmiş verilerini getir
            const historyData = await this.fetchDeviceHistory(cihazID, startDate, endDate, 1000);
            
            // Her bir geçmiş kaydını lokal database'e kaydet
            for (const record of historyData.gecmisVeriler) {
                const historyQuery = `
                    INSERT IGNORE INTO cihazBilgi (
                        cihaz_kodu, sicaklik, nem, basinc, isik, pil_seviyesi,
                        enlem, boylam, ip_adres, operator, ulke, zaman,
                        durum, aktif, okuma_tipi
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'aktif', 1, 'otomatik')
                `;

                await connection.execute(historyQuery, [
                    record.cihazID,
                    record.sensorler.sicaklik,
                    record.sensorler.nem,
                    record.sensorler.basinc,
                    record.sensorler.isik,
                    record.sensorler.pil,
                    record.konum.enlem,
                    record.konum.boylam,
                    record.baglantiBilgisi.ipAdres,
                    record.baglantiBilgisi.operator,
                    record.baglantiBilgisi.ulke,
                    record.tarih
                ]);
            }

            await connection.commit();
            console.log(`Device ${cihazID} history synchronized: ${historyData.gecmisVeriler.length} records`);
            return historyData.gecmisVeriler.length;

        } catch (error) {
            await connection.rollback();
            console.error(`Error syncing device ${cihazID} history:`, error);
            throw error;
        } finally {
            connection.release();
        }
    }

    // Müşteriye ait tüm cihazları senkronize et
    async syncCustomerDevices(musteriID) {
        try {
            console.log(`Starting sync for customer ${musteriID}`);
            
            // Müşteriye ait cihazları bul
            const [devices] = await this.pool.execute(
                'SELECT CihazID FROM cihazID WHERE MusteriID = ? AND KullanimBitti = 0',
                [musteriID]
            );

            let syncedCount = 0;
            for (const device of devices) {
                try {
                    await this.syncDeviceToLocal(device.CihazID);
                    syncedCount++;
                } catch (error) {
                    console.error(`Failed to sync device ${device.CihazID}:`, error);
                }
            }

            console.log(`Customer ${musteriID} sync completed: ${syncedCount}/${devices.length} devices`);
            return {
                total: devices.length,
                synced: syncedCount,
                failed: devices.length - syncedCount
            };

        } catch (error) {
            console.error(`Error syncing customer ${musteriID} devices:`, error);
            throw error;
        }
    }

    // Cihazı sevkiyata atama
    async assignDeviceToShipment(cihazID, sevkiyatID) {
        try {
            const connection = await this.pool.getConnection();
            
            await connection.beginTransaction();

            // Cihazı sevkiyata ata
            await connection.execute(
                'UPDATE cihazBilgi SET sevkiyat_id = ? WHERE cihaz_kodu = ?',
                [sevkiyatID, cihazID]
            );

            // Sevkiyatı güncelle
            await connection.execute(
                'UPDATE sevkiyatlar SET cihaz_kodu = ? WHERE id = ?',
                [cihazID, sevkiyatID]
            );

            // External API'den güncel veriyi çek ve kaydet
            await this.syncDeviceToLocal(cihazID);

            await connection.commit();
            connection.release();

            console.log(`Device ${cihazID} assigned to shipment ${sevkiyatID}`);
            return true;

        } catch (error) {
            console.error(`Error assigning device ${cihazID} to shipment ${sevkiyatID}:`, error);
            throw error;
        }
    }

    // Cihazın sevkiyat atamasını kaldır
    async unassignDeviceFromShipment(cihazID) {
        try {
            await this.pool.execute(
                'UPDATE cihazBilgi SET sevkiyat_id = NULL WHERE cihaz_kodu = ?',
                [cihazID]
            );

            await this.pool.execute(
                'UPDATE sevkiyatlar SET cihaz_kodu = NULL WHERE cihaz_kodu = ?',
                [cihazID]
            );

            console.log(`Device ${cihazID} unassigned from shipment`);
            return true;

        } catch (error) {
            console.error(`Error unassigning device ${cihazID}:`, error);
            throw error;
        }
    }

    // Aktif cihazların durumunu toplu güncelle
    async bulkUpdateDeviceStatus() {
        try {
            // Aktif sevkiyatlardaki cihazları bul
            const [activeDevices] = await this.pool.execute(`
                SELECT DISTINCT c.cihaz_kodu 
                FROM cihazBilgi c 
                JOIN sevkiyatlar s ON c.sevkiyat_id = s.id 
                WHERE s.durum IN ('hazırlanıyor', 'yolda') 
                AND c.aktif = 1
            `);

            let updatedCount = 0;
            for (const device of activeDevices) {
                try {
                    await this.syncDeviceToLocal(device.cihaz_kodu);
                    updatedCount++;
                } catch (error) {
                    console.error(`Failed to update device ${device.cihaz_kodu}:`, error);
                }
            }

            console.log(`Bulk update completed: ${updatedCount}/${activeDevices.length} devices updated`);
            return {
                total: activeDevices.length,
                updated: updatedCount,
                failed: activeDevices.length - updatedCount
            };

        } catch (error) {
            console.error('Error in bulk update:', error);
            throw error;
        }
    }

    // Bağlantı havuzunu kapat
    async closeConnection() {
        await this.pool.end();
    }
}

export default ExternalApiService;