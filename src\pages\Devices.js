import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faDesktop, faEye, faUserPlus, faSync, faBatteryFull, faBatteryHalf, 
  faBatteryEmpty, faBatteryQuarter, faBatteryThreeQuarters, faCheckCircle,
  faTimesCircle, faCalendarAlt, faSearch, faUser, faTruck
} from '@fortawesome/free-solid-svg-icons';
import Header from '../components/Header';
import Sidebar from '../components/Sidebar';
import Footer from '../components/Footer';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';
import { musteriService, cihazBilgiService } from '../api/dbService';
import deviceService from '../services/deviceService';

const Devices = () => {
  const navigate = useNavigate();
  const [devices, setDevices] = useState([]);
  const [filteredDevices, setFilteredDevices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [refreshing, setRefreshing] = useState(false);

  // Cihaz atama modal state'leri
  const [showAssignModal, setShowAssignModal] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [kullaniciListesi, setKullaniciListesi] = useState([]);
  const [secilenKullanici, setSecilenKullanici] = useState('');
  const [atamaDurum, setAtamaDurum] = useState('');
  const [yeniCihazKodu, setYeniCihazKodu] = useState('');
  const [sorgulanancihaz, setSorgulanancihaz] = useState(null);

  // Sevkiyat detay modal state'leri
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedDeviceDetail, setSelectedDeviceDetail] = useState(null);

  // Check user role and redirect if not admin
  useEffect(() => {
    const checkUserRole = () => {
      try {
        const user = JSON.parse(localStorage.getItem('user'));
        const userRole = user?.user?.role || user?.user?.gorev || 'user';
        
        if (userRole !== 'admin') {
          // Redirect non-admin users to home page
          navigate('/');
          return;
        }
      } catch (error) {
        console.error('Error checking user role:', error);
        navigate('/');
      }
    };

    checkUserRole();
  }, [navigate]);


  // Pil durumu ikonu
  const getBatteryIcon = (level) => {
    if (!level) return faBatteryEmpty;
    if (level <= 10) return faBatteryEmpty;
    if (level <= 25) return faBatteryQuarter;
    if (level <= 50) return faBatteryHalf;
    if (level <= 75) return faBatteryThreeQuarters;
    return faBatteryFull;
  };

  // Pil durumu rengi
  const getBatteryColor = (level) => {
    if (!level) return 'text-muted';
    if (level <= 10) return 'text-danger';
    if (level <= 25) return 'text-warning';
    if (level <= 50) return 'text-info';
    return 'text-success';
  };

  // Durum badge'i
  const getStatusBadge = (aktif) => {
    return aktif ? 
      <span className="badge bg-success">Aktif</span> : 
      <span className="badge bg-secondary">İnaktif</span>;
  };

  // Pil voltajını yüzdeye çevirme fonksiyonu (InactiveDevices ile aynı)
  const calculateBatteryPercentage = (voltage) => {
    if (!voltage || voltage <= 0) return 0;
    
    // 4.2V = %100, 3.4V = %30
    const maxVoltage = 4.2;  // %100
    const minVoltage = 3.4;  // %30
    const minPercentage = 30;
    const maxPercentage = 100;
    
    // Gerilim 3.4V'un altındaysa %0
    if (voltage < minVoltage) return 0;
    
    // Gerilim 4.2V'un üstündeyse %100
    if (voltage > maxVoltage) return 100;
    
    // Linear interpolation
    const voltageRange = maxVoltage - minVoltage;
    const percentageRange = maxPercentage - minPercentage;
    const percentage = minPercentage + ((voltage - minVoltage) / voltageRange) * percentageRange;
    
    return Math.round(percentage);
  };

  // Tüm cihazları getir
  const fetchDevices = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await cihazBilgiService.getAllDevices();
      
      if (response.success) {
        setDevices(response.data);
        setFilteredDevices(response.data);
      } else {
        setError('Cihazlar alınamadı');
      }
    } catch (err) {
      console.error('Cihazlar yüklenirken hata:', err);
      
      // Daha kullanıcı dostu hata mesajları
      if (err.response?.status === 404 || err.message?.includes('404')) {
        setError('Sistemde kayıtlı cihaz bulunmamaktadır.');
      } else if (err.response?.status === 500 || err.message?.includes('500')) {
        setError('Sistemde kayıtlı cihaz bulunmamaktadır.');
      } else if (err.code === 'ERR_NETWORK' || err.message?.includes('Network Error')) {
        setError('Sunucuya bağlanılamıyor. Lütfen internet bağlantınızı kontrol edin.');
      } else {
        setError('Sistemde kayıtlı cihaz bulunmamaktadır.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Kullanıcı listesini getir
  const fetchKullanicilar = async () => {
    try {
      const data = await musteriService.getMusteriler();
      setKullaniciListesi(data);
    } catch (err) {
      console.error('Kullanıcılar alınırken hata:', err);
      setKullaniciListesi([]);
    }
  };

  // Arama filtresi
  const handleSearch = (term) => {
    setSearchTerm(term);
    if (term.trim() === '') {
      setFilteredDevices(devices);
    } else {
      const filtered = devices.filter(device =>
        device.cihazKodu.toLowerCase().includes(term.toLowerCase()) ||
        (device.kullaniciAdi && device.kullaniciAdi.toLowerCase().includes(term.toLowerCase())) ||
        (device.sevkiyatAdi && device.sevkiyatAdi.toLowerCase().includes(term.toLowerCase())) ||
        (device.plakaNo && device.plakaNo.toLowerCase().includes(term.toLowerCase()))
      );
      setFilteredDevices(filtered);
    }
  };

  // Cihaz atama modalını aç
  const openAssignModal = (device) => {
    setSelectedDevice(device);
    setSecilenKullanici(device.kullaniciID || '');
    setAtamaDurum('');
    setYeniCihazKodu('');
    setSorgulanancihaz(null);
    setShowAssignModal(true);
  };

  // Yeni cihaz atama modalını aç
  const openNewAssignModal = () => {
    setSelectedDevice(null);
    setSecilenKullanici('');
    setAtamaDurum('');
    setYeniCihazKodu('');
    setSorgulanancihaz(null);
    setShowAssignModal(true);
  };

  // Önce lokal, sonra harici API'den cihaz sorgula
  const handleCihazSorgulaAtama = async () => {
    if (!yeniCihazKodu.trim()) {
      setAtamaDurum('Lütfen cihaz kodu giriniz');
      return;
    }
    
    try {
      setAtamaDurum('Cihaz bilgisi kontrol ediliyor...');
      
      // Önce lokal database'de kontrol et
      try {
        const localData = await cihazBilgiService.getCihazByCihazKodu(yeniCihazKodu);
        if (localData && localData.cihaz_kodu) {
          // Lokal database'de bulundu
          setSorgulanancihaz({
            cihazBilgi: localData,
            harici_bilgi: {
              sonSensorler: {
                pil: localData.pil_seviyesi,
                sicaklik: localData.sicaklik,
                nem: localData.nem
              }
            }
          });
          setAtamaDurum('Cihaz lokal database\'de bulundu');
          return;
        }
      } catch (localError) {
        console.log('Lokal database\'de bulunamadı, harici API deneniyor...');
      }
      
      // Lokal database'de bulunamadı, harici API'yi dene
      setAtamaDurum('Cihaz harici API\'de aranıyor...');
      
      try {
        const externalData = await cihazBilgiService.getCihazFromExternalAPI(yeniCihazKodu);
        
        if (externalData && externalData.success && externalData.data) {
          // Harici API'de bulundu
          setSorgulanancihaz({
            cihazBilgi: {
              cihaz_kodu: externalData.data.cihazID,
              aktif: externalData.data.aktif || 1
            },
            harici_bilgi: {
              sonSensorler: externalData.data.sonSensorler || {}
            }
          });
          setAtamaDurum('Cihaz harici API\'de bulundu');
        } else {
          setSorgulanancihaz(null);
          setAtamaDurum('Cihaz ne lokal database\'de ne de harici API\'de bulunamadı');
        }
      } catch (externalError) {
        console.error('Harici API hatası:', externalError);
        setSorgulanancihaz(null);
        setAtamaDurum('Cihaz bulunamadı - Lokal ve harici kaynaklarda mevcut değil');
      }
      
    } catch (err) {
      console.error('Cihaz sorgula hatası:', err);
      setSorgulanancihaz(null);
      setAtamaDurum('Cihaz bilgisi alınırken hata oluştu: ' + (err.message || ''));
    }
  };

  // Cihaz atama işlemi
  const handleCihazAta = async () => {
    // Mevcut cihaz atama
    if (selectedDevice && secilenKullanici) {
      try {
        setAtamaDurum('İşlem yapılıyor...');
        
        await cihazBilgiService.updateCihaz(selectedDevice.cihazKodu, { 
          kullanici_ID: secilenKullanici
        });
        
        const secilenKullaniciObj = kullaniciListesi.find(k => k.musteri_ID == secilenKullanici);
        setAtamaDurum(`Cihaz başarıyla ${secilenKullaniciObj?.musteri_adi} kullanıcısına atandı!`);
        
        // Tabloyu güncelle
        setTimeout(() => {
          setShowAssignModal(false);
          fetchDevices();
        }, 1500);
        
      } catch (err) {
        console.error('Cihaz atama hatası:', err);
        
        // Daha kullanıcı dostu hata mesajları
        if (err.response?.status === 404) {
          setAtamaDurum('Atama başarısız: Kullanıcı bulunamadı.');
        } else if (err.response?.status === 500 || err.message?.includes('500')) {
          setAtamaDurum('Atama başarısız: Sunucu hatası oluştu. Lütfen daha sonra tekrar deneyin.');
        } else if (err.code === 'ERR_NETWORK' || err.message?.includes('Network Error')) {
          setAtamaDurum('Atama başarısız: Sunucuya bağlanılamıyor.');
        } else {
          setAtamaDurum('Atama başarısız: Beklenmeyen bir hata oluştu.');
        }
      }
    }
    // Yeni cihaz atama
    else if (sorgulanancihaz && secilenKullanici) {
      try {
        setAtamaDurum('İşlem yapılıyor...');
        
        const pilSeviyesi = sorgulanancihaz.harici_bilgi?.sonSensorler?.pil || null;
        
        await cihazBilgiService.updateCihaz(sorgulanancihaz.cihazBilgi.cihaz_kodu, { 
          kullanici_ID: secilenKullanici,
          pil_seviyesi: pilSeviyesi
        });
        
        const secilenKullaniciObj = kullaniciListesi.find(k => k.musteri_ID == secilenKullanici);
        setAtamaDurum(`Cihaz başarıyla ${secilenKullaniciObj?.musteri_adi} kullanıcısına atandı!`);
        
        // Tabloyu güncelle
        setTimeout(() => {
          setShowAssignModal(false);
          fetchDevices();
        }, 1500);
        
      } catch (err) {
        console.error('Cihaz atama hatası:', err);
        
        // Daha kullanıcı dostu hata mesajları
        if (err.response?.status === 404) {
          setAtamaDurum('Atama başarısız: Kullanıcı bulunamadı.');
        } else if (err.response?.status === 500 || err.message?.includes('500')) {
          setAtamaDurum('Atama başarısız: Sunucu hatası oluştu. Lütfen daha sonra tekrar deneyin.');
        } else if (err.code === 'ERR_NETWORK' || err.message?.includes('Network Error')) {
          setAtamaDurum('Atama başarısız: Sunucuya bağlanılamıyor.');
        } else {
          setAtamaDurum('Atama başarısız: Beklenmeyen bir hata oluştu.');
        }
      }
    }
    else {
      setAtamaDurum('Lütfen kullanıcı seçiniz');
    }
  };

  // Sevkiyat detaylarını göster
  const openDetailModal = (device) => {
    setSelectedDeviceDetail(device);
    setShowDetailModal(true);
  };


  // Yenile
  const refreshDevices = async () => {
    setRefreshing(true);
    await fetchDevices();
    setRefreshing(false);
  };

  useEffect(() => {
    fetchDevices();
    fetchKullanicilar();
  }, []);

  useEffect(() => {
    handleSearch(searchTerm);
  }, [devices, searchTerm]);

  return (
    <>
      <Header />
      <div className="container-fluid">
        <div className="row">
          <Sidebar />

          <main className="col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white">
            <div className="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
              <h1 className="h2">
                <FontAwesomeIcon icon={faDesktop} className="me-2" />
                Cihaz Yönetimi
                <span className="badge bg-primary ms-2">{filteredDevices.length}</span>
              </h1>
              <div className="btn-toolbar">
                <div className="btn-group me-2">
                  <button
                    className="btn btn-success"
                    onClick={openNewAssignModal}
                  >
                    <FontAwesomeIcon icon={faUserPlus} className="me-2" />
                    Yeni Cihaz Ata
                  </button>
                </div>
                <button
                  className="btn btn-outline-secondary"
                  onClick={refreshDevices}
                  disabled={refreshing}
                >
                  <FontAwesomeIcon icon={faSync} className={refreshing ? 'fa-spin' : ''} />
                  {refreshing ? ' Yenileniyor...' : ' Yenile'}
                </button>
              </div>
            </div>

            {/* Arama filtresi */}
            <div className="card mb-4">
              <div className="card-body">
                <div className="row g-3">
                  <div className="col-md-6">
                    <div className="input-group">
                      <span className="input-group-text">
                        <FontAwesomeIcon icon={faSearch} />
                      </span>
                      <input
                        type="text"
                        className="form-control"
                        placeholder="Cihaz kodu, kullanıcı, sevkiyat veya plaka ile ara..."
                        value={searchTerm}
                        onChange={(e) => handleSearch(e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {loading ? (
              <LoadingSpinner size="lg" variant="primary" message="Cihazlar yükleniyor..." centered={true} />
            ) : error ? (
              <ErrorMessage
                message={error}
                variant="danger"
                title="Veri Yükleme Hatası"
                dismissible={true}
                onDismiss={() => setError('')}
              >
                <button className="btn btn-primary btn-sm mt-2" onClick={fetchDevices}>
                  Yeniden Dene
                </button>
              </ErrorMessage>
            ) : (
              <>
                {filteredDevices.length === 0 ? (
                  <div className="text-center py-5">
                    <FontAwesomeIcon icon={faDesktop} className="fa-3x text-muted mb-3" />
                    <h5 className="text-muted">Cihaz Bulunamadı</h5>
                    <p className="text-muted">Arama kriterlerinize uygun cihaz bulunamadı.</p>
                  </div>
                ) : (
                  <div className="card">
                    <div className="card-header">
                      <h5 className="mb-0">Cihazlar Listesi</h5>
                    </div>
                    <div className="card-body">
                      <div className="table-responsive">
                        <table className="table table-striped table-hover">
                          <thead>
                            <tr>
                              <th>Cihaz Kodu</th>
                              <th>Durum</th>
                              <th>Kullanıcı</th>
                              <th>Firma</th>
                              <th>Pil Seviyesi</th>
                              <th>Kontör Sonu</th>
                              <th>Sevkiyat</th>
                              <th>Plaka</th>
                              <th>Güzergah</th>
                              <th>İşlemler</th>
                            </tr>
                          </thead>
                          <tbody>
                            {filteredDevices.map((device, index) => (
                              <tr key={index}>
                                <td>
                                  <strong>{device.cihazKodu}</strong>
                                </td>
                                <td>
                                  {getStatusBadge(device.aktif)}
                                </td>
                                <td>
                                  {device.kullaniciAdi ? (
                                    <span>
                                      <FontAwesomeIcon icon={faUser} className="me-2 text-muted" />
                                      {device.kullaniciAdi}
                                    </span>
                                  ) : (
                                    <span className="text-muted">
                                      <FontAwesomeIcon icon={faUser} className="me-2" />
                                      Atanmamış
                                    </span>
                                  )}
                                </td>
                                <td>
                                  {device.firma ? (
                                    <span className="text-muted">{device.firma}</span>
                                  ) : (
                                    <span className="text-muted">-</span>
                                  )}
                                </td>
                                <td>
                                  {device.pilSeviyesi ? (
                                    (() => {
                                      const batteryPercentage = calculateBatteryPercentage(device.pilSeviyesi);
                                      return (
                                        <span className={getBatteryColor(batteryPercentage)} title={`${device.pilSeviyesi}V`}>
                                          <FontAwesomeIcon 
                                            icon={getBatteryIcon(batteryPercentage)} 
                                            className="me-2"
                                          />
                                          {batteryPercentage}%
                                        </span>
                                      );
                                    })()
                                  ) : (
                                    <span className="text-muted">
                                      <FontAwesomeIcon icon={faBatteryEmpty} className="me-2" />
                                      -
                                    </span>
                                  )}
                                </td>
                                <td>
                                  {device.kontorSonu ? (
                                    <span>
                                      <FontAwesomeIcon icon={faCalendarAlt} className="me-2 text-muted" />
                                      {device.kontorSonu}
                                    </span>
                                  ) : (
                                    <span className="text-muted">
                                      <FontAwesomeIcon icon={faCalendarAlt} className="me-2" />
                                      -
                                    </span>
                                  )}
                                </td>
                                <td>
                                  {device.sevkiyatAdi ? (
                                    <span className="text-truncate" style={{maxWidth: '150px'}} title={device.sevkiyatAdi}>
                                      <FontAwesomeIcon icon={faTruck} className="me-2 text-muted" />
                                      {device.sevkiyatAdi}
                                    </span>
                                  ) : (
                                    <span className="text-muted">
                                      <FontAwesomeIcon icon={faTruck} className="me-2" />
                                      -
                                    </span>
                                  )}
                                </td>
                                <td>
                                  {device.plakaNo ? (
                                    <span className="badge bg-secondary text-white">
                                      <FontAwesomeIcon icon={faTruck} className="me-1" />
                                      {device.plakaNo}
                                    </span>
                                  ) : (
                                    <span className="text-muted">-</span>
                                  )}
                                </td>
                                <td>
                                  {device.cikisLokasyon && device.varisLokasyon ? (
                                    <span className="text-muted small">
                                      {device.cikisLokasyon} → {device.varisLokasyon}
                                    </span>
                                  ) : (
                                    <span className="text-muted">-</span>
                                  )}
                                </td>
                                <td>
                                  <div className="btn-group">
                                    <button
                                      className="btn btn-sm btn-outline-primary"
                                      onClick={() => openAssignModal(device)}
                                      title="Kullanıcıya Ata"
                                    >
                                      <FontAwesomeIcon icon={faUserPlus} />
                                    </button>
                                    <button
                                      className="btn btn-sm btn-outline-info"
                                      onClick={() => openDetailModal(device)}
                                      title="Sevkiyat Detayları"
                                    >
                                      <FontAwesomeIcon icon={faEye} />
                                    </button>
                                  </div>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}
          </main>

          <Footer />
        </div>
      </div>

      {/* Cihaz Atama Modalı */}
      {showAssignModal && (
        <div className="modal show d-block" tabIndex="-1" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-lg">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">
                  {selectedDevice ? `Cihaz Atama - ${selectedDevice.cihazKodu}` : 'Yeni Cihaz Ata'}
                </h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => setShowAssignModal(false)}
                ></button>
              </div>
              <div className="modal-body">
                {/* Yeni cihaz atama için cihaz kodu girişi */}
                {!selectedDevice && (
                  <div className="mb-3">
                    <label htmlFor="yeniCihazKodu" className="form-label">Cihaz Kodu</label>
                    <div className="input-group">
                      <input
                        type="text"
                        className="form-control"
                        id="yeniCihazKodu"
                        placeholder="Cihaz kodunu giriniz"
                        value={yeniCihazKodu}
                        onChange={e => setYeniCihazKodu(e.target.value)}
                        onKeyPress={e => e.key === 'Enter' && handleCihazSorgulaAtama()}
                      />
                      <button
                        className="btn btn-outline-primary"
                        onClick={handleCihazSorgulaAtama}
                        disabled={!yeniCihazKodu.trim()}
                      >
                        <FontAwesomeIcon icon={faSearch} className="me-2" />
                        Sorgula
                      </button>
                    </div>
                  </div>
                )}

                {/* Sorgulanmış cihaz bilgileri */}
                {sorgulanancihaz && (
                  <div className="card mb-3">
                    <div className="card-header">
                      <h6 className="mb-0">Bulunan Cihaz - {sorgulanancihaz.cihazBilgi?.cihaz_kodu}</h6>
                    </div>
                    <div className="card-body">
                      <div className="row">
                        <div className="col-md-6">
                          <p><strong>Durum:</strong> {getStatusBadge(sorgulanancihaz.cihazBilgi?.aktif)}</p>
                          <p><strong>Pil Seviyesi:</strong> 
                            {sorgulanancihaz.harici_bilgi?.sonSensorler?.pil ? (
                              <span className={getBatteryColor(sorgulanancihaz.harici_bilgi.sonSensorler.pil)}>
                                <FontAwesomeIcon 
                                  icon={getBatteryIcon(sorgulanancihaz.harici_bilgi.sonSensorler.pil)} 
                                  className="me-2 ms-2"
                                />
                                {sorgulanancihaz.harici_bilgi.sonSensorler.pil}%
                              </span>
                            ) : (
                              <span className="text-muted ms-2">-</span>
                            )}
                          </p>
                        </div>
                        <div className="col-md-6">
                          <p><strong>Sıcaklık:</strong> {sorgulanancihaz.harici_bilgi?.sonSensorler?.sicaklik || '-'}°C</p>
                          <p><strong>Nem:</strong> {sorgulanancihaz.harici_bilgi?.sonSensorler?.nem || '-'}%</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Kullanıcı seçimi */}
                {(selectedDevice || sorgulanancihaz) && (
                  <div className="mb-3">
                    <label htmlFor="kullaniciSec" className="form-label">Kullanıcı Seç</label>
                    <select
                      className="form-select"
                      id="kullaniciSec"
                      value={secilenKullanici}
                      onChange={e => setSecilenKullanici(e.target.value)}
                    >
                      <option value="">Kullanıcı seçiniz</option>
                      {kullaniciListesi.map(k => (
                        <option key={k.musteri_ID} value={k.musteri_ID}>
                          {k.musteri_adi} {k.firma ? `(${k.firma})` : ''}
                        </option>
                      ))}
                    </select>
                  </div>
                )}

                {atamaDurum && (
                  <div className={`alert ${atamaDurum.includes('başarı') ? 'alert-success' : atamaDurum.includes('hata') || atamaDurum.includes('bulunamadı') ? 'alert-danger' : 'alert-info'}`}>
                    {atamaDurum}
                  </div>
                )}
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => setShowAssignModal(false)}
                >
                  İptal
                </button>
                <button
                  type="button"
                  className="btn btn-primary"
                  onClick={handleCihazAta}
                  disabled={!secilenKullanici || (!selectedDevice && !sorgulanancihaz)}
                >
                  <FontAwesomeIcon icon={faUserPlus} className="me-2" />
                  Ata
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Sevkiyat Detay Modalı */}
      {showDetailModal && selectedDeviceDetail && (
        <div className="modal show d-block" tabIndex="-1" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-lg">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Sevkiyat Detayları - {selectedDeviceDetail.cihazKodu}</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => setShowDetailModal(false)}
                ></button>
              </div>
              <div className="modal-body">
                <div className="row">
                  <div className="col-md-6">
                    <h6>Cihaz Bilgileri</h6>
                    <table className="table table-sm">
                      <tbody>
                        <tr>
                          <td><strong>Cihaz Kodu:</strong></td>
                          <td>{selectedDeviceDetail.cihazKodu}</td>
                        </tr>
                        <tr>
                          <td><strong>Durum:</strong></td>
                          <td>{getStatusBadge(selectedDeviceDetail.aktif)}</td>
                        </tr>
                        <tr>
                          <td><strong>Pil Seviyesi:</strong></td>
                          <td>
                            {selectedDeviceDetail.pilSeviyesi ? (
                              (() => {
                                const batteryPercentage = calculateBatteryPercentage(selectedDeviceDetail.pilSeviyesi);
                                return (
                                  <span className={getBatteryColor(batteryPercentage)} title={`${selectedDeviceDetail.pilSeviyesi}V`}>
                                    <FontAwesomeIcon 
                                      icon={getBatteryIcon(batteryPercentage)} 
                                      className="me-2"
                                    />
                                    {batteryPercentage}%
                                  </span>
                                );
                              })()
                            ) : '-'}
                          </td>
                        </tr>
                        <tr>
                          <td><strong>Kontör Sonu:</strong></td>
                          <td>{selectedDeviceDetail.kontorSonu || '-'}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <div className="col-md-6">
                    <h6>Sevkiyat Bilgileri</h6>
                    <table className="table table-sm">
                      <tbody>
                        <tr>
                          <td><strong>Sevkiyat Adı:</strong></td>
                          <td>{selectedDeviceDetail.sevkiyatAdi || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Plaka:</strong></td>
                          <td>{selectedDeviceDetail.plakaNo || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Çıkış:</strong></td>
                          <td>{selectedDeviceDetail.cikisLokasyon || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Varış:</strong></td>
                          <td>{selectedDeviceDetail.varisLokasyon || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Nakliyeci:</strong></td>
                          <td>{selectedDeviceDetail.nakliyeci || '-'}</td>
                        </tr>
                        <tr>
                          <td><strong>Oluşturma:</strong></td>
                          <td>{selectedDeviceDetail.olusturmaTarihi || '-'}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => setShowDetailModal(false)}
                >
                  Kapat
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

    </>
  );
};

export default Devices;