import React, { useState, useEffect, Suspense, lazy } from 'react';
import { HashRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import './assets/css/style.css';
import 'bootstrap/dist/css/bootstrap.min.css';
import 'bootstrap/dist/js/bootstrap.bundle.min';
import 'react-toastify/dist/ReactToastify.css';
import LoadingSpinner from './components/LoadingSpinner';
import { preloadCriticalRoutes, preloadByUserRole, retryImport } from './utils/dynamicImports';
import { initPerformanceMonitoring, trackResourceLoading, monitorConnection } from './utils/performanceMonitoring';

// Login ve Register sayfalarını doğrudan import et (ilk yüklemede gerekli)
import Login from './pages/Login';
import Register from './pages/Register';

// Diğer sayfaları retry-enabled lazy loading ile yükle
const Home = lazy(() => retryImport(() => import('./pages/Home')));
const ShipmentView = lazy(() => retryImport(() => import('./pages/ShipmentView')));
const ShipmentAdd = lazy(() => retryImport(() => import('./pages/ShipmentAdd')));
const Profile = lazy(() => retryImport(() => import('./pages/Profile')));
const Payment = lazy(() => retryImport(() => import('./pages/Payment')));
const InactiveDevices = lazy(() => retryImport(() => import('./pages/InactiveDevices')));
const ShipmentHistory = lazy(() => retryImport(() => import('./pages/ShipmentHistory')));
const Viewers = lazy(() => retryImport(() => import('./pages/Viewers')));
const Invoices = lazy(() => retryImport(() => import('./pages/Invoices')));
const Shipments = lazy(() => retryImport(() => import('./pages/Shipments')));
const Notifications = lazy(() => retryImport(() => import('./pages/Notifications')));
const Devices = lazy(() => retryImport(() => import('./pages/Devices')));
const NotFound = lazy(() => retryImport(() => import('./pages/NotFound')));

function App() {
  // Oturum durumu (localStorage kontrolü ile)
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  
  // Performance monitoring başlat
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      initPerformanceMonitoring();
      trackResourceLoading();
      monitorConnection();
    }
  }, []);

  // Oturum kontrolü - localStorage'daki user bilgisini kontrol et
  useEffect(() => {
    const checkLoginStatus = () => {
      try {
        const storedUser = localStorage.getItem('user');
        if (storedUser) {
          const userData = JSON.parse(storedUser);
          // User object'inin doğru yapıda olup olmadığını kontrol et
          if (userData && userData.user && (userData.user.musteri_ID || userData.user.id)) {
            setIsAuthenticated(true);
            
            // Kullanıcı giriş yaptıktan sonra kritik route'ları preload et
            setTimeout(() => {
              preloadCriticalRoutes();
              // Kullanıcı rolüne göre ek preload
              const userRole = userData.user.role || userData.user.gorev || 'user';
              preloadByUserRole(userRole);
            }, 1000);
          } else {
            setIsAuthenticated(false);
            // Hatalı veri varsa temizle
            localStorage.removeItem('user');
          }
        } else {
          setIsAuthenticated(false);
        }
      } catch (error) {
        console.error('localStorage kontrolünde hata:', error);
        setIsAuthenticated(false);
        localStorage.removeItem('user');
      } finally {
        setIsLoading(false);
      }
    };

    checkLoginStatus();
  }, []);

  // Auth expired event listener - axios interceptor'dan gelen logout sinyali
  useEffect(() => {
    const handleAuthExpired = () => {
      console.log('Auth expired event received - logging out user');
      setIsAuthenticated(false);
      localStorage.removeItem('user');
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_data');
    };

    window.addEventListener('auth-expired', handleAuthExpired);
    
    return () => {
      window.removeEventListener('auth-expired', handleAuthExpired);
    };
  }, []);

  // Yetki gerektiren rotalar için koruma bileşeni
  const ProtectedRoute = ({ children }) => {
    if (isLoading) {
      return (
        <LoadingSpinner 
          size="lg" 
          variant="primary" 
          message="Uygulama başlatılıyor..." 
          overlay={true}
        />
      );
    }

    if (!isAuthenticated) {
      return <Navigate to="/login" replace />;
    }

    return (
      <Suspense fallback={
        <LoadingSpinner 
          size="lg" 
          variant="primary" 
          message="Sayfa yükleniyor..." 
          centered={true}
        />
      }>
        {children}
      </Suspense>
    );
  };

  return (
    <Router>
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />

        {/* Korumalı rotalar - Artık Suspense ProtectedRoute içinde */}
        <Route
          path="/"
          element={
            <ProtectedRoute>
              <Home />
            </ProtectedRoute>
          }
        />

        <Route
          path="/view/:id"
          element={
            <ProtectedRoute>
              <ShipmentView />
            </ProtectedRoute>
          }
        />

        <Route
          path="/add"
          element={
            <ProtectedRoute>
              <ShipmentAdd />
            </ProtectedRoute>
          }
        />

        <Route
          path="/profile"
          element={
            <ProtectedRoute>
              <Profile />
            </ProtectedRoute>
          }
        />

        <Route
          path="/payment"
          element={
            <ProtectedRoute>
              <Payment />
            </ProtectedRoute>
          }
        />

        <Route
          path="/inactive-devices"
          element={
            <ProtectedRoute>
              <InactiveDevices />
            </ProtectedRoute>
          }
        />

        <Route
          path="/history"
          element={
            <ProtectedRoute>
              <ShipmentHistory />
            </ProtectedRoute>
          }
        />


        <Route
          path="/viewers"
          element={
            <ProtectedRoute>
              <Viewers />
            </ProtectedRoute>
          }
        />

        <Route
          path="/invoices"
          element={
            <ProtectedRoute>
              <Invoices />
            </ProtectedRoute>
          }
        />

        <Route
          path="/shipments"
          element={
            <ProtectedRoute>
              <Shipments />
            </ProtectedRoute>
          }
        />

        <Route
          path="/notifications"
          element={
            <ProtectedRoute>
              <Notifications />
            </ProtectedRoute>
          }
        />


        <Route
          path="/devices"
          element={
            <ProtectedRoute>
              <Devices />
            </ProtectedRoute>
          }
        />

        <Route 
          path="*" 
          element={
            <ProtectedRoute>
              <NotFound />
            </ProtectedRoute>
          } 
        />
      </Routes>
      
      {/* Toast Notification Container */}
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
        limit={5}
      />
    </Router>
  );
}

export default App;
