import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFilePdf, faSpinner } from '@fortawesome/free-solid-svg-icons';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';

const ReportGenerator = ({ cihazID, tableData, onReportGenerated }) => {
  
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState(null);

  // PDF rapor oluşturma fonksiyonu
  const generatePDFReport = async () => {
    try {
      setIsGenerating(true);
      setError(null);

      if (!tableData || tableData.length === 0) {
        setError('Rapor oluşturmak için veri bulunamadı.');
        return;
      }

      // PDF oluştur
      const doc = new jsPDF();
      
      // Türkçe karakter desteği için font ayarla
      doc.setFont('helvetica', 'normal');
      
      // UTF-8 encoding kullan
      doc.setLanguage('tr');
      
      // Türkçe karakter desteği için yardımcı fonksiyon
      const fixTurkishChars = (text) => {
        return text
          .replace(/ğ/g, 'g')
          .replace(/Ğ/g, 'G')
          .replace(/ü/g, 'u')
          .replace(/Ü/g, 'U')
          .replace(/ş/g, 's')
          .replace(/Ş/g, 'S')
          .replace(/ı/g, 'i')
          .replace(/İ/g, 'I')
          .replace(/ö/g, 'o')
          .replace(/Ö/g, 'O')
          .replace(/ç/g, 'c')
          .replace(/Ç/g, 'C');
      };
      
      // Başlık - üst ortada
      doc.setFontSize(16);
      doc.setFont('helvetica', 'bold');
      const pageWidth = doc.internal.pageSize.width;
      const title = fixTurkishChars('MGZ24 Cihaz Veri Raporu');
      const titleWidth = doc.getTextWidth(title);
      const titleX = (pageWidth - titleWidth) / 2;
      doc.text(title, titleX, 20);
      
      // Alt başlık bilgileri
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');
      const currentDate = new Date().toLocaleDateString('tr-TR');
      const recordCount = tableData.length;
      const subtitle = fixTurkishChars(`Cihaz ID: ${cihazID}    Rapor Tarihi: ${currentDate}    KAYIT SAYISI: ${recordCount}`);
      const subtitleWidth = doc.getTextWidth(subtitle);
      const subtitleX = (pageWidth - subtitleWidth) / 2;
      doc.text(subtitle, subtitleX, 30);

      // Pil voltajını yüzdeye çevirme fonksiyonu (DeviceHistoryTable ile aynı)
      const calculateBatteryPercentage = (voltage) => {
        const volt = parseFloat(voltage);
        if (isNaN(volt)) return 0;
        
        const minVoltage = 2.5;
        const maxVoltage = 3.4;
        const minPercentage = 30;
        const maxPercentage = 100;
        
        const percentage = minPercentage + ((volt - minVoltage) / (maxVoltage - minVoltage)) * (maxPercentage - minPercentage);
        return Math.round(Math.max(minPercentage, Math.min(maxPercentage, percentage)));
      };

      // Tablo verileri hazırla
      const tableHeaders = ['Tarih', 'Sicaklik', 'Nem', 'Pil (%)', 'Enlem', 'Boylam'].map(header => fixTurkishChars(header));
      const tableRows = tableData.map(row => [
        row.tarih || row.zaman || new Date().toLocaleDateString('tr-TR'),
        row.sensorler?.sicaklik ? `${row.sensorler.sicaklik}°C` : '-',
        row.sensorler?.nem ? `${row.sensorler.nem}%` : '-',
        row.sensorler?.pil ? `${calculateBatteryPercentage(row.sensorler.pil)}%` : '-',
        row.konum?.enlem || '-',
        row.konum?.boylam || '-'
      ]);

      // Tablo oluştur
      autoTable(doc, {
        head: [tableHeaders],
        body: tableRows,
        startY: 40,
        styles: {
          fontSize: 8,
          cellPadding: 3,
        },
        headStyles: {
          fillColor: [41, 128, 185], // Mavi başlık
          textColor: 255,
          fontStyle: 'bold'
        },
        alternateRowStyles: {
          fillColor: [245, 245, 245] // Zebra çizgili
        },
        margin: { top: 40, left: 14, right: 14 }
      });

      // PDF'i indir
      const fileName = `MGZ24_Cihaz_Raporu_${cihazID}_${new Date().toISOString().split('T')[0]}.pdf`;
      doc.save(fileName);

      // Başarı callback'i
      if (onReportGenerated) {
        onReportGenerated('pdf', fileName);
      }

    } catch (error) {
      console.error('PDF rapor oluşturma hatası:', error);
      setError('PDF raporu oluşturulurken hata oluştu: ' + error.message);
    } finally {
      setIsGenerating(false);
    }
  };


  return (
    <div className="card">
      <div className="card-body">
        {/* Hata Mesajı */}
        {error && (
          <div className="alert alert-danger">
            <small>{error}</small>
          </div>
        )}

        {/* PDF İndir Butonu */}
        <div className="d-flex align-items-center justify-content-between">
          <div>
            <button
              className="btn btn-danger"
              onClick={generatePDFReport}
              disabled={isGenerating}
            >
              {isGenerating ? (
                <>
                  <FontAwesomeIcon icon={faSpinner} className="me-2 fa-spin" />
                  Oluşturuluyor...
                </>
              ) : (
                <>
                  <FontAwesomeIcon icon={faFilePdf} className="me-2" />
                  PDF İndir
                </>
              )}
            </button>
          </div>
          
          {/* Rapor Bilgileri */}
          <div>
            <small className="text-muted">
              Sadece gösterilen veriler raporlanır.
            </small>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReportGenerator;