import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
    faCreditCard, faCalendarAlt, faLock, faTag,
    faTurkishLiraSign, faHistory, faClock, faUser, faMicrochip, faCoins, faEuroSign
} from '@fortawesome/free-solid-svg-icons';

import { paymentService, cihazIDService } from '../api/dbService';
import Header from '../components/Header';
import Sidebar from '../components/Sidebar';
import Footer from '../components/Footer';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';
import ParamPOSForm from '../components/payment/ParamPOSForm';
import PaymentSuccess from '../components/payment/PaymentSuccess';

const Payment = () => {
    const [loading, setLoading] = useState(true);
    const [packages, setPackages] = useState([]);
    const [selectedPackage, setSelectedPackage] = useState(null);
    const [exchangeRate, setExchangeRate] = useState(0);
    const [eurRate, setEurRate] = useState(null);
    const [userDevices, setUserDevices] = useState([]);
    const [selectedDeviceForCredit, setSelectedDeviceForCredit] = useState('');
    const [paymentHistory, setPaymentHistory] = useState([]);
    const [remainingUsage, setRemainingUsage] = useState(null);
    const [error, setError] = useState(null);
    const [showHistory, setShowHistory] = useState(false);
    const [processingPayment, setProcessingPayment] = useState(false);
    const [paymentSuccess, setPaymentSuccess] = useState(false);
    const [paymentError, setPaymentError] = useState(null);
    const [completedPayment, setCompletedPayment] = useState(null);
    const [showParamPOS, setShowParamPOS] = useState(false);


    // Seçili paket state'i
    const [selectedPackageForPayment, setSelectedPackageForPayment] = useState(null);

    // EUR kuru alma fonksiyonu - TCMB resmi XML API
    const fetchEURRate = async () => {
        try {
            // Backend API'den EUR kuru al
            const response = await fetch(`${process.env.REACT_APP_API_URL || '//mgz24.com/api'}/payments/exchange-rate`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.EUR && data.EUR.selling) {
                setEurRate(data.EUR.selling);
                setExchangeRate(data.EUR.selling);
                
                // Eğer fallback değer kullanıldıysa uyarı göster
                if (data.EUR.fallback) {
                    console.warn('Canlı kur alınamadı, fallback değer kullanılıyor:', data.EUR.error);
                }
            } else {
                throw new Error('EUR kuru backend\'den alınamadı');
            }
        } catch (error) {
            console.error('TCMB EUR kuru alınırken hata:', error);
            // Fallback değer kullan
            const fallbackRate = 34.98;
            setEurRate(fallbackRate);
            setExchangeRate(fallbackRate);
            console.warn('Fallback EUR kuru kullanılıyor:', fallbackRate);
        }
    };

    // Kullanıcının tüm cihazlarını getir (aktif + inaktif)
    const fetchUserDevices = async (userId) => {
        try {
            // Kullanıcının tüm cihazlarını getir
            const response = await fetch(`${process.env.REACT_APP_API_URL || '//mgz24.com/api'}/user-devices?userId=${userId}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                setUserDevices(data || []);
            } else {
                console.error('Kullanıcı cihazları alınamadı');
                setUserDevices([]);
            }
        } catch (error) {
            console.error('Kullanıcı cihazları alınırken hata:', error);
            setUserDevices([]);
        }
    };


    // Kredi kartı form state'i
    const [cardForm, setCardForm] = useState({
        cardHolder: '',
        cardNumber: '',
        expiryMonth: '',
        expiryYear: '',
        cvv: '',
        agree: false
    });

    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);
                setError(null);

                // Local storage'dan kullanıcı bilgilerini al
                const storedUser = JSON.parse(localStorage.getItem('user'));

                // Debug için localStorage içeriğini detaylı logla
                console.log('=== Payment.js Debug ===');
                console.log('Full localStorage user:', storedUser);
                console.log('storedUser?.user:', storedUser?.user);
                if (storedUser?.user) {
                    console.log('Available keys in user object:', Object.keys(storedUser.user));
                    console.log('musteri_ID:', storedUser.user.musteri_ID);
                    console.log('id:', storedUser.user.id);
                }

                // Kullanıcı ID'sini al (musteri_ID veya id)
                const userId = storedUser?.user?.musteri_ID || storedUser?.user?.id;

                console.log('Final userId:', userId);

                if (!userId) {
                    console.error('Kullanıcı ID bulunamadı:', storedUser);
                    setError('Kullanıcı bilgilerine erişilemiyor. Lütfen tekrar giriş yapın.');
                    return;
                }


                // Demo verilerle çalışacak şekilde düzelt
                let rateData, packagesData, usageData, historyData;
                
                try {
                    rateData = await paymentService.getExchangeRate();
                    packagesData = await paymentService.getPaymentPackages();
                    usageData = await paymentService.getRemainingUsage(userId);
                    historyData = await paymentService.getPaymentHistory(userId);
                } catch (apiError) {
                    console.warn('API çağrısı başarısız, demo veriler kullanılıyor:', apiError);
                    // Demo veriler
                    rateData = 34.98;
                    packagesData = [
                        { id: 1, days: 5, priceEUR: 3, name: '5 Gün Kullanım' },
                        { id: 2, days: 10, priceEUR: 5, name: '10 Gün Kullanım' },
                        { id: 3, days: 20, priceEUR: 6, name: '20 Gün Kullanım' },
                        { id: 4, days: 30, priceEUR: 7, name: '30 Gün Kullanım' },
                        { id: 5, days: 90, priceEUR: 18, name: '3 Ay Kullanım' },
                        { id: 6, days: 180, priceEUR: 33, name: '6 Ay Kullanım' },
                        { id: 7, days: 360, priceEUR: 60, name: '1 Yıl Kullanım' }
                    ];
                    usageData = {
                        remainingDays: 23,
                        expiryDate: new Date(Date.now() + 23 * 24 * 60 * 60 * 1000).toISOString(),
                        status: 'active'
                    };
                    historyData = [
                        {
                            id: 'TRX-DEMO123',
                            packageName: '30 Gün Kullanım',
                            amountEUR: 7,
                            amountTRY: 244.86,
                            exchangeRate: 34.98,
                            date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
                            status: 'completed'
                        }
                    ];
                }

                setExchangeRate(rateData);
                setPackages(packagesData);
                setRemainingUsage(usageData);
                setPaymentHistory(historyData);
                
                // EUR kurunu getir
                await fetchEURRate();
                
                // Kullanıcının cihazlarını getir
                await fetchUserDevices(userId);

                // Varsayılan olarak 30 günlük paketi seç
                const defaultPackage = packagesData.find(pkg => pkg.days === 30) || packagesData[0];
                setSelectedPackage(defaultPackage);

            } catch (err) {
                console.error("Veri alınırken hata:", err);
                setError(err.message || "Veri alınırken bir hata oluştu");
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    const handleCardFormChange = (e) => {
        const { name, value, type, checked } = e.target;
        setCardForm({
            ...cardForm,
            [name]: type === 'checkbox' ? checked : value
        });
    };


    const formatCardNumber = (value) => {
        // Kredi kartı numarasını formatla (4 hane + boşluk)
        const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
        const matches = v.match(/\d{4,16}/g);
        const match = (matches && matches[0]) || '';
        const parts = [];

        for (let i = 0; i < match.length; i += 4) {
            parts.push(match.substring(i, i + 4));
        }

        if (parts.length) {
            return parts.join(' ');
        } else {
            return value;
        }
    };


    const calculateTotalPrice = () => {
        if (!selectedPackage || !exchangeRate) return 0;
        return (selectedPackage.priceEUR * exchangeRate).toFixed(2);
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        // Form doğrulama
        if (!cardForm.cardHolder ||
            !cardForm.cardNumber ||
            !cardForm.expiryMonth ||
            !cardForm.expiryYear ||
            !cardForm.cvv ||
            !cardForm.agree) {
            setPaymentError("Lütfen tüm alanları doldurun ve şartları kabul edin");
            return;
        }

        // Kredi kartı numarası kontrolü (basit)
        const cardNumberClean = cardForm.cardNumber.replace(/\s/g, '');
        if (cardNumberClean.length < 16) {
            setPaymentError("Geçerli bir kredi kartı numarası girin");
            return;
        }

        try {
            setProcessingPayment(true);
            setPaymentError(null);

            // Kullanıcı bilgilerini al
            const storedUser = JSON.parse(localStorage.getItem('user'));
            const userId = storedUser?.user?.musteri_ID || storedUser?.user?.id;

            // Ödeme verisini hazırla
            const paymentData = {
                userId: userId,
                packageId: selectedPackage.id,
                cardDetails: {
                    holderName: cardForm.cardHolder,
                    number: cardNumberClean,
                    expiryMonth: cardForm.expiryMonth,
                    expiryYear: cardForm.expiryYear,
                    cvv: cardForm.cvv
                },
                amount: {
                    eur: selectedPackage.priceEUR,
                    try: calculateTotalPrice(),
                    exchangeRate: exchangeRate
                }
            };

            // Demo ödeme işlemi simüle et
            let result;
            try {
                result = await paymentService.processPayment(paymentData);
            } catch (apiError) {
                console.warn('Ödeme API\'si başarısız, demo ödeme simüle ediliyor:', apiError);
                // Demo başarılı ödeme
                result = {
                    success: true,
                    message: 'Demo ödeme başarıyla tamamlandı',
                    transactionId: 'TRX-DEMO' + Math.random().toString(36).substring(2, 8).toUpperCase(),
                    date: new Date().toISOString()
                };
            }

            if (result && result.success) {
                setPaymentSuccess(true);

                // Demo olarak kullanım süresini güncelle
                const updatedUsage = {
                    remainingDays: (remainingUsage?.remainingDays || 0) + selectedPackage.days,
                    expiryDate: new Date(Date.now() + ((remainingUsage?.remainingDays || 0) + selectedPackage.days) * 24 * 60 * 60 * 1000).toISOString(),
                    status: 'active'
                };

                const newPayment = {
                    id: result.transactionId,
                    packageName: selectedPackage.name,
                    amountEUR: selectedPackage.priceEUR,
                    amountTRY: parseFloat(calculateTotalPrice()),
                    exchangeRate: exchangeRate,
                    date: result.date,
                    status: 'completed'
                };

                setRemainingUsage(updatedUsage);
                setPaymentHistory([newPayment, ...paymentHistory]);

                // Form alanlarını temizle
                setCardForm({
                    cardHolder: '',
                    cardNumber: '',
                    expiryMonth: '',
                    expiryYear: '',
                    cvv: '',
                    agree: false
                });
            } else {
                setPaymentError("Ödeme işlemi sırasında bir hata oluştu");
            }
        } catch (err) {
            console.error("Ödeme işleminde hata:", err);
            setPaymentError(err.message || "Ödeme işlemi sırasında bir hata oluştu");
        } finally {
            setProcessingPayment(false);
        }
    };


    const handlePaymentSuccess = async (paymentIntent) => {
        console.log('Ödeme başarılı:', paymentIntent);
        setCompletedPayment(paymentIntent);
        setPaymentSuccess(true);
        setShowParamPOS(false);
        
        // Seçili cihaza kredi ekle
        try {
            if (selectedDeviceForCredit && selectedPackage) {
                // Backend'e cihaza kredi ekleme isteği gönder
                const response = await fetch(`${process.env.REACT_APP_API_URL || '//mgz24.com/api'}/cihaz-bilgi/${selectedDeviceForCredit}/add-credit`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
                    },
                    body: JSON.stringify({
                        days: selectedPackage.days,
                        paymentId: paymentIntent.id,
                        packageName: selectedPackage.name
                    })
                });

                if (response.ok) {
                    console.log('Cihaza kredi başarıyla eklendi');
                    // Kullanıcı cihazlarını yeniden yükle
                    const storedUser = JSON.parse(localStorage.getItem('user'));
                    const userId = storedUser?.user?.musteri_ID || storedUser?.user?.id;
                    if (userId) {
                        await fetchUserDevices(userId);
                    }
                } else {
                    console.error('Cihaza kredi ekleme hatası:', await response.text());
                }
            }
        } catch (error) {
            console.error('Cihaz kredi ekleme hatası:', error);
        }
        
        // Kullanım süresini güncelle
        try {
            const storedUser = JSON.parse(localStorage.getItem('user'));
            const userId = storedUser?.user?.musteri_ID || storedUser?.user?.id;
            
            if (userId) {
                const [updatedUsage, updatedHistory] = await Promise.all([
                    paymentService.getRemainingUsage(userId),
                    paymentService.getPaymentHistory(userId)
                ]);

                setRemainingUsage(updatedUsage);
                setPaymentHistory(updatedHistory);
            }
        } catch (error) {
            console.error('Kullanım süresi güncellenirken hata:', error);
        }
    };

    const handlePaymentError = (error) => {
        console.error('Ödeme hatası:', error);
        setPaymentError(error.message || 'Ödeme işlemi başarısız');
        setShowParamPOS(false);
    };

    const handleDownloadReceipt = () => {
        if (!completedPayment) return;
        
        // Makbuz PDF oluştur ve indir
        const receiptContent = `
            MGZ24 ÖDEME MAKBUZu
            ==================
            İşlem No: ${completedPayment.id}
            Tutar: ${(completedPayment.amount / 100).toFixed(2)} ${completedPayment.currency.toUpperCase()}
            Tarih: ${new Date(completedPayment.created * 1000).toLocaleString('tr-TR')}
            Durum: Başarılı
            
            Bu makbuz ödeme kanıtınızdır.
        `;
        
        const blob = new Blob([receiptContent], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `MGZ24_Makbuz_${completedPayment.id}.txt`;
        a.click();
        URL.revokeObjectURL(url);
    };

    const handleSendEmail = async () => {
        if (!completedPayment) return;
        
        try {
            // E-posta gönderme API'sini çağır
            const response = await fetch('/api/send-receipt-email', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    paymentIntentId: completedPayment.id,
                    userEmail: JSON.parse(localStorage.getItem('user'))?.user?.email
                })
            });
            
            if (response.ok) {
                alert('Makbuz e-posta adresinize gönderildi.');
            } else {
                alert('E-posta gönderilirken hata oluştu.');
            }
        } catch (error) {
            console.error('E-posta gönderme hatası:', error);
            alert('E-posta gönderilirken hata oluştu.');
        }
    };

    const handlePrintReceipt = () => {
        if (!completedPayment) return;
        
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>MGZ24 Ödeme Makbuzu</title>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; }
                        .header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; }
                        .details { margin: 20px 0; }
                        .row { display: flex; justify-content: space-between; margin: 5px 0; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h2>MGZ24 ÖDEME MAKBUZU</h2>
                    </div>
                    <div class="details">
                        <div class="row"><span>İşlem No:</span><span>${completedPayment.id}</span></div>
                        <div class="row"><span>Tutar:</span><span>${(completedPayment.amount / 100).toFixed(2)} ${completedPayment.currency.toUpperCase()}</span></div>
                        <div class="row"><span>Tarih:</span><span>${new Date(completedPayment.created * 1000).toLocaleString('tr-TR')}</span></div>
                        <div class="row"><span>Durum:</span><span>Başarılı</span></div>
                    </div>
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    };

    const handleReturnHome = () => {
        setPaymentSuccess(false);
        setCompletedPayment(null);
        setShowParamPOS(false);
    };

    // Eğer ödeme başarılıysa success sayfasını göster
    if (paymentSuccess && completedPayment) {
        return (
            <div className="container-fluid">
                <div className="row">
                    <Header />
                    <Sidebar />
                    <main className="col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white">
                        <PaymentSuccess 
                            paymentIntent={completedPayment}
                            packageInfo={selectedPackageForPayment}
                            onDownloadReceipt={handleDownloadReceipt}
                            onSendEmail={handleSendEmail}
                            onPrintReceipt={handlePrintReceipt}
                            onReturnHome={handleReturnHome}
                        />
                    </main>
                    <Footer />
                </div>
            </div>
        );
    }

    // Eğer Param POS formu gösteriliyorsa
    if (showParamPOS && selectedPackageForPayment) {
        return (
            <div className="container-fluid">
                <div className="row">
                    <Header />
                    <Sidebar />
                    <main className="col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white">
                        <div className="pt-3 pb-1 mt-3 mb-3 border-bottom">
                            <h1 className="h4 text-dark">
                                Güvenli Ödeme - {selectedPackageForPayment.name}
                            </h1>
                            <button 
                                className="btn btn-outline-secondary btn-sm"
                                onClick={() => setShowParamPOS(false)}
                            >
                                ← Geri Dön
                            </button>
                        </div>
                        
                        <div className="row justify-content-center">
                            <div className="col-lg-8">
                                <ParamPOSForm 
                                    amount={selectedPackage.priceEUR * exchangeRate}
                                    currency="TRY"
                                    packageInfo={selectedPackage}
                                    deviceId={selectedDeviceForCredit}
                                    onSuccess={handlePaymentSuccess}
                                    onError={handlePaymentError}
                                    disabled={processingPayment}
                                />
                            </div>
                        </div>
                    </main>
                    <Footer />
                </div>
            </div>
        );
    }

    return (
        <div className="container-fluid">
            <div className="row">
                <Header />
                <Sidebar />

                <main className="col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white">
                    <div className="pt-3 pb-1 mt-3 mb-3 border-bottom">
                        <div className="d-flex justify-content-between align-items-center">
                            <h1 className="h4 text-dark mb-0">Kullanım Süresi Satın Al</h1>
                            {eurRate && (
                                <div className="d-flex align-items-center text-muted">
                                    <span className="me-2 small">Güncel Euro Kuru:</span>
                                    <span className="badge bg-primary">
                                        <FontAwesomeIcon icon={faTurkishLiraSign} className="me-1" />
                                        {eurRate.toFixed(2)}
                                    </span>
                                </div>
                            )}
                        </div>
                    </div>

                    {loading ? (
                        <LoadingSpinner 
                            size="lg" 
                            variant="primary" 
                            message="Paket bilgileri yükleniyor..." 
                            centered={true}
                        />
                    ) : error ? (
                        <ErrorMessage 
                            message={error} 
                            variant="danger"
                            title="Veri Yükleme Hatası"
                            dismissible={true}
                            onDismiss={() => setError('')}
                        >
                            <button className="btn btn-primary btn-sm mt-2" onClick={() => window.location.reload()}>
                                Yeniden Dene
                            </button>
                        </ErrorMessage>
                    ) : (
                        <div className="row">
                            {/* Paket Seçimi */}
                            <div className="col-lg-6 mb-4">
                                <div className="card border-0 shadow-sm">
                                    <div className="card-header bg-primary text-white">
                                        <FontAwesomeIcon icon={faTag} className="me-2" />
                                        <span className="fw-bold">Kullanım Paketi Seçin</span>
                                    </div>
                                    <div className="card-body">
                                        <div className="row g-3">
                                            {packages.map((pkg) => (
                                                <div key={pkg.id} className="col-12">
                                                    <div 
                                                        className={`card border ${selectedPackage?.id === pkg.id ? 'border-primary bg-primary bg-opacity-10' : 'border-light'} cursor-pointer`}
                                                        onClick={() => setSelectedPackage(pkg)}
                                                        style={{ cursor: 'pointer', transition: 'all 0.2s' }}
                                                    >
                                                        <div className="card-body py-3">
                                                            <div className="d-flex justify-content-between align-items-center">
                                                                <div>
                                                                    <h6 className="card-title mb-1 fw-bold">
                                                                        <FontAwesomeIcon icon={faClock} className="me-2 text-primary" />
                                                                        {pkg.days} Gün
                                                                    </h6>
                                                                    <small className="text-muted">{pkg.name}</small>
                                                                </div>
                                                                <div className="text-end">
                                                                    <div className="fw-bold text-primary">
                                                                        <FontAwesomeIcon icon={faEuroSign} className="me-1" />
                                                                        €{pkg.priceEUR}
                                                                    </div>
                                                                    <small className="text-muted">
                                                                        <FontAwesomeIcon icon={faTurkishLiraSign} className="me-1" />
                                                                        ₺{(pkg.priceEUR * exchangeRate).toFixed(2)}
                                                                    </small>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Cihaz Seçimi ve Ödeme */}
                            <div className="col-lg-6 mb-4">
                                <div className="card border-0 shadow-sm">
                                    <div className="card-header bg-success text-white">
                                        <FontAwesomeIcon icon={faMicrochip} className="me-2" />
                                        <span className="fw-bold">Cihazınızı Seçin</span>
                                    </div>
                                    <div className="card-body">
                                        {userDevices.length > 0 ? (
                                            <div className="mb-3">
                                                <label className="form-label small fw-bold">Kredi Eklenecek Cihaz:</label>
                                                <select 
                                                    className="form-select"
                                                    value={selectedDeviceForCredit}
                                                    onChange={(e) => setSelectedDeviceForCredit(e.target.value)}
                                                >
                                                    <option value="">Cihaz seçiniz...</option>
                                                    {userDevices.map((device) => (
                                                        <option key={device.ID} value={device.CihazID}>
                                                            {device.CihazID} - {device.durum} - {device.kredi_gun}
                                                        </option>
                                                    ))}
                                                </select>
                                            </div>
                                        ) : (
                                            <div className="alert alert-warning">
                                                <FontAwesomeIcon icon={faMicrochip} className="me-2" />
                                                Size ait cihaz bulunamadı. Lütfen önce bir cihaz edinin.
                                            </div>
                                        )}

                                        {/* Seçili Paket ve Cihaz Özeti */}
                                        {selectedPackage && selectedDeviceForCredit && (
                                            <div className="card bg-light border-0 mb-3">
                                                <div className="card-body">
                                                    <h6 className="card-title text-success">
                                                        <FontAwesomeIcon icon={faCoins} className="me-2" />
                                                        Ödeme Özeti
                                                    </h6>
                                                    <hr />
                                                    <div className="row">
                                                        <div className="col-6">
                                                            <small className="text-muted">Paket:</small>
                                                            <div className="fw-bold">{selectedPackage.days} Gün</div>
                                                        </div>
                                                        <div className="col-6">
                                                            <small className="text-muted">Cihaz:</small>
                                                            <div className="fw-bold">{selectedDeviceForCredit}</div>
                                                        </div>
                                                        <div className="col-6 mt-2">
                                                            <small className="text-muted">Euro:</small>
                                                            <div className="fw-bold text-primary">€{selectedPackage.priceEUR}</div>
                                                        </div>
                                                        <div className="col-6 mt-2">
                                                            <small className="text-muted">Türk Lirası:</small>
                                                            <div className="fw-bold text-success">₺{(selectedPackage.priceEUR * exchangeRate).toFixed(2)}</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                        {/* Ödeme Butonu */}
                                        {selectedPackage && selectedDeviceForCredit && (
                                            <button 
                                                className="btn btn-success w-100 py-3 fw-bold"
                                                onClick={() => setShowParamPOS(true)}
                                                disabled={processingPayment}
                                            >
                                                <FontAwesomeIcon icon={faCreditCard} className="me-2" />
                                                {processingPayment ? 'İşlem Yapılıyor...' : 'Güvenli Ödeme Yap'}
                                            </button>
                                        )}
                                    </div>
                                </div>
                            </div>

                            {/* Kullanım Durumu ve Geçmiş */}
                            <div className="col-12 mb-4">
                                <div className="card border-0 shadow-sm">
                                    <div className="card-header bg-info text-white">
                                        <FontAwesomeIcon icon={faClock} className="me-2" />
                                        <span className="fw-bold">Hesap Durumu</span>
                                        <button
                                            className="btn btn-sm btn-outline-light ms-auto"
                                            onClick={() => setShowHistory(!showHistory)}
                                        >
                                            <FontAwesomeIcon icon={faHistory} className="me-1" />
                                            {showHistory ? 'Gizle' : 'Geçmiş'}
                                        </button>
                                    </div>
                                    <div className="card-body">
                                        <div className="row">
                                            <div className="col-md-3">
                                                <div className="text-center">
                                                    <div className="display-6 text-primary fw-bold">
                                                        {remainingUsage?.remainingDays || 0}
                                                    </div>
                                                    <small className="text-muted">Kalan Gün</small>
                                                </div>
                                            </div>
                                            <div className="col-md-3">
                                                <div className="text-center">
                                                    <div className="display-6 text-success fw-bold">
                                                        {userDevices.length}
                                                    </div>
                                                    <small className="text-muted">Aktif Cihaz</small>
                                                </div>
                                            </div>
                                            <div className="col-md-6">
                                                <h6 className="text-muted">Son Kullanım:</h6>
                                                <p className="mb-0">
                                                    {remainingUsage?.expiryDate ? 
                                                        new Date(remainingUsage.expiryDate).toLocaleDateString('tr-TR') : 
                                                        'Belirtilmemiş'
                                                    }
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Kredi Kartı Formu */}
                            <div className="col-12 mb-4">
                                <div className="card border-0 shadow-sm">
                                    <div className="card-header bg-warning text-dark">
                                        <FontAwesomeIcon icon={faCreditCard} className="me-2" />
                                        <span className="fw-bold">Kredi Kartı ile Ödeme</span>
                                    </div>
                                    <div className="card-body">
                                        {paymentSuccess ? (
                                            <div className="alert alert-success">
                                                <h5 className="alert-heading">
                                                    <FontAwesomeIcon icon={faCoins} className="me-2" />
                                                    Ödeme Başarılı!
                                                </h5>
                                                <p>Ödemeniz başarıyla gerçekleştirildi. Seçtiğiniz cihaza kredi eklenmiştir.</p>
                                                <hr />
                                                <button
                                                    className="btn btn-success"
                                                    onClick={() => setPaymentSuccess(false)}
                                                >
                                                    Yeni Ödeme Yap
                                                </button>
                                            </div>
                                        ) : selectedPackage && selectedDeviceForCredit ? (
                                            <form onSubmit={handleSubmit}>
                                                {paymentError && (
                                                    <div className="alert alert-danger mb-3">
                                                        {paymentError}
                                                    </div>
                                                )}

                                                <div className="row g-3">
                                                    <div className="col-md-6">
                                                        <label htmlFor="cardHolder" className="form-label">Kart Sahibinin Adı Soyadı</label>
                                                        <div className="input-group">
                                                            <span className="input-group-text">
                                                                <FontAwesomeIcon icon={faUser} />
                                                            </span>
                                                            <input
                                                                type="text"
                                                                className="form-control"
                                                                id="cardHolder"
                                                                name="cardHolder"
                                                                value={cardForm.cardHolder}
                                                                onChange={handleCardFormChange}
                                                                placeholder="Kart üzerindeki isim"
                                                                required
                                                            />
                                                        </div>
                                                    </div>

                                                    <div className="col-md-6">
                                                        <label htmlFor="cardNumber" className="form-label">Kart Numarası</label>
                                                        <div className="input-group">
                                                            <span className="input-group-text">
                                                                <FontAwesomeIcon icon={faCreditCard} />
                                                            </span>
                                                            <input
                                                                type="text"
                                                                className="form-control"
                                                                id="cardNumber"
                                                                name="cardNumber"
                                                                value={cardForm.cardNumber}
                                                                onChange={handleCardFormChange}
                                                                placeholder="1234 5678 9012 3456"
                                                                maxLength="19"
                                                                required
                                                            />
                                                        </div>
                                                    </div>

                                                    <div className="col-md-4">
                                                        <label htmlFor="expiryDate" className="form-label">Son Kullanma Tarihi</label>
                                                        <div className="input-group">
                                                            <span className="input-group-text">
                                                                <FontAwesomeIcon icon={faCalendarAlt} />
                                                            </span>
                                                            <input
                                                                type="text"
                                                                className="form-control"
                                                                id="expiryDate"
                                                                name="expiryDate"
                                                                value={cardForm.expiryDate}
                                                                onChange={handleCardFormChange}
                                                                placeholder="AA/YY"
                                                                maxLength="5"
                                                                required
                                                            />
                                                        </div>
                                                    </div>

                                                    <div className="col-md-4">
                                                        <label htmlFor="cvv" className="form-label">CVV</label>
                                                        <div className="input-group">
                                                            <span className="input-group-text">
                                                                <FontAwesomeIcon icon={faLock} />
                                                            </span>
                                                            <input
                                                                type="text"
                                                                className="form-control"
                                                                id="cvv"
                                                                name="cvv"
                                                                value={cardForm.cvv}
                                                                onChange={handleCardFormChange}
                                                                placeholder="123"
                                                                maxLength="4"
                                                                required
                                                            />
                                                        </div>
                                                    </div>

                                                    <div className="col-md-4">
                                                        <label className="form-label">Toplam Tutar</label>
                                                        <div className="input-group">
                                                            <span className="input-group-text bg-success text-white">
                                                                <FontAwesomeIcon icon={faTurkishLiraSign} />
                                                            </span>
                                                            <input
                                                                type="text"
                                                                className="form-control bg-light"
                                                                value={`₺${(selectedPackage.priceEUR * exchangeRate).toFixed(2)}`}
                                                                readOnly
                                                            />
                                                        </div>
                                                    </div>

                                                    <div className="col-12">
                                                        <button
                                                            type="submit"
                                                            className="btn btn-success btn-lg w-100"
                                                            disabled={processingPayment}
                                                        >
                                                            {processingPayment ? (
                                                                <>
                                                                    <span className="spinner-border spinner-border-sm me-2" role="status"></span>
                                                                    İşlem Yapılıyor...
                                                                </>
                                                            ) : (
                                                                <>
                                                                    <FontAwesomeIcon icon={faCreditCard} className="me-2" />
                                                                    Ödemeyi Tamamla
                                                                </>
                                                            )}
                                                        </button>
                                                    </div>
                                                </div>
                                            </form>
                                        ) : (
                                            <div className="alert alert-info">
                                                <FontAwesomeIcon icon={faTag} className="me-2" />
                                                Ödeme yapmak için lütfen önce bir paket ve cihaz seçiniz.
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>

                        </div>
                    )}

                    {/* ParamPOS Modal */}
                    {showParamPOS && selectedPackage && selectedDeviceForCredit && (
                        <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
                            <div className="modal-dialog modal-lg">
                                <div className="modal-content">
                                    <div className="modal-header">
                                        <h5 className="modal-title">Güvenli Ödeme</h5>
                                        <button
                                            type="button"
                                            className="btn-close"
                                            onClick={() => setShowParamPOS(false)}
                                        ></button>
                                    </div>
                                    <div className="modal-body">
                                        <ParamPOSForm 
                                            amount={selectedPackage.priceEUR * exchangeRate}
                                            currency="TRY"
                                            packageInfo={selectedPackage}
                                            deviceId={selectedDeviceForCredit}
                                            onSuccess={handlePaymentSuccess}
                                            onError={handlePaymentError}
                                            disabled={processingPayment}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </main>
                <Footer />
            </div>
        </div>
    );
};

export default Payment;

