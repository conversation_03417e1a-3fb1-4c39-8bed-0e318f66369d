{"version": 3, "file": "static/js/95.c8a6cca8.chunk.js", "mappings": "+JASA,MAwGA,EAxGgBA,KACZ,MAAMC,GAAWC,EAAAA,EAAAA,MAaXC,EAAuB,UAVTC,MAChB,IAAK,IAADC,EAAAC,EACA,MAAMC,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C,OAAW,OAAJJ,QAAI,IAAJA,GAAU,QAANF,EAAJE,EAAMA,YAAI,IAAAF,OAAN,EAAJA,EAAYO,QAAY,OAAJL,QAAI,IAAJA,GAAU,QAAND,EAAJC,EAAMA,YAAI,IAAAD,OAAN,EAAJA,EAAYO,QAAS,MACpD,CAAE,MAAAC,GACE,MAAO,MACX,GAGaV,GAGjB,OACIW,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC1DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6BAA6BG,SAAS,KAAKC,GAAG,UAAU,kBAAgB,UAASH,SAAA,EAC5FC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,EAC7BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,kBAAkBI,GAAG,UAASH,SAAC,gBAC7CF,EAAAA,EAAAA,KAAA,UAAQM,KAAK,SAASL,UAAU,YAAY,kBAAgB,YAAY,iBAAe,WAAW,aAAW,cAEjHE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mEAAkEC,SAAA,EAC7EF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,kCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,MAAtBtB,EAASuB,SAAmB,SAAW,IAAMC,GAAG,IAAGR,SAAA,EAC5GF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMC,EAAAA,MAAW,0BAG1Cb,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,SAAtBtB,EAASuB,SAAsB,SAAW,IAAMC,GAAG,OAAMR,SAAA,EAClHF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAME,EAAAA,MAAgB,mCAG/Cd,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,sBAAtBtB,EAASuB,SAAmC,SAAW,IAAMC,GAAG,oBAAmBR,SAAA,EAC5IF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMG,EAAAA,MAAY,8BAG3Cf,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMI,EAAAA,MAAmB,sCAKtDhB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wBACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,CAEpCd,IACGY,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMK,EAAAA,MAAa,0BAIhDjB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,mBAAtBtB,EAASuB,SAAgC,SAAW,IAAMC,GAAG,iBAAgBR,SAAA,EACtIF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMM,EAAAA,MAAU,uBAK7ClB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,uCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMO,EAAAA,MAAU,qCAGzCnB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMQ,EAAAA,MAAgB,6CAKnDpB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMS,EAAAA,MAAgB,qBAG/CrB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,cAAtBtB,EAASuB,SAA2B,SAAW,IAAMC,GAAG,YAAWR,SAAA,EAC5HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMU,EAAAA,MAAe,4BAKlDtB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wBAG1I,C,sDC3Gd,MAQA,EAReqB,KAEPvB,EAAAA,EAAAA,KAAA,UAAQC,UAAU,kBAAiBC,UAC/BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,cAAaC,SAAC,iD,qGCEvC,MA6GA,EA7GesB,KACX,MAAMC,GAAWC,EAAAA,EAAAA,OACVC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,YAClCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAGvCG,EAAAA,EAAAA,YAAU,KACgBC,WAClB,IAAK,IAADC,EAAAC,EAAAC,EAAAC,EAEA,MAAMC,EAAa7C,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C2C,GAAmB,OAAVD,QAAU,IAAVA,GAAgB,QAANJ,EAAVI,EAAY9C,YAAI,IAAA0C,OAAN,EAAVA,EAAkBM,cAAwB,OAAVF,QAAU,IAAVA,GAAgB,QAANH,EAAVG,EAAY9C,YAAI,IAAA2C,OAAN,EAAVA,EAAkB9B,IAC3DsB,GAAqB,OAAVW,QAAU,IAAVA,GAAgB,QAANF,EAAVE,EAAY9C,YAAI,IAAA4C,OAAN,EAAVA,EAAkBK,QAAkB,OAAVH,QAAU,IAAVA,GAAgB,QAAND,EAAVC,EAAY9C,YAAI,IAAA6C,OAAN,EAAVA,EAAkBK,aAE7D,IAAKH,EAID,OAHAI,QAAQC,KAAK,kCACbhB,EAAY,gBACZG,GAAW,GAKf,GAAIJ,EAGA,OAFAC,EAAYD,QACZI,GAAW,GAKf,IACI,MAAMc,QAAiBC,EAAAA,GAAiBC,aAAaR,GAGjDM,GAAYA,EAASH,cACrBd,EAAYiB,EAASH,aAGP,OAAVJ,QAAU,IAAVA,GAAAA,EAAY9C,OACZ8C,EAAW9C,KAAKiD,KAAOI,EAASH,YAChC/C,aAAaqD,QAAQ,OAAQvD,KAAKwD,UAAUX,KAGxD,CAAE,MAAOY,GACLP,QAAQC,KAAK,gGAEbhB,EAAYD,GAAY,sBAC5B,CACJ,CAAE,MAAOwB,GACLR,QAAQQ,MAAM,0DAAuCA,GAErDvB,EAAY,sBAChB,CAAC,QACGG,GAAW,EACf,GAGJqB,EAAe,GAChB,IAWH,OACIjD,EAAAA,EAAAA,MAAA,UAAQF,UAAU,0DAA0D,gBAAc,OAAMC,SAAA,EAC5FF,EAAAA,EAAAA,KAACO,EAAAA,GAAI,CAACN,UAAU,gEAAgES,GAAG,IAAGR,UAClFF,EAAAA,EAAAA,KAAA,OAAKqD,I,60RAAeC,IAAI,aAAaC,OAAO,UAEhDpD,EAAAA,EAAAA,MAAA,MAAIF,UAAU,yCAAwCC,SAAA,EAClDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8CAA6CC,UACvDC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,aAAYC,SAAA,EACxBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMO,EAAAA,IAAQlB,UAAU,SACxC6B,EAAU,mBAAkBH,QAGrC3B,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4BAA2BC,UACrCF,EAAAA,EAAAA,KAAA,UACIwD,QAtBCC,KAEjB9D,aAAa+D,WAAW,QAGxBjC,EAAS,SAAS,EAkBFxB,UAAU,gBACV,iBAAe,UACf,oBAAkB,OAClB,gBAAc,+BAAYC,UAE1BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM+C,EAAAA,WAG/B3D,EAAAA,EAAAA,KAAA,MAAIC,UAAU,iCAAgCC,UAC1CF,EAAAA,EAAAA,KAAA,UACIC,UAAU,gBACVK,KAAK,SACL,iBAAe,YACf,iBAAe,WACf,gBAAc,UACd,gBAAc,QACd,aAAW,kBAAcJ,UAEzBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMgD,EAAAA,eAI9B,C,8JC9FjB,MA+bA,EA3awBC,KACpB,MAAOC,EAASC,IAAclC,EAAAA,EAAAA,UAAS,KAChCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCsB,EAAOa,IAAYnC,EAAAA,EAAAA,UAAS,OAC5BoC,EAAiBC,IAAsBrC,EAAAA,EAAAA,UAAS,IAAIsC,KAGrDC,EAA0BC,IAC5B,IAAKA,EACD,MAAO,CAAEC,KAAM,EAAGC,MAAO,EAAGC,WAAW,EAAMC,YAAa,YAG9D,MAAMC,EAAM,IAAIC,KAEVC,EADU,IAAID,KAAKN,GACAK,EAEzB,GAAIE,GAAU,EACV,MAAO,CAAEN,KAAM,EAAGC,MAAO,EAAGC,WAAW,EAAMC,YAAa,YAG9D,MAAMI,EAAWC,KAAKC,MAAMH,EAAM,OAC5BI,EAAYF,KAAKC,MAAOH,EAAM,MAAwB,MAE5D,IAAIH,EAAc,GAYlB,OAXII,EAAW,GACXJ,EAAW,GAAAjE,OAAMqE,EAAQ,WACrBG,EAAY,IACZP,GAAW,IAAAjE,OAAQwE,EAAS,WAGhCP,EADOO,EAAY,EACR,GAAAxE,OAAMwE,EAAS,SAEZ,eAGX,CACHV,KAAMO,EACNN,MAAOS,EACPR,WAAW,EACXC,cACAQ,UAAWJ,EAAW,EACzB,EAICK,EAAkBC,GACfA,EACDA,GAAS,GAAWC,EAAAA,IACpBD,GAAS,GAAWE,EAAAA,IACpBF,GAAS,GAAWG,EAAAA,IACpBH,GAAS,GAAWI,EAAAA,IACjBC,EAAAA,GALYJ,EAAAA,IA6BjBK,EAAuBxD,UACzB,IACIF,GAAW,GACXiC,EAAS,MAET,MAAM0B,QAAiBC,EAAAA,GAAuBC,qBAE1CF,EAASG,QACT9B,EAAW2B,EAASI,MAEpB9B,EAAS,4CAEjB,CAAE,MAAO+B,GACLpD,QAAQQ,MAAM,6CAAsC4C,GACpD/B,EAAS,8CAAiC+B,EAAIC,QAClD,CAAC,QACGjE,GAAW,EACf,GAOJ,OAJAC,EAAAA,EAAAA,YAAU,KACNyD,GAAsB,GACvB,KAGCtF,EAAAA,EAAAA,MAAA8F,EAAAA,SAAA,CAAA/F,SAAA,EACIF,EAAAA,EAAAA,KAAA,SAAAE,SAzHA,+SA0HAF,EAAAA,EAAAA,KAACwB,EAAAA,EAAM,KACPxB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAChBF,EAAAA,EAAAA,KAACf,EAAAA,EAAO,KAERkB,EAAAA,EAAAA,MAAA,QAAMF,UAAU,iFAAgFC,SAAA,EAC5FF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0GAAyGC,UACpHC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,KAAIC,SAAA,EACdF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMK,EAAAA,IAAWhB,UAAU,SAAS,yBAErDD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,0BAAyBC,SAAE4D,EAAQoC,cAI1DpE,GACG9B,EAAAA,EAAAA,KAACmG,EAAAA,EAAc,CAACC,KAAK,KAAKC,QAAQ,UAAUL,QAAQ,yCAAiCM,UAAU,IAC/FnD,GACAnD,EAAAA,EAAAA,KAACuG,EAAAA,EAAY,CACTP,QAAS7C,EACTkD,QAAQ,SACRG,MAAM,8BACNC,aAAa,EACbC,UAAWA,IAAM1C,EAAS,IAAI9D,UAE9BF,EAAAA,EAAAA,KAAA,UAAQC,UAAU,8BAA8BuD,QAASiC,EAAqBvF,SAAC,oBAKnFF,EAAAA,EAAAA,KAAAiG,EAAAA,SAAA,CAAA/F,SACwB,IAAnB4D,EAAQoC,QACL/F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,EAC7BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMK,EAAAA,IAAWhB,UAAU,2BAC5CD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,aAAYC,SAAC,wCAC3BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,aAAYC,SAAC,iEAG9BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACjBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,cAAaC,UACxBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,OAAMC,SAAC,qCAEzBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACtBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,UAC7BC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kCAAiCC,SAAA,EAC9CF,EAAAA,EAAAA,KAAA,SAAAE,UACIC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACZF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,qBACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,WACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,kBACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,uBACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,mCACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,qBACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,6BAGIF,EAAAA,EAAAA,KAAA,SAAAE,SACX4D,EAAQ6C,KAAI,CAACC,EAAQC,KAClB1G,SAAAA,EAAAA,MAAC2G,EAAAA,SAAc,CAAA5G,SAAA,EACXC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,aAAYC,SAAA,EACtBF,EAAAA,EAAAA,KAAA,MAAAE,UACIC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qBAAoBC,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,4BAA2BC,SAAE0G,EAAOG,YAClDH,EAAOI,WACJhH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,SAC5B0G,EAAOI,WAGfJ,EAAOK,QACJ9G,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,EAC7BF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,WAAe,IAAE0G,EAAOK,eAKhDjH,EAAAA,EAAAA,KAAA,MAAAE,UACIF,EAAAA,EAAAA,KAAA,QAAMC,UAAS,SAAAO,OACM,UAAjBoG,EAAOM,MAAoB,aACV,UAAjBN,EAAOM,MAAoB,eACV,YAAjBN,EAAOM,MAAsB,aAAe,gBAC7ChH,SACE0G,EAAOM,OAAS,kBAGzBlH,EAAAA,EAAAA,KAAA,MAAAE,SACK0G,EAAOO,aACJhH,EAAAA,EAAAA,MAAA,QAAMF,WApIrCkF,EAoIgEyB,EAAOO,YAnIvFhC,EACDA,GAAS,GAAW,cACpBA,GAAS,GAAW,eACpBA,GAAS,GAAW,YACjB,eAJY,cAmIsFjF,SAAA,EACjDF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CACZC,KAAMsE,EAAe0B,EAAOO,aAC5BlH,UAAU,SAEb2G,EAAOO,YAAY,QAGxBhH,EAAAA,EAAAA,MAAA,QAAMF,UAAU,aAAYC,SAAA,EACxBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMwE,EAAAA,IAAgBnF,UAAU,SAAS,mBAKtED,EAAAA,EAAAA,KAAA,MAAAE,SACK0G,EAAOQ,aACJjH,EAAAA,EAAAA,MAAA,QAAAD,SAAA,EACIF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMyG,EAAAA,IAAepH,UAAU,oBAC/C2G,EAAOQ,gBAGZjH,EAAAA,EAAAA,MAAA,QAAMF,UAAU,aAAYC,SAAA,EACxBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMyG,EAAAA,IAAepH,UAAU,SAAS,4CAKrED,EAAAA,EAAAA,KAAA,MAAAE,SACK,MACG,MAAMoH,EAAYlD,EAAuBwC,EAAOvC,YAChD,OACIlE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4BAA2BC,SAAA,EACtCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CACZC,KAAM0G,EAAUrC,UAAYsC,EAAAA,IAAwBC,EAAAA,GACpDvH,UAAS,QAAAO,OACL8G,EAAU9C,WAAa8C,EAAUrC,UAC3B,cACA,mBAGdjF,EAAAA,EAAAA,KAAA,QAAMC,UACFqH,EAAU9C,WAAa8C,EAAUrC,UAC3B,sBACA,eACT/E,SACIoH,EAAU7C,gBAI1B,EArBA,MAuBLzE,EAAAA,EAAAA,KAAA,MAAAE,SACK0G,EAAOa,eACJtH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qBAAoBC,SAAA,EAC/BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0BAAyBC,SAAA,EACpCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM8G,EAAAA,IAAgBzH,UAAU,SAChD2G,EAAOa,cAAcE,eAE1BxH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,CAC5B0G,EAAOa,cAAcG,cAAc,WAAIhB,EAAOa,cAAcI,kBAEjE1H,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,CAC5B0G,EAAOa,cAAcK,eACrBlB,EAAOa,cAAcM,aAAW,MAAAvH,OAAUoG,EAAOa,cAAcM,oBAIxE5H,EAAAA,EAAAA,MAAA,QAAMF,UAAU,aAAYC,SAAA,EACxBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM8G,EAAAA,IAAgBzH,UAAU,SAAS,qBAKtED,EAAAA,EAAAA,KAAA,MAAAE,UACIC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qBAAoBC,SAAA,EAC/BC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CACDG,GAAG,WACHT,UAAU,sCACVuG,MAAM,wCAA0BtG,SAAA,EAEhCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMS,EAAAA,IAAcpB,UAAU,SAAS,4CAG5DE,EAAAA,EAAAA,MAAA,UACIF,UAAU,8BACVuD,QAASA,IAhNtCuD,KAC3B,MAAMiB,EAAc,IAAI7D,IAAIF,GACxB+D,EAAYC,IAAIlB,GAChBiB,EAAYE,OAAOnB,GAEnBiB,EAAYG,IAAIpB,GAEpB7C,EAAmB8D,EAAY,EAyMwCI,CAAsBxB,EAAOG,WAC5CP,MAAOvC,EAAgBgE,IAAIrB,EAAOG,WAAa,uBAAoB,2BAAmB7G,SAAA,EAEtFF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CACZC,KAAMqD,EAAgBgE,IAAIrB,EAAOG,WAAasB,EAAAA,IAAaC,EAAAA,IAC3DrI,UAAU,SAEbgE,EAAgBgE,IAAIrB,EAAOG,WAAa,QAAU,mBAKlE9C,EAAgBgE,IAAIrB,EAAOG,aACxB/G,EAAAA,EAAAA,KAAA,MAAIC,UAAU,qBAAoBC,UAC9BF,EAAAA,EAAAA,KAAA,MAAIuI,QAAQ,IAAItI,UAAU,WAAUC,UAChCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,MAAKC,UAChBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAChBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACrBC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,oBAAmBC,SAAA,EAC7BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM4H,EAAAA,IAAcvI,UAAU,SAAS,yBAG5DE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAChBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYC,SAAA,EACvBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4BAA2BC,SAAA,EACtCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM0E,EAAAA,IAAerF,UAAU,uBAChDD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,UAASC,SAAC,sBAE9BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,SAChB0G,EAAO6B,YAAW,GAAAjI,OAAMoG,EAAO6B,YAAW,OAAAjI,OAAMoG,EAAOO,YAAW,MAAO,iBAGlFhH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYC,SAAA,EACvBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4BAA2BC,SAAA,EACtCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM8H,EAAAA,IAAQzI,UAAU,oBACzCD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,UAASC,SAAC,aAE9BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,SAChB0G,EAAO+B,IAAG,GAAAnI,OAAMoG,EAAO+B,IAAG,KAAM,iBAGzCxI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYC,SAAA,EACvBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4BAA2BC,SAAA,EACtCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMgI,EAAAA,IAAa3I,UAAU,uBAC9CD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,UAASC,SAAC,wBAE9BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,SAChB0G,EAAOiC,KAAOjC,EAAOiC,KAAO,iBAGrC1I,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYC,SAAA,EACvBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4BAA2BC,SAAA,EACtCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMkI,EAAAA,IAAgB7I,UAAU,uBACjDD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,UAASC,SAAC,eAE9BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,SAChB0G,EAAOmC,OAASnC,EAAOmC,MAAMC,OAASpC,EAAOmC,MAAME,QAChD9I,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACIC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,CAAK,UAAQ0G,EAAOmC,MAAMC,UAC1B7I,EAAAA,EAAAA,MAAA,OAAAD,SAAA,CAAK,WAAS0G,EAAOmC,MAAME,UAC1BrC,EAAOmC,MAAMG,YAAa/I,EAAAA,EAAAA,MAAA,OAAAD,SAAA,CAAK,iBAAY0G,EAAOmC,MAAMG,UAAU,UAEvE,0BAGZ/I,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYC,SAAA,EACvBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4BAA2BC,SAAA,EACtCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM4G,EAAAA,GAASvH,UAAU,uBAC1CD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,UAASC,SAAC,qBAE9BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,SAChB,MACG,MAAMoH,EAAYlD,EAAuBwC,EAAOvC,YAChD,OACIlE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACIF,EAAAA,EAAAA,KAAA,OAAKC,UACDqH,EAAU9C,WAAa8C,EAAUrC,UAC3B,sBACA,eACT/E,SACIoH,EAAU7C,cAEdmC,EAAOvC,aACJrE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,SAC5B,IAAIyE,KAAKiC,EAAOvC,YAAY8E,eAAe,aAK/D,EAlBA,eAuBjBhJ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACrBC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,oBAAmBC,SAAA,EAC7BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM8G,EAAAA,IAAgBzH,UAAU,SAAS,iCAG7D2G,EAAOa,eACJtH,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACIC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACjBF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,iBAAqB,IAAE0G,EAAOa,cAAcE,eAExDxH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACjBF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,6BAAe,IAAE0G,EAAOa,cAAcG,kBAElDzH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACjBF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,qBAAe,IAAE0G,EAAOa,cAAcI,kBAElD1H,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACjBF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,gBAAc,IAAE0G,EAAOa,cAAc2B,gBAEjDjJ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACjBF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,eAAmB,IAAE0G,EAAOa,cAAc4B,cAEtDlJ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACjBF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,4BAAmB,IAAE0G,EAAOa,cAAcK,kBAErDlB,EAAOa,cAAcM,cAClB5H,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACjBF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,gBAAe,IAAE0G,EAAOa,cAAcM,mBAK1D/H,EAAAA,EAAAA,KAAA,OAAKC,UAAU,aAAYC,SAAC,sFA/OvC2G,GAxGpC1B,KAkWY,sBAWrBnF,EAAAA,EAAAA,KAACuB,EAAAA,EAAM,WAGhB,C", "sources": ["components/Sidebar.js", "components/Footer.js", "components/Header.js", "pages/InactiveDevices.js"], "sourcesContent": ["import React from 'react';\r\nimport { Link, useLocation } from 'react-router-dom';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport {\r\n    faDolly, faFolderPlus, faAnchor, faFlag<PERSON>he<PERSON>ed,\r\n    faUser, faStreetView, faCreditCard, faFileLines,\r\n    faBell, faDesktop\r\n} from '@fortawesome/free-solid-svg-icons';\r\n\r\nconst Sidebar = () => {\r\n    const location = useLocation();\r\n    \r\n    // Get user role from localStorage\r\n    const getUserRole = () => {\r\n        try {\r\n            const user = JSON.parse(localStorage.getItem('user'));\r\n            return user?.user?.role || user?.user?.gorev || 'user';\r\n        } catch {\r\n            return 'user';\r\n        }\r\n    };\r\n    \r\n    const userRole = getUserRole();\r\n    const isAdmin = userRole === 'admin';\r\n\r\n    return (\r\n        <div className=\"sidebar col-md-4 col-lg-2 p-0 bg-light-subtle\">\r\n            <div className=\"offcanvas-md offcanvas-end\" tabIndex=\"-1\" id=\"yanMenu\" aria-labelledby=\"yanMenu\">\r\n                <div className=\"offcanvas-header\">\r\n                    <h5 className=\"offcanvas-title\" id=\"yanMenu\">MGZ24 Gold</h5>\r\n                    <button type=\"button\" className=\"btn-close\" data-bs-dismiss=\"offcanvas\" data-bs-target=\"#yanMenu\" aria-label=\"Kapat\"></button>\r\n                </div>\r\n                <div className=\"offcanvas-body d-md-flex flex-column p-0 pt-lg-3 overflow-y-auto\">\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Sevkiyat İşlemleri</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/' ? 'active' : ''}`} to=\"/\">\r\n                                <FontAwesomeIcon icon={faDolly} />Aktif Sevkiyatlar\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/add' ? 'active' : ''}`} to=\"/add\">\r\n                                <FontAwesomeIcon icon={faFolderPlus} />Yeni Sevkiyat Oluştur\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/inactive-devices' ? 'active' : ''}`} to=\"/inactive-devices\">\r\n                                <FontAwesomeIcon icon={faAnchor} />İnaktif Cihazlar\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/history' ? 'active' : ''}`} to=\"/history\">\r\n                                <FontAwesomeIcon icon={faFlagCheckered} />Geçmiş Sevkiyatlar\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Sistem Yönetimi</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        {/* Cihaz Yönetimi - Sadece admin kullanıcılarına göster */}\r\n                        {isAdmin && (\r\n                            <li className=\"nav-item\">\r\n                                <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/devices' ? 'active' : ''}`} to=\"/devices\">\r\n                                    <FontAwesomeIcon icon={faDesktop} />Cihaz Yönetimi\r\n                                </Link>\r\n                            </li>\r\n                        )}\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/notifications' ? 'active' : ''}`} to=\"/notifications\">\r\n                                <FontAwesomeIcon icon={faBell} />Bildirimler\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Kullanıcı Ayarları</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/profile' ? 'active' : ''}`} to=\"/profile\">\r\n                                <FontAwesomeIcon icon={faUser} />Kullanıcı Profilim\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/viewers' ? 'active' : ''}`} to=\"/viewers\">\r\n                                <FontAwesomeIcon icon={faStreetView} />İzleyici İşlemleri\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Ödeme ve Yapılandırma</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/payment' ? 'active' : ''}`} to=\"/payment\">\r\n                                <FontAwesomeIcon icon={faCreditCard} />Ödeme Yap\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/invoices' ? 'active' : ''}`} to=\"/invoices\">\r\n                                <FontAwesomeIcon icon={faFileLines} />Faturalarım\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-body-secondary text-uppercase\">Kredi Durumu</h6>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Sidebar;", "import React from 'react';\r\n\r\nconst Footer = () => {\r\n    return (\r\n        <footer className=\"py-5 border-top\">\r\n            <p className=\"text-center\">&copy;2025 Inkatech Ölçüm Sistemleri</p>\r\n        </footer>\r\n    );\r\n};\r\n\r\nexport default Footer; ", "import React, { useState, useEffect } from 'react';\r\nimport { Link, useNavigate } from 'react-router-dom';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { faRightFromBracket, faBars, faUser } from '@fortawesome/free-solid-svg-icons';\r\nimport logoDark from '../assets/img/logo.png';\r\nimport { kullaniciService } from '../api/dbService';\r\n\r\nconst Header = () => {\r\n    const navigate = useNavigate();\r\n    const [userName, setUserName] = useState('Misafir');\r\n    const [loading, setLoading] = useState(true);\r\n\r\n    // Kullanıcı bilgilerini veritabanından al\r\n    useEffect(() => {\r\n        const fetchUserData = async () => {\r\n            try {\r\n                // Local storage'dan kullanıcı bilgisini al\r\n                const storedUser = JSON.parse(localStorage.getItem('user'));\r\n                const userId = storedUser?.user?.musteri_ID || storedUser?.user?.id;\r\n                const userName = storedUser?.user?.name || storedUser?.user?.musteri_adi;\r\n\r\n                if (!userId) {\r\n                    console.warn('Oturum bilgisi bulunamadı');\r\n                    setUserName('Misafir');\r\n                    setLoading(false);\r\n                    return;\r\n                }\r\n\r\n                // Eğer localStorage'da isim varsa direkt kullan\r\n                if (userName) {\r\n                    setUserName(userName);\r\n                    setLoading(false);\r\n                    return;\r\n                }\r\n\r\n                // API'den kullanıcı bilgilerini getir (backend çalışıyorsa)\r\n                try {\r\n                    const userData = await kullaniciService.getKullanici(userId);\r\n\r\n                    // musteri_adi alanını kullan\r\n                    if (userData && userData.musteri_adi) {\r\n                        setUserName(userData.musteri_adi);\r\n\r\n                        // localStorage'ı güncelle\r\n                        if (storedUser?.user) {\r\n                            storedUser.user.name = userData.musteri_adi;\r\n                            localStorage.setItem('user', JSON.stringify(storedUser));\r\n                        }\r\n                    }\r\n                } catch (apiError) {\r\n                    console.warn('API\\'den kullanıcı bilgisi alınamadı, localStorage kullanılıyor');\r\n                    // API hatası durumunda localStorage'daki adı kullan\r\n                    setUserName(userName || 'Kullanıcı');\r\n                }\r\n            } catch (error) {\r\n                console.error('Kullanıcı bilgileri alınırken hata:', error);\r\n                // Hata durumunda varsayılan isim kullan\r\n                setUserName('Kullanıcı');\r\n            } finally {\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        fetchUserData();\r\n    }, []);\r\n\r\n    // Çıkış işlemi\r\n    const handleLogout = () => {\r\n        // Local storage'dan kullanıcı bilgilerini temizle\r\n        localStorage.removeItem('user');\r\n\r\n        // Login sayfasına yönlendir\r\n        navigate('/login');\r\n    };\r\n\r\n    return (\r\n        <header className=\"navbar sticky-top bg-darkblue flex-md-nowrap p-0 shadow\" data-bs-theme=\"dark\">\r\n            <Link className=\"navbar-brand col-md-4 col-lg-2 me-0 px-3 py-2 fs-6 text-white\" to=\"/\">\r\n                <img src={logoDark} alt=\"MGZ24 Logo\" height=\"40\" />\r\n            </Link>\r\n            <ul className=\"navbar-nav flex-row align-items-center\">\r\n                <li className=\"nav-item text-nowrap d-none d-md-block me-3\">\r\n                    <span className=\"text-white\">\r\n                        <FontAwesomeIcon icon={faUser} className=\"me-2\" />\r\n                        {loading ? 'Yükleniyor...' : userName}\r\n                    </span>\r\n                </li>\r\n                <li className=\"nav-item text-nowrap px-1\">\r\n                    <button\r\n                        onClick={handleLogout}\r\n                        className=\"nav-link px-3\"\r\n                        data-bs-toggle=\"tooltip\"\r\n                        data-bs-placement=\"left\"\r\n                        data-bs-title=\"Çıkış yap!\"\r\n                    >\r\n                        <FontAwesomeIcon icon={faRightFromBracket} />\r\n                    </button>\r\n                </li>\r\n                <li className=\"nav-item text-nowrap d-md-none\">\r\n                    <button\r\n                        className=\"nav-link px-3\"\r\n                        type=\"button\"\r\n                        data-bs-toggle=\"offcanvas\"\r\n                        data-bs-target=\"#yanMenu\"\r\n                        aria-controls=\"yanMenu\"\r\n                        aria-expanded=\"false\"\r\n                        aria-label=\"menu aç/kapa\"\r\n                    >\r\n                        <FontAwesomeIcon icon={faBars} />\r\n                    </button>\r\n                </li>\r\n            </ul>\r\n        </header>\r\n    );\r\n};\r\n\r\nexport default Header; ", "import React, { useState, useEffect } from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport Header from '../components/Header';\r\nimport Sidebar from '../components/Sidebar';\r\nimport Footer from '../components/Footer';\r\nimport LoadingSpinner from '../components/LoadingSpinner';\r\nimport ErrorMessage from '../components/ErrorMessage';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport {\r\n    faDesktop, faBatteryQuarter, faBatteryHalf, faBatteryThreeQuarters, \r\n    faBatteryFull, faBatteryEmpty, faCalendarAlt, faThermometerHalf,\r\n    faTint, faLightbulb, faMapMarkerAlt, faShippingFast, faInfoCircle,\r\n    faEye, faEyeSlash, faChevronDown, faChevronUp, faExclamationTriangle,\r\n    faClock, faCreditCard\r\n} from '@fortawesome/free-solid-svg-icons';\r\nimport { inactiveDevicesService } from '../api/dbService';\r\n\r\n// CSS stilleri\r\nconst styles = `\r\n.device-row:hover {\r\n    background-color: #f8f9fa !important;\r\n}\r\n\r\n.device-details-row {\r\n    border-top: 2px solid #dee2e6;\r\n}\r\n\r\n.device-details-row td {\r\n    border-top: none !important;\r\n}\r\n\r\n.badge {\r\n    font-size: 0.75em;\r\n    text-transform: uppercase;\r\n    letter-spacing: 0.5px;\r\n}\r\n`;\r\n\r\nconst InactiveDevices = () => {\r\n    const [devices, setDevices] = useState([]);\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState(null);\r\n    const [expandedDevices, setExpandedDevices] = useState(new Set());\r\n\r\n    // Kontor sonu tarihinden kalan süreyi hesaplama\r\n    const calculateRemainingTime = (kontorSonu) => {\r\n        if (!kontorSonu) {\r\n            return { days: 0, hours: 0, isExpired: true, displayText: '0 gün' };\r\n        }\r\n\r\n        const now = new Date();\r\n        const endDate = new Date(kontorSonu);\r\n        const diffMs = endDate - now;\r\n\r\n        if (diffMs <= 0) {\r\n            return { days: 0, hours: 0, isExpired: true, displayText: '0 gün' };\r\n        }\r\n\r\n        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\r\n        const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));\r\n\r\n        let displayText = '';\r\n        if (diffDays > 0) {\r\n            displayText = `${diffDays} gün`;\r\n            if (diffHours > 0) {\r\n                displayText += ` ${diffHours} saat`;\r\n            }\r\n        } else if (diffHours > 0) {\r\n            displayText = `${diffHours} saat`;\r\n        } else {\r\n            displayText = '1 saatten az';\r\n        }\r\n\r\n        return {\r\n            days: diffDays,\r\n            hours: diffHours,\r\n            isExpired: false,\r\n            displayText,\r\n            isWarning: diffDays < 5\r\n        };\r\n    };\r\n\r\n    // Pil durumu ikonu göster\r\n    const getBatteryIcon = (level) => {\r\n        if (!level) return faBatteryEmpty;\r\n        if (level <= 10) return faBatteryEmpty;\r\n        if (level <= 25) return faBatteryQuarter;\r\n        if (level <= 50) return faBatteryHalf;\r\n        if (level <= 75) return faBatteryThreeQuarters;\r\n        return faBatteryFull;\r\n    };\r\n\r\n    // Pil durumu rengi\r\n    const getBatteryColor = (level) => {\r\n        if (!level) return 'text-muted';\r\n        if (level <= 10) return 'text-danger';\r\n        if (level <= 25) return 'text-warning';\r\n        if (level <= 50) return 'text-info';\r\n        return 'text-success';\r\n    };\r\n\r\n    // Cihaz detaylarını genişletme/daraltma\r\n    const toggleDeviceExpansion = (cihazKodu) => {\r\n        const newExpanded = new Set(expandedDevices);\r\n        if (newExpanded.has(cihazKodu)) {\r\n            newExpanded.delete(cihazKodu);\r\n        } else {\r\n            newExpanded.add(cihazKodu);\r\n        }\r\n        setExpandedDevices(newExpanded);\r\n    };\r\n\r\n    // İnaktif cihazları getir\r\n    const fetchInactiveDevices = async () => {\r\n        try {\r\n            setLoading(true);\r\n            setError(null);\r\n            \r\n            const response = await inactiveDevicesService.getInactiveDevices();\r\n            \r\n            if (response.success) {\r\n                setDevices(response.data);\r\n            } else {\r\n                setError('İnaktif cihazlar alınamadı');\r\n            }\r\n        } catch (err) {\r\n            console.error('İnaktif cihazlar yüklenirken hata:', err);\r\n            setError('İnaktif cihazlar alınamadı: ' + err.message);\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        fetchInactiveDevices();\r\n    }, []);\r\n\r\n    return (\r\n        <>\r\n            <style>{styles}</style>\r\n            <Header />\r\n            <div className=\"container-fluid\">\r\n                <div className=\"row\">\r\n                    <Sidebar />\r\n\r\n                    <main className=\"col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white\">\r\n                        <div className=\"d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom\">\r\n                            <h1 className=\"h2\">\r\n                                <FontAwesomeIcon icon={faDesktop} className=\"me-2\" />\r\n                                İnaktif Cihazlar\r\n                                <span className=\"badge bg-secondary ms-2\">{devices.length}</span>\r\n                            </h1>\r\n                        </div>\r\n\r\n                        {loading ? (\r\n                            <LoadingSpinner size=\"lg\" variant=\"primary\" message=\"İnaktif cihazlar yükleniyor...\" centered={true} />\r\n                        ) : error ? (\r\n                            <ErrorMessage\r\n                                message={error}\r\n                                variant=\"danger\"\r\n                                title=\"Veri Yükleme Hatası\"\r\n                                dismissible={true}\r\n                                onDismiss={() => setError('')}\r\n                            >\r\n                                <button className=\"btn btn-primary btn-sm mt-2\" onClick={fetchInactiveDevices}>\r\n                                    Yeniden Dene\r\n                                </button>\r\n                            </ErrorMessage>\r\n                        ) : (\r\n                            <>\r\n                                {devices.length === 0 ? (\r\n                                    <div className=\"text-center py-5\">\r\n                                        <FontAwesomeIcon icon={faDesktop} className=\"fa-3x text-muted mb-3\" />\r\n                                        <h5 className=\"text-muted\">İnaktif Cihaz Bulunamadı</h5>\r\n                                        <p className=\"text-muted\">Şu anda inaktif durumda cihaz bulunmamaktadır.</p>\r\n                                    </div>\r\n                                ) : (\r\n                                    <div className=\"card\">\r\n                                        <div className=\"card-header\">\r\n                                            <h5 className=\"mb-0\">İnaktif Cihazlar Listesi</h5>\r\n                                        </div>\r\n                                        <div className=\"card-body\">\r\n                                            <div className=\"table-responsive\">\r\n                                                <table className=\"table table-striped table-hover\">\r\n                                                    <thead>\r\n                                                        <tr>\r\n                                            <th>Cihaz Bilgileri</th>\r\n                                            <th>Durum</th>\r\n                                            <th>Pil Seviyesi</th>\r\n                                            <th>Son Kullanım</th>\r\n                                            <th>Kalan Kullanım Süresi</th>\r\n                                            <th>En Son Sevkiyat</th>\r\n                                            <th>İşlemler</th>\r\n                                        </tr>\r\n                                                    </thead>\r\n                                                    <tbody>\r\n                                        {devices.map((device, index) => (\r\n                                            <React.Fragment key={index}>\r\n                                                <tr className=\"device-row\">\r\n                                                    <td>\r\n                                                        <div className=\"d-flex flex-column\">\r\n                                                            <div className=\"fw-bold text-primary mb-1\">{device.cihazKodu}</div>\r\n                                                            {device.cihazAdi && (\r\n                                                                <div className=\"text-muted small\">\r\n                                                                    {device.cihazAdi}\r\n                                                                </div>\r\n                                                            )}\r\n                                                            {device.model && (\r\n                                                                <div className=\"text-muted small\">\r\n                                                                    <strong>Model:</strong> {device.model}\r\n                                                                </div>\r\n                                                            )}\r\n                                                        </div>\r\n                                                    </td>\r\n                                                    <td>\r\n                                                        <span className={`badge ${\r\n                                                            device.durum === 'aktif' ? 'bg-success' :\r\n                                                            device.durum === 'pasif' ? 'bg-secondary' :\r\n                                                            device.durum === 'bakimda' ? 'bg-warning' : 'bg-secondary'\r\n                                                        }`}>\r\n                                                            {device.durum || 'Bilinmiyor'}\r\n                                                        </span>\r\n                                                    </td>\r\n                                                    <td>\r\n                                                        {device.pilSeviyesi ? (\r\n                                                            <span className={getBatteryColor(device.pilSeviyesi)}>\r\n                                                                <FontAwesomeIcon \r\n                                                                    icon={getBatteryIcon(device.pilSeviyesi)} \r\n                                                                    className=\"me-2\"\r\n                                                                />\r\n                                                                {device.pilSeviyesi}%\r\n                                                            </span>\r\n                                                        ) : (\r\n                                                            <span className=\"text-muted\">\r\n                                                                <FontAwesomeIcon icon={faBatteryEmpty} className=\"me-2\" />\r\n                                                                Bilinmiyor\r\n                                                            </span>\r\n                                                        )}\r\n                                                    </td>\r\n                                                    <td>\r\n                                                        {device.sonKullanim ? (\r\n                                                            <span>\r\n                                                                <FontAwesomeIcon icon={faCalendarAlt} className=\"me-2 text-muted\" />\r\n                                                                {device.sonKullanim}\r\n                                                            </span>\r\n                                                        ) : (\r\n                                                            <span className=\"text-muted\">\r\n                                                                <FontAwesomeIcon icon={faCalendarAlt} className=\"me-2\" />\r\n                                                                Hiç kullanılmamış\r\n                                                            </span>\r\n                                                        )}\r\n                                                    </td>\r\n                                                    <td>\r\n                                                        {(() => {\r\n                                                            const remaining = calculateRemainingTime(device.kontorSonu);\r\n                                                            return (\r\n                                                                <div className=\"d-flex align-items-center\">\r\n                                                                    <FontAwesomeIcon \r\n                                                                        icon={remaining.isWarning ? faExclamationTriangle : faClock} \r\n                                                                        className={`me-2 ${\r\n                                                                            remaining.isExpired || remaining.isWarning \r\n                                                                                ? 'text-danger' \r\n                                                                                : 'text-success'\r\n                                                                        }`} \r\n                                                                    />\r\n                                                                    <span className={\r\n                                                                        remaining.isExpired || remaining.isWarning \r\n                                                                            ? 'text-danger fw-bold' \r\n                                                                            : 'text-success'\r\n                                                                    }>\r\n                                                                        {remaining.displayText}\r\n                                                                    </span>\r\n                                                                </div>\r\n                                                            );\r\n                                                        })()}\r\n                                                    </td>\r\n                                                    <td>\r\n                                                        {device.enSonSevkiyat ? (\r\n                                                            <div className=\"d-flex flex-column\">\r\n                                                                <div className=\"fw-bold small text-info\">\r\n                                                                    <FontAwesomeIcon icon={faShippingFast} className=\"me-1\" />\r\n                                                                    {device.enSonSevkiyat.sevkiyatNo}\r\n                                                                </div>\r\n                                                                <div className=\"text-muted small\">\r\n                                                                    {device.enSonSevkiyat.cikisLokasyon} → {device.enSonSevkiyat.varisLokasyon}\r\n                                                                </div>\r\n                                                                <div className=\"text-muted small\">\r\n                                                                    {device.enSonSevkiyat.sevkiyatTarihi}\r\n                                                                    {device.enSonSevkiyat.bitisTarihi && ` - ${device.enSonSevkiyat.bitisTarihi}`}\r\n                                                                </div>\r\n                                                            </div>\r\n                                                        ) : (\r\n                                                            <span className=\"text-muted\">\r\n                                                                <FontAwesomeIcon icon={faShippingFast} className=\"me-2\" />\r\n                                                                Sevkiyat yok\r\n                                                            </span>\r\n                                                        )}\r\n                                                    </td>\r\n                                                    <td>\r\n                                                        <div className=\"btn-group-vertical\">\r\n                                                            <Link \r\n                                                                to=\"/payment\"\r\n                                                                className=\"btn btn-sm btn-outline-primary mb-1\"\r\n                                                                title=\"Kullanım süresi satın al\"\r\n                                                            >\r\n                                                                <FontAwesomeIcon icon={faCreditCard} className=\"me-1\" />\r\n                                                                Kullanım Süresi Satın Al\r\n                                                            </Link>\r\n                                                            <button \r\n                                                                className=\"btn btn-sm btn-outline-info\"\r\n                                                                onClick={() => toggleDeviceExpansion(device.cihazKodu)}\r\n                                                                title={expandedDevices.has(device.cihazKodu) ? 'Detayları Gizle' : 'Detayları Göster'}\r\n                                                            >\r\n                                                                <FontAwesomeIcon \r\n                                                                    icon={expandedDevices.has(device.cihazKodu) ? faEyeSlash : faEye} \r\n                                                                    className=\"me-1\"\r\n                                                                />\r\n                                                                {expandedDevices.has(device.cihazKodu) ? 'Gizle' : 'Detay'}\r\n                                                            </button>\r\n                                                        </div>\r\n                                                    </td>\r\n                                                </tr>\r\n                                                {expandedDevices.has(device.cihazKodu) && (\r\n                                                    <tr className=\"device-details-row\">\r\n                                                        <td colSpan=\"7\" className=\"bg-light\">\r\n                                                            <div className=\"p-3\">\r\n                                                                <div className=\"row\">\r\n                                                                    <div className=\"col-md-6\">\r\n                                                                        <h6 className=\"text-primary mb-3\">\r\n                                                                            <FontAwesomeIcon icon={faInfoCircle} className=\"me-2\" />\r\n                                                                            Sensör Verileri\r\n                                                                        </h6>\r\n                                                                        <div className=\"row\">\r\n                                                                            <div className=\"col-6 mb-2\">\r\n                                                                                <div className=\"d-flex align-items-center\">\r\n                                                                                    <FontAwesomeIcon icon={faBatteryHalf} className=\"text-warning me-2\" />\r\n                                                                                    <span className=\"fw-bold\">Pil Gerilimi:</span>\r\n                                                                                </div>\r\n                                                                                <div className=\"ms-4\">\r\n                                                                                    {device.pilGerilimi ? `${device.pilGerilimi}V (${device.pilSeviyesi}%)` : 'Veri yok'}\r\n                                                                                </div>\r\n                                                                            </div>\r\n                                                                            <div className=\"col-6 mb-2\">\r\n                                                                                <div className=\"d-flex align-items-center\">\r\n                                                                                    <FontAwesomeIcon icon={faTint} className=\"text-info me-2\" />\r\n                                                                                    <span className=\"fw-bold\">Nem:</span>\r\n                                                                                </div>\r\n                                                                                <div className=\"ms-4\">\r\n                                                                                    {device.nem ? `${device.nem}%` : 'Veri yok'}\r\n                                                                                </div>\r\n                                                                            </div>\r\n                                                                            <div className=\"col-6 mb-2\">\r\n                                                                                <div className=\"d-flex align-items-center\">\r\n                                                                                    <FontAwesomeIcon icon={faLightbulb} className=\"text-warning me-2\" />\r\n                                                                                    <span className=\"fw-bold\">Işık:</span>\r\n                                                                                </div>\r\n                                                                                <div className=\"ms-4\">\r\n                                                                                    {device.isik ? device.isik : 'Veri yok'}\r\n                                                                                </div>\r\n                                                                            </div>\r\n                                                                            <div className=\"col-6 mb-2\">\r\n                                                                                <div className=\"d-flex align-items-center\">\r\n                                                                                    <FontAwesomeIcon icon={faMapMarkerAlt} className=\"text-success me-2\" />\r\n                                                                                    <span className=\"fw-bold\">Konum:</span>\r\n                                                                                </div>\r\n                                                                                <div className=\"ms-4\">\r\n                                                                                    {device.konum && device.konum.enlem && device.konum.boylam ? (\r\n                                                                                        <div>\r\n                                                                                            <div>Enlem: {device.konum.enlem}</div>\r\n                                                                                            <div>Boylam: {device.konum.boylam}</div>\r\n                                                                                            {device.konum.yukseklik && <div>Yükseklik: {device.konum.yukseklik}m</div>}\r\n                                                                                        </div>\r\n                                                                                    ) : 'Konum bilgisi yok'}\r\n                                                                                </div>\r\n                                                                            </div>\r\n                                                                            <div className=\"col-6 mb-2\">\r\n                                                                                <div className=\"d-flex align-items-center\">\r\n                                                                                    <FontAwesomeIcon icon={faClock} className=\"text-primary me-2\" />\r\n                                                                                    <span className=\"fw-bold\">Kontor Sonu:</span>\r\n                                                                                </div>\r\n                                                                                <div className=\"ms-4\">\r\n                                                                                    {(() => {\r\n                                                                                        const remaining = calculateRemainingTime(device.kontorSonu);\r\n                                                                                        return (\r\n                                                                                            <div>\r\n                                                                                                <div className={\r\n                                                                                                    remaining.isExpired || remaining.isWarning \r\n                                                                                                        ? 'text-danger fw-bold' \r\n                                                                                                        : 'text-success'\r\n                                                                                                }>\r\n                                                                                                    {remaining.displayText}\r\n                                                                                                </div>\r\n                                                                                                {device.kontorSonu && (\r\n                                                                                                    <div className=\"text-muted small\">\r\n                                                                                                        {new Date(device.kontorSonu).toLocaleString('tr-TR')}\r\n                                                                                                    </div>\r\n                                                                                                )}\r\n                                                                                            </div>\r\n                                                                                        );\r\n                                                                                    })()}\r\n                                                                                </div>\r\n                                                                            </div>\r\n                                                                        </div>\r\n                                                                    </div>\r\n                                                                    <div className=\"col-md-6\">\r\n                                                                        <h6 className=\"text-primary mb-3\">\r\n                                                                            <FontAwesomeIcon icon={faShippingFast} className=\"me-2\" />\r\n                                                                            Son Sevkiyat Detayları\r\n                                                                        </h6>\r\n                                                                        {device.enSonSevkiyat ? (\r\n                                                                            <div>\r\n                                                                                <div className=\"mb-2\">\r\n                                                                                    <strong>Sevkiyat No:</strong> {device.enSonSevkiyat.sevkiyatNo}\r\n                                                                                </div>\r\n                                                                                <div className=\"mb-2\">\r\n                                                                                    <strong>Çıkış:</strong> {device.enSonSevkiyat.cikisLokasyon}\r\n                                                                                </div>\r\n                                                                                <div className=\"mb-2\">\r\n                                                                                    <strong>Varış:</strong> {device.enSonSevkiyat.varisLokasyon}\r\n                                                                                </div>\r\n                                                                                <div className=\"mb-2\">\r\n                                                                                    <strong>Ürün:</strong> {device.enSonSevkiyat.urunBilgisi}\r\n                                                                                </div>\r\n                                                                                <div className=\"mb-2\">\r\n                                                                                    <strong>Nakliyeci:</strong> {device.enSonSevkiyat.nakliyeci}\r\n                                                                                </div>\r\n                                                                                <div className=\"mb-2\">\r\n                                                                                    <strong>Başlangıç:</strong> {device.enSonSevkiyat.sevkiyatTarihi}\r\n                                                                                </div>\r\n                                                                                {device.enSonSevkiyat.bitisTarihi && (\r\n                                                                                    <div className=\"mb-2\">\r\n                                                                                        <strong>Bitiş:</strong> {device.enSonSevkiyat.bitisTarihi}\r\n                                                                                    </div>\r\n                                                                                )}\r\n                                                                            </div>\r\n                                                                        ) : (\r\n                                                                            <div className=\"text-muted\">\r\n                                                                                Bu cihaz için henüz sevkiyat kaydı bulunmamaktadır.\r\n                                                                            </div>\r\n                                                                        )}\r\n                                                                    </div>\r\n                                                                </div>\r\n                                                            </div>\r\n                                                        </td>\r\n                                                    </tr>\r\n                                                )}\r\n                                            </React.Fragment>\r\n                                        ))}\r\n                                                    </tbody>\r\n                                                </table>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                )}\r\n                            </>\r\n                        )}\r\n                    </main>\r\n\r\n                    <Footer />\r\n                </div>\r\n            </div>\r\n        </>\r\n    );\r\n};\r\n\r\nexport default InactiveDevices;"], "names": ["Sidebar", "location", "useLocation", "isAdmin", "getUserRole", "_user$user", "_user$user2", "user", "JSON", "parse", "localStorage", "getItem", "role", "gorev", "_unused", "_jsx", "className", "children", "_jsxs", "tabIndex", "id", "type", "Link", "concat", "pathname", "to", "FontAwesomeIcon", "icon", "faDolly", "faFolderPlus", "faAnchor", "faFlag<PERSON><PERSON><PERSON><PERSON>", "faDesktop", "faBell", "faUser", "faStreetView", "faCreditCard", "faFileLines", "Footer", "Header", "navigate", "useNavigate", "userName", "setUserName", "useState", "loading", "setLoading", "useEffect", "async", "_storedUser$user", "_storedUser$user2", "_storedUser$user3", "_storedUser$user4", "storedUser", "userId", "musteri_ID", "name", "musteri_adi", "console", "warn", "userData", "kullaniciService", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setItem", "stringify", "apiError", "error", "fetchUserData", "src", "alt", "height", "onClick", "handleLogout", "removeItem", "faRightFromBracket", "faBars", "InactiveDevices", "devices", "setDevices", "setError", "expandedDevices", "setExpandedDevices", "Set", "calculateRemainingTime", "kontorSonu", "days", "hours", "isExpired", "displayText", "now", "Date", "diffMs", "diffDays", "Math", "floor", "diffHours", "isWarning", "getBatteryIcon", "level", "faBatteryEmpty", "faBatteryQuarter", "faBatteryHalf", "faBatteryThreeQuarters", "faBatteryFull", "fetchInactiveDevices", "response", "inactiveDevicesService", "getInactiveDevices", "success", "data", "err", "message", "_Fragment", "length", "LoadingSpinner", "size", "variant", "centered", "ErrorMessage", "title", "dismissible", "on<PERSON><PERSON><PERSON>", "map", "device", "index", "React", "cihazKodu", "cihazAdi", "model", "durum", "pil<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "faCalendarAlt", "remaining", "faExclamationTriangle", "faClock", "enSonSevkiyat", "faShippingFast", "sevkiyatNo", "cikisLokasyon", "varis<PERSON><PERSON><PERSON><PERSON>", "sevkiyat<PERSON><PERSON><PERSON>", "bitisTarihi", "newExpanded", "has", "delete", "add", "toggleDeviceExpansion", "faEyeSlash", "faEye", "colSpan", "faInfoCircle", "pil<PERSON><PERSON><PERSON><PERSON>", "faTint", "nem", "faLightbulb", "isik", "faMapMarkerAlt", "konum", "enlem", "boy<PERSON>", "yukseklik", "toLocaleString", "urunBil<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "sourceRoot": ""}