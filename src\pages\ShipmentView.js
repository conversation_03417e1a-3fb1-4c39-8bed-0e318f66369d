import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTemperatureThreeQuarters, faDroplet, faBell, faLocationDot, faTruck, faCheck } from '@fortawesome/free-solid-svg-icons';
import { faLightbulb } from '@fortawesome/free-regular-svg-icons';
import Header from '../components/Header';
import Sidebar from '../components/Sidebar';
import Footer from '../components/Footer';
import ReportGenerator from '../components/ReportGenerator';
import RealTimeDeviceData from '../components/RealTimeDeviceData';
import DeviceHistoryTable from '../components/DeviceHistoryTable';
import { GoogleMap, useJsApiLoader, Marker, InfoWindow } from '@react-google-maps/api';
// import axios from 'axios'; // Gerekirse eklenecek
import { sevkiyatService, cihazBilgiService } from '../api/dbService';

// CSS Stil tanımlamaları (gerekirse kullanılacak)
// const markerStyle = {
//     background: '#007f97',
//     borderRadius: '50%',
//     width: '40px',
//     height: '40px',
//     display: 'flex',
//     alignItems: 'center',
//     justifyContent: 'center',
//     color: 'white',
//     border: '2px solid white',
//     boxShadow: '0 0 10px rgba(0, 0, 0, 0.3)',
//     position: 'relative'
// };

// Google Maps API için kütüphaneleri statik bir değişken olarak tanımla
const libraries = ['marker'];

const ShipmentView = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    // Kullanıcı bilgisini localStorage'dan al
    const userString = localStorage.getItem('user');
    const userData = userString ? JSON.parse(userString) : null;
    const user = userData ? userData.user : null;

    // API anahtarı
    const apiKey = 'AIzaSyA2cfEmiPMyvcGfRiCyB9khWrccCgqpxKs';

    // Harita durumu
    const [mapCenter, setMapCenter] = useState({ lat: 41.0082, lng: 28.9784 });
    const [zoom, setZoom] = useState(7);
    const mapRef = useRef(null);

    // InfoWindow durumu
    const [showInfoWindow, setShowInfoWindow] = useState(false);
    const [selectedInfoType, setSelectedInfoType] = useState('');

    // Pulsating marker animasyonu için (gerekirse eklenecek)
    // const [isPulsing, setIsPulsing] = useState(false);

    // Sevkiyat ve sensör verileri
    const [shipmentDetail, setShipmentDetail] = useState(null);

    // Sevkiyat tamamlama durumu
    const [isCompleting, setIsCompleting] = useState(false);
    const [showCompleteModal, setShowCompleteModal] = useState(false);
    const [completeNotes, setCompleteNotes] = useState('');

    // Gerçek zamanlı cihaz verileri için state'ler
    const [historyLocations, setHistoryLocations] = useState([]);
    const [showAllLocations, setShowAllLocations] = useState(false);

    // Google Maps API'yi yükle
    const { isLoaded, loadError } = useJsApiLoader({
        id: 'google-map-script',
        googleMapsApiKey: apiKey,
        libraries: libraries
    });

    // Harita referansını kaydet
    const onMapLoad = useCallback((map) => {
        mapRef.current = map;

        // Zoom değişikliğini izle
        if (map) {
            map.addListener('zoom_changed', () => {
                setZoom(map.getZoom());
            });
        }
    }, []);


    // DeviceHistoryTable'dan konum verilerini alma callback'i - useCallback ile optimize edildi
    const handleHistoryData = useCallback((locations) => {
        console.log('History Locations from DeviceHistoryTable:', locations);
        setHistoryLocations(locations);
    }, []);

    // DeviceHistoryTable için tarih aralığı - useMemo ile optimize edildi
    const deviceDateRange = useMemo(() => {
        if (!shipmentDetail?.olusturmaZamani) {
            console.warn('DeviceHistoryTable - olusturmaZamani yok, tarih filtresi uygulanmayacak!');
            return {};
        }
        
        // API'nin beklediği format: YYYY-MM-DD HH:MM:SS (Database Türkiye saati)
        const formatDateForAPI = (dateString) => {
            if (!dateString) return null;
            
            // Database Türkiye saatinde ama JavaScript UTC olarak parse ediyor
            // Türkiye yaz saati UTC+3, ama database zaten +1 saat farkı var
            // Bu yüzden sadece +2 saat eklemeli
            const date = new Date(dateString);
            const turkiyeSaati = new Date(date.getTime() + (2 * 60 * 60 * 1000));
            const formatted = turkiyeSaati.toISOString().replace('T', ' ').split('.')[0];
            
            console.log('📅 Tarih dönüşümü FİX:', {
                original: dateString,
                jsDate: date.toISOString(),
                turkiyeSaati: formatted,
                expectedDB: 'Database: 17:24:40 → Formatted: 17:24:40'
            });
            
            return formatted;
        };
        
        let startDate, endDate;
        
        if (shipmentDetail.isCompleted && shipmentDetail.tamamlanmaZamani) {
            // Geçmiş sevkiyat: tamamlanma_zamani'ndan geriye olusturma_zamani'na kadar
            startDate = formatDateForAPI(shipmentDetail.olusturmaZamani);
            endDate = formatDateForAPI(shipmentDetail.tamamlanmaZamani);
        } else {
            // Aktif sevkiyat: şu anki zamandan geriye olusturma_zamani'na kadar
            startDate = formatDateForAPI(shipmentDetail.olusturmaZamani);
            endDate = formatDateForAPI(new Date().toISOString());
        }
        
        console.log('🎯 ShipmentView - Hesaplanan tarih aralığı:', {
            baslangic_tarihi: startDate,
            bitis_tarihi: endDate,
            isCompleted: shipmentDetail.isCompleted,
            originalStart: shipmentDetail.olusturmaZamani,
            originalEnd: shipmentDetail.tamamlanmaZamani,
            sevkiyatTipi: shipmentDetail.isCompleted ? 'Geçmiş Sevkiyat' : 'Aktif Sevkiyat',
            tarihlervMevcut: !!(shipmentDetail.olusturmaZamani)
        });
        
        return {
            baslangic_tarihi: startDate,
            bitis_tarihi: endDate
        };
    }, [shipmentDetail?.olusturmaZamani, shipmentDetail?.tamamlanmaZamani, shipmentDetail?.isCompleted]);

    // Verileri API'den getir
    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);
                setError(null); // Her yüklemede hata durumunu sıfırla

                // Sevkiyat detaylarını getir
                const shipmentResponse = await sevkiyatService.getSevkiyatById(id);

                // Sensör verilerini uzak API'den getir
                let sensorResponse = [];
                try {
                    if (shipmentResponse.mgz24_kodu) {
                        // Uzak API'den cihaz verilerini al
                        const response = await fetch(`https://ffl21.fun:3001/api/cihaz/${shipmentResponse.mgz24_kodu}`);
                        if (response.ok) {
                            const data = await response.json();
                            if (data.success && data.data) {
                                sensorResponse = [data.data]; // Array formatında
                            }
                        }
                    }
                } catch (sensorError) {
                    console.warn(`Sensör verileri alınamadı (MGZ24: ${shipmentResponse.mgz24_kodu}):`, sensorError);
                    // Sensör hatası durumunda sadece uyarı ver, ama işleme devam et
                }

                // Son sensör verilerini bul (en son tarihli)
                const latestSensorData = sensorResponse && sensorResponse.length > 0
                    ? sensorResponse[0] // API zaten zamana göre sıralanmış olarak geliyor
                    : null;

                // Debug: API'den gelen sensor verilerini konsola yazdır
                console.log('API Sensor Response:', sensorResponse);
                console.log('Latest Sensor Data:', latestSensorData);
                if (latestSensorData?.sonKonum) {
                    console.log('Son Konum Verisi:', latestSensorData.sonKonum);
                }
                console.log('Sevkiyat Tarihleri - Oluşturma:', shipmentResponse.olusturma_zamani, 'Tamamlanma:', shipmentResponse.tamamlanma_zamani);
                // Log işlemini setShipmentDetail'den sonra taşıyacağız
                
                // CRITICAL: Eğer olusturmaZamani null/undefined ise filtreleme çalışmaz!
                if (!shipmentResponse.olusturma_zamani) {
                    console.error('UYARI: olusturma_zamani değeri yok!', {
                        olusturma_zamani: shipmentResponse.olusturma_zamani,
                        sevkiyat_id: shipmentResponse.id || id
                    });
                }

                // Verileri işle ve state'e kaydet
                if (shipmentResponse) {
                    // Doğrudan sevkiyat nesnesindeki metin alanlarını kullan
                    const fromName = shipmentResponse.nereden || shipmentResponse.cikis_lokasyon || shipmentResponse.gonderen_firma || 'Bilinmiyor';
                    const toName = shipmentResponse.nereye || shipmentResponse.varis_lokasyon || shipmentResponse.alici_firma || 'Bilinmiyor';
                    const carrierName = shipmentResponse.nakliyeci || shipmentResponse.surucu_adi || 'Bilinmiyor';
                    const productName = shipmentResponse.urun || shipmentResponse.urun_bilgisi || 'Bilinmiyor';
                    
                    // Sevkiyatın tamamlanmış olup olmadığını kontrol et
                    const isCompleted = shipmentResponse.tamamlandi_mi === 1 || shipmentResponse.tamamlandi_mi === '1';

                    setShipmentDetail({
                        id: shipmentResponse.id || id,
                        sevkiyatID: shipmentResponse.sevkiyat_ID || 'Bilinmiyor',
                        mgzKodu: shipmentResponse.mgz24_kodu || 'Bilinmiyor',
                        name: shipmentResponse.sevkiyat_adi || 'Bilinmeyen Sevkiyat',
                        plate: shipmentResponse.plaka_no || 'Plaka yok',
                        from: fromName,
                        to: toName,
                        carrier: carrierName,
                        product: productName,
                        orderNo: shipmentResponse.mgz24_kodu || 'Sipariş no yok',
                        pallet: shipmentResponse.palet_sayisi?.toString() || '0',
                        net: shipmentResponse.net_agirlik?.toString() || '0',
                        gross: shipmentResponse.brut_agirlik?.toString() || '0',
                        added: formatDate(shipmentResponse.olusturma_zamani) || 'Bilinmiyor',
                        lastUpdate: formatDate(shipmentResponse.guncelleme_zamani, true) || 'Bilinmiyor',
                        isCompleted: isCompleted,
                        olusturmaZamani: shipmentResponse.olusturma_zamani,
                        tamamlanmaZamani: shipmentResponse.tamamlanma_zamani,
                        location: {
                            lat: latestSensorData?.sonKonum?.enlem ? parseFloat(latestSensorData.sonKonum.enlem) : 41.0082,
                            lng: latestSensorData?.sonKonum?.boylam ? parseFloat(latestSensorData.sonKonum.boylam) : 28.9784,
                            address: 'İstanbul, Türkiye' // Gerçek adres için geocoding API kullanılabilir
                        },
                        temperature: {
                            value: latestSensorData?.sonSensorler?.sicaklik?.toString() || '0',
                            min: (shipmentResponse.sicaklik_araligi || '15-25°C').split('-')[0].replace('°C', '').trim(),
                            max: (shipmentResponse.sicaklik_araligi || '15-25°C').split('-')[1]?.replace('°C', '').trim() || '25'
                        },
                        humidity: {
                            value: latestSensorData?.sonSensorler?.nem?.toString() || '0',
                            min: '5',
                            max: '95'
                        },
                        light: {
                            value: latestSensorData?.sonSensorler?.isik?.toString() || '0',
                            message: latestSensorData?.sonSensorler?.kapi === 'acik' ? 'Kapak açıldı!' : 'Kapak kapalı'
                        }
                    });
                    
                    // ShipmentDetail set edildikten sonra log'la
                    console.log('✅ ShipmentDetail SET EDİLDİ:', {
                        olusturmaZamani: shipmentResponse.olusturma_zamani,
                        tamamlanmaZamani: shipmentResponse.tamamlanma_zamani,
                        isCompleted: isCompleted
                    });

                    // Harita merkezini güncelle
                    if (latestSensorData?.sonKonum?.enlem && latestSensorData?.sonKonum?.boylam) {
                        setMapCenter({
                            lat: parseFloat(latestSensorData.sonKonum.enlem),
                            lng: parseFloat(latestSensorData.sonKonum.boylam)
                        });
                    }

                }

                setLoading(false);
            } catch (err) {
                console.error('Veri getirme hatası:', err);

                // API hata mesajını göster
                let errorMessage = 'Sevkiyat bilgileri yüklenirken hata oluştu.';
                let errorType = 'Sunucu Hatası';

                if (err.response) {
                    // Sunucudan yanıt geldi, ancak hata yanıtı (4xx, 5xx)
                    if (err.response.status === 404) {
                        errorType = 'Kayıt Bulunamadı';
                        errorMessage = err.message || `${id} numaralı sevkiyat kaydı bulunamadı. Silinen veya hiç oluşturulmamış bir kayıt olabilir.`;
                    } else {
                        errorMessage += ` Sunucu hatası: ${err.response.status}`;
                        if (err.response.data && err.response.data.message) {
                            errorMessage += ` - ${err.response.data.message}`;
                        }
                    }
                } else if (err.code === 'ECONNABORTED') {
                    errorType = 'Zaman Aşımı';
                    errorMessage = 'Sunucu yanıt vermedi, istek zaman aşımına uğradı. Lütfen daha sonra tekrar deneyin.';
                } else if (err.code === 'ECONNRESET') {
                    errorType = 'Bağlantı Hatası';
                    errorMessage = 'Sunucu bağlantısı sıfırlandı. Lütfen internet bağlantınızı kontrol edin ve tekrar deneyin.';
                } else if (err.request) {
                    // İstek yapıldı ama yanıt alınamadı (ağ hatası vb)
                    errorType = 'Ağ Hatası';
                    errorMessage = 'Sunucuya bağlanılamadı. Lütfen ağ bağlantınızı kontrol edin.';
                }

                setError({ type: errorType, message: errorMessage });
                setLoading(false);

                // Demo verisi kullan
                const demoData = {
                    id: id || '51692580',
                    sevkiyatID: 'SVK000001',
                    mgzKodu: '200000780',
                    name: 'Demo Veri - Moldova',
                    plate: '34 FH 3581',
                    from: 'Düzce',
                    to: 'Moldova',
                    carrier: 'Transgood',
                    product: 'Limon',
                    orderNo: '200000780',
                    pallet: '46',
                    net: '8932',
                    gross: '11061',
                    added: '30.04.2025',
                    lastUpdate: '04.05.2025 23:20',
                    isCompleted: false, // Demo için false varsayalım
                    olusturmaZamani: '2025-04-30T10:00:00',
                    tamamlanmaZamani: null, // Demo için null (aktif sevkiyat)
                    location: {
                        lat: 41.0082,
                        lng: 28.9784,
                        address: 'İstanbul, Türkiye'
                    },
                    temperature: {
                        value: '21.6',
                        min: '15',
                        max: '25'
                    },
                    humidity: {
                        value: '45',
                        min: '5',
                        max: '95'
                    },
                    light: {
                        value: '0.5',
                        message: 'Kapak kapalı'
                    }
                };

                setShipmentDetail(demoData);
            }
        };

        fetchData();

        // Marker pulse animasyonunu başlat (gerekirse eklenecek)
        // setIsPulsing(true);
        // const timer = setTimeout(() => {
        //     setIsPulsing(false);
        // }, 2000);

        // return () => clearTimeout(timer);
    }, [id]);

    // Google Maps deprecated uyarısını bastır
    useEffect(() => {
        const originalWarn = console.warn;
        console.warn = (...args) => {
            if (args[0] && typeof args[0] === 'string' && args[0].includes('google.maps.Marker is deprecated')) {
                return; // Bu uyarıyı bastır
            }
            originalWarn.apply(console, args);
        };

        return () => {
            console.warn = originalWarn;
        };
    }, []);

    // Tarih formatla
    const formatDate = (dateString, includeTime = false) => {
        if (!dateString) return '';

        const date = new Date(dateString);
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();

        if (includeTime) {
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            return `${day}.${month}.${year} ${hours}:${minutes}`;
        }

        return `${day}.${month}.${year}`;
    };

    // Sevkiyat tamamlama onay modalını göster
    const handleCompleteShipment = () => {
        setShowCompleteModal(true);
    };

    // Sevkiyat tamamlama işlemini gerçekleştir
    const confirmCompleteShipment = async () => {
        try {
            setIsCompleting(true);

            const userId = user?.user?.musteri_ID || user?.user?.id;
            if (!userId) {
                throw new Error('Kullanıcı bilgisi bulunamadı');
            }

            const result = await sevkiyatService.completeSevkiyat(
                shipmentDetail.id,
                userId,
                completeNotes
            );

            if (result.success) {
                // Cihazı inaktif duruma getir
                try {
                    // Demo için cihaz kodu sevkiyat detayından al
                    const cihazKodu = shipmentDetail.mgzKodu || `D-${shipmentDetail.id}`;
                    await cihazBilgiService.updateCihazStatus(cihazKodu, {
                        aktif: false,
                        son_kullanim_tarihi: new Date().toISOString(),
                        notlar: `${shipmentDetail.sevkiyatID} sevkiyatı tamamlandı`
                    });
                    console.log('Cihaz başarıyla inaktif duruma getirildi');
                } catch (cihazError) {
                    console.warn('Cihaz durumu güncellenirken hata:', cihazError);
                    // Cihaz hatası sevkiyat tamamlama işlemini engellemez
                }

                // Başarılı olduğunda ana sayfaya yönlendir
                alert('Sevkiyat başarıyla tamamlandı! Cihaz inaktif cihazlar kategorisine taşındı.');
                navigate('/');
            } else {
                throw new Error(result.message || 'Sevkiyat tamamlanırken hata oluştu');
            }
        } catch (error) {
            console.error('Sevkiyat tamamlama hatası:', error);
            alert('Sevkiyat tamamlanırken hata oluştu: ' + error.message);
        } finally {
            setIsCompleting(false);
            setShowCompleteModal(false);
            setCompleteNotes('');
        }
    };

    // Konum URL'i oluştur
    const getGoogleMapsDirectionUrl = () => {
        if (!shipmentDetail) return '#';
        const { lat, lng } = shipmentDetail.location;
        return `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`;
    };

    // Yükleniyor göstergesi
    if (loading || !shipmentDetail) {
        return (
            <>
                <Header />
                <div className="container-fluid">
                    <div className="row">
                        <Sidebar />
                        <main className="col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white">
                            <div className="d-flex justify-content-center align-items-center" style={{ height: '80vh' }}>
                                <div className="spinner-border text-primary" role="status">
                                    <span className="visually-hidden">Yükleniyor...</span>
                                </div>
                                <span className="ms-2">Sevkiyat bilgileri yükleniyor...</span>
                            </div>
                        </main>
                    </div>
                </div>
            </>
        );
    }

    // Hata göstergesi
    if (error) {
        return (
            <>
                <Header />
                <div className="container-fluid">
                    <div className="row">
                        <Sidebar />
                        <main className="col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white">
                            <div className="pt-3 pb-1 mt-3 mb-3 border-bottom">
                                <h1 className="h4 text-dark mb-0">
                                    <FontAwesomeIcon icon={faTruck} className="me-2 text-primary" />
                                    #{id} Sevkiyat Takip
                                </h1>
                            </div>

                            <div className="row mb-4">
                                <div className="col-12">
                                    <div className="alert alert-danger">
                                        <h5 className="alert-heading">
                                            <FontAwesomeIcon icon={faBell} className="me-2" />
                                            API Bağlantı Hatası
                                        </h5>
                                        <p>Sunucuya bağlanılamadı. Lütfen internet bağlantınızı kontrol edin.</p>
                                        <hr />
                                        <div className="d-flex justify-content-between align-items-center">
                                            <p className="mb-0">
                                                Gerçek veriler için sunucu bağlantısı gereklidir.
                                            </p>

                                            <div className="btn-group">
                                                <button
                                                    className="btn btn-primary"
                                                    onClick={() => window.location.reload()}
                                                >
                                                    <FontAwesomeIcon icon={faBell} className="me-2" />
                                                    Yeniden Dene
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Sevkiyat verilerini hata durumunda da göster (demo veriler) */}
                            {shipmentDetail && (
                                <>
                                    {/* sevkiyat bilgi */}
                                    <div className="row mb-4">
                                        <div className="col-12">
                                            <div className="card bg-light border">
                                                <div className="card-header bg-dark-subtle py-2">
                                                    <div className="d-flex justify-content-between align-items-center">
                                                        <h5 className="mb-0 fw-bold">{shipmentDetail.name}</h5>
                                                        <small>Demo Veri</small>
                                                    </div>
                                                </div>
                                                <div className="card-body">
                                                    <div className="table-responsive mb-0">
                                                        <table className="table table-light table-bordered table-striped mb-0">
                                                            <thead>
                                                                <tr>
                                                                    <th>Sistem ID</th>
                                                                    <th>Sevkiyat ID</th>
                                                                    <th>MGZ24 Kodu</th>
                                                                    <th>Sevkiyat Adı</th>
                                                                    <th>Plaka No</th>
                                                                    <th>Nereden</th>
                                                                    <th>Nereye</th>
                                                                    <th>Nakliyeci</th>
                                                                    <th>Ürün</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr>
                                                                    <td>{shipmentDetail.id}</td>
                                                                    <td>{shipmentDetail.sevkiyatID}</td>
                                                                    <td>
                                                                        <span className="badge bg-primary text-white">
                                                                            {shipmentDetail.mgzKodu}
                                                                        </span>
                                                                    </td>
                                                                    <td>{shipmentDetail.name}</td>
                                                                    <td>{shipmentDetail.plate}</td>
                                                                    <td>{shipmentDetail.from}</td>
                                                                    <td>{shipmentDetail.to}</td>
                                                                    <td>{shipmentDetail.carrier}</td>
                                                                    <td>{shipmentDetail.product}</td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </>
                            )}
                        </main>
                    </div>
                </div>
            </>
        );
    }

    // Info Window'da gösterilecek içeriği belirle
    const getInfoContent = (type) => {
        switch (type) {
            case 'temperature':
                return (
                    <div className="card-body p-2">
                        <h6 className="card-title mb-1 fw-bold"><FontAwesomeIcon icon={faTemperatureThreeQuarters} /> Sıcaklık</h6>
                        <p className="mb-1 fs-4 fw-light">{shipmentDetail.temperature.value} °C</p>
                        <small>Min/Max: {shipmentDetail.temperature.min} - {shipmentDetail.temperature.max} °C</small>
                    </div>
                );
            case 'humidity':
                return (
                    <div className="card-body p-2">
                        <h6 className="card-title mb-1 fw-bold"><FontAwesomeIcon icon={faDroplet} /> Nem</h6>
                        <p className="mb-1 fs-4 fw-light">{shipmentDetail.humidity.value} %</p>
                        <small>Min/Max: {shipmentDetail.humidity.min} - {shipmentDetail.humidity.max} %</small>
                    </div>
                );
            case 'light':
                return (
                    <div className="card-body p-2">
                        <h6 className="card-title mb-1 fw-bold"><FontAwesomeIcon icon={faLightbulb} /> Işık</h6>
                        <p className="mb-1 fs-4 fw-light">{shipmentDetail.light.value}</p>
                        <small>{shipmentDetail.light.message}</small>
                    </div>
                );
            case 'location':
                return (
                    <div className="card-body p-2">
                        <h6 className="card-title mb-1 fw-bold"><FontAwesomeIcon icon={faLocationDot} /> Konum</h6>
                        <p className="mb-1 fs-5 fw-light">{shipmentDetail.location.address}</p>
                        <small>Son Güncelleme: {shipmentDetail.lastUpdate}</small>
                        <div className="mt-2">
                            <a
                                href={getGoogleMapsDirectionUrl()}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="btn btn-sm btn-primary"
                            >
                                <FontAwesomeIcon icon={faLocationDot} className="me-1" /> Yol Tarifi Al
                            </a>
                        </div>
                    </div>
                );
            default:
                return null;
        }
    };

    return (
        <>
            <Header />
            <div className="container-fluid">
                <div className="row">
                    <Sidebar />

                    {/* main sütun */}
                    <main className="col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white">
                        <div className="pt-3 pb-1 mt-3 mb-3 border-bottom">
                            <div className="d-flex justify-content-between align-items-center">
                                <h1 className="h4 text-dark mb-0">
                                    <FontAwesomeIcon icon={faTruck} className="me-2 text-primary" />
                                    Sevkiyat: <span className="fw-bold">{shipmentDetail.sevkiyatID}</span>
                                    {shipmentDetail.mgzKodu && shipmentDetail.mgzKodu !== 'Bilinmiyor' && (
                                        <span className="ms-2 badge bg-primary">MGZ24: {shipmentDetail.mgzKodu}</span>
                                    )}
                                </h1>
                            </div>
                        </div>

                        {/* En Son Veriler Gösterimi */}
                        <RealTimeDeviceData 
                            cihazID={shipmentDetail?.mgzKodu}
                            dateRange={{
                                baslangic_tarihi: shipmentDetail?.olusturmaZamani,
                                bitis_tarihi: shipmentDetail?.tamamlanmaZamani
                            }}
                        />

                        {/* Sevkiyat Bilgisi */}
                        <div className="row mb-4">
                            <div className="col-12">
                                <div className="card border-tertiary-subtle px-3 py-3">
                                    <div className="card-header bg-light d-flex justify-content-between align-items-center">
                                        <h5 className="mb-0 text-dark">
                                            <FontAwesomeIcon icon={faTruck} className="me-2 text-primary" />
                                            Antalya Sevkiyatı
                                        </h5>
                                    </div>
                                    <div className="card-body p-0">
                                        <div className="table-responsive">
                                            <table className="table table-striped table-hover mb-0">
                                                <thead className="table-dark">
                                                    <tr>
                                                        <th>Sistem ID</th>
                                                        <th>Sevkiyat ID</th>
                                                        <th>MGZ24 Kodu</th>
                                                        <th>Sevkiyat Adı</th>
                                                        <th>Plaka No</th>
                                                        <th>Nereden</th>
                                                        <th>Nereye</th>
                                                        <th>Nakliyeci</th>
                                                        <th>Ürün</th>
                                                        <th>Palet</th>
                                                        <th>Net</th>
                                                        <th>Brüt</th>
                                                        <th>Eklenme</th>
                                                        <th>Sevkiyat Tamamlandı</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>{shipmentDetail.id}</td>
                                                        <td>{shipmentDetail.sevkiyatID}</td>
                                                        <td>
                                                            <span className="badge bg-primary">
                                                                {shipmentDetail.mgzKodu}
                                                            </span>
                                                        </td>
                                                        <td>{shipmentDetail.name}</td>
                                                        <td>{shipmentDetail.plate}</td>
                                                        <td>{shipmentDetail.from}</td>
                                                        <td>{shipmentDetail.to}</td>
                                                        <td>{shipmentDetail.carrier || 'Bilinmiyor'}</td>
                                                        <td>{shipmentDetail.product}</td>
                                                        <td>{shipmentDetail.pallet}</td>
                                                        <td>{shipmentDetail.net}</td>
                                                        <td>{shipmentDetail.gross}</td>
                                                        <td>{formatDate(shipmentDetail.olusturmaZamani, true)}</td>
                                                        <td>
                                                            <div className="form-check">
                                                                <input
                                                                    className="form-check-input"
                                                                    type="checkbox"
                                                                    checked={shipmentDetail.isCompleted}
                                                                    onChange={handleCompleteShipment}
                                                                    disabled={isCompleting || shipmentDetail.isCompleted}
                                                                />
                                                                <label className="form-check-label">
                                                                    Sevkiyat Tamamlandı
                                                                </label>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Harita */}
                        <div className="row my-4">
                            <div className="col-12">
                                <div className="card border-tertiary-subtle px-3 py-3">
                                    <div className="card-body p-0" style={{ height: '60vh', boxShadow: 'inset 0 0 20px rgba(0, 0, 0, 0.1)' }}>
                                        {loadError && (
                                            <div className="alert alert-danger">
                                                Google Maps yüklenemedi. Lütfen daha sonra tekrar deneyin.
                                            </div>
                                        )}

                                        {!isLoaded ? (
                                            <div className="d-flex justify-content-center align-items-center h-100">
                                                <div className="spinner-border text-primary" role="status">
                                                    <span className="visually-hidden">Harita yükleniyor...</span>
                                                </div>
                                                <span className="ms-2">Harita yükleniyor...</span>
                                            </div>
                                        ) : (
                                            <div style={{ position: 'relative', width: '100%', height: '100%' }}>
                                                {/* Harita kontrolleri */}
                                                {historyLocations.length > 0 && (
                                                    <div style={{ position: 'absolute', top: 16, left: 16, zIndex: 2 }}>
                                                        <button
                                                            onClick={() => setShowAllLocations(!showAllLocations)}
                                                            className="btn btn-sm btn-primary shadow-sm"
                                                            style={{ fontSize: '12px' }}
                                                        >
                                                            <FontAwesomeIcon icon={faLocationDot} className="me-1" />
                                                            {showAllLocations ? 'Konumları Gizle' : `Tüm Konumları Göster (${historyLocations.length})`}
                                                        </button>
                                                    </div>
                                                )}

                                                <GoogleMap
                                                    mapContainerStyle={{ width: '100%', height: '100%', borderRadius: '4px' }}
                                                    center={mapCenter}
                                                    zoom={zoom}
                                                    onLoad={onMapLoad}
                                                    options={{
                                                        mapTypeId: 'roadmap',
                                                        disableDefaultUI: true,
                                                        zoomControl: true,
                                                        mapTypeControl: false,
                                                        scaleControl: false,
                                                        streetViewControl: false,
                                                        rotateControl: false,
                                                        clickableIcons: true,
                                                        fullscreenControl: true,
                                                        gestureHandling: 'greedy',
                                                        minZoom: 2,
                                                        maxZoom: 20,
                                                        zoomControlOptions: {
                                                            position: window.google?.maps?.ControlPosition?.TOP_RIGHT
                                                        },
                                                        fullscreenControlOptions: {
                                                            position: window.google?.maps?.ControlPosition?.TOP_RIGHT
                                                        }
                                                    }}
                                                >
                                                    {/* Ana konum marker'ı */}
                                                    <Marker
                                                        position={shipmentDetail.location}
                                                        onClick={() => {
                                                            setSelectedInfoType('location');
                                                            setShowInfoWindow(!showInfoWindow);
                                                        }}
                                                        animation={window.google?.maps?.Animation?.DROP}
                                                        icon={{
                                                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                                                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z" fill="#007f97" stroke="#ffffff" stroke-width="1.5"/>
                                                                </svg>
                                                            `),
                                                            scaledSize: window.google && window.google.maps ? new window.google.maps.Size(32, 32) : undefined,
                                                            anchor: window.google && window.google.maps ? new window.google.maps.Point(16, 32) : undefined
                                                        }}
                                                    >
                                                        {showInfoWindow && (
                                                            <InfoWindow
                                                                position={shipmentDetail.location}
                                                                onCloseClick={() => setShowInfoWindow(false)}
                                                            >
                                                                <div className="shadow-sm" style={{ borderRadius: '8px', overflow: 'hidden' }}>
                                                                    {getInfoContent(selectedInfoType)}
                                                                </div>
                                                            </InfoWindow>
                                                        )}
                                                    </Marker>

                                                    {/* Tablodaki konum geçmişi marker'ları */}
                                                    {showAllLocations && historyLocations.map((location, index) => {
                                                        const enlem = location.konum?.enlem;
                                                        const boylam = location.konum?.boylam;
                                                        console.log(`Location ${index}:`, location, 'Enlem:', enlem, 'Boylam:', boylam);
                                                        if (!enlem || !boylam) return null;
                                                        
                                                        return (
                                                            <Marker
                                                                key={index}
                                                                position={{
                                                                    lat: parseFloat(enlem),
                                                                    lng: parseFloat(boylam)
                                                                }}
                                                                icon={{
                                                                    url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                            <circle cx="12" cy="12" r="8" fill="#ff6b35" stroke="#ffffff" stroke-width="2"/>
                                                                            <circle cx="12" cy="12" r="3" fill="#ffffff"/>
                                                                        </svg>
                                                                    `),
                                                                    scaledSize: window.google && window.google.maps ? new window.google.maps.Size(20, 20) : undefined,
                                                                    anchor: window.google && window.google.maps ? new window.google.maps.Point(10, 10) : undefined
                                                                }}
                                                                title={`${location.tarih} - ${location.sensorler?.sicaklik || 0}°C, %${location.sensorler?.nem || 0}`}
                                                            />
                                                        );
                                                    })}
                                                </GoogleMap>
                                            </div>
                                        )}
                                    </div>
                                </div>
                                {/* /google map */}
                            </div>
                        </div>

                        {/* Cihaz Veri Geçmişi Tablosu */}
                        <DeviceHistoryTable 
                            cihazID={shipmentDetail?.mgzKodu} 
                            onHistoryDataChange={handleHistoryData}
                            dateRange={deviceDateRange}
                        />

                        {/* PDF Rapor Oluşturma */}
                        {shipmentDetail?.mgzKodu && (
                            <div className="row mb-4">
                                <div className="col-12">
                                    <ReportGenerator
                                        cihazID={shipmentDetail.mgzKodu}
                                        tableData={historyLocations}
                                        onReportGenerated={(format, fileName) => {
                                            console.log(`${format} raporu oluşturuldu: ${fileName}`);
                                        }}
                                    />
                                </div>
                            </div>
                        )}

                    </main>
                    {/* /main sütun */}

                    <Footer />
                </div >
            </div >

            {/* Pulse animasyonu için CSS */}
            < style >
                {`
                    @keyframes pulse {
                        0% {
                            transform: scale(0.8);
                            opacity: 0.8;
                        }
                        70% {
                            transform: scale(1.5);
                            opacity: 0;
                        }
                        100% {
                            transform: scale(1.8);
                            opacity: 0;
                        }
                    }
                `}
            </style >

            {/* Sevkiyat Tamamlama Onay Modalı */}
            {
                showCompleteModal && (
                    <div className="modal fade show" style={{ display: 'block', backgroundColor: 'rgba(0,0,0,0.5)' }} tabIndex="-1">
                        <div className="modal-dialog modal-dialog-centered">
                            <div className="modal-content">
                                <div className="modal-header">
                                    <h5 className="modal-title">
                                        <FontAwesomeIcon icon={faCheck} className="me-2 text-success" />
                                        Sevkiyat Tamamlama Onayı
                                    </h5>
                                    <button
                                        type="button"
                                        className="btn-close"
                                        onClick={() => setShowCompleteModal(false)}
                                        disabled={isCompleting}
                                    ></button>
                                </div>
                                <div className="modal-body">
                                    <div className="alert alert-warning">
                                        <FontAwesomeIcon icon={faBell} className="me-2" />
                                        <strong>Dikkat!</strong> Bu işlemle birlikte sevkiyat tamamlanmış sayılacak ve geçmiş sevkiyatlara taşınacaktır. Devam etmek istiyor musunuz?
                                    </div>
                                    <div className="mb-3">
                                        <label htmlFor="completeNotes" className="form-label">Tamamlama Notları (İsteğe bağlı):</label>
                                        <textarea
                                            id="completeNotes"
                                            className="form-control"
                                            rows="3"
                                            value={completeNotes}
                                            onChange={(e) => setCompleteNotes(e.target.value)}
                                            placeholder="Sevkiyat tamamlama ile ilgili notlarınızı buraya yazabilirsiniz..."
                                            disabled={isCompleting}
                                        ></textarea>
                                    </div>
                                </div>
                                <div className="modal-footer">
                                    <button
                                        type="button"
                                        className="btn btn-secondary"
                                        onClick={() => setShowCompleteModal(false)}
                                        disabled={isCompleting}
                                    >
                                        İptal
                                    </button>
                                    <button
                                        type="button"
                                        className="btn btn-success"
                                        onClick={confirmCompleteShipment}
                                        disabled={isCompleting}
                                    >
                                        {isCompleting ? (
                                            <>
                                                <div className="spinner-border spinner-border-sm me-2" role="status">
                                                    <span className="visually-hidden">Loading...</span>
                                                </div>
                                                Tamamlanıyor...
                                            </>
                                        ) : (
                                            <>
                                                <FontAwesomeIcon icon={faCheck} className="me-1" />
                                                Sevkiyatı Tamamla
                                            </>
                                        )}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                )
            }
        </>
    );
};

export default ShipmentView; 