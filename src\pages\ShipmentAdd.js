import React, { useState, useEffect, useRef } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCircleCheck } from '@fortawesome/free-regular-svg-icons';
import {
    faTruck, faLocationDot, faIndustry, faPhoneVolume,
    faLeaf, faPallet, faWeightScale, faWeightHanging,
    faUsers, faTemperatureHalf, faIdCard,
    faArrowRight, faPlus, faMicrochip, faBattery2, faCoins
} from '@fortawesome/free-solid-svg-icons';

// Statik CSS import
import '../styles/ionRangeSlider.css';

import Header from '../components/Header';
import Sidebar from '../components/Sidebar';
import Footer from '../components/Footer';
import { cihazIDService } from '../api/dbService';

const ShipmentAdd = () => {
    // Aktif kullanıcı bilgisini al
    const [activeUser, setActiveUser] = useState(null);

    // İnaktif cihazlar listesi
    const [availableDevices, setAvailableDevices] = useState([]);
    const [loadingDevices, setLoadingDevices] = useState(false);
    const [selectedDevice, setSelectedDevice] = useState(null);

    // Form başlangıç durumunu tanımla (backend database şemasına uygun)
    const [formData, setFormData] = useState({
        mgz24_kodu: '',
        sevkiyat_adi: '',
        plaka_no: '',
        cikis_lokasyon: '',
        varis_lokasyon: '',
        surucu_adi: '',
        surucu_telefon: '',
        urun_bilgisi: '',
        palet_sayisi: '',
        net_agirlik: '',
        brut_agirlik: '',
        sicaklik_araligi: '2°C - 8°C',
        musteri_ID: '', // Kullanıcı ID'si için alan ekledik
        cihaz_id: '', // Seçilen cihaz ID'si
        gonderen_firma_id: '',
        alici_firma_id: ''
    });

    const [tempRange, setTempRange] = useState({
        min: 2,
        max: 8
    });

    const sliderRef = useRef(null);
    const sliderInstance = useRef(null);

    // Kullanıcı bilgilerini localStorage'dan al ve inaktif cihazları yükle
    useEffect(() => {
        const user = JSON.parse(localStorage.getItem('user'));
        if (user) {
            setActiveUser(user);
            const userId = user?.user?.musteri_ID || user?.user?.id || user?.id;

            // Kullanıcı ID'sini formData'ya ekle
            setFormData(prevState => ({
                ...prevState,
                musteri_ID: userId
            }));

            // İnaktif cihazları yükle
            loadAvailableDevices(userId);
        }
    }, []);

    // Kullanılabilir cihazları yükle
    const loadAvailableDevices = async (userId) => {
        try {
            setLoadingDevices(true);
            const devices = await cihazIDService.getInaktifCihazlar(userId);

            // Tüm inaktif cihazları göster (filtreleme kaldırıldı)
            setAvailableDevices(devices);
        } catch (error) {
            console.error('Cihazlar yüklenirken hata:', error);
            setAvailableDevices([]);
        } finally {
            setLoadingDevices(false);
        }
    };

    // Cihaz seçimi
    const handleDeviceSelect = (device) => {
        setSelectedDevice(device);
        setFormData(prevState => ({
            ...prevState,
            cihaz_id: device.CihazID,
            mgz24_kodu: device.CihazID // MGZ24 kodunu da otomatik doldur
        }));
    };

    // IonRangeSlider'ı başlat
    useEffect(() => {
        if (sliderRef.current && window.jQuery) {
            const $ = window.jQuery;
            const $slider = $(sliderRef.current);

            // IonRangeSlider'ı başlat
            $slider.ionRangeSlider({
                type: 'double',
                grid: true,
                min: -25,
                max: 35,
                from: tempRange.min,
                to: tempRange.max,
                step: 0.5,
                postfix: ' °C',
                prettify_enabled: true,
                skin: "flat",
                onChange: (data) => {
                    setTempRange({
                        min: data.from,
                        max: data.to
                    });
                    setFormData(prevData => ({
                        ...prevData,
                        sicaklik_araligi: `${data.from}°C - ${data.to}°C`
                    }));
                }
            });

            // Slider'ı sakla
            sliderInstance.current = $slider.data("ionRangeSlider");

            // Temizlik fonksiyonu
            return () => {
                if (sliderInstance.current) {
                    sliderInstance.current.destroy();
                }
            };
        }
    }, []);

    // tempRange değiştiğinde formData'yı güncelle
    useEffect(() => {
        setFormData(prev => ({
            ...prev,
            sicaklik_araligi: `${tempRange.min}°C - ${tempRange.max}°C`
        }));
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [tempRange]);

    const handleChange = (e) => {
        const { name, value, type, options } = e.target;

        if (type === 'select-multiple') {
            const selectedOptions = Array.from(options)
                .filter(option => option.selected)
                .map(option => option.value);

            setFormData({
                ...formData,
                [name]: selectedOptions
            });
        } else {
            // MGZ24 kodu seçildiğinde cihaz_id alanını da doldur
            if (name === 'mgz24_kodu' && value) {
                const selectedDevice = availableDevices.find(device => device.CihazID === value);
                setFormData({
                    ...formData,
                    [name]: value,
                    cihaz_id: selectedDevice ? selectedDevice.CihazID : ''
                });
            } else {
                setFormData({
                    ...formData,
                    [name]: value
                });
            }
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        // Kullanıcı bilgisi yoksa uyarı ver
        if (!formData.musteri_ID) {
            alert('Oturum bilgileriniz bulunamadı. Lütfen yeniden giriş yapın.');
            return;
        }

        // İnaktif cihaz kontrolü
        if (availableDevices.length === 0) {
            alert('Kullanılabilir inaktif cihaz bulunamadı. Sevkiyat oluşturulamaz.');
            return;
        }

        // MGZ24 kodu seçilmiş mi kontrol et
        if (!formData.mgz24_kodu) {
            alert('Lütfen bir MGZ24 cihazı seçiniz.');
            return;
        }

        console.log('Sevkiyat Bilgileri:', formData);

        try {
            // Form verileri zaten database şemasına uygun alan adlarında
            const apiData = {
                ...formData,
                // Boş alanları null olarak gönder
                mgz24_kodu: formData.mgz24_kodu || null,
                surucu_adi: formData.surucu_adi || null,
                surucu_telefon: formData.surucu_telefon || null,
                urun_bilgisi: formData.urun_bilgisi || null,
                palet_sayisi: formData.palet_sayisi || null,
                net_agirlik: formData.net_agirlik || null,
                brut_agirlik: formData.brut_agirlik || null,
                sicaklik_araligi: formData.sicaklik_araligi || null,
                gonderen_firma_id: formData.gonderen_firma_id || null,
                alici_firma_id: formData.alici_firma_id || null
            };

            // Servisi kullanarak API'ye verileri gönder
            const response = await fetch(`${process.env.REACT_APP_API_URL || '//mgz24.com/api'}/sevkiyatlar`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(apiData)
            });

            const data = await response.json();

            if (!response.ok) {
                console.error('API Response Error:', data);

                // Validation hatalarını detaylı göster
                if (data.errors && data.errors.length > 0) {
                    const errorMessages = data.errors.map(err =>
                        `${err.field}: ${err.message}`
                    ).join('\n');

                    throw new Error(`Validation Hataları:\n${errorMessages}\n\n${data.details || ''}`);
                }

                throw new Error(data.error || data.message || 'Sevkiyat eklenirken bir hata oluştu');
            }

            alert('Sevkiyat başarıyla oluşturuldu!');
            // İşlem başarılı olduğunda gerekirse yönlendirme yapılabilir
            // window.location.href = '/';
        } catch (error) {
            console.error('Sevkiyat eklenirken hata:', error);

            // Hata mesajını alert ile göster (uzun hatalar için scrollable)
            const errorMsg = error.message.length > 200
                ? error.message.substring(0, 200) + '...\n\nDetaylar için konsolu kontrol edin.'
                : error.message;

            alert(`Hata: ${errorMsg}`);
        }
    };

    return (
        <>
            <Header />
            <div className="container-fluid">
                <div className="row">
                    <Sidebar />

                    {/* main sütun */}
                    <main className="col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white">
                        <div className="pt-4 pb-2 mt-3 mb-4">
                            <div className="d-flex justify-content-between align-items-center mb-2">
                                <h1 className="h3 text-primary">
                                    <FontAwesomeIcon icon={faPlus} className="me-2" />
                                    Yeni Sevkiyat Oluştur
                                </h1>
                                {activeUser && (
                                    <div className="badge bg-light text-dark border py-2 px-3 d-flex align-items-center">
                                        <FontAwesomeIcon icon={faUsers} className="me-2" />
                                        <div>
                                            <small className="d-block text-muted">Müşteri</small>
                                            <strong>{activeUser.name}</strong> <small className="text-muted">#{activeUser.id}</small>
                                        </div>
                                    </div>
                                )}
                            </div>
                            <p className="text-muted">Yeni bir sevkiyat kaydı oluşturmak için aşağıdaki formu doldurun.</p>
                        </div>

                        {/* sevkiyat bilgi */}
                        <div className="row mb-4">
                            <div className="col-12">
                                <form className="row g-4" onSubmit={handleSubmit}>
                                    {/* Temel Sevkiyat Bilgileri Bölümü */}
                                    <div className="col-12">
                                        <div className="card shadow-sm border-0 rounded-3 mb-4">
                                            <div className="card-header bg-primary bg-opacity-10 border-0">
                                                <h5 className="mb-0 py-2">
                                                    <FontAwesomeIcon icon={faIdCard} className="me-2" />
                                                    Temel Sevkiyat Bilgileri
                                                </h5>
                                            </div>
                                            <div className="card-body pt-4">
                                                <div className="row g-4">
                                                    <div className="col-lg-3">
                                                        <label htmlFor="mgz24_kodu" className="form-label small text-uppercase fw-bold mb-1 text-secondary bg-transparent ps-0">MGZ24 Kodu*</label>
                                                        <div className="input-group shadow-sm">
                                                            <select
                                                                className="form-select border"
                                                                name="mgz24_kodu"
                                                                id="mgz24_kodu"
                                                                value={formData.mgz24_kodu}
                                                                onChange={handleChange}
                                                                required
                                                                disabled={loadingDevices || availableDevices.length === 0}
                                                            >
                                                                <option value="">
                                                                    {loadingDevices
                                                                        ? 'Cihazlar yükleniyor...'
                                                                        : availableDevices.length === 0
                                                                            ? 'Kullanılabilir cihaz yok'
                                                                            : 'Cihaz seçiniz'
                                                                    }
                                                                </option>
                                                                {availableDevices.map((device) => (
                                                                    <option key={device.ID} value={device.CihazID}>
                                                                        {device.CihazID} - {device.kredi_gun}
                                                                    </option>
                                                                ))}
                                                            </select>
                                                            <span className="input-group-text bg-white border">
                                                                <FontAwesomeIcon icon={faIdCard} />
                                                            </span>
                                                        </div>
                                                        {availableDevices.length === 0 && !loadingDevices && (
                                                            <div className="form-text text-danger">
                                                                <FontAwesomeIcon icon={faMicrochip} className="me-1" />
                                                                Kullanılabilir inaktif cihaz bulunamadı. Sevkiyat oluşturulamaz.
                                                            </div>
                                                        )}
                                                    </div>

                                                    <div className="col-lg-6">
                                                        <label htmlFor="sevkiyat_adi" className="form-label small text-uppercase fw-bold mb-1 text-secondary bg-transparent ps-0">Sevkiyat Adı*</label>
                                                        <div className="input-group shadow-sm">
                                                            <input
                                                                type="text"
                                                                className="form-control border"
                                                                name="sevkiyat_adi"
                                                                id="sevkiyat_adi"
                                                                value={formData.sevkiyat_adi}
                                                                onChange={handleChange}
                                                                required
                                                                placeholder="Örn: Moldova Sevkiyatı"
                                                            />
                                                            <span className="input-group-text bg-white border"><FontAwesomeIcon icon={faTruck} /></span>
                                                        </div>
                                                    </div>

                                                    <div className="col-lg-3">
                                                        <label htmlFor="plaka_no" className="form-label small text-uppercase fw-bold mb-1 text-secondary bg-transparent ps-0">Plaka no*</label>
                                                        <div className="input-group shadow-sm">
                                                            <input
                                                                type="text"
                                                                className="form-control border"
                                                                name="plaka_no"
                                                                id="plaka_no"
                                                                value={formData.plaka_no}
                                                                onChange={handleChange}
                                                                required
                                                                placeholder="Örn: 34 AB 1234"
                                                            />
                                                            <span className="input-group-text bg-white border"><FontAwesomeIcon icon={faTruck} /></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Nakliye ve Ürün Detayları Bölümü */}
                                    <div className="col-12">
                                        <div className="card shadow-sm border-0 rounded-3 mb-4">
                                            <div className="card-header bg-primary bg-opacity-10 border-0">
                                                <h5 className="mb-0 py-2">
                                                    <FontAwesomeIcon icon={faLocationDot} className="me-2" />
                                                    Nakliye ve Ürün Detayları
                                                </h5>
                                            </div>
                                            <div className="card-body pt-4">
                                                <div className="row g-4">
                                                    <div className="col-md-6 col-lg-3">
                                                        <label htmlFor="cikis_lokasyon" className="form-label small text-uppercase fw-bold mb-1 text-secondary bg-transparent ps-0">Çıkış Yeri*</label>
                                                        <div className="input-group shadow-sm">
                                                            <input
                                                                type="text"
                                                                className="form-control border"
                                                                name="cikis_lokasyon"
                                                                id="cikis_lokasyon"
                                                                value={formData.cikis_lokasyon}
                                                                onChange={handleChange}
                                                                required
                                                                placeholder="Örn: İstanbul"
                                                            />
                                                            <span className="input-group-text bg-white border"><FontAwesomeIcon icon={faLocationDot} /></span>
                                                        </div>
                                                    </div>

                                                    <div className="col-md-6 col-lg-3">
                                                        <div className="position-relative">
                                                            <div className="d-none d-lg-block position-absolute" style={{ top: "20px", left: "-20px", zIndex: "1" }}>
                                                                <FontAwesomeIcon icon={faArrowRight} className="text-primary" />
                                                            </div>
                                                        </div>
                                                        <label htmlFor="varis_lokasyon" className="form-label small text-uppercase fw-bold mb-1 text-secondary bg-transparent ps-0">Varış Yeri*</label>
                                                        <div className="input-group shadow-sm">
                                                            <input
                                                                type="text"
                                                                className="form-control border"
                                                                name="varis_lokasyon"
                                                                id="varis_lokasyon"
                                                                value={formData.varis_lokasyon}
                                                                onChange={handleChange}
                                                                required
                                                                placeholder="Örn: Ankara"
                                                            />
                                                            <span className="input-group-text bg-white border"><FontAwesomeIcon icon={faLocationDot} /></span>
                                                        </div>
                                                    </div>

                                                    <div className="col-md-6 col-lg-3">
                                                        <label htmlFor="surucu_adi" className="form-label small text-uppercase fw-bold mb-1 text-secondary bg-transparent ps-0">Şoför Adı</label>
                                                        <div className="input-group shadow-sm">
                                                            <input
                                                                type="text"
                                                                className="form-control border"
                                                                name="surucu_adi"
                                                                id="surucu_adi"
                                                                value={formData.surucu_adi}
                                                                onChange={handleChange}
                                                                placeholder="Örn: Ahmet Yılmaz"
                                                            />
                                                            <span className="input-group-text bg-white border"><FontAwesomeIcon icon={faUsers} /></span>
                                                        </div>
                                                    </div>

                                                    <div className="col-md-6 col-lg-3">
                                                        <label htmlFor="surucu_telefon" className="form-label small text-uppercase fw-bold mb-1 text-secondary bg-transparent ps-0">Şoför Telefon</label>
                                                        <div className="input-group shadow-sm">
                                                            <input
                                                                type="text"
                                                                className="form-control border"
                                                                name="surucu_telefon"
                                                                id="surucu_telefon"
                                                                value={formData.surucu_telefon}
                                                                onChange={handleChange}
                                                                placeholder="Örn: 05XX XXX XX XX"
                                                            />
                                                            <span className="input-group-text bg-white border"><FontAwesomeIcon icon={faPhoneVolume} /></span>
                                                        </div>
                                                    </div>

                                                    <div className="col-md-6 col-lg-3">
                                                        <label htmlFor="urun_bilgisi" className="form-label small text-uppercase fw-bold mb-1 text-secondary bg-transparent ps-0">Ürün Bilgisi</label>
                                                        <div className="input-group shadow-sm">
                                                            <input
                                                                type="text"
                                                                className="form-control border"
                                                                name="urun_bilgisi"
                                                                id="urun_bilgisi"
                                                                value={formData.urun_bilgisi}
                                                                onChange={handleChange}
                                                                placeholder="Örn: Limon"
                                                            />
                                                            <span className="input-group-text bg-white border"><FontAwesomeIcon icon={faLeaf} /></span>
                                                        </div>
                                                    </div>

                                                    <div className="col-md-6 col-lg-3">
                                                        <label htmlFor="palet_sayisi" className="form-label small text-uppercase fw-bold mb-1 text-secondary bg-transparent ps-0">Palet Sayısı</label>
                                                        <div className="input-group shadow-sm">
                                                            <input
                                                                type="text"
                                                                className="form-control border"
                                                                name="palet_sayisi"
                                                                id="palet_sayisi"
                                                                value={formData.palet_sayisi}
                                                                onChange={handleChange}
                                                                placeholder="Örn: 46"
                                                            />
                                                            <span className="input-group-text bg-white border"><FontAwesomeIcon icon={faPallet} /></span>
                                                        </div>
                                                    </div>

                                                    <div className="col-md-6 col-lg-3">
                                                        <label htmlFor="net_agirlik" className="form-label small text-uppercase fw-bold mb-1 text-secondary bg-transparent ps-0">Net Ağırlık (kg)</label>
                                                        <div className="input-group shadow-sm">
                                                            <input
                                                                type="text"
                                                                className="form-control border"
                                                                name="net_agirlik"
                                                                id="net_agirlik"
                                                                value={formData.net_agirlik}
                                                                onChange={handleChange}
                                                                placeholder="Örn: 8932"
                                                            />
                                                            <span className="input-group-text bg-white border"><FontAwesomeIcon icon={faWeightScale} /></span>
                                                        </div>
                                                    </div>

                                                    <div className="col-md-6 col-lg-3">
                                                        <label htmlFor="brut_agirlik" className="form-label small text-uppercase fw-bold mb-1 text-secondary bg-transparent ps-0">Brüt Ağırlık (kg)</label>
                                                        <div className="input-group shadow-sm">
                                                            <input
                                                                type="text"
                                                                className="form-control border"
                                                                name="brut_agirlik"
                                                                id="brut_agirlik"
                                                                value={formData.brut_agirlik}
                                                                onChange={handleChange}
                                                                placeholder="Örn: 11061"
                                                            />
                                                            <span className="input-group-text bg-white border"><FontAwesomeIcon icon={faWeightHanging} /></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* İzleme ve Sıcaklık Ayarları Bölümü */}
                                    <div className="col-12">
                                        <div className="card shadow-sm border-0 rounded-3 mb-4">
                                            <div className="card-header bg-primary bg-opacity-10 border-0">
                                                <h5 className="mb-0 py-2">
                                                    <FontAwesomeIcon icon={faUsers} className="me-2" />
                                                    İzleme ve Sıcaklık Ayarları
                                                </h5>
                                            </div>
                                            <div className="card-body pt-4">
                                                <div className="row g-4">
                                                    <div className="col-lg-6">
                                                        <label htmlFor="izleyici" className="form-label small text-uppercase fw-bold mb-1 text-secondary bg-transparent ps-0">Sevkiyat izleme yetkilileri</label>
                                                        <div className="shadow-sm border rounded-3 p-1 bg-light">
                                                            <p className="text-muted small mb-2 mt-2 px-2">
                                                                <i className="fas fa-info-circle me-1"></i>
                                                                CTRL tuşuna basık tutup birden fazla seçim yapabilirsiniz.
                                                            </p>
                                                            <select
                                                                multiple
                                                                className="form-select border-0"
                                                                name="izleyici"
                                                                id="izleyici"
                                                                value={formData.izleyici}
                                                                onChange={handleChange}
                                                                style={{ height: "150px" }}
                                                            >
                                                                <option value="<EMAIL>"><EMAIL> (CCL markets)</option>
                                                                <option value="<EMAIL>"><EMAIL> (CCL markets)</option>
                                                                <option value="<EMAIL>"><EMAIL> (Nakliye)</option>
                                                                <option value="<EMAIL>"><EMAIL> (Müşteri)</option>
                                                            </select>
                                                        </div>
                                                    </div>

                                                    <div className="col-lg-6">
                                                        <label htmlFor="sicaklik_aralik" className="form-label small text-uppercase fw-bold mb-1 text-secondary bg-transparent ps-0">
                                                            <FontAwesomeIcon icon={faTemperatureHalf} className="me-2" />
                                                            Sıcaklık limit aralığı
                                                        </label>

                                                        <div className="shadow-sm border rounded-3 p-4 bg-light">
                                                            <div className="mb-4 mt-2">
                                                                <input
                                                                    ref={sliderRef}
                                                                    type="text"
                                                                    className="js-range-slider"
                                                                    name="sicaklik_slider"
                                                                    value=""
                                                                />
                                                            </div>

                                                            <div className="input-group mt-3">
                                                                <input
                                                                    type="text"
                                                                    className="form-control border"
                                                                    name="sicaklik_aralik"
                                                                    id="sicaklik_aralik"
                                                                    value={formData.sicaklik_aralik}
                                                                    onChange={handleChange}
                                                                    readOnly
                                                                />
                                                                <span className="input-group-text bg-white border"><FontAwesomeIcon icon={faTemperatureHalf} /></span>
                                                            </div>
                                                            <small className="form-text text-muted d-block mt-2">
                                                                <i className="fas fa-info-circle me-1"></i>
                                                                Önerilen değer: 2°C - 8°C (Gıda ürünleri için)
                                                            </small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="col-12">
                                        <div className="d-flex justify-content-end">
                                            <button type="submit" className="btn btn-lg btn-primary px-5 fw-bold shadow-sm">
                                                <FontAwesomeIcon icon={faCircleCheck} className="me-2" /> Sevkiyatı oluştur
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </main>
                    {/* /main sütun */}

                    <Footer />
                </div>
            </div>
        </>
    );
};

export default ShipmentAdd; 