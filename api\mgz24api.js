import express from 'express';
import cors from 'cors';
import https from 'https';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import pool from '../inc/db.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const port = 3001; // HTTPS için port 3001

// SSL sertifika dosyaları
const sslOptions = {
    key: fs.readFileSync('/etc/letsencrypt/live/ffl21.fun/privkey.pem'),
    cert: fs.readFileSync('/etc/letsencrypt/live/ffl21.fun/fullchain.pem')
};

// Middleware
app.use(cors({
    origin: ['https://ffl21.fun', 'http://localhost:3000', 'https://mgz24.com', 'https://www.mgz24.com'],
    credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// CORS headers (manual)
app.use((req, res, next) => {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept');
    res.setHeader('Access-Control-Allow-Credentials', 'true');

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
        return;
    }

    next();
});

// Güvenlik headers
app.use((req, res, next) => {
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    next();
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

// Tüm cihazları listeleyen endpoint
app.get('/api/cihazlar', async (req, res) => {
    try {
        const limit = parseInt(req.query.limit) || 100;
        const page = parseInt(req.query.page) || 1;
        const offset = (page - 1) * limit;

        if (limit <= 0 || limit > 1000) {
            return res.status(400).json({
                success: false,
                message: 'Limit değeri 1-1000 arasında olmalıdır.'
            });
        }

        // Cihazları ve son verilerini getir
        const [rows] = await pool.execute(
            `SELECT
                c.CihazID,
                c.IP_adres,
                c.Operator,
                c.Ulke,
                c.Enlem,
                c.Boylam,
                c.Sicaklik,
                c.Nem,
                c.Basinc,
                c.Isik,
                c.Pil,
                c.KayitAralik,
                c.CihazGun,
                c.Aktarim,
                c.Notlar,
                DATE_FORMAT(c.Tarih, '%Y-%m-%d %H:%i:%s') as SonGuncellenme,
                s.MusteriID,
                s.GoldCihaz,
                s.KullanimBitti,
                s.KalanSure,
                DATE_FORMAT(s.BaslamaTarihi, '%Y-%m-%d %H:%i:%s') as BaslamaTarihi,
                DATE_FORMAT(s.BitisTarihi, '%Y-%m-%d %H:%i:%s') as BitisTarihi
            FROM CihazBilgi c
            LEFT JOIN SimICCID s ON c.CihazID = s.CihazID
            WHERE c.CihazID IS NOT NULL
            GROUP BY c.CihazID
            ORDER BY c.Tarih DESC
            LIMIT ? OFFSET ?`,
            [limit, offset]
        );

        // Toplam cihaz sayısını al
        const [countRows] = await pool.execute(
            'SELECT COUNT(DISTINCT CihazID) as total FROM CihazBilgi WHERE CihazID IS NOT NULL'
        );

        const totalDevices = countRows[0]?.total || 0;
        const totalPages = Math.ceil(totalDevices / limit);

        const cihazlar = rows.map(row => ({
            cihazID: row.CihazID,
            ipAdres: row.IP_adres,
            operator: row.Operator,
            ulke: row.Ulke,
            sonKonum: {
                enlem: row.Enlem ? parseFloat(row.Enlem) : null,
                boylam: row.Boylam ? parseFloat(row.Boylam) : null
            },
            sonSensorler: {
                sicaklik: row.Sicaklik ? parseFloat(row.Sicaklik) : null,
                nem: row.Nem ? parseFloat(row.Nem) : null,
                basinc: row.Basinc ? parseFloat(row.Basinc) : null,
                isik: row.Isik ? parseFloat(row.Isik) : null,
                pil: row.Pil ? parseFloat(row.Pil) : null
            },
            cihazDurum: {
                kayitAralik: row.KayitAralik,
                cihazGun: row.CihazGun,
                aktarim: row.Aktarim,
                sonGuncellenme: row.SonGuncellenme
            },
            simBilgisi: {
                musteriID: row.MusteriID,
                goldCihaz: row.GoldCihaz,
                kullanimBitti: row.KullanimBitti,
                kalanSure: row.KalanSure,
                baslamaTarihi: row.BaslamaTarihi,
                bitisTarihi: row.BitisTarihi
            },
            notlar: row.Notlar
        }));

        res.json({
            success: true,
            data: {
                cihazlar: cihazlar,
                pagination: {
                    currentPage: page,
                    totalPages: totalPages,
                    totalDevices: totalDevices,
                    limit: limit
                }
            }
        });

    } catch (error) {
        console.error('Cihaz listesi hatası:', error);
        res.status(500).json({
            success: false,
            message: 'Sunucu hatası oluştu.'
        });
    }
});

// Belirli bir cihazın detaylı bilgilerini getiren endpoint
app.get('/api/cihaz/:cihazID', async (req, res) => {
    try {
        const { cihazID } = req.params;

        // Cihazın temel bilgilerini ve son verilerini getir
        const [cihazRows] = await pool.execute(
            `SELECT
                c.CihazID,
                c.IP_adres,
                c.ICCID,
                c.Operator,
                c.Ulke,
                c.Enlem,
                c.Boylam,
                c.URL,
                c.DataNo,
                c.Sicaklik,
                c.Nem,
                c.Basinc,
                c.Isik,
                c.Pil,
                c.KayitAralik,
                c.CihazGun,
                c.Secim,
                c.Notlar,
                c.Aktarim,
                DATE_FORMAT(c.Tarih, '%Y-%m-%d %H:%i:%s') as SonGuncellenme
            FROM CihazBilgi c
            WHERE c.CihazID = ?
            ORDER BY c.Tarih DESC
            LIMIT 1`,
            [cihazID]
        );

        if (cihazRows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Cihaz bulunamadı.'
            });
        }

        // SIM kart bilgilerini getir
        const [simRows] = await pool.execute(
            `SELECT
                s.ICCID,
                s.TestBitti,
                s.GoldCihaz,
                DATE_FORMAT(s.SistemeEklemeTarihi, '%Y-%m-%d %H:%i:%s') as SistemeEklemeTarihi,
                DATE_FORMAT(s.PaketlemeTarihi, '%Y-%m-%d %H:%i:%s') as PaketlemeTarihi,
                DATE_FORMAT(s.BaslamaTarihi, '%Y-%m-%d %H:%i:%s') as BaslamaTarihi,
                DATE_FORMAT(s.BitisTarihi, '%Y-%m-%d %H:%i:%s') as BitisTarihi,
                s.KullanimBitti,
                s.KalanSure,
                s.MusteriID,
                s.CihazGun,
                s.KutudakiAdet,
                s.KutuBarkod,
                DATE_FORMAT(s.KutuTarih, '%Y-%m-%d %H:%i:%s') as KutuTarih,
                s.KoliBarkod,
                DATE_FORMAT(s.KoliTarih, '%Y-%m-%d %H:%i:%s') as KoliTarih,
                s.Reset,
                s.Notlar as SimNotlar,
                s.Notlaradmin
            FROM SimICCID s
            WHERE s.CihazID = ?
            ORDER BY s.SistemeEklemeTarihi DESC
            LIMIT 1`,
            [cihazID]
        );

        const cihaz = cihazRows[0];
        const sim = simRows[0];

        // Son 24 saatteki veri sayısını al
        const [veriSayisiRows] = await pool.execute(
            `SELECT COUNT(*) as veriSayisi
            FROM CihazBilgi
            WHERE CihazID = ? AND Tarih >= DATE_SUB(NOW(), INTERVAL 24 HOUR)`,
            [cihazID]
        );

        const cihazDetay = {
            cihazID: cihaz.CihazID,
            baglantiBilgisi: {
                ipAdres: cihaz.IP_adres,
                iccid: cihaz.ICCID,
                operator: cihaz.Operator,
                ulke: cihaz.Ulke,
                url: cihaz.URL,
                dataNo: cihaz.DataNo
            },
            sonKonum: {
                enlem: cihaz.Enlem ? parseFloat(cihaz.Enlem) : null,
                boylam: cihaz.Boylam ? parseFloat(cihaz.Boylam) : null
            },
            sonSensorler: {
                sicaklik: cihaz.Sicaklik ? parseFloat(cihaz.Sicaklik) : null,
                nem: cihaz.Nem ? parseFloat(cihaz.Nem) : null,
                basinc: cihaz.Basinc ? parseFloat(cihaz.Basinc) : null,
                isik: cihaz.Isik ? parseFloat(cihaz.Isik) : null,
                pil: cihaz.Pil ? parseFloat(cihaz.Pil) : null
            },
            cihazDurum: {
                kayitAralik: cihaz.KayitAralik,
                cihazGun: cihaz.CihazGun,
                aktarim: cihaz.Aktarim,
                secim: cihaz.Secim,
                sonGuncellenme: cihaz.SonGuncellenme,
                son24SaatVeri: veriSayisiRows[0]?.veriSayisi || 0
            },
            simBilgisi: sim ? {
                iccid: sim.ICCID,
                testBitti: sim.TestBitti,
                goldCihaz: sim.GoldCihaz,
                sistemeEklemeTarihi: sim.SistemeEklemeTarihi,
                paketlemeTarihi: sim.PaketlemeTarihi,
                baslamaTarihi: sim.BaslamaTarihi,
                bitisTarihi: sim.BitisTarihi,
                kullanimBitti: sim.KullanimBitti,
                kalanSure: sim.KalanSure,
                musteriID: sim.MusteriID,
                cihazGun: sim.CihazGun,
                kutudakiAdet: sim.KutudakiAdet,
                kutuBarkod: sim.KutuBarkod,
                kutuTarih: sim.KutuTarih,
                koliBarkod: sim.KoliBarkod,
                koliTarih: sim.KoliTarih,
                reset: sim.Reset,
                simNotlar: sim.SimNotlar,
                adminNotlar: sim.Notlaradmin
            } : null,
            notlar: cihaz.Notlar
        };

        res.json({
            success: true,
            data: cihazDetay
        });

    } catch (error) {
        console.error('Cihaz detay hatası:', error);
        res.status(500).json({
            success: false,
            message: 'Sunucu hatası oluştu.'
        });
    }
});

// SIM kart bilgilerini getiren endpoint
app.get('/api/sim/:cihazID', async (req, res) => {
    try {
        const { cihazID } = req.params;

        const [simRows] = await pool.execute(
            `SELECT
                s.ID,
                s.CihazID,
                s.ICCID,
                s.TestBitti,
                s.GoldCihaz,
                DATE_FORMAT(s.SistemeEklemeTarihi, '%Y-%m-%d %H:%i:%s') as SistemeEklemeTarihi,
                DATE_FORMAT(s.PaketlemeTarihi, '%Y-%m-%d %H:%i:%s') as PaketlemeTarihi,
                DATE_FORMAT(s.BaslamaTarihi, '%Y-%m-%d %H:%i:%s') as BaslamaTarihi,
                DATE_FORMAT(s.BitisTarihi, '%Y-%m-%d %H:%i:%s') as BitisTarihi,
                s.KullanimBitti,
                s.KalanSure,
                s.MusteriID,
                s.CihazGun,
                s.KutudakiAdet,
                s.KutuBarkod,
                DATE_FORMAT(s.KutuTarih, '%Y-%m-%d %H:%i:%s') as KutuTarih,
                s.KoliBarkod,
                DATE_FORMAT(s.KoliTarih, '%Y-%m-%d %H:%i:%s') as KoliTarih,
                s.Reset,
                s.Notlar,
                s.Notlaradmin
            FROM SimICCID s
            WHERE s.CihazID = ?
            ORDER BY s.SistemeEklemeTarihi DESC`,
            [cihazID]
        );

        if (simRows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Bu cihaza ait SIM kart bilgisi bulunamadı.'
            });
        }

        const simBilgileri = simRows.map(row => ({
            id: row.ID,
            cihazID: row.CihazID,
            iccid: row.ICCID,
            durum: {
                testBitti: row.TestBitti,
                goldCihaz: row.GoldCihaz,
                kullanimBitti: row.KullanimBitti,
                kalanSure: row.KalanSure,
                reset: row.Reset
            },
            tarihler: {
                sistemeEklemeTarihi: row.SistemeEklemeTarihi,
                paketlemeTarihi: row.PaketlemeTarihi,
                baslamaTarihi: row.BaslamaTarihi,
                bitisTarihi: row.BitisTarihi
            },
            musteriID: row.MusteriID,
            cihazGun: row.CihazGun,
            kutudakiAdet: row.KutudakiAdet,
            barkodlar: {
                kutuBarkod: row.KutuBarkod,
                kutuTarih: row.KutuTarih,
                koliBarkod: row.KoliBarkod,
                koliTarih: row.KoliTarih
            },
            notlar: row.Notlar,
            adminNotlar: row.Notlaradmin
        }));

        res.json({
            success: true,
            data: {
                cihazID: cihazID,
                simBilgileri: simBilgileri
            }
        });

    } catch (error) {
        console.error('SIM bilgisi hatası:', error);
        res.status(500).json({
            success: false,
            message: 'Sunucu hatası oluştu.'
        });
    }
});

// Müşteri ID'ye göre SIM kartları getiren endpoint
app.get('/api/sim/musteri/:musteriID', async (req, res) => {
    try {
        const { musteriID } = req.params;
        const limit = parseInt(req.query.limit) || 100;
        const page = parseInt(req.query.page) || 1;
        const offset = (page - 1) * limit;

        const [simRows] = await pool.execute(
            `SELECT
                s.ID,
                s.CihazID,
                s.ICCID,
                s.TestBitti,
                s.GoldCihaz,
                DATE_FORMAT(s.SistemeEklemeTarihi, '%Y-%m-%d %H:%i:%s') as SistemeEklemeTarihi,
                DATE_FORMAT(s.BaslamaTarihi, '%Y-%m-%d %H:%i:%s') as BaslamaTarihi,
                DATE_FORMAT(s.BitisTarihi, '%Y-%m-%d %H:%i:%s') as BitisTarihi,
                s.KullanimBitti,
                s.KalanSure,
                s.CihazGun
            FROM SimICCID s
            WHERE s.MusteriID = ?
            ORDER BY s.SistemeEklemeTarihi DESC
            LIMIT ? OFFSET ?`,
            [musteriID, limit, offset]
        );

        const musteriSimleri = simRows.map(row => ({
            id: row.ID,
            cihazID: row.CihazID,
            iccid: row.ICCID,
            goldCihaz: row.GoldCihaz,
            baslamaTarihi: row.BaslamaTarihi,
            bitisTarihi: row.BitisTarihi,
            kullanimBitti: row.KullanimBitti,
            kalanSure: row.KalanSure,
            cihazGun: row.CihazGun
        }));

        res.json({
            success: true,
            data: {
                musteriID: musteriID,
                simKartlari: musteriSimleri,
                toplamSim: simRows.length
            }
        });

    } catch (error) {
        console.error('Müşteri SIM bilgisi hatası:', error);
        res.status(500).json({
            success: false,
            message: 'Sunucu hatası oluştu.'
        });
    }
});

// Cihaz durumu ve batarya bilgilerini getiren endpoint
app.get('/api/cihaz/:cihazID/status', async (req, res) => {
    try {
        const { cihazID } = req.params;

        // Son 1 saat, 24 saat ve 7 günlük veri istatistikleri
        const [statusRows] = await pool.execute(
            `SELECT
                CihazID,
                Pil,
                Sicaklik,
                Nem,
                Enlem,
                Boylam,
                Aktarim,
                DATE_FORMAT(Tarih, '%Y-%m-%d %H:%i:%s') as SonGuncellenme,
                TIMESTAMPDIFF(MINUTE, Tarih, NOW()) as DakikalarOnce,
                (
                    SELECT COUNT(*)
                    FROM CihazBilgi
                    WHERE CihazID = ? AND Tarih >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
                ) as Son1SaatVeri,
                (
                    SELECT COUNT(*)
                    FROM CihazBilgi
                    WHERE CihazID = ? AND Tarih >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                ) as Son24SaatVeri,
                (
                    SELECT COUNT(*)
                    FROM CihazBilgi
                    WHERE CihazID = ? AND Tarih >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                ) as Son7GunVeri,
                (
                    SELECT AVG(Pil)
                    FROM CihazBilgi
                    WHERE CihazID = ? AND Tarih >= DATE_SUB(NOW(), INTERVAL 24 HOUR) AND Pil IS NOT NULL
                ) as OrtalamaBatarya24Saat
            FROM CihazBilgi
            WHERE CihazID = ?
            ORDER BY Tarih DESC
            LIMIT 1`,
            [cihazID, cihazID, cihazID, cihazID, cihazID]
        );

        if (statusRows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Cihaz bulunamadı.'
            });
        }

        const status = statusRows[0];
        const dakikalarOnce = status.DakikalarOnce || 0;

        // Cihaz durumunu belirle
        let cihazDurumu;
        if (dakikalarOnce <= 10) {
            cihazDurumu = 'online';
        } else if (dakikalarOnce <= 60) {
            cihazDurumu = 'warning';
        } else {
            cihazDurumu = 'offline';
        }

        // Batarya durumunu belirle
        let bataryaDurumu;
        const batarya = status.Pil ? parseFloat(status.Pil) : 0;
        if (batarya > 60) {
            bataryaDurumu = 'good';
        } else if (batarya > 30) {
            bataryaDurumu = 'medium';
        } else {
            bataryaDurumu = 'low';
        }

        const cihazStatus = {
            cihazID: cihazID,
            durum: cihazDurumu,
            sonGuncellenme: status.SonGuncellenme,
            dakikalarOnce: dakikalarOnce,
            batarya: {
                mevcut: batarya,
                durum: bataryaDurumu,
                ortalama24Saat: status.OrtalamaBatarya24Saat ? parseFloat(status.OrtalamaBatarya24Saat) : null
            },
            sonSensorler: {
                sicaklik: status.Sicaklik ? parseFloat(status.Sicaklik) : null,
                nem: status.Nem ? parseFloat(status.Nem) : null
            },
            konum: {
                enlem: status.Enlem ? parseFloat(status.Enlem) : null,
                boylam: status.Boylam ? parseFloat(status.Boylam) : null
            },
            istatistikler: {
                son1SaatVeri: status.Son1SaatVeri || 0,
                son24SaatVeri: status.Son24SaatVeri || 0,
                son7GunVeri: status.Son7GunVeri || 0
            },
            aktarim: status.Aktarim
        };

        res.json({
            success: true,
            data: cihazStatus
        });

    } catch (error) {
        console.error('Cihaz durumu hatası:', error);
        res.status(500).json({
            success: false,
            message: 'Sunucu hatası oluştu.'
        });
    }
});

// Tüm cihazların durumlarını getiren endpoint
app.get('/api/cihazlar/status', async (req, res) => {
    try {
        const limit = parseInt(req.query.limit) || 50;
        const musteriID = req.query.musteriID;

        let whereClause = 'WHERE c.CihazID IS NOT NULL';
        let params = [];

        if (musteriID) {
            whereClause += ' AND s.MusteriID = ?';
            params.push(musteriID);
        }

        const [statusRows] = await pool.execute(
            `SELECT
                c.CihazID,
                c.Pil,
                c.Sicaklik,
                c.Nem,
                c.Enlem,
                c.Boylam,
                c.Aktarim,
                DATE_FORMAT(c.Tarih, '%Y-%m-%d %H:%i:%s') as SonGuncellenme,
                TIMESTAMPDIFF(MINUTE, c.Tarih, NOW()) as DakikalarOnce,
                s.GoldCihaz,
                s.MusteriID,
                s.KullanimBitti
            FROM CihazBilgi c
            LEFT JOIN SimICCID s ON c.CihazID = s.CihazID
            ${whereClause}
            GROUP BY c.CihazID
            ORDER BY c.Tarih DESC
            LIMIT ?`,
            [...params, limit]
        );

        const cihazDurumları = statusRows.map(row => {
            const dakikalarOnce = row.DakikalarOnce || 0;
            const batarya = row.Pil ? parseFloat(row.Pil) : 0;

            let cihazDurumu;
            if (dakikalarOnce <= 10) {
                cihazDurumu = 'online';
            } else if (dakikalarOnce <= 60) {
                cihazDurumu = 'warning';
            } else {
                cihazDurumu = 'offline';
            }

            let bataryaDurumu;
            if (batarya > 60) {
                bataryaDurumu = 'good';
            } else if (batarya > 30) {
                bataryaDurumu = 'medium';
            } else {
                bataryaDurumu = 'low';
            }

            return {
                cihazID: row.CihazID,
                durum: cihazDurumu,
                sonGuncellenme: row.SonGuncellenme,
                dakikalarOnce: dakikalarOnce,
                batarya: {
                    mevcut: batarya,
                    durum: bataryaDurumu
                },
                sonSensorler: {
                    sicaklik: row.Sicaklik ? parseFloat(row.Sicaklik) : null,
                    nem: row.Nem ? parseFloat(row.Nem) : null
                },
                konum: {
                    enlem: row.Enlem ? parseFloat(row.Enlem) : null,
                    boylam: row.Boylam ? parseFloat(row.Boylam) : null
                },
                goldCihaz: row.GoldCihaz,
                musteriID: row.MusteriID,
                kullanimBitti: row.KullanimBitti
            };
        });

        // Durum istatistikleri
        const online = cihazDurumları.filter(c => c.durum === 'online').length;
        const warning = cihazDurumları.filter(c => c.durum === 'warning').length;
        const offline = cihazDurumları.filter(c => c.durum === 'offline').length;

        res.json({
            success: true,
            data: {
                cihazlar: cihazDurumları,
                istatistikler: {
                    toplamCihaz: cihazDurumları.length,
                    online: online,
                    warning: warning,
                    offline: offline
                }
            }
        });

    } catch (error) {
        console.error('Cihaz durumları hatası:', error);
        res.status(500).json({
            success: false,
            message: 'Sunucu hatası oluştu.'
        });
    }
});

// Cihaz geçmişi verilerini getiren endpoint (tarih aralığı ile)
app.get('/api/cihaz/:cihazID/history', async (req, res) => {
    try {
        const { cihazID } = req.params;
        const limit = parseInt(req.query.limit) || 100;
        const page = parseInt(req.query.page) || 1;
        const offset = (page - 1) * limit;
        let baslangicTarihi = req.query.baslangicTarihi;
        let bitisTarihi = req.query.bitisTarihi;

        if (limit <= 0 || limit > 1000) {
            return res.status(400).json({
                success: false,
                message: 'Limit değeri 1-1000 arasında olmalıdır.'
            });
        }

        // Tarih formatını kontrol et ve düzenle
        if (baslangicTarihi) {
            if (baslangicTarihi.match(/^\d{4}-\d{2}-\d{2}$/)) {
                baslangicTarihi += ' 00:00:00';
            }
        }

        if (bitisTarihi) {
            if (bitisTarihi.match(/^\d{4}-\d{2}-\d{2}$/)) {
                bitisTarihi += ' 23:59:59';
            }
        }

        let query = `
            SELECT
                CihazID,
                IP_adres,
                ICCID,
                Operator,
                Ulke,
                Enlem,
                Boylam,
                URL,
                DataNo,
                Sicaklik,
                Nem,
                Basinc,
                Isik,
                Pil,
                KayitAralik,
                CihazGun,
                Secim,
                Aktarim,
                Notlar,
                DATE_FORMAT(Tarih, '%Y-%m-%d %H:%i:%s') as Tarih
            FROM CihazBilgi
            WHERE CihazID = ?
        `;

        const params = [cihazID];

        if (baslangicTarihi) {
            query += ` AND Tarih >= ?`;
            params.push(baslangicTarihi);
        }

        if (bitisTarihi) {
            query += ` AND Tarih <= ?`;
            params.push(bitisTarihi);
        }

        query += ` ORDER BY Tarih DESC LIMIT ${limit} OFFSET ${offset}`;
        // Bu satırı değiştirdim - ayrı ayrı push et
        //params.push(limit);
        //params.push(offset);

        console.log('SQL Query:', query);
        console.log('Parameters:', params);

        const [historyRows] = await pool.execute(query, params);

        if (historyRows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Belirtilen kriterlere uygun veri bulunamadı.'
            });
        }

        // Toplam kayıt sayısını al
        let countQuery = `SELECT COUNT(*) as total FROM CihazBilgi WHERE CihazID = ?`;
        const countParams = [cihazID];

        if (baslangicTarihi) {
            countQuery += ` AND Tarih >= ?`;
            countParams.push(baslangicTarihi);
        }

        if (bitisTarihi) {
            countQuery += ` AND Tarih <= ?`;
            countParams.push(bitisTarihi);
        }

        const [countRows] = await pool.execute(countQuery, countParams);
        const totalRecords = countRows[0]?.total || 0;
        const totalPages = Math.ceil(totalRecords / limit);

        const gecmisVeriler = historyRows.map(row => ({
            cihazID: row.CihazID,
            tarih: row.Tarih,
            baglantiBilgisi: {
                ipAdres: row.IP_adres,
                iccid: row.ICCID,
                operator: row.Operator,
                ulke: row.Ulke,
                url: row.URL,
                dataNo: row.DataNo
            },
            konum: {
                enlem: row.Enlem ? parseFloat(row.Enlem) : null,
                boylam: row.Boylam ? parseFloat(row.Boylam) : null
            },
            sensorler: {
                sicaklik: row.Sicaklik ? parseFloat(row.Sicaklik) : null,
                nem: row.Nem ? parseFloat(row.Nem) : null,
                basinc: row.Basinc ? parseFloat(row.Basinc) : null,
                isik: row.Isik ? parseFloat(row.Isik) : null,
                pil: row.Pil ? parseFloat(row.Pil) : null
            },
            cihazDurum: {
                kayitAralik: row.KayitAralik,
                cihazGun: row.CihazGun,
                secim: row.Secim,
                aktarim: row.Aktarim
            },
            notlar: row.Notlar
        }));

        res.json({
            success: true,
            data: {
                cihazID: cihazID,
                gecmisVeriler: gecmisVeriler,
                pagination: {
                    currentPage: page,
                    totalPages: totalPages,
                    totalRecords: totalRecords,
                    limit: limit,
                    hasNextPage: page < totalPages,
                    hasPreviousPage: page > 1
                },
                tarihAraligi: {
                    baslangic: baslangicTarihi,
                    bitis: bitisTarihi
                }
            }
        });

    } catch (error) {
        console.error('Cihaz geçmişi hatası:', error);
        res.status(500).json({
            success: false,
            message: 'Sunucu hatası oluştu.'
        });
    }
});

// Cihaz için rapor verileri getiren endpoint
app.get('/api/cihaz/:cihazID/report', async (req, res) => {
    try {
        const { cihazID } = req.params;
        let baslangicTarihi = req.query.baslangicTarihi;
        let bitisTarihi = req.query.bitisTarihi;
        const reportType = req.query.type || 'summary'; // summary, detailed, sensors

        // Varsayılan tarih aralığı (son 7 gün)
        if (!baslangicTarihi) {
            const date = new Date();
            date.setDate(date.getDate() - 7);
            baslangicTarihi = date.toISOString().split('T')[0] + ' 00:00:00';
        }

        if (!bitisTarihi) {
            bitisTarihi = new Date().toISOString().split('T')[0] + ' 23:59:59';
        }

        // Özet istatistikleri
        const [summaryRows] = await pool.execute(
            `SELECT
                COUNT(*) as toplamKayit,
                MIN(Tarih) as ilkKayit,
                MAX(Tarih) as sonKayit,
                AVG(Sicaklik) as ortalamaSicaklik,
                MIN(Sicaklik) as minSicaklik,
                MAX(Sicaklik) as maxSicaklik,
                AVG(Nem) as ortalamaNem,
                MIN(Nem) as minNem,
                MAX(Nem) as maxNem,
                AVG(Pil) as ortalamaPil,
                MIN(Pil) as minPil,
                MAX(Pil) as maxPil,
                COUNT(DISTINCT DATE(Tarih)) as aktifGunler
            FROM CihazBilgi
            WHERE CihazID = ? AND Tarih >= ? AND Tarih <= ?`,
            [cihazID, baslangicTarihi, bitisTarihi]
        );

        // Günlük istatistikler
        const [dailyRows] = await pool.execute(
            `SELECT
                DATE(Tarih) as tarih,
                COUNT(*) as kayitSayisi,
                AVG(Sicaklik) as ortalamaSicaklik,
                AVG(Nem) as ortalamaNem,
                AVG(Pil) as ortalamaPil,
                MIN(Tarih) as ilkKayit,
                MAX(Tarih) as sonKayit
            FROM CihazBilgi
            WHERE CihazID = ? AND Tarih >= ? AND Tarih <= ?
            GROUP BY DATE(Tarih)
            ORDER BY DATE(Tarih) DESC`,
            [cihazID, baslangicTarihi, bitisTarihi]
        );

        const summary = summaryRows[0];
        const raporVerisi = {
            cihazID: cihazID,
            raporTuru: reportType,
            tarihAraligi: {
                baslangic: baslangicTarihi,
                bitis: bitisTarihi
            },
            ozet: {
                toplamKayit: summary.toplamKayit || 0,
                ilkKayit: summary.ilkKayit,
                sonKayit: summary.sonKayit,
                aktifGunler: summary.aktifGunler || 0,
                sicaklik: {
                    ortalama: summary.ortalamaSicaklik ? parseFloat(summary.ortalamaSicaklik) : null,
                    minimum: summary.minSicaklik ? parseFloat(summary.minSicaklik) : null,
                    maksimum: summary.maxSicaklik ? parseFloat(summary.maxSicaklik) : null
                },
                nem: {
                    ortalama: summary.ortalamaNem ? parseFloat(summary.ortalamaNem) : null,
                    minimum: summary.minNem ? parseFloat(summary.minNem) : null,
                    maksimum: summary.maxNem ? parseFloat(summary.maxNem) : null
                },
                pil: {
                    ortalama: summary.ortalamaPil ? parseFloat(summary.ortalamaPil) : null,
                    minimum: summary.minPil ? parseFloat(summary.minPil) : null,
                    maksimum: summary.maxPil ? parseFloat(summary.maxPil) : null
                }
            },
            gunlukIstatistikler: dailyRows.map(row => ({
                tarih: row.tarih,
                kayitSayisi: row.kayitSayisi,
                ortalamaSicaklik: row.ortalamaSicaklik ? parseFloat(row.ortalamaSicaklik) : null,
                ortalamaNem: row.ortalamaNem ? parseFloat(row.ortalamaNem) : null,
                ortalamaPil: row.ortalamaPil ? parseFloat(row.ortalamaPil) : null,
                ilkKayit: row.ilkKayit,
                sonKayit: row.sonKayit
            }))
        };

        res.json({
            success: true,
            data: raporVerisi
        });

    } catch (error) {
        console.error('Rapor hatası:', error);
        res.status(500).json({
            success: false,
            message: 'Sunucu hatası oluştu.'
        });
    }
});

// Cihaz arama ve filtreleme endpoint'i
app.get('/api/cihazlar/search', async (req, res) => {
    try {
        const searchTerm = req.query.q || '';
        const operator = req.query.operator;
        const ulke = req.query.ulke;
        const goldCihaz = req.query.goldCihaz;
        const kullanimBitti = req.query.kullanimBitti;
        const musteriID = req.query.musteriID;
        const limit = parseInt(req.query.limit) || 50;
        const page = parseInt(req.query.page) || 1;
        const offset = (page - 1) * limit;
        const sortBy = req.query.sortBy || 'Tarih';
        const sortOrder = req.query.sortOrder || 'DESC';

        // Geçerli sıralama alanları
        const validSortFields = ['Tarih', 'CihazID', 'Sicaklik', 'Pil', 'Operator'];
        const validSortOrders = ['ASC', 'DESC'];

        if (!validSortFields.includes(sortBy) || !validSortOrders.includes(sortOrder)) {
            return res.status(400).json({
                success: false,
                message: 'Geçersiz sıralama parametresi.'
            });
        }

        let whereClause = 'WHERE c.CihazID IS NOT NULL';
        let params = [];

        // Arama terimi
        if (searchTerm) {
            whereClause += ' AND (c.CihazID LIKE ? OR c.IP_adres LIKE ? OR c.ICCID LIKE ?)';
            const searchPattern = `%${searchTerm}%`;
            params.push(searchPattern, searchPattern, searchPattern);
        }

        // Operator filtresi
        if (operator) {
            whereClause += ' AND c.Operator = ?';
            params.push(operator);
        }

        // Ülke filtresi
        if (ulke) {
            whereClause += ' AND c.Ulke = ?';
            params.push(ulke);
        }

        // Müşteri ID filtresi
        if (musteriID) {
            whereClause += ' AND s.MusteriID = ?';
            params.push(musteriID);
        }

        // Gold cihaz filtresi
        if (goldCihaz !== undefined) {
            whereClause += ' AND s.GoldCihaz = ?';
            params.push(goldCihaz);
        }

        // Kullanım bitti filtresi
        if (kullanimBitti !== undefined) {
            whereClause += ' AND s.KullanimBitti = ?';
            params.push(kullanimBitti);
        }

        // Ana sorgu
        const [searchRows] = await pool.execute(
            `SELECT
                c.CihazID,
                c.IP_adres,
                c.ICCID,
                c.Operator,
                c.Ulke,
                c.Enlem,
                c.Boylam,
                c.Sicaklik,
                c.Nem,
                c.Basinc,
                c.Isik,
                c.Pil,
                c.KayitAralik,
                c.CihazGun,
                c.Aktarim,
                c.Notlar,
                DATE_FORMAT(c.Tarih, '%Y-%m-%d %H:%i:%s') as SonGuncellenme,
                TIMESTAMPDIFF(MINUTE, c.Tarih, NOW()) as DakikalarOnce,
                s.MusteriID,
                s.GoldCihaz,
                s.KullanimBitti,
                s.KalanSure,
                DATE_FORMAT(s.BaslamaTarihi, '%Y-%m-%d %H:%i:%s') as BaslamaTarihi,
                DATE_FORMAT(s.BitisTarihi, '%Y-%m-%d %H:%i:%s') as BitisTarihi
            FROM CihazBilgi c
            LEFT JOIN SimICCID s ON c.CihazID = s.CihazID
            ${whereClause}
            GROUP BY c.CihazID
            ORDER BY c.${sortBy} ${sortOrder}
            LIMIT ? OFFSET ?`,
            [...params, limit, offset]
        );

        // Toplam kayıt sayısı
        const [countRows] = await pool.execute(
            `SELECT COUNT(DISTINCT c.CihazID) as total
            FROM CihazBilgi c
            LEFT JOIN SimICCID s ON c.CihazID = s.CihazID
            ${whereClause}`,
            params
        );

        const totalRecords = countRows[0]?.total || 0;
        const totalPages = Math.ceil(totalRecords / limit);

        // Sonuçları formatla
        const aramaSonuclari = searchRows.map(row => {
            const dakikalarOnce = row.DakikalarOnce || 0;
            const batarya = row.Pil ? parseFloat(row.Pil) : 0;

            let cihazDurumu;
            if (dakikalarOnce <= 10) {
                cihazDurumu = 'online';
            } else if (dakikalarOnce <= 60) {
                cihazDurumu = 'warning';
            } else {
                cihazDurumu = 'offline';
            }

            return {
                cihazID: row.CihazID,
                baglantiBilgisi: {
                    ipAdres: row.IP_adres,
                    iccid: row.ICCID,
                    operator: row.Operator,
                    ulke: row.Ulke
                },
                konum: {
                    enlem: row.Enlem ? parseFloat(row.Enlem) : null,
                    boylam: row.Boylam ? parseFloat(row.Boylam) : null
                },
                sonSensorler: {
                    sicaklik: row.Sicaklik ? parseFloat(row.Sicaklik) : null,
                    nem: row.Nem ? parseFloat(row.Nem) : null,
                    basinc: row.Basinc ? parseFloat(row.Basinc) : null,
                    isik: row.Isik ? parseFloat(row.Isik) : null,
                    pil: batarya
                },
                durum: cihazDurumu,
                sonGuncellenme: row.SonGuncellenme,
                dakikalarOnce: dakikalarOnce,
                simBilgisi: {
                    musteriID: row.MusteriID,
                    goldCihaz: row.GoldCihaz,
                    kullanimBitti: row.KullanimBitti,
                    kalanSure: row.KalanSure,
                    baslamaTarihi: row.BaslamaTarihi,
                    bitisTarihi: row.BitisTarihi
                },
                notlar: row.Notlar
            };
        });

        res.json({
            success: true,
            data: {
                aramaSonuclari: aramaSonuclari,
                pagination: {
                    currentPage: page,
                    totalPages: totalPages,
                    totalRecords: totalRecords,
                    limit: limit
                },
                filtreler: {
                    searchTerm: searchTerm,
                    operator: operator,
                    ulke: ulke,
                    goldCihaz: goldCihaz,
                    kullanimBitti: kullanimBitti,
                    musteriID: musteriID,
                    sortBy: sortBy,
                    sortOrder: sortOrder
                }
            }
        });

    } catch (error) {
        console.error('Arama hatası:', error);
        res.status(500).json({
            success: false,
            message: 'Sunucu hatası oluştu.'
        });
    }
});

// Filtreleme için kullanılabilir değerleri getiren endpoint
app.get('/api/cihazlar/filters', async (req, res) => {
    try {
        // Operatör listesi
        const [operatorRows] = await pool.execute(
            'SELECT DISTINCT Operator FROM CihazBilgi WHERE Operator IS NOT NULL ORDER BY Operator'
        );

        // Ülke listesi
        const [ulkeRows] = await pool.execute(
            'SELECT DISTINCT Ulke FROM CihazBilgi WHERE Ulke IS NOT NULL ORDER BY Ulke'
        );

        // Müşteri ID listesi
        const [musteriRows] = await pool.execute(
            'SELECT DISTINCT MusteriID FROM SimICCID WHERE MusteriID IS NOT NULL ORDER BY MusteriID'
        );

        // Cihaz sayısı istatistikleri
        const [statsRows] = await pool.execute(
            `SELECT
                COUNT(DISTINCT c.CihazID) as toplamCihaz,
                COUNT(DISTINCT CASE WHEN s.GoldCihaz = 1 THEN c.CihazID END) as goldCihazSayisi,
                COUNT(DISTINCT CASE WHEN s.KullanimBitti = 1 THEN c.CihazID END) as kullanimBittiSayisi,
                COUNT(DISTINCT CASE WHEN TIMESTAMPDIFF(MINUTE, c.Tarih, NOW()) <= 10 THEN c.CihazID END) as onlineCihaz,
                COUNT(DISTINCT CASE WHEN TIMESTAMPDIFF(MINUTE, c.Tarih, NOW()) > 60 THEN c.CihazID END) as offlineCihaz
            FROM CihazBilgi c
            LEFT JOIN SimICCID s ON c.CihazID = s.CihazID
            WHERE c.CihazID IS NOT NULL`
        );

        const filterOptions = {
            operators: operatorRows.map(row => row.Operator),
            ulkeler: ulkeRows.map(row => row.Ulke),
            musteriIDs: musteriRows.map(row => row.MusteriID),
            istatistikler: statsRows[0] || {},
            sortOptions: [
                { value: 'Tarih', label: 'Tarih' },
                { value: 'CihazID', label: 'Cihaz ID' },
                { value: 'Sicaklik', label: 'Sıcaklık' },
                { value: 'Pil', label: 'Batarya' },
                { value: 'Operator', label: 'Operatör' }
            ]
        };

        res.json({
            success: true,
            data: filterOptions
        });

    } catch (error) {
        console.error('Filtre seçenekleri hatası:', error);
        res.status(500).json({
            success: false,
            message: 'Sunucu hatası oluştu.'
        });
    }
});

// CihazID'ye göre konum bilgilerini getiren endpoint
app.get('/api/konum/:cihazID', async (req, res) => {
    try {
        const { cihazID } = req.params;
        const limit = parseInt(req.query.limit) || 10; // Varsayılan olarak 10 kayıt

        // Geçerli bir limit değeri kontrolü
        if (limit <= 0 || limit > 500) {
            return res.status(400).json({
                success: false,
                message: 'Limit değeri 1-500 arasında olmalıdır.'
            });
        }

        // Veritabanı sorgusu - CihazID, konum ve tarih bilgilerini al
        const [rows] = await pool.execute(
            `SELECT
                CihazID,
                Enlem,
                Boylam,
                DATE_FORMAT(Tarih, '%Y-%m-%d %H:%i:%s') as Tarih
            FROM CihazBilgi
            WHERE
                CihazID = ?
                AND Enlem IS NOT NULL
                AND Boylam IS NOT NULL
            ORDER BY Tarih DESC
            LIMIT ?`,
            [cihazID.toString(), limit.toString()]
        );

        if (rows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Bu cihaza ait konum bilgisi bulunamadı.'
            });
        }

        // Konum ve tarih bilgilerini formatla
        const konumlar = rows.map(row => ({
            enlem: parseFloat(row.Enlem),
            boylam: parseFloat(row.Boylam),
            tarih: row.Tarih
        }));

        res.json({
            success: true,
            data: {
                cihazID: cihazID,
                konumlar: konumlar
            }
        });

    } catch (error) {
        console.error('Veritabanı hatası:', error);
        res.status(500).json({
            success: false,
            message: 'Sunucu hatası oluştu.'
        });
    }
});

// CihazID'ye göre sıcaklık ve nem bilgilerini getiren endpoint
app.get('/api/sicaklik-nem/:cihazID', async (req, res) => {
    try {
        const { cihazID } = req.params;
        const limit = parseInt(req.query.limit) || 10;
        let baslangicTarihi = req.query.baslangicTarihi;
        let bitisTarihi = req.query.bitisTarihi;

        if (limit <= 0 || limit > 500) {
            return res.status(400).json({
                success: false,
                message: 'Limit değeri 1-500 arasında olmalıdır.'
            });
        }

        // Tarih formatını kontrol et ve düzenle
        if (baslangicTarihi) {
            // Eğer tarih formatı YYYY-MM-DD ise, saat ekle
            if (baslangicTarihi.match(/^\d{4}-\d{2}-\d{2}$/)) {
                baslangicTarihi += ' 00:00:00';
            }
            // Tarih formatını kontrol et
            if (!baslangicTarihi.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)) {
                return res.status(400).json({
                    success: false,
                    message: 'Başlangıç tarihi formatı geçersiz. Örnek format: 2025-03-25 21:15:42'
                });
            }
        }

        if (bitisTarihi) {
            // Eğer tarih formatı YYYY-MM-DD ise, saat ekle
            if (bitisTarihi.match(/^\d{4}-\d{2}-\d{2}$/)) {
                bitisTarihi += ' 23:59:59';
            }
            // Tarih formatını kontrol et
            if (!bitisTarihi.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)) {
                return res.status(400).json({
                    success: false,
                    message: 'Bitiş tarihi formatı geçersiz. Örnek format: 2025-03-25 21:15:42'
                });
            }
        }

        let query = `
            SELECT
                CihazID,
                Sicaklik,
                Nem,
                Enlem,
                Boylam,
                DATE_FORMAT(Tarih, '%Y-%m-%d %H:%i:%s') as Tarih
            FROM CihazBilgi
            WHERE
                CihazID = ?
                AND (Sicaklik IS NOT NULL OR Enlem IS NOT NULL OR Boylam IS NOT NULL)
        `;

        const params = [cihazID.toString()];

        if (baslangicTarihi) {
            query += ` AND Tarih >= ?`;
            params.push(baslangicTarihi);
        }

        if (bitisTarihi) {
            query += ` AND Tarih <= ?`;
            params.push(bitisTarihi);
        }

        query += ` ORDER BY Tarih DESC LIMIT ?`;
        params.push(limit.toString());

        const [rows] = await pool.execute(query, params);

        if (rows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Bu cihaza ait veri bulunamadı.'
            });
        }

        const olcumler = rows.map(row => {
            const olcum = {
                tarih: row.Tarih
            };

            // Sıcaklık bilgisi varsa ekle
            if (row.Sicaklik !== null) {
                olcum.sicaklik = parseFloat(row.Sicaklik);
            }

            // Nem bilgisi varsa ekle
            if (row.Nem !== null) {
                olcum.nem = parseFloat(row.Nem);
            }

            // Konum bilgisi varsa ekle
            if (row.Enlem !== null && row.Boylam !== null) {
                olcum.konum = {
                    enlem: parseFloat(row.Enlem),
                    boylam: parseFloat(row.Boylam)
                };
            }

            return olcum;
        });

        res.json({
            success: true,
            data: {
                cihazID: cihazID,
                olcumler: olcumler
            }
        });

    } catch (error) {
        console.error('Veritabanı hatası:', error);
        res.status(500).json({
            success: false,
            message: 'Sunucu hatası oluştu.'
        });
    }
});

// Son verileri getiren endpoint
app.get('/api/son-veriler/:cihazID', async (req, res) => {
    try {
        const { cihazID } = req.params;
        const limit = parseInt(req.query.limit) || 10;

        if (limit <= 0 || limit > 500) {
            return res.status(400).json({
                success: false,
                message: 'Limit değeri 1-500 arasında olmalıdır.'
            });
        }

        const [rows] = await pool.execute(
            `SELECT
                CihazID,
                Sicaklik,
                Nem,
                Enlem,
                Boylam,
                DATE_FORMAT(Tarih, '%Y-%m-%d %H:%i:%s') as Tarih
            FROM CihazBilgi
            WHERE
                CihazID = ?
                AND (Sicaklik IS NOT NULL OR Enlem IS NOT NULL OR Boylam IS NOT NULL)
            ORDER BY Tarih DESC
            LIMIT ?`,
            [cihazID.toString(), limit.toString()]
        );

        if (rows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Bu cihaza ait veri bulunamadı.'
            });
        }

        const veriler = rows.map(row => {
            const veri = {
                tarih: row.Tarih
            };

            // Sıcaklık bilgisi varsa ekle
            if (row.Sicaklik !== null) {
                veri.sicaklik = parseFloat(row.Sicaklik);
            }

            // Nem bilgisi varsa ekle
            if (row.Nem !== null) {
                veri.nem = parseFloat(row.Nem);
            }

            // Konum bilgisi varsa ekle
            if (row.Enlem !== null && row.Boylam !== null) {
                veri.konum = {
                    enlem: parseFloat(row.Enlem),
                    boylam: parseFloat(row.Boylam)
                };
            }

            return veri;
        });

        res.json({
            success: true,
            data: {
                cihazID: cihazID,
                veriler: veriler
            }
        });

    } catch (error) {
        console.error('Veritabanı hatası:', error);
        res.status(500).json({
            success: false,
            message: 'Sunucu hatası oluştu.'
        });
    }
});

// HTTPS sunucusunu başlat
https.createServer(sslOptions, app).listen(port, () => {
    console.log(`Konum API sunucusu https://ffl21.fun:${port} adresinde çalışıyor`);
    console.log(`SSL sertifikaları başarıyla yüklendi.`);
    console.log(`API endpoints: https://ffl21.fun:${port}/api/...`);
});