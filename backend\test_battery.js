function calculateBatteryPercentage(voltage) {
  if (!voltage || voltage <= 0) return null;
  
  const maxVoltage = 4.2;  
  const minVoltage = 3.4;  
  const minPercentage = 30;
  const maxPercentage = 100;
  
  if (voltage < minVoltage) return 0;
  if (voltage >= maxVoltage) return 100;
  
  const voltageRange = maxVoltage - minVoltage;
  const percentageRange = maxPercentage - minPercentage;
  
  const percentage = minPercentage + ((voltage - minVoltage) / voltageRange) * percentageRange;
  
  return Math.round(percentage);
}

console.log('Pil seviyesi hesaplama testleri:');
console.log('3.0V ->', calculateBatteryPercentage(3.0), '%');
console.log('3.4V ->', calculateBatteryPercentage(3.4), '%');
console.log('3.8V ->', calculateBatteryPercentage(3.8), '%');
console.log('4.0V ->', calculateBatteryPercentage(4.0), '% (mevcut cihaz)');
console.log('4.2V ->', calculateBatteryPercentage(4.2), '%');
console.log('4.5V ->', calculateBatteryPercentage(4.5), '%');