{"name": "mgz-gold-backend", "version": "1.0.0", "description": "MGZ Gold Web Uygulaması Backend", "type": "module", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"axios": "^1.10.0", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.18.2", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "mysql2": "^3.6.1", "node-fetch": "^3.3.2", "pdfkit": "^0.17.1", "xmldom": "^0.6.0"}, "devDependencies": {"eslint": "^8.56.0", "nodemon": "^3.0.1"}}