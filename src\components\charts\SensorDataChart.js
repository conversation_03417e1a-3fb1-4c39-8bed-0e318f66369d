import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  Responsive<PERSON><PERSON>r,
  <PERSON>
} from 'recharts';

/**
 * Sensör verilerini gösteren çizgi grafik bileşeni
 * Context7 referansı: /recharts/recharts - LineChart with multiple lines
 * @param {Object} props - Component props
 * @param {Array} props.data - Sensör verileri
 * @param {string} props.title - <PERSON><PERSON> başlığı
 * @param {number} props.height - Grafik yüksekliği
 */
const SensorDataChart = ({ 
  data = [], 
  title = 'Sensör Verileri',
  height = 300 
}) => {
  // Custom tooltip component
  const CustomTooltip = ({ payload, label, active }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white border border-gray-300 rounded p-3 shadow-lg">
          <p className="font-semibold">{`Zaman: ${label}`}</p>
          {payload.map((entry, index) => (
            <p key={index} style={{ color: entry.color }}>
              {`${entry.name}: ${entry.value}${getSensorUnit(entry.dataKey)}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // Sensör birimlerini getir
  const getSensorUnit = (sensorType) => {
    const units = {
      sicaklik: '°C',
      nem: '%',
      darbe: 'G',
      isik: 'Lux',
      pil_seviyesi: '%'
    };
    return units[sensorType] || '';
  };

  // Tarih formatla
  const formatTime = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleTimeString('tr-TR', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  // Veriyi formatla
  const formattedData = data.map(item => ({
    ...item,
    time: formatTime(item.zaman),
    sicaklik: item.sicaklik ? parseFloat(item.sicaklik) : null,
    nem: item.nem ? parseFloat(item.nem) : null,
    darbe: item.darbe_buyuklugu ? parseFloat(item.darbe_buyuklugu) : null,
    isik: item.isik ? parseFloat(item.isik) : null,
    pil_seviyesi: item.pil_seviyesi ? parseInt(item.pil_seviyesi) : null
  }));

  return (
    <div className="w-100">
      <h5 className="text-center mb-3">{title}</h5>
      <ResponsiveContainer width="100%" height={height}>
        <LineChart
          data={formattedData}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
          accessibilityLayer
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
          <XAxis 
            dataKey="time" 
            stroke="#666"
            fontSize={12}
          />
          <YAxis 
            stroke="#666"
            fontSize={12}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          
          {/* Sıcaklık çizgisi */}
          <Line
            type="monotone"
            dataKey="sicaklik"
            stroke="#ff6b6b"
            strokeWidth={2}
            name="Sıcaklık"
            connectNulls={false}
            dot={{ fill: '#ff6b6b', strokeWidth: 2, r: 3 }}
          />
          
          {/* Nem çizgisi */}
          <Line
            type="monotone"
            dataKey="nem"
            stroke="#4ecdc4"
            strokeWidth={2}
            name="Nem"
            connectNulls={false}
            dot={{ fill: '#4ecdc4', strokeWidth: 2, r: 3 }}
          />
          
          {/* Darbe çizgisi */}
          <Line
            type="monotone"
            dataKey="darbe"
            stroke="#ffa726"
            strokeWidth={2}
            name="Darbe"
            connectNulls={false}
            dot={{ fill: '#ffa726', strokeWidth: 2, r: 3 }}
          />
          
          {/* Işık çizgisi */}
          <Line
            type="monotone"
            dataKey="isik"
            stroke="#66bb6a"
            strokeWidth={2}
            name="Işık"
            connectNulls={false}
            dot={{ fill: '#66bb6a', strokeWidth: 2, r: 3 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default SensorDataChart;