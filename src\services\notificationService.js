import { toast } from 'react-toastify';

// Notification service for MGZ24 system
class NotificationService {
  constructor() {
    this.alertHistory = new Map(); // Debounce için alert geçmişi
    this.debounceTime = 30000; // 30 saniye debounce
  }

  // Sıcaklık alarm kontrolü
  checkTemperatureAlert(deviceId, temperature, thresholds = { min: -5, max: 8 }) {
    const alertKey = `temp_${deviceId}`;
    const now = Date.now();
    
    // Debounce kontrolü
    if (this.alertHistory.has(alertKey)) {
      const lastAlert = this.alertHistory.get(alertKey);
      if (now - lastAlert.timestamp < this.debounceTime) {
        return; // Çok yakın zamanda aynı alarm verildi
      }
    }

    if (temperature > thresholds.max) {
      this.alertHistory.set(alertKey, { timestamp: now, type: 'high_temp' });
      
      toast.error(`⚠️ YÜKSEK SICAKLIK ALARMI!\n\nCihaz: ${deviceId}\nSıcaklık: ${temperature}°C\nLimiti aştı! (Max: ${thresholds.max}°C)`, {
        position: "top-center",
        autoClose: 10000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        toastId: `temp_high_${deviceId}`,
      });
    } else if (temperature < thresholds.min) {
      this.alertHistory.set(alertKey, { timestamp: now, type: 'low_temp' });
      
      toast.warning(`🧊 DÜŞÜK SICAKLIK ALARMI!\n\nCihaz: ${deviceId}\nSıcaklık: ${temperature}°C\nLimitin altında! (Min: ${thresholds.min}°C)`, {
        position: "top-center",
        autoClose: 8000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        toastId: `temp_low_${deviceId}`,
      });
    }
  }

  // Pil seviyesi alarm kontrolü
  checkBatteryAlert(deviceId, batteryLevel, threshold = 20) {
    const alertKey = `battery_${deviceId}`;
    const now = Date.now();
    
    // Debounce kontrolü
    if (this.alertHistory.has(alertKey)) {
      const lastAlert = this.alertHistory.get(alertKey);
      if (now - lastAlert.timestamp < this.debounceTime) {
        return;
      }
    }

    if (batteryLevel < threshold) {
      this.alertHistory.set(alertKey, { timestamp: now, type: 'low_battery' });
      
      const severity = batteryLevel < 10 ? 'error' : 'warning';
      const icon = batteryLevel < 10 ? '🔋' : '⚡';
      
      toast[severity](`${icon} DÜŞÜK PİL ALARMI!\n\nCihaz: ${deviceId}\nPil Seviyesi: %${batteryLevel}\nLütfen cihazı şarj edin!`, {
        position: "top-center",
        autoClose: batteryLevel < 10 ? 15000 : 8000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        toastId: `battery_${deviceId}`,
      });
    }
  }

  // Nem seviyesi alarm kontrolü
  checkHumidityAlert(deviceId, humidity, thresholds = { min: 10, max: 80 }) {
    const alertKey = `humidity_${deviceId}`;
    const now = Date.now();
    
    // Debounce kontrolü
    if (this.alertHistory.has(alertKey)) {
      const lastAlert = this.alertHistory.get(alertKey);
      if (now - lastAlert.timestamp < this.debounceTime) {
        return;
      }
    }

    if (humidity > thresholds.max) {
      this.alertHistory.set(alertKey, { timestamp: now, type: 'high_humidity' });
      
      toast.warning(`💧 YÜKSEK NEM ALARMI!\n\nCihaz: ${deviceId}\nNem: %${humidity}\nLimiti aştı! (Max: %${thresholds.max})`, {
        position: "top-center",
        autoClose: 8000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        toastId: `humidity_high_${deviceId}`,
      });
    } else if (humidity < thresholds.min) {
      this.alertHistory.set(alertKey, { timestamp: now, type: 'low_humidity' });
      
      toast.info(`🏜️ DÜŞÜK NEM ALARMI!\n\nCihaz: ${deviceId}\nNem: %${humidity}\nLimitin altında! (Min: %${thresholds.min})`, {
        position: "top-center",
        autoClose: 8000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        toastId: `humidity_low_${deviceId}`,
      });
    }
  }

  // Kapı durumu alarm kontrolü
  checkDoorAlert(deviceId, doorStatus, openDuration = 0) {
    const alertKey = `door_${deviceId}`;
    const now = Date.now();
    
    // Kapı açık ve 10 dakikadan fazla açık kaldıysa
    if (doorStatus === 'acik' && openDuration > 600) { // 10 dakika = 600 saniye
      // Debounce kontrolü
      if (this.alertHistory.has(alertKey)) {
        const lastAlert = this.alertHistory.get(alertKey);
        if (now - lastAlert.timestamp < this.debounceTime) {
          return;
        }
      }

      this.alertHistory.set(alertKey, { timestamp: now, type: 'door_open' });
      
      toast.warning(`🚪 KAPI AÇIK ALARMI!\n\nCihaz: ${deviceId}\nKapı ${Math.floor(openDuration / 60)} dakikadır açık!\nLütfen kapıyı kapatın.`, {
        position: "top-center",
        autoClose: 12000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        toastId: `door_${deviceId}`,
      });
    }
  }

  // Bağlantı durumu alarm kontrolü
  checkConnectionAlert(deviceId, lastDataTime, maxOfflineTime = 300) { // 5 dakika
    const alertKey = `connection_${deviceId}`;
    const now = Date.now();
    const timeDiff = (now - new Date(lastDataTime).getTime()) / 1000; // saniye cinsinden
    
    if (timeDiff > maxOfflineTime) {
      // Debounce kontrolü
      if (this.alertHistory.has(alertKey)) {
        const lastAlert = this.alertHistory.get(alertKey);
        if (now - lastAlert.timestamp < this.debounceTime) {
          return;
        }
      }

      this.alertHistory.set(alertKey, { timestamp: now, type: 'offline' });
      
      toast.error(`📡 BAĞLANTI ALARMI!\n\nCihaz: ${deviceId}\nSon veri: ${Math.floor(timeDiff / 60)} dakika önce\nCihaz çevrimdışı olabilir!`, {
        position: "top-center",
        autoClose: 15000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        toastId: `connection_${deviceId}`,
      });
    }
  }

  // Genel başarı bildirimi
  showSuccess(message, options = {}) {
    toast.success(message, {
      position: "top-right",
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      ...options
    });
  }

  // Genel hata bildirimi
  showError(message, options = {}) {
    toast.error(message, {
      position: "top-right",
      autoClose: 8000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      ...options
    });
  }

  // Genel uyarı bildirimi
  showWarning(message, options = {}) {
    toast.warning(message, {
      position: "top-right",
      autoClose: 6000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      ...options
    });
  }

  // Genel bilgi bildirimi
  showInfo(message, options = {}) {
    toast.info(message, {
      position: "top-right",
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      ...options
    });
  }

  // Sensör verilerini kontrol et ve gerekirse alarm ver
  checkSensorData(deviceId, sensorData) {
    if (!deviceId || !sensorData) return;

    const {
      sicaklik,
      nem,
      pil_seviyesi,
      kapi_durumu,
      kapi_acik_kalma_suresi,
      son_guncelleme
    } = sensorData;

    // Sıcaklık kontrolü
    if (sicaklik !== undefined && sicaklik !== null) {
      this.checkTemperatureAlert(deviceId, parseFloat(sicaklik));
    }

    // Nem kontrolü
    if (nem !== undefined && nem !== null) {
      this.checkHumidityAlert(deviceId, parseFloat(nem));
    }

    // Pil kontrolü
    if (pil_seviyesi !== undefined && pil_seviyesi !== null) {
      this.checkBatteryAlert(deviceId, parseFloat(pil_seviyesi));
    }

    // Kapı kontrolü
    if (kapi_durumu && kapi_acik_kalma_suresi !== undefined) {
      this.checkDoorAlert(deviceId, kapi_durumu, parseInt(kapi_acik_kalma_suresi));
    }

    // Bağlantı kontrolü
    if (son_guncelleme) {
      this.checkConnectionAlert(deviceId, son_guncelleme);
    }
  }

  // Alert geçmişini temizle
  clearAlertHistory(deviceId = null) {
    if (deviceId) {
      // Belirli cihaz için temizle
      for (const [key] of this.alertHistory) {
        if (key.includes(deviceId)) {
          this.alertHistory.delete(key);
        }
      }
    } else {
      // Tüm geçmişi temizle
      this.alertHistory.clear();
    }
  }

  // Belirli bir alarm tipini temizle
  clearAlertType(deviceId, alertType) {
    const alertKey = `${alertType}_${deviceId}`;
    this.alertHistory.delete(alertKey);
  }
}

// Singleton instance
const notificationService = new NotificationService();

export default notificationService;