import axios from 'axios';
import { apiConfig } from './config';

// Axios instance oluştur
const axiosInstance = axios.create({
    baseURL: apiConfig.baseURL,
    timeout: apiConfig.timeout,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
});

// Başlangıçta localStorage'dan token'ı yükle
const initializeAuth = () => {
    const token = localStorage.getItem('auth_token');
    console.log('initializeAuth - token from localStorage:', token);
    if (token) {
        axiosInstance.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        console.log('Authorization header set:', axiosInstance.defaults.headers.common['Authorization']);
    }
};

// Auth'u başlat
initializeAuth();

// İstek araya girmesi (JWT token ve request monitoring)
axiosInstance.interceptors.request.use(
    config => {
        // HTTP durum kodu için özel zaman aşımlarını kontrol et
        if (config.status && apiConfig.timeoutByStatus[config.status]) {
            config.timeout = apiConfig.timeoutByStatus[config.status];
        }

        // JWT token'ı header'a ekle (eğer henüz eklenmemişse)
        if (!config.headers.Authorization) {
            const token = localStorage.getItem('auth_token');
            console.log('Request interceptor - token from localStorage:', token);
            if (token) {
                config.headers.Authorization = `Bearer ${token}`;
                console.log('Request interceptor - Authorization header set:', config.headers.Authorization);
            } else {
                console.warn('Request interceptor - No token found in localStorage');
            }
        }

        // İstek loglaması - geliştirme amacıyla
        console.debug(`API İsteği: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
    },
    error => {
        console.error('Axios istek hatası:', error);
        return Promise.reject(error);
    }
);

// Yanıt araya girmesi (401/403 token hatalarını yönet)
axiosInstance.interceptors.response.use(
    response => {
        // Başarılı yanıt
        console.debug(`API Yanıtı: ${response.status} ${response.config.url}`);
        return response;
    },
    async error => {
        const originalRequest = error.config;

        // Yeniden deneme sayacını başlat
        if (!originalRequest._retryCount) {
            originalRequest._retryCount = 0;
        }

        // JWT token süresi dolmuş (401 Unauthorized) veya geçersiz token (403 Forbidden)
        if (error.response && (error.response.status === 401 || error.response.status === 403)) {
            console.warn('Token süresi dolmuş veya geçersiz - kullanıcı çıkış yapılıyor');

            // Authentication verilerini temizle
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_data');
            delete axiosInstance.defaults.headers.common['Authorization'];

            // Kullanıcıyı login sayfasına yönlendir - custom event ile
            if (window.location.pathname !== '/login') {
                window.dispatchEvent(new CustomEvent('auth-expired'));
            }
            return Promise.reject(error);
        }

        // 404 hataları için özel mesaj oluştur
        if (error.response && error.response.status === 404) {
            const url = originalRequest.url;

            // Sevkiyat ID'sini URL'den çıkart
            if (url.includes('/sevkiyatlar/')) {
                const idMatch = url.match(/\/sevkiyatlar\/(\d+)/);
                if (idMatch && idMatch[1]) {
                    const id = idMatch[1];
                    error.isCustomHandled = true;
                    error.customMessage = apiConfig.errorMessages[404] || `${id} numaralı sevkiyat kaydı veritabanında bulunamadı.`;
                }
            }

            return Promise.reject(error);
        }

        // 500 hataları için özel mesaj
        if (error.response && error.response.status >= 500) {
            error.isCustomHandled = true;
            error.customMessage = apiConfig.errorMessages[500];
        }

        // Zaman aşımı hatası
        if (error.code === 'ECONNABORTED') {
            error.isCustomHandled = true;
            error.customMessage = 'İstek zaman aşımına uğradı. Lütfen tekrar deneyin.';
        }

        // Ağ hatası
        if (error.code === 'ERR_NETWORK' || error.message === 'Network Error') {
            error.isCustomHandled = true;
            error.customMessage = 'Ağ bağlantısı hatası. İnternet bağlantınızı kontrol edin.';
        }

        // Bağlantı hatası, zaman aşımı veya 5xx sunucu hatası durumunda otomatik yeniden deneme
        if (
            (error.code === 'ECONNABORTED' || error.code === 'ECONNRESET' ||
                (error.response && error.response.status >= 500)) &&
            !originalRequest._retry &&
            originalRequest._retryCount < apiConfig.retryCount
        ) {
            originalRequest._retry = true;
            originalRequest._retryCount += 1;

            console.warn(`İstek yeniden deneniyor (${originalRequest._retryCount}/${apiConfig.retryCount}): ${originalRequest.url}`);

            // Belirtilen süre kadar bekle ve yeniden dene
            await new Promise(resolve => setTimeout(resolve, apiConfig.retryDelay * originalRequest._retryCount));

            return axiosInstance(originalRequest);
        }

        return Promise.reject(error);
    }
);

// Only real API will be used

/**
 * Sevkiyat servisleri
 */
export const sevkiyatService = {
    // Tüm sevkiyatları getir
    getSevkiyatlar: async () => {
        try {
            const response = await axiosInstance.get('/sevkiyatlar');
            return response.data;
        } catch (error) {
            console.error('Sevkiyatlar alınırken hata:', error);

            // Only real API error will be returned
            throw error;
        }
    },

    // Müşteri ID'sine göre aktif sevkiyatları getir (tamamlandi_mi = 0)
    getSevkiyatlarByMusteriId: async (musteriId) => {
        try {
            const response = await axiosInstance.get(`/sevkiyatlar?musteri_ID=${musteriId}`);
            return response.data;
        } catch (error) {
            console.error(`Müşteri (ID: ${musteriId}) için aktif sevkiyatlar alınırken hata:`, error);

            // Demo veri kullanımı etkinse
            if (apiConfig.enableDemoData) {
                console.warn(`Müşteri (ID: ${musteriId}) için demo sevkiyat verileri kullanılıyor`);
                return [
                    {
                        id: 1,
                        sevkiyat_ID: 'SVK-2024-001',
                        sevkiyat_adi: 'İstanbul - Ankara Sevkiyatı',
                        mgz24_kodu: 'MGZ24-001',
                        plaka_no: '34 ABC 123',
                        cikis_lokasyon: 'İstanbul',
                        varis_lokasyon: 'Ankara',
                        durum: 'yolda',
                        olusturma_zamani: '2024-01-15T10:30:00',
                        nakliyeci: 'Demo Nakliye A.Ş.',
                        sicaklik_araligi: '2-8°C',
                        musteri_ID: musteriId
                    }
                ];
            }

            throw error;
        }
    },

    // Müşteri ID'sine göre tamamlanmış sevkiyatları getir (tamamlandi_mi = 1)
    getGecmisSevkiyatlarByMusteriId: async (musteriId) => {
        try {
            const response = await axiosInstance.get(`/sevkiyatlar/gecmis?musteri_ID=${musteriId}`);
            return response.data;
        } catch (error) {
            console.error(`Müşteri (ID: ${musteriId}) için geçmiş sevkiyatlar alınırken hata:`, error);
            throw error;
        }
    },

    // ID'ye göre sevkiyat getir
    getSevkiyatById: async (id) => {
        try {
            const response = await axiosInstance.get(`/sevkiyatlar/${id}`);
            return response.data;
        } catch (error) {
            console.error(`Sevkiyat (ID: ${id}) alınırken hata:`, error);

            // Özel mesaj varsa onu kullan
            if (error.isCustomHandled && error.customMessage) {
                error.message = error.customMessage;
            }

            // Only real API error will be returned
            throw error;
        }
    },

    // Yeni sevkiyat oluştur
    createSevkiyat: async (sevkiyatData) => {
        try {
            const response = await axiosInstance.post('/sevkiyatlar', sevkiyatData);
            return response.data;
        } catch (error) {
            console.error('Sevkiyat oluşturulurken hata:', error);
            throw error;
        }
    },

    // Sevkiyat güncelle
    updateSevkiyat: async (id, sevkiyatData) => {
        try {
            const response = await axiosInstance.put(`/sevkiyatlar/${id}`, sevkiyatData);
            return response.data;
        } catch (error) {
            console.error(`Sevkiyat (ID: ${id}) güncellenirken hata:`, error);
            throw error;
        }
    },

    // Sevkiyat sil
    deleteSevkiyat: async (id) => {
        try {
            const response = await axiosInstance.delete(`/sevkiyatlar/${id}`);
            return response.data;
        } catch (error) {
            console.error(`${id} ID'li sevkiyat silinirken hata:`, error);
            throw error;
        }
    },

    // Sevkiyat tamamla
    completeSevkiyat: async (sevkiyatId, userId, notes = '') => {
        try {
            const response = await axiosInstance.post('/sevkiyatlar/complete', {
                sevkiyat_id: sevkiyatId,
                tamamlayan_kullanici_id: userId,
                notlar: notes
            });
            return response.data;
        } catch (error) {
            console.error(`Sevkiyat (ID: ${sevkiyatId}) tamamlanırken hata:`, error);

            // Özel hata mesajlarını kontrol et
            if (error.response && error.response.data && error.response.data.error) {
                throw new Error(error.response.data.error);
            }

            throw error;
        }
    }
};

/**
 * Sensör veri servisleri - KULLANIMI KALDIRILDI
 * Sensör verileri artık uzak API'den (https://ffl21.fun:3001) mgz24_kodu ile alınıyor
 * cihazBilgiService.getCihazFromExternalAPI() fonksiyonu kullanılmalı
 */
export const sensorService = {
    // KULLANIMI KALDIRILDI: Belirli bir sevkiyat için sensör verilerini getir
    // Artık mgz24_kodu ile uzak API'den alınıyor (cihazBilgiService.getCihazFromExternalAPI)
    getSensorData: async (sevkiyatId, dateRange = {}) => {
        try {
            let url = `/sensor-data/sevkiyat/${sevkiyatId}`;
            const params = new URLSearchParams();
            
            if (dateRange.baslangic_tarihi) {
                params.append('baslangic_tarihi', dateRange.baslangic_tarihi);
            }
            
            if (dateRange.bitis_tarihi) {
                params.append('bitis_tarihi', dateRange.bitis_tarihi);
            }
            
            if (params.toString()) {
                url += `?${params.toString()}`;
            }
            
            const response = await axiosInstance.get(url);
            return response.data;
        } catch (error) {
            console.error(`Sevkiyat (ID: ${sevkiyatId}) için sensör verileri alınırken hata:`, error);

            // Özel mesaj varsa onu kullan
            if (error.isCustomHandled && error.customMessage) {
                error.message = error.customMessage;
            }

            // Only real API error will be returned
            throw error;
        }
    },

    // KULLANIMI KALDIRILDI: Sıcaklık alarmlarını getir
    // Backend'de ilgili endpoint comentlendiği için artık çalışmıyor
    getTemperatureAlarms: async (sevkiyatId) => {
        try {
            const response = await axiosInstance.get(`/alarms/temperature/${sevkiyatId}`);
            return response.data;
        } catch (error) {
            console.error(`Sevkiyat (ID: ${sevkiyatId}) için sıcaklık alarmları alınırken hata:`, error);

            // Özel mesaj varsa onu kullan
            if (error.isCustomHandled && error.customMessage) {
                error.message = error.customMessage;
            }


            throw error;
        }
    }
};

/**
 * Sevkiyat Geçmişi servisleri
 */
export const sevkiyatGecmisService = {
    // Tüm sevkiyat geçmişlerini getir
    getSevkiyatGecmisler: async (params = {}) => {
        try {
            const queryParams = new URLSearchParams();

            // Müşteri ID'si ve cihaz kodu filtresi ekle
            if (params.musteriId) {
                queryParams.append('musteri_ID', params.musteriId);
            }
            if (params.cihazKodu) {
                queryParams.append('mgz24_kodu', params.cihazKodu);
            }

            const url = `/sevkiyat-gecmis${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
            const response = await axiosInstance.get(url);
            return response.data;
        } catch (error) {
            console.error('Sevkiyat geçmişleri alınırken hata:', error);

            // Demo veri kullanımı etkinse
            if (apiConfig.enableDemoData) {
                console.warn('Demo sevkiyat geçmişi verileri kullanılıyor');
                return [
                    {
                        id: 1,
                        sevkiyat_ID: 1,
                        sevkiyat_adi: 'İstanbul - Ankara Nakliye',
                        mgz24_kodu: 'MGZ24-001',
                        plaka_no: '34 ABC 123',
                        durum: 'tamamlandi',
                        musteri_ID: params.musteriId || 1,
                        baslangic_zamani: '2024-01-10T08:00:00',
                        bitis_zamani: '2024-01-12T16:30:00',
                        sicaklik_min: 15.5,
                        sicaklik_max: 18.2,
                        nem_min: 40.0,
                        nem_max: 60.0
                    }
                ];
            }

            throw error;
        }
    },

    // ID'ye göre sevkiyat geçmişi getir
    getSevkiyatGecmisById: async (id) => {
        try {
            const response = await axiosInstance.get(`/sevkiyat-gecmis/${id}`);
            return response.data;
        } catch (error) {
            console.error(`Sevkiyat geçmişi (ID: ${id}) alınırken hata:`, error);


            throw error;
        }
    },

    // Yeni sevkiyat geçmişi oluştur
    createSevkiyatGecmis: async (gecmisData) => {
        try {
            const response = await axiosInstance.post('/sevkiyat-gecmis', gecmisData);
            return response.data;
        } catch (error) {
            console.error('Sevkiyat geçmişi oluşturulurken hata:', error);
            throw error;
        }
    },

    // Sevkiyat geçmişi güncelle
    updateSevkiyatGecmis: async (id, gecmisData) => {
        try {
            const response = await axiosInstance.put(`/sevkiyat-gecmis/${id}`, gecmisData);
            return response.data;
        } catch (error) {
            console.error(`Sevkiyat geçmişi (ID: ${id}) güncellenirken hata:`, error);
            throw error;
        }
    },

    // Sevkiyat geçmişi sil
    deleteSevkiyatGecmis: async (id) => {
        try {
            const response = await axiosInstance.delete(`/sevkiyat-gecmis/${id}`);
            return response.data;
        } catch (error) {
            console.error(`Sevkiyat geçmişi (ID: ${id}) silinirken hata:`, error);
            throw error;
        }
    }
};

/**
 * Helper function to categorize Axios errors - Context7 pattern
 */
const categorizeAxiosError = (error, strategyName) => {
    if (error.response) {
        // Server responded with error status
        return {
            type: 'response_error',
            strategy: strategyName,
            status: error.response.status,
            message: `HTTP ${error.response.status}: ${error.response.statusText}`,
            data: error.response.data,
            headers: error.response.headers
        };
    } else if (error.request) {
        // Request made but no response received
        const requestError = {
            type: 'network_error',
            strategy: strategyName,
            message: 'No response received from server'
        };

        // Detect specific network error types
        if (error.code === 'ERR_NETWORK') {
            requestError.subtype = 'network_unreachable';
            requestError.message = 'Network error - server unreachable';
        } else if (error.code === 'ECONNREFUSED') {
            requestError.subtype = 'connection_refused';
            requestError.message = 'Connection refused by server';
        } else if (error.message?.includes('Mixed Content')) {
            requestError.subtype = 'mixed_content';
            requestError.message = 'Mixed Content error - HTTPS page trying to access HTTP resource';
        } else if (error.message?.includes('CORS')) {
            requestError.subtype = 'cors_error';
            requestError.message = 'CORS policy blocked the request';
        }

        return requestError;
    } else {
        // Request setup error
        return {
            type: 'setup_error',
            strategy: strategyName,
            message: error.message || 'Request configuration error',
            stack: error.stack
        };
    }
};

/**
 * Cihaz Bilgi servisleri
 */
export const cihazBilgiService = {
    // Tüm cihazları getir
    getCihazlar: async (params = {}) => {
        try {
            const queryParams = new URLSearchParams();

            // Cihaz kodu araması için search endpoint'ini kullan
            if (params.cihazKodu) {
                queryParams.append('q', params.cihazKodu);
            }
            if (params.limit) {
                queryParams.append('limit', params.limit);
            }

            const url = params.cihazKodu
                ? `/api/cihazlar/search${queryParams.toString() ? '?' + queryParams.toString() : ''}`
                : `/api/cihazlar${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
            const response = await axiosInstance.get(url);
            return response.data.success ? response.data.data : response.data;
        } catch (error) {
            console.error('Cihazlar alınırken hata:', error);


            throw error;
        }
    },

    // Cihaz koduna göre cihaz getir (yerel veritabanından)
    getCihazByCihazKodu: async (cihazKodu) => {
        try {
            // Yerel backend'den cihaz bilgisi al
            const response = await axiosInstance.get(`/api/cihaz-bilgi?mgz24_kodu=${cihazKodu}`);
            console.log('Yerel DB response:', response.data);
            if (response.data.length === 0) {
                throw new Error('Cihaz bulunamadı');
            }
            return response.data[0];
        } catch (error) {
            // 404 hatası normal - cihaz local DB'de yok demek
            if (error.response?.status === 404) {
                console.log(`Cihaz (Kod: ${cihazKodu}) yerel veritabanında bulunamadı - bu normal bir durum`);
                throw new Error('Cihaz yerel veritabanında bulunamadı');
            }
            
            console.error(`Cihaz (Kod: ${cihazKodu}) alınırken hata:`, error);

            // Demo veri kullanımı etkinse
            if (apiConfig.enableDemoData) {
                console.warn(`Kod: ${cihazKodu} için demo cihaz verisi kullanılıyor`);
                return {
                    mgz24_kodu: cihazKodu,
                    musteri_ID: 1,
                    model: 'MGZ24-T1',
                    uretim_tarihi: '2023-01-15',
                    son_kalibrasyon: '2023-06-20',
                    aktif: 1,
                    batarya_durumu: 85,
                    firmware_versiyon: '2.4.1',
                    notlar: 'Demo cihaz'
                };
            }

            throw error;
        }
    },

    // Aktif olmayan cihazları getir
    getInaktifCihazlar: async () => {
        try {
            const response = await axiosInstance.get('/cihaz-bilgi?aktif=false');
            return response.data;
        } catch (error) {
            console.error('İnaktif cihazlar alınırken hata:', error);


            throw error;
        }
    },

    // Cihaz durumunu güncelle (aktif/inaktif)
    updateCihazStatus: async (cihazKodu, statusData) => {
        try {
            const response = await axiosInstance.put(`/cihaz-bilgi/${cihazKodu}/status`, statusData);
            return response.data;
        } catch (error) {
            console.error(`Cihaz durumu güncellenirken hata (Kod: ${cihazKodu}):`, error);


            throw error;
        }
    },

    // Cihaz bilgilerini güncelle (kullanıcı atama dahil)
    updateCihaz: async (cihazKodu, updateData) => {
        try {
            const url = `/cihaz-bilgi/${cihazKodu}/assign`;
            const data = {
                musteriId: updateData.kullanici_ID || updateData.musteriId,
                pilSeviyesi: updateData.pil_seviyesi
            };

            console.log('updateCihaz - İstek URL:', url);
            console.log('updateCihaz - İstek verisi:', data);
            console.log('updateCihaz - Base URL:', axiosInstance.defaults.baseURL);

            const response = await axiosInstance.put(url, data);
            return response.data;
        } catch (error) {
            console.error(`Cihaz güncellenirken hata (Kod: ${cihazKodu}):`, error);
            console.error('Hata detayları:', {
                message: error.message,
                status: error.response?.status,
                statusText: error.response?.statusText,
                data: error.response?.data,
                config: error.config
            });
            throw error;
        }
    },

    // Cihaz bilgilerini getir (cihazBilgi tablosundan)
    getCihazBilgi: async (cihazKodu) => {
        try {
            const response = await axiosInstance.get(`/cihaz-bilgi/${cihazKodu}`);
            return response.data;
        } catch (error) {
            console.error(`Cihaz bilgisi alınırken hata (Kod: ${cihazKodu}):`, error);
            throw error;
        }
    },

    // Tüm cihazları getir (cihazBilgi + sevkiyatlar JOIN)
    getAllDevices: async () => {
        try {
            const response = await axiosInstance.get('/all-devices');
            return response.data;
        } catch (error) {
            console.error('Tüm cihazlar alınırken hata:', error);
            throw error;
        }
    },

    // Harici API'den cihaz bilgilerini sorgula - Direkt HTTPS bağlantısı
    getCihazFromExternalAPI: async (cihazKodu) => {
        try {
            console.log(`Fetching device ${cihazKodu} from external API...`);

            const response = await axios.get(`https://ffl21.fun:3001/api/cihaz/${cihazKodu}`, {
                timeout: 10000,
                validateStatus: function (status) {
                    return status >= 200 && status < 500;
                },
                headers: {
                    'Accept': 'application/json'
                },
                crossdomain: true
            });

            if (response.data && response.data.success) {
                console.log(`Successfully fetched device ${cihazKodu}`);
                return {
                    success: true,
                    data: response.data.data,
                    strategy: 'https_direct',
                    status: response.status
                };
            } else {
                throw new Error('API returned unsuccessful response');
            }
        } catch (error) {
            console.error(`Failed to fetch device ${cihazKodu}:`, {
                message: error.message,
                code: error.code,
                response: error.response?.data,
                status: error.response?.status,
                statusText: error.response?.statusText
            });
            throw error;
        }
    }
};

/**
 * Lokasyon servisleri
 */
export const lokasyonService = {
    // Tüm lokasyonları getir
    getLokasyonlar: async () => {
        try {
            const response = await axiosInstance.get('/lokasyonlar');
            return response.data;
        } catch (error) {
            console.error('Lokasyonlar alınırken hata:', error);
            throw error;
        }
    }
};

/**
 * Taşıyıcı servisleri
 */
export const tasiyiciService = {
    // Tüm taşıyıcıları getir
    getTasiyicilar: async () => {
        try {
            const response = await axiosInstance.get('/tasiyicilar');
            return response.data;
        } catch (error) {
            console.error('Taşıyıcılar alınırken hata:', error);
            throw error;
        }
    }
};

/**
 * Cihaz ID yönetimi servisleri
 */
export const cihazIDService = {
    // Müşteriye atanan cihazları getir
    getCihazlar: async (params = {}) => {
        try {
            const queryParams = new URLSearchParams();

            if (params.musteriId) {
                queryParams.append('musteriId', params.musteriId);
            }
            if (params.aktif !== undefined) {
                queryParams.append('aktif', params.aktif);
            }
            if (params.krediVar !== undefined) {
                queryParams.append('krediVar', params.krediVar);
            }

            const url = `/cihaz-id${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
            const response = await axiosInstance.get(url);
            return response.data;
        } catch (error) {
            console.error('Cihazlar alınırken hata:', error);

            // Demo veri kullanımı etkinse
            if (apiConfig.enableDemoData) {
                console.warn('Demo cihaz verileri kullanılıyor');
                return [
                    {
                        ID: 1,
                        CihazID: '56988735',
                        ICCID: '8944538532054447572',
                        MusteriID: 1,
                        KalanSure: 432000, // 5 gün
                        kredi_durumu: 'var',
                        kredi_gun: 5,
                        cihaz_durumu: 'inaktif',
                        pil_seviyesi: 85,
                        sensör_durumu: 'aktif',
                        SistemeEklemeTarihi: '2025-05-21T16:33:37.000Z',
                        Notlar: 'Test cihazı'
                    },
                    {
                        ID: 2,
                        CihazID: '56987713',
                        ICCID: '8944538532054447580',
                        MusteriID: 1,
                        KalanSure: 0, // Kredi yok
                        kredi_durumu: 'yok',
                        kredi_gun: 0,
                        cihaz_durumu: 'inaktif',
                        pil_seviyesi: 25,
                        sensör_durumu: 'pasif',
                        SistemeEklemeTarihi: '2025-05-20T14:06:58.000Z',
                        Notlar: 'Kredi bitmiş'
                    }
                ];
            }

            throw error;
        }
    },

    // Admin: Cihazı müşteriye ata
    assignCihaz: async (cihazId, musteriId) => {
        try {
            const response = await axiosInstance.post('/cihaz-id/assign', {
                cihazId: cihazId,
                musteriId: musteriId
            });
            return response.data;
        } catch (error) {
            console.error('Cihaz atama hatası:', error);

            // Özel hata mesajlarını kontrol et
            if (error.response && error.response.data && error.response.data.error) {
                throw new Error(error.response.data.error);
            }

            throw error;
        }
    },

    // Cihaz kredisi satın al
    buyCredit: async (cihazId, krediGun, odemeDetaylari = {}) => {
        try {
            const response = await axiosInstance.post('/cihaz-id/buy-credit', {
                cihazId: cihazId,
                krediGun: krediGun,
                odemeDetaylari: odemeDetaylari
            });
            return response.data;
        } catch (error) {
            console.error('Kredi satın alma hatası:', error);

            // Özel hata mesajlarını kontrol et
            if (error.response && error.response.data && error.response.data.error) {
                throw new Error(error.response.data.error);
            }

            throw error;
        }
    },

    // İnaktif cihazları getir (sevkiyata atanmamış)
    getInaktifCihazlar: async (musteriId) => {
        try {
            const response = await axiosInstance.get(`/cihaz-id/inaktif?musteriId=${musteriId}`);
            return response.data;
        } catch (error) {
            console.error('İnaktif cihazlar alınırken hata:', error);
            throw error;
        }
    },

    // Kredisi olmayan cihazları getir
    getKredisizCihazlar: async (musteriId) => {
        return await cihazIDService.getCihazlar({
            musteriId: musteriId,
            krediVar: false
        });
    }
};

/**
 * Ürün servisleri
 */
export const urunService = {
    // Tüm ürünleri getir
    getUrunler: async () => {
        try {
            const response = await axiosInstance.get('/urunler');
            return response.data;
        } catch (error) {
            console.error('Ürünler alınırken hata:', error);
            throw error;
        }
    },

    // ID'ye göre ürün getir
    getUrunById: async (id) => {
        try {
            const response = await axiosInstance.get(`/urunler/${id}`);
            return response.data;
        } catch (error) {
            console.error(`${id} ID'li ürün alınırken hata:`, error);
            throw error;
        }
    }
};

/**
 * Müşteri servisleri
 */
export const musteriService = {
    // Tüm müşterileri getir
    getMusteriler: async () => {
        try {
            const response = await axiosInstance.get('/kullanicilar');
            return response.data;
        } catch (error) {
            console.error('Müşteriler alınırken hata:', error);

            // Demo veri kullanımı etkinse
            if (apiConfig.enableDemoData) {
                console.warn('Demo müşteri verileri kullanılıyor');
                return [
                    {
                        musteri_ID: 1,
                        musteri_adi: 'Ahmet Yılmaz',
                        kullanici: 'ahmet.yilmaz',
                        email: '<EMAIL>',
                        tel: '0532-123-4567',
                        firma: 'Demo Şirketi A.Ş.',
                        adres: 'İstanbul, Türkiye',
                        gorev: 'kullanici'
                    },
                    {
                        musteri_ID: 2,
                        musteri_adi: 'Ayşe Kaya',
                        kullanici: 'ayse.kaya',
                        email: '<EMAIL>',
                        tel: '0533-234-5678',
                        firma: 'Demo Lojistik Ltd.',
                        adres: 'Ankara, Türkiye',
                        gorev: 'kullanici'
                    }
                ];
            }

            throw error;
        }
    },

    // ID'ye göre müşteri getir
    getMusteriById: async (id) => {
        try {
            const response = await axiosInstance.get(`/kullanicilar/${id}`);
            return response.data;
        } catch (error) {
            console.error(`Müşteri (ID: ${id}) alınırken hata:`, error);
            throw error;
        }
    },

    // Yeni müşteri oluştur
    createMusteri: async (musteriData) => {
        try {
            const response = await axiosInstance.post('/kullanicilar', musteriData);
            return response.data;
        } catch (error) {
            console.error('Müşteri oluşturulurken hata:', error);
            throw error;
        }
    },

    // Müşteri güncelle
    updateMusteri: async (id, musteriData) => {
        try {
            const response = await axiosInstance.put(`/kullanicilar/${id}`, musteriData);
            return response.data;
        } catch (error) {
            console.error(`Müşteri (ID: ${id}) güncellenirken hata:`, error);
            throw error;
        }
    },

    // Müşteri sil
    deleteMusteri: async (id) => {
        try {
            const response = await axiosInstance.delete(`/kullanicilar/${id}`);
            return response.data;
        } catch (error) {
            console.error(`Müşteri (ID: ${id}) silinirken hata:`, error);
            throw error;
        }
    }
};

/**
 * Kullanıcı servisleri
 */
export const kullaniciService = {
    // Kullanıcı bilgilerini getir
    getKullanici: async (id) => {
        try {
            const response = await axiosInstance.get(`/kullanicilar/${id}`);
            return response.data;
        } catch (error) {
            console.error(`Kullanıcı (ID: ${id}) bilgileri alınırken hata:`, error);
            throw error;
        }
    },

    // Kullanıcı bilgilerini güncelle
    updateKullanici: async (id, kullaniciData) => {
        try {
            // Veritabanı alan adlarına uygun dönüşüm
            const dbData = {
                kullanici: kullaniciData.username,
                email: kullaniciData.email,
                musteri_adi: kullaniciData.full_name,
                tel: kullaniciData.phone,
                firma: kullaniciData.company,
                adres: kullaniciData.address || ''
            };

            // Şifre güncelleme varsa
            if (kullaniciData.current_password && kullaniciData.new_password) {
                dbData.current_password = kullaniciData.current_password;
                dbData.password = kullaniciData.new_password;
            }

            const response = await axiosInstance.put(`/kullanicilar/${id}`, dbData);
            return response.data;
        } catch (error) {
            console.error(`Kullanıcı (ID: ${id}) bilgileri güncellenirken hata:`, error);
            throw error;
        }
    }
};

/**
 * Kullanıcı kimlik doğrulama servisleri
 */
export const authService = {
    // Kullanıcı girişi - Sadece gerçek backend authentication
    login: async (email, password) => {
        try {
            // Input validation
            if (!email || !password) {
                return {
                    success: false,
                    message: 'Email ve şifre alanları gereklidir'
                };
            }

            // Email format validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                return {
                    success: false,
                    message: 'Geçerli bir email adresi giriniz'
                };
            }

            // Backend'e authentication isteği gönder
            const response = await axiosInstance.post('/auth/login', {
                email: email.toLowerCase().trim(),
                password
            });

            // Backend'den gelen yanıtı kontrol et
            if (response.data && response.data.success) {
                // Token'ı localStorage'a kaydet (güvenlik için)
                if (response.data.token) {
                    localStorage.setItem('auth_token', response.data.token);
                    localStorage.setItem('user_data', JSON.stringify(response.data.user));

                    // Axios için default Authorization header'ı ayarla
                    axiosInstance.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`;
                }

                return {
                    success: true,
                    message: response.data.message || 'Giriş başarılı',
                    user: response.data.user,
                    token: response.data.token
                };
            } else {
                return {
                    success: false,
                    message: response.data.message || 'Giriş başarısız'
                };
            }

        } catch (error) {
            console.error('Giriş yapılırken hata:', error);

            // HTTP hata kodlarına göre spesifik mesajlar
            if (error.response) {
                const status = error.response.status;
                const responseData = error.response.data;

                switch (status) {
                    case 401:
                        return {
                            success: false,
                            message: responseData.message || 'Email veya şifre hatalı'
                        };
                    case 403:
                        return {
                            success: false,
                            message: 'Hesabınız engellenmiş. Lütfen yönetici ile iletişime geçin'
                        };
                    case 429:
                        return {
                            success: false,
                            message: 'Çok fazla giriş denemesi. Lütfen bir süre bekleyin'
                        };
                    case 500:
                        return {
                            success: false,
                            message: 'Sunucu hatası. Lütfen daha sonra tekrar deneyin'
                        };
                    default:
                        return {
                            success: false,
                            message: responseData.message || 'Giriş yapılırken hata oluştu'
                        };
                }
            } else if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
                return {
                    success: false,
                    message: 'Sunucuya bağlanılamıyor. Lütfen internet bağlantınızı kontrol edin'
                };
            } else {
                return {
                    success: false,
                    message: 'Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin'
                };
            }
        }
    },

    // Kullanıcı çıkışı
    logout: () => {
        try {
            // Token'ı ve kullanıcı verilerini temizle
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_data');

            // Axios Authorization header'ını temizle
            delete axiosInstance.defaults.headers.common['Authorization'];

            return {
                success: true,
                message: 'Çıkış başarılı'
            };
        } catch (error) {
            console.error('Çıkış yapılırken hata:', error);
            return {
                success: false,
                message: 'Çıkış yapılırken hata oluştu'
            };
        }
    },

    // Token geçerliliğini kontrol et
    validateToken: async () => {
        try {
            const token = localStorage.getItem('auth_token');
            if (!token) {
                return { valid: false, message: 'Token bulunamadı' };
            }

            const response = await axiosInstance.get('/auth/validate');
            return {
                valid: response.data.valid,
                user: response.data.user,
                message: response.data.message
            };
        } catch (error) {
            console.error('Token doğrulanırken hata:', error);
            // Token geçersizse temizle
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_data');
            delete axiosInstance.defaults.headers.common['Authorization'];

            return {
                valid: false,
                message: 'Token geçersiz veya süresi dolmuş'
            };
        }
    },

    // Mevcut kullanıcı bilgilerini getir
    getCurrentUser: () => {
        try {
            const userData = localStorage.getItem('user_data');
            return userData ? JSON.parse(userData) : null;
        } catch (error) {
            console.error('Kullanıcı verisi alınırken hata:', error);
            return null;
        }
    }
};

/**
 * Ödeme servisleri
 */
export const paymentService = {
    // Kuveyt Türk döviz kurunu getir
    getExchangeRate: async () => {
        try {
            // Gerçek projede burası Kuveyt Türk API'sine bağlanmalıdır
            // Şimdilik sabit bir kur değeri döndürüyoruz
            // NOT: Gerçek uygulamada bu değer bir API'den alınmalıdır
            const mockResponse = {
                data: {
                    EUR: {
                        selling: 34.98 // Euro satış kuru (örnek)
                    }
                }
            };

            return mockResponse.data.EUR.selling;
        } catch (error) {
            console.error('Döviz kuru alınırken hata:', error);
            // Hata durumunda varsayılan bir kur değeri
            return 35.00;
        }
    },

    // Ödeme paketlerini getir
    getPaymentPackages: async () => {
        try {
            // Backend API'den paketleri çek
            const response = await axiosInstance.get('/payments/packages');
            console.log('Paketler API yanıtı:', response.data);
            return response.data;
        } catch (error) {
            console.warn('Paketler API başarısız, fallback veriler kullanılıyor:', error);
            // Fallback veriler (test paketi dahil)
            const packages = [
                { id: 0, days: 1, priceEUR: 0.03, name: 'Test Paketi (1 Gün)', priceTL: 1, isTest: true },
                { id: 1, days: 5, priceEUR: 3, name: '5 Gün Kullanım' },
                { id: 2, days: 10, priceEUR: 5, name: '10 Gün Kullanım' },
                { id: 3, days: 20, priceEUR: 6, name: '20 Gün Kullanım' },
                { id: 4, days: 30, priceEUR: 7, name: '30 Gün Kullanım' },
                { id: 5, days: 90, priceEUR: 18, name: '3 Ay Kullanım' },
                { id: 6, days: 180, priceEUR: 33, name: '6 Ay Kullanım' },
                { id: 7, days: 360, priceEUR: 60, name: '1 Yıl Kullanım' }
            ];
            return packages;
        }
    },

    // Ödeme işlemini gerçekleştir
    processPayment: async (paymentData) => {
        try {
            // Gerçek projede burada ödeme API'sine istek yapılır
            // Şimdilik başarılı bir ödeme simüle ediyoruz
            const response = await axiosInstance.post('/payments/process', paymentData);
            return response.data;
        } catch (error) {
            console.error('Ödeme işlemi sırasında hata:', error);
            throw error;
        }
    },

    // Kullanıcının ödeme geçmişini getir
    getPaymentHistory: async (userId) => {
        try {
            const response = await axiosInstance.get(`/payments/history/${userId}`);
            return response.data;
        } catch (error) {
            console.error(`Ödeme geçmişi alınırken hata (Kullanıcı ID: ${userId}):`, error);
            throw error;
        }
    },

    // Kullanıcının kalan kullanım süresini getir
    getRemainingUsage: async (userId) => {
        try {
            const response = await axiosInstance.get(`/users/${userId}/usage`);
            return response.data;
        } catch (error) {
            console.error(`Kullanım süresi alınırken hata (Kullanıcı ID: ${userId}):`, error);
            throw error;
        }
    }
};

/**
 * İnaktif cihazlar servisi
 */
export const inactiveDevicesService = {
    // İnaktif cihazları getir
    getInactiveDevices: async () => {
        try {
            const response = await axiosInstance.get('/inactive-devices');
            return response.data;
        } catch (error) {
            console.error('İnaktif cihazlar alınırken hata:', error);
            throw error;
        }
    }
}; 