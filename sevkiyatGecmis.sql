CREATE TABLE `sevkiyatGecmis` (
  `id` int NOT NULL AUTO_INCREMENT,
  `cihaz_kodu` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `sevkiyat_ID` int NOT NULL,
  `baslangic_zamani` timestamp NOT NULL,
  `bitis_zamani` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gonderen_firma_id` int DEFAULT NULL,
  `alici_firma_id` int DEFAULT NULL,
  `sicaklik_min` decimal(5,2) DEFAULT NULL,
  `sicaklik_max` decimal(5,2) DEFAULT NULL,
  `nem_min` decimal(5,2) DEFAULT NULL,
  `nem_max` decimal(5,2) DEFAULT NULL,
  `durum` enum('tamamlandi','iptal_edildi','iade_edildi','ariza_bildirildi') NOT NULL DEFAULT 'tamamlandi',
  `musteri_ID` int DEFAULT NULL,
  `notlar` text,
  `olusturma_zamani` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `guncelleme_zamani` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_cihaz_kodu` (`cihaz_kodu`),
  KEY `idx_sevkiyat_ID` (`sevkiyat_ID`),
  KEY `idx_gonderen_firma` (`gonderen_firma_id`),
  KEY `idx_alici_firma` (`alici_firma_id`),
  KEY `idx_musteri_ID` (`musteri_ID`),
  KEY `idx_durum` (`durum`),
  KEY `idx_tarih_araligi` (`baslangic_zamani`,`bitis_zamani`),
  CONSTRAINT `fk_sevkiyatGecmis_sevkiyat` FOREIGN KEY (`sevkiyat_ID`) REFERENCES `sevkiyatlar` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_sevkiyatGecmis_musteri` FOREIGN KEY (`musteri_ID`) REFERENCES `kullanicilar` (`musteri_ID`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; 