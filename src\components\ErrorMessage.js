import React from 'react';

/**
 * Reusable Error Message Component
 * @param {Object} props - Component props
 * @param {string} props.message - Error message to display
 * @param {string} props.variant - Style variant: 'danger', 'warning', 'info'
 * @param {boolean} props.dismissible - Whether error can be dismissed
 * @param {function} props.onDismiss - Callback when error is dismissed
 * @param {boolean} props.showIcon - Whether to show icon
 * @param {string} props.title - Optional title for the error
 * @param {React.ReactNode} props.children - Optional additional content
 */
const ErrorMessage = ({ 
  message = 'Bir hata o<PERSON>', 
  variant = 'danger',
  dismissible = false,
  onDismiss,
  showIcon = true,
  title,
  children 
}) => {
  // Icon mappings for different variants
  const iconClasses = {
    danger: 'bi bi-exclamation-triangle-fill',
    warning: 'bi bi-exclamation-circle-fill',
    info: 'bi bi-info-circle-fill'
  };

  // Alert classes
  const alertClasses = [
    'alert',
    `alert-${variant}`,
    dismissible ? 'alert-dismissible' : '',
    'd-flex align-items-start'
  ].filter(Boolean).join(' ');

  return (
    <div className={alertClasses} role="alert">
      {showIcon && (
        <i className={`${iconClasses[variant]} me-2 mt-1`} style={{ fontSize: '1.1rem' }}></i>
      )}
      
      <div className="flex-grow-1">
        {title && (
          <div className="fw-bold mb-1">{title}</div>
        )}
        <div>{message}</div>
        {children && (
          <div className="mt-2">
            {children}
          </div>
        )}
      </div>

      {dismissible && (
        <button
          type="button"
          className="btn-close"
          aria-label="Kapat"
          onClick={onDismiss}
        ></button>
      )}
    </div>
  );
};

export default ErrorMessage;