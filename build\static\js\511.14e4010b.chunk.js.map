{"version": 3, "file": "static/js/511.14e4010b.chunk.js", "mappings": "gKASA,MAwGA,EAxGgBA,KACZ,MAAMC,GAAWC,EAAAA,EAAAA,MAaXC,EAAuB,UAVTC,MAChB,IAAK,IAADC,EAAAC,EACA,MAAMC,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C,OAAW,OAAJJ,QAAI,IAAJA,GAAU,QAANF,EAAJE,EAAMA,YAAI,IAAAF,OAAN,EAAJA,EAAYO,QAAY,OAAJL,QAAI,IAAJA,GAAU,QAAND,EAAJC,EAAMA,YAAI,IAAAD,OAAN,EAAJA,EAAYO,QAAS,MACpD,CAAE,MAAAC,GACE,MAAO,MACX,GAGaV,GAGjB,OACIW,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC1DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6BAA6BG,SAAS,KAAKC,GAAG,UAAU,kBAAgB,UAASH,SAAA,EAC5FC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,EAC7BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,kBAAkBI,GAAG,UAASH,SAAC,gBAC7CF,EAAAA,EAAAA,KAAA,UAAQM,KAAK,SAASL,UAAU,YAAY,kBAAgB,YAAY,iBAAe,WAAW,aAAW,cAEjHE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mEAAkEC,SAAA,EAC7EF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,kCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,MAAtBtB,EAASuB,SAAmB,SAAW,IAAMC,GAAG,IAAGR,SAAA,EAC5GF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMC,EAAAA,MAAW,0BAG1Cb,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,SAAtBtB,EAASuB,SAAsB,SAAW,IAAMC,GAAG,OAAMR,SAAA,EAClHF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAME,EAAAA,MAAgB,mCAG/Cd,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,sBAAtBtB,EAASuB,SAAmC,SAAW,IAAMC,GAAG,oBAAmBR,SAAA,EAC5IF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMG,EAAAA,MAAY,8BAG3Cf,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMI,EAAAA,MAAmB,sCAKtDhB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wBACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,CAEpCd,IACGY,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMK,EAAAA,MAAa,0BAIhDjB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,mBAAtBtB,EAASuB,SAAgC,SAAW,IAAMC,GAAG,iBAAgBR,SAAA,EACtIF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMM,EAAAA,MAAU,uBAK7ClB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,uCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMO,EAAAA,MAAU,qCAGzCnB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMQ,EAAAA,MAAgB,6CAKnDpB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMS,EAAAA,MAAgB,qBAG/CrB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,cAAtBtB,EAASuB,SAA2B,SAAW,IAAMC,GAAG,YAAWR,SAAA,EAC5HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMU,EAAAA,MAAe,4BAKlDtB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wBAG1I,C,sDC3Gd,MAQA,EAReqB,KAEPvB,EAAAA,EAAAA,KAAA,UAAQC,UAAU,kBAAiBC,UAC/BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,cAAaC,SAAC,iD,qGCEvC,MA6GA,EA7GesB,KACX,MAAMC,GAAWC,EAAAA,EAAAA,OACVC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,YAClCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAGvCG,EAAAA,EAAAA,YAAU,KACgBC,WAClB,IAAK,IAADC,EAAAC,EAAAC,EAAAC,EAEA,MAAMC,EAAa7C,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C2C,GAAmB,OAAVD,QAAU,IAAVA,GAAgB,QAANJ,EAAVI,EAAY9C,YAAI,IAAA0C,OAAN,EAAVA,EAAkBM,cAAwB,OAAVF,QAAU,IAAVA,GAAgB,QAANH,EAAVG,EAAY9C,YAAI,IAAA2C,OAAN,EAAVA,EAAkB9B,IAC3DsB,GAAqB,OAAVW,QAAU,IAAVA,GAAgB,QAANF,EAAVE,EAAY9C,YAAI,IAAA4C,OAAN,EAAVA,EAAkBK,QAAkB,OAAVH,QAAU,IAAVA,GAAgB,QAAND,EAAVC,EAAY9C,YAAI,IAAA6C,OAAN,EAAVA,EAAkBK,aAE7D,IAAKH,EAID,OAHAI,QAAQC,KAAK,kCACbhB,EAAY,gBACZG,GAAW,GAKf,GAAIJ,EAGA,OAFAC,EAAYD,QACZI,GAAW,GAKf,IACI,MAAMc,QAAiBC,EAAAA,GAAiBC,aAAaR,GAGjDM,GAAYA,EAASH,cACrBd,EAAYiB,EAASH,aAGP,OAAVJ,QAAU,IAAVA,GAAAA,EAAY9C,OACZ8C,EAAW9C,KAAKiD,KAAOI,EAASH,YAChC/C,aAAaqD,QAAQ,OAAQvD,KAAKwD,UAAUX,KAGxD,CAAE,MAAOY,GACLP,QAAQC,KAAK,gGAEbhB,EAAYD,GAAY,sBAC5B,CACJ,CAAE,MAAOwB,GACLR,QAAQQ,MAAM,0DAAuCA,GAErDvB,EAAY,sBAChB,CAAC,QACGG,GAAW,EACf,GAGJqB,EAAe,GAChB,IAWH,OACIjD,EAAAA,EAAAA,MAAA,UAAQF,UAAU,0DAA0D,gBAAc,OAAMC,SAAA,EAC5FF,EAAAA,EAAAA,KAACO,EAAAA,GAAI,CAACN,UAAU,gEAAgES,GAAG,IAAGR,UAClFF,EAAAA,EAAAA,KAAA,OAAKqD,I,60RAAeC,IAAI,aAAaC,OAAO,UAEhDpD,EAAAA,EAAAA,MAAA,MAAIF,UAAU,yCAAwCC,SAAA,EAClDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8CAA6CC,UACvDC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,aAAYC,SAAA,EACxBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMO,EAAAA,IAAQlB,UAAU,SACxC6B,EAAU,mBAAkBH,QAGrC3B,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4BAA2BC,UACrCF,EAAAA,EAAAA,KAAA,UACIwD,QAtBCC,KAEjB9D,aAAa+D,WAAW,QAGxBjC,EAAS,SAAS,EAkBFxB,UAAU,gBACV,iBAAe,UACf,oBAAkB,OAClB,gBAAc,+BAAYC,UAE1BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM+C,EAAAA,WAG/B3D,EAAAA,EAAAA,KAAA,MAAIC,UAAU,iCAAgCC,UAC1CF,EAAAA,EAAAA,KAAA,UACIC,UAAU,gBACVK,KAAK,SACL,iBAAe,YACf,iBAAe,WACf,gBAAc,UACd,gBAAc,QACd,aAAW,kBAAcJ,UAEzBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMgD,EAAAA,eAI9B,C,8JClGjB,MAohBA,EAphBsBC,KACpB,MAAOC,EAAeC,IAAoBlC,EAAAA,EAAAA,UAAS,KAC5CmC,EAAuBC,IAA4BpC,EAAAA,EAAAA,UAAS,KAC5DC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCsB,EAAOe,IAAYrC,EAAAA,EAAAA,UAAS,OAC5BsC,EAAQC,IAAavC,EAAAA,EAAAA,UAAS,QAC9BwC,EAAuBC,IAA4BzC,EAAAA,EAAAA,UAAS,KAC5D0C,EAAcC,IAAmB3C,EAAAA,EAAAA,WAAS,IAEjDG,EAAAA,EAAAA,YAAU,KACRyC,GAAoB,GACnB,KAEHzC,EAAAA,EAAAA,YAAU,KACR0C,GAAqB,GACpB,CAACZ,EAAeK,IAEnB,MAAMM,EAAqBxC,UACzB,IACEF,GAAW,GACXmC,EAAS,MAGT,MAAMS,EAAoB,CACxB,CACEtE,GAAI,EACJC,KAAM,cACNsE,SAAU,OACVC,MAAO,2CACPC,QAAS,0EACTC,SAAU,UACVC,WAAY,eACZC,UAAW,IAAIC,KAAKA,KAAKC,MAAQ,KACjCC,MAAM,EACNlG,SAAU,8BACVmG,UAAU,GAEZ,CACEhF,GAAI,EACJC,KAAM,UACNsE,SAAU,SACVC,MAAO,2BACPC,QAAS,qCACTC,SAAU,UACVC,WAAY,eACZC,UAAW,IAAIC,KAAKA,KAAKC,MAAQ,MACjCC,MAAM,EACNlG,SAAU,2BACVmG,UAAU,GAEZ,CACEhF,GAAI,EACJC,KAAM,WACNsE,SAAU,MACVC,MAAO,iCACPC,QAAS,kDACTC,SAAU,UACVC,WAAY,eACZC,UAAW,IAAIC,KAAKA,KAAKC,MAAQ,MACjCC,MAAM,EACNlG,SAAU,8BACVmG,UAAU,GAEZ,CACEhF,GAAI,EACJC,KAAM,OACNsE,SAAU,OACVC,MAAO,kCACPC,QAAS,mDACTC,SAAU,UACVC,WAAY,eACZC,UAAW,IAAIC,KAAKA,KAAKC,MAAQ,OACjCC,MAAM,EACNlG,SAAU,sBACVmG,UAAU,GAEZ,CACEhF,GAAI,EACJC,KAAM,aACNsE,SAAU,SACVC,MAAO,gCACPC,QAAS,+CACTC,SAAU,UACVC,WAAY,eACZC,UAAW,IAAIC,KAAKA,KAAKC,MAAQ,OACjCC,MAAM,EACNlG,SAAU,uBACVmG,UAAU,IAIdtB,EAAiBY,GACjB5C,GAAW,EACb,CAAE,MAAOoB,GACPR,QAAQQ,MAAM,8CAAqCA,GACnDe,EAAS,sDAAgDf,EAAM2B,SAC/D/C,GAAW,EACb,GAGI2C,EAAsBA,KAC1B,IAAIY,EAAW,IAAIxB,GAEnB,OAAQK,GACN,IAAK,SACHmB,EAAWA,EAASnB,QAAOoB,IAAMA,EAAEH,OACnC,MACF,IAAK,OACHE,EAAWA,EAASnB,QAAOoB,GAAoB,SAAfA,EAAEX,WAClC,MACF,IAAK,SACHU,EAAWA,EAASnB,QAAOoB,GAAoB,WAAfA,EAAEX,WAClC,MACF,IAAK,MACHU,EAAWA,EAASnB,QAAOoB,GAAoB,QAAfA,EAAEX,WAClC,MACF,IAAK,WACHU,EAAWA,EAASnB,QAAOoB,GAAKA,EAAEF,WAClC,MACF,IAAK,aACHC,EAAWA,EAASnB,QAAOoB,IAAMA,EAAEF,WAMvCpB,EAAyBqB,EAAS,EAG9BE,EAAiBP,IACrB,MACMQ,EADM,IAAIP,KACGD,EACbS,EAAUC,KAAKC,MAAMH,EAAI,KACzBI,EAAQF,KAAKC,MAAMH,EAAI,MACvBK,EAAOH,KAAKC,MAAMH,EAAI,OAE5B,OAAIC,EAAU,GACN,GAANlF,OAAUkF,EAAO,mBACRG,EAAQ,GACX,GAANrF,OAAUqF,EAAK,iBAET,GAANrF,OAAUsF,EAAI,kBAChB,EAGIC,EAAuBzF,IAC3B,OAAQA,GACN,IAAK,cACH,OAAO0F,EAAAA,IACT,IAAK,UACH,OAAOC,EAAAA,IACT,IAAK,WACH,OAAOC,EAAAA,IACT,IAAK,OACH,OAAOC,EAAAA,IACT,IAAK,aACH,OAAOC,EAAAA,IACT,QACE,OAAOlF,EAAAA,IACX,EAGImF,EAAoBzB,IACxB,OAAQA,GACN,IAAK,OACH,OAAO5E,EAAAA,EAAAA,KAAA,QAAMC,UAAU,kBAAiBC,SAAC,cAC3C,IAAK,SACH,OAAOF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mBAAkBC,SAAC,SAC5C,IAAK,MACH,OAAOF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gBAAeC,SAAC,qBACzC,QACE,OAAOF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,qBAAoBC,SAAC,eAChD,EAuCIoG,EAAkBC,IACtB,OAAQA,GACN,IAAK,SACH,OAAOzC,EAAcK,QAAOoB,IAAMA,EAAEH,OAAMoB,OAC5C,IAAK,OACH,OAAO1C,EAAcK,QAAOoB,GAAoB,SAAfA,EAAEX,WAAqB4B,OAC1D,IAAK,SACH,OAAO1C,EAAcK,QAAOoB,GAAoB,WAAfA,EAAEX,WAAuB4B,OAC5D,IAAK,MACH,OAAO1C,EAAcK,QAAOoB,GAAoB,QAAfA,EAAEX,WAAoB4B,OACzD,IAAK,WACH,OAAO1C,EAAcK,QAAOoB,GAAKA,EAAEF,WAAUmB,OAC/C,IAAK,aACH,OAAO1C,EAAcK,QAAOoB,IAAMA,EAAEF,WAAUmB,OAChD,QACE,OAAO1C,EAAc0C,OACzB,EAGF,OACErG,EAAAA,EAAAA,MAAAsG,EAAAA,SAAA,CAAAvG,SAAA,EACEF,EAAAA,EAAAA,KAACwB,EAAAA,EAAM,KACPxB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC9BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAClBF,EAAAA,EAAAA,KAACf,EAAAA,EAAO,KAERkB,EAAAA,EAAAA,MAAA,QAAMF,UAAU,iFAAgFC,SAAA,EAC9FC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0GAAyGC,SAAA,EACtHC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,KAAIC,SAAA,EAChBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMM,EAAAA,IAAQjB,UAAU,SAAS,cAEjD6D,EAAcK,QAAOoB,IAAMA,EAAEH,OAAMoB,OAAS,IAC3CxG,EAAAA,EAAAA,KAAA,QAAMC,UAAU,uBAAsBC,SACnC4D,EAAcK,QAAOoB,IAAMA,EAAEH,OAAMoB,aAI1CrG,EAAAA,EAAAA,MAAA,UACEF,UAAU,4BACVuD,QAASA,IAAMgB,GAAiBD,GAAcrE,SAAA,EAE9CF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM8F,EAAAA,GAAOzG,UAAU,SAAS,gBAKpD6B,GACC9B,EAAAA,EAAAA,KAAC2G,EAAAA,EAAc,CAACC,KAAK,KAAKC,QAAQ,UAAU/B,QAAQ,+BAA4BgC,UAAU,IACxF3D,GACFnD,EAAAA,EAAAA,KAAC+G,EAAAA,EAAY,CACXjC,QAAS3B,EACT0D,QAAQ,SACRhC,MAAM,8BACNmC,aAAa,EACbC,UAAWA,IAAM/C,EAAS,IAAIhE,UAE9BF,EAAAA,EAAAA,KAAA,UAAQC,UAAU,8BAA8BuD,QAASiB,EAAmBvE,SAAC,oBAK/EC,EAAAA,EAAAA,MAAAsG,EAAAA,SAAA,CAAAvG,SAAA,EAEEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,UAASC,SAAA,EACtBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACvBC,EAAAA,EAAAA,MAAA,UACEF,UAAS,cAAAO,OAA2B,QAAX2D,EAAmB,cAAgB,uBAC5DX,QAASA,IAAMY,EAAU,OAAOlE,SAAA,CACjC,eACQoG,EAAe,OAAO,UAGjCtG,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACvBC,EAAAA,EAAAA,MAAA,UACEF,UAAS,cAAAO,OAA2B,WAAX2D,EAAsB,cAAgB,uBAC/DX,QAASA,IAAMY,EAAU,UAAUlE,SAAA,CACpC,wBACaoG,EAAe,UAAU,UAGzCtG,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACvBC,EAAAA,EAAAA,MAAA,UACEF,UAAS,cAAAO,OAA2B,SAAX2D,EAAoB,aAAe,sBAC5DX,QAASA,IAAMY,EAAU,QAAQlE,SAAA,CAClC,cACUoG,EAAe,QAAQ,UAGpCtG,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACvBC,EAAAA,EAAAA,MAAA,UACEF,UAAS,cAAAO,OAA2B,WAAX2D,EAAsB,cAAgB,uBAC/DX,QAASA,IAAMY,EAAU,UAAUlE,SAAA,CACpC,SACQoG,EAAe,UAAU,UAGpCtG,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACvBC,EAAAA,EAAAA,MAAA,UACEF,UAAS,cAAAO,OAA2B,QAAX2D,EAAmB,WAAa,oBACzDX,QAASA,IAAMY,EAAU,OAAOlE,SAAA,CACjC,qBACSoG,EAAe,OAAO,UAGlCtG,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACvBC,EAAAA,EAAAA,MAAA,UACEF,UAAS,cAAAO,OAA2B,eAAX2D,EAA0B,cAAgB,uBACnEX,QAASA,IAAMY,EAAU,cAAclE,SAAA,CACxC,6BACcoG,EAAe,cAAc,YAK/CjC,EAAsBmC,OAAS,IAC9BrG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,MAAA,UAAQF,UAAU,iCAAiCuD,QAlI9C0D,KACzBnD,EAAiBD,EAAcqD,KAAI5B,GACjClB,EAAsB+C,SAAS7B,EAAElF,KAAGgH,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAQ9B,GAAC,IAAEH,MAAM,IAASG,KAEhEjB,EAAyB,GAAG,EA8HuEpE,SAAA,EAC7EF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM0G,EAAAA,IAASrH,UAAU,SAAS,8BACjCoE,EAAsBmC,OAAO,QAEjDrG,EAAAA,EAAAA,MAAA,UAAQF,UAAU,gCAAgCuD,QA/HjD+D,KACrBxD,EAAiBD,EAAcK,QAAOoB,IAAMlB,EAAsB+C,SAAS7B,EAAElF,OAC7EiE,EAAyB,GAAG,EA6HkEpE,SAAA,EACxEF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM4G,EAAAA,IAASvH,UAAU,SAAS,QAC7CoE,EAAsBmC,OAAO,gBAQ7CxG,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,SACM,IAAjC8D,EAAsBwC,QACrBrG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,EAC/BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMM,EAAAA,IAAQjB,UAAU,2BACzCD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,aAAYC,SAAC,8BAC3BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,aAAYC,SAAC,2DAG5BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8BAA6BC,SACzC8D,EAAsBmD,KAAKM,IAC1BzH,EAAAA,EAAAA,KAAA,OAEEC,UAAS,mBAAAO,OAAsBiH,EAAarC,KAAiC,GAA1B,yBAA+BlF,UAElFC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2BAA0BC,SAAA,EACvCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,KAAA,SACEM,KAAK,WACLL,UAAU,mBACVyH,QAASrD,EAAsB+C,SAASK,EAAapH,IACrDsH,SAAUA,KAAMC,OA7KXvH,EA6KuCoH,EAAapH,QA5KvFiE,GAAyBuD,GACvBA,EAAKT,SAAS/G,GACVwH,EAAK1D,QAAO2D,GAAOA,IAAQzH,IAC3B,IAAIwH,EAAMxH,KAJmBA,KA6KwD,OAIjEL,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,KAAA,OAAKC,UAAS,qBAAAO,OAAuBiH,EAAa7C,UAAW1E,UAC3DF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CACdC,KAAMmF,EAAoB0B,EAAanH,MACvCL,UAAS,QAAAO,OAAoC,SAA1BiH,EAAa7C,SAAsB,SAC1B,WAA1B6C,EAAa7C,SAAwB,UAAY,eAKzDzE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mDAAkDC,SAAA,EAC/DC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,OAAMC,SAAA,CACjBuH,EAAa5C,OACZ4C,EAAarC,OACbpF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,wBAAuBC,SAAC,SAEzCuH,EAAapC,WACZrF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,wBAAuBC,SAAC,4BAG5CF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,OAAMC,SAAEuH,EAAa3C,WAClC3E,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,EAC/BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMmH,EAAAA,IAAgB9H,UAAU,SAChDwH,EAAavI,UACdc,EAAAA,EAAAA,KAAA,QAAMC,UAAU,OAAMC,SAAC,YACvBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMoH,EAAAA,IAAe/H,UAAU,SAC/CuF,EAAciC,EAAaxC,kBAGhCjF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,SACtBmG,EAAiBoB,EAAa7C,gBAInC5E,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yBAAwBC,SAAA,EACrCC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CACHG,GAAE,SAAAF,OAAWiH,EAAazC,YAC1B/E,UAAU,0BAAyBC,SAAA,EAEnCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMqH,EAAAA,IAAOhI,UAAU,SAAS,YAGjDwH,EAAarC,OACbjF,EAAAA,EAAAA,MAAA,UACEF,UAAU,0BACVuD,QAASA,KAAM0E,OAjPjC7H,EAiP4CoH,EAAapH,QAhP3E0D,EAAiBD,EAAcqD,KAAI5B,GACjCA,EAAElF,KAAOA,GAAEgH,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAQ9B,GAAC,IAAEH,MAAM,IAASG,KAFrBlF,KAiP6D,EAAAH,SAAA,EAE3CF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM0G,EAAAA,IAASrH,UAAU,SAAS,aAIrDwH,EAAapC,WACblF,EAAAA,EAAAA,MAAA,UACEF,UAAU,0BACVuD,QAASA,KAAM2E,OApP7B9H,EAoP4CoH,EAAapH,QAnP/E0D,EAAiBD,EAAcqD,KAAI5B,GACjCA,EAAElF,KAAOA,GAAEgH,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAQ9B,GAAC,IAAEF,UAAU,IAASE,KAFrBlF,KAoP6D,EAAAH,SAAA,EAE/CF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM0G,EAAAA,IAASrH,UAAU,SAAS,0BAIvDD,EAAAA,EAAAA,KAAA,UACEC,UAAU,yBACVuD,QAASA,KAAM4E,OAtPvB/H,EAsP0CoH,EAAapH,QArPjF0D,EAAiBD,EAAcK,QAAOoB,GAAKA,EAAElF,KAAOA,KAD1BA,KAsP2D,EAAAH,UAEnDF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM4G,EAAAA,oBAhF5BC,EAAapH,mBAgGpCL,EAAAA,EAAAA,KAACuB,EAAAA,EAAM,SAKVgD,IACCvE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qBAAqBG,SAAS,KAAKiI,MAAO,CAAEC,gBAAiB,mBAAoBpI,UAC9FF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,eAAcC,UAC3BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,eAAcC,SAAA,EAC3BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,cAAaC,SAAC,4BAC5BF,EAAAA,EAAAA,KAAA,UACEM,KAAK,SACLL,UAAU,YACVuD,QAASA,IAAMgB,GAAgB,SAGnCrE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYC,SAAA,EACzBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,mBAAmBK,KAAK,WAAWD,GAAG,qBAAqBkI,gBAAc,KAC1FvI,EAAAA,EAAAA,KAAA,SAAOC,UAAU,mBAAmBuI,QAAQ,qBAAoBtI,SAAC,6BAInEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,mBAAmBK,KAAK,WAAWD,GAAG,sBACvDL,EAAAA,EAAAA,KAAA,SAAOC,UAAU,mBAAmBuI,QAAQ,mBAAkBtI,SAAC,yBAIjEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,mBAAmBK,KAAK,WAAWD,GAAG,oBAAoBkI,gBAAc,KACzFvI,EAAAA,EAAAA,KAAA,SAAOC,UAAU,mBAAmBuI,QAAQ,oBAAmBtI,SAAC,0BAIlEF,EAAAA,EAAAA,KAAA,UACAA,EAAAA,EAAAA,KAAA,MAAAE,SAAI,yBACJC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,mBAAmBK,KAAK,WAAWD,GAAG,aAAakI,gBAAc,KAClFvI,EAAAA,EAAAA,KAAA,SAAOC,UAAU,mBAAmBuI,QAAQ,aAAYtI,SAAC,0CAI3DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,mBAAmBK,KAAK,WAAWD,GAAG,gBAAgBkI,gBAAc,KACrFvI,EAAAA,EAAAA,KAAA,SAAOC,UAAU,mBAAmBuI,QAAQ,gBAAetI,SAAC,+BAI9DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,mBAAmBK,KAAK,WAAWD,GAAG,aAAakI,gBAAc,KAClFvI,EAAAA,EAAAA,KAAA,SAAOC,UAAU,mBAAmBuI,QAAQ,aAAYtI,SAAC,oCAK7DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,eAAcC,SAAA,EAC3BF,EAAAA,EAAAA,KAAA,UACEM,KAAK,SACLL,UAAU,oBACVuD,QAASA,IAAMgB,GAAgB,GAAOtE,SACvC,gBAGDF,EAAAA,EAAAA,KAAA,UACEM,KAAK,SACLL,UAAU,kBACVuD,QAASA,IAAMgB,GAAgB,GAAOtE,SACvC,uBAQV,C", "sources": ["components/Sidebar.js", "components/Footer.js", "components/Header.js", "pages/Notifications.js"], "sourcesContent": ["import React from 'react';\r\nimport { Link, useLocation } from 'react-router-dom';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport {\r\n    faDolly, faFolderPlus, faAnchor, faFlag<PERSON>he<PERSON>ed,\r\n    faUser, faStreetView, faCreditCard, faFileLines,\r\n    faBell, faDesktop\r\n} from '@fortawesome/free-solid-svg-icons';\r\n\r\nconst Sidebar = () => {\r\n    const location = useLocation();\r\n    \r\n    // Get user role from localStorage\r\n    const getUserRole = () => {\r\n        try {\r\n            const user = JSON.parse(localStorage.getItem('user'));\r\n            return user?.user?.role || user?.user?.gorev || 'user';\r\n        } catch {\r\n            return 'user';\r\n        }\r\n    };\r\n    \r\n    const userRole = getUserRole();\r\n    const isAdmin = userRole === 'admin';\r\n\r\n    return (\r\n        <div className=\"sidebar col-md-4 col-lg-2 p-0 bg-light-subtle\">\r\n            <div className=\"offcanvas-md offcanvas-end\" tabIndex=\"-1\" id=\"yanMenu\" aria-labelledby=\"yanMenu\">\r\n                <div className=\"offcanvas-header\">\r\n                    <h5 className=\"offcanvas-title\" id=\"yanMenu\">MGZ24 Gold</h5>\r\n                    <button type=\"button\" className=\"btn-close\" data-bs-dismiss=\"offcanvas\" data-bs-target=\"#yanMenu\" aria-label=\"Kapat\"></button>\r\n                </div>\r\n                <div className=\"offcanvas-body d-md-flex flex-column p-0 pt-lg-3 overflow-y-auto\">\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Sevkiyat İşlemleri</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/' ? 'active' : ''}`} to=\"/\">\r\n                                <FontAwesomeIcon icon={faDolly} />Aktif Sevkiyatlar\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/add' ? 'active' : ''}`} to=\"/add\">\r\n                                <FontAwesomeIcon icon={faFolderPlus} />Yeni Sevkiyat Oluştur\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/inactive-devices' ? 'active' : ''}`} to=\"/inactive-devices\">\r\n                                <FontAwesomeIcon icon={faAnchor} />İnaktif Cihazlar\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/history' ? 'active' : ''}`} to=\"/history\">\r\n                                <FontAwesomeIcon icon={faFlagCheckered} />Geçmiş Sevkiyatlar\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Sistem Yönetimi</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        {/* Cihaz Yönetimi - Sadece admin kullanıcılarına göster */}\r\n                        {isAdmin && (\r\n                            <li className=\"nav-item\">\r\n                                <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/devices' ? 'active' : ''}`} to=\"/devices\">\r\n                                    <FontAwesomeIcon icon={faDesktop} />Cihaz Yönetimi\r\n                                </Link>\r\n                            </li>\r\n                        )}\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/notifications' ? 'active' : ''}`} to=\"/notifications\">\r\n                                <FontAwesomeIcon icon={faBell} />Bildirimler\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Kullanıcı Ayarları</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/profile' ? 'active' : ''}`} to=\"/profile\">\r\n                                <FontAwesomeIcon icon={faUser} />Kullanıcı Profilim\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/viewers' ? 'active' : ''}`} to=\"/viewers\">\r\n                                <FontAwesomeIcon icon={faStreetView} />İzleyici İşlemleri\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Ödeme ve Yapılandırma</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/payment' ? 'active' : ''}`} to=\"/payment\">\r\n                                <FontAwesomeIcon icon={faCreditCard} />Ödeme Yap\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/invoices' ? 'active' : ''}`} to=\"/invoices\">\r\n                                <FontAwesomeIcon icon={faFileLines} />Faturalarım\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-body-secondary text-uppercase\">Kredi Durumu</h6>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Sidebar;", "import React from 'react';\r\n\r\nconst Footer = () => {\r\n    return (\r\n        <footer className=\"py-5 border-top\">\r\n            <p className=\"text-center\">&copy;2025 Inkatech Ölçüm Sistemleri</p>\r\n        </footer>\r\n    );\r\n};\r\n\r\nexport default Footer; ", "import React, { useState, useEffect } from 'react';\r\nimport { Link, useNavigate } from 'react-router-dom';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { faRightFromBracket, faBars, faUser } from '@fortawesome/free-solid-svg-icons';\r\nimport logoDark from '../assets/img/logo.png';\r\nimport { kullaniciService } from '../api/dbService';\r\n\r\nconst Header = () => {\r\n    const navigate = useNavigate();\r\n    const [userName, setUserName] = useState('Misafir');\r\n    const [loading, setLoading] = useState(true);\r\n\r\n    // Kullanıcı bilgilerini veritabanından al\r\n    useEffect(() => {\r\n        const fetchUserData = async () => {\r\n            try {\r\n                // Local storage'dan kullanıcı bilgisini al\r\n                const storedUser = JSON.parse(localStorage.getItem('user'));\r\n                const userId = storedUser?.user?.musteri_ID || storedUser?.user?.id;\r\n                const userName = storedUser?.user?.name || storedUser?.user?.musteri_adi;\r\n\r\n                if (!userId) {\r\n                    console.warn('Oturum bilgisi bulunamadı');\r\n                    setUserName('Misafir');\r\n                    setLoading(false);\r\n                    return;\r\n                }\r\n\r\n                // Eğer localStorage'da isim varsa direkt kullan\r\n                if (userName) {\r\n                    setUserName(userName);\r\n                    setLoading(false);\r\n                    return;\r\n                }\r\n\r\n                // API'den kullanıcı bilgilerini getir (backend çalışıyorsa)\r\n                try {\r\n                    const userData = await kullaniciService.getKullanici(userId);\r\n\r\n                    // musteri_adi alanını kullan\r\n                    if (userData && userData.musteri_adi) {\r\n                        setUserName(userData.musteri_adi);\r\n\r\n                        // localStorage'ı güncelle\r\n                        if (storedUser?.user) {\r\n                            storedUser.user.name = userData.musteri_adi;\r\n                            localStorage.setItem('user', JSON.stringify(storedUser));\r\n                        }\r\n                    }\r\n                } catch (apiError) {\r\n                    console.warn('API\\'den kullanıcı bilgisi alınamadı, localStorage kullanılıyor');\r\n                    // API hatası durumunda localStorage'daki adı kullan\r\n                    setUserName(userName || 'Kullanıcı');\r\n                }\r\n            } catch (error) {\r\n                console.error('Kullanıcı bilgileri alınırken hata:', error);\r\n                // Hata durumunda varsayılan isim kullan\r\n                setUserName('Kullanıcı');\r\n            } finally {\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        fetchUserData();\r\n    }, []);\r\n\r\n    // Çıkış işlemi\r\n    const handleLogout = () => {\r\n        // Local storage'dan kullanıcı bilgilerini temizle\r\n        localStorage.removeItem('user');\r\n\r\n        // Login sayfasına yönlendir\r\n        navigate('/login');\r\n    };\r\n\r\n    return (\r\n        <header className=\"navbar sticky-top bg-darkblue flex-md-nowrap p-0 shadow\" data-bs-theme=\"dark\">\r\n            <Link className=\"navbar-brand col-md-4 col-lg-2 me-0 px-3 py-2 fs-6 text-white\" to=\"/\">\r\n                <img src={logoDark} alt=\"MGZ24 Logo\" height=\"40\" />\r\n            </Link>\r\n            <ul className=\"navbar-nav flex-row align-items-center\">\r\n                <li className=\"nav-item text-nowrap d-none d-md-block me-3\">\r\n                    <span className=\"text-white\">\r\n                        <FontAwesomeIcon icon={faUser} className=\"me-2\" />\r\n                        {loading ? 'Yükleniyor...' : userName}\r\n                    </span>\r\n                </li>\r\n                <li className=\"nav-item text-nowrap px-1\">\r\n                    <button\r\n                        onClick={handleLogout}\r\n                        className=\"nav-link px-3\"\r\n                        data-bs-toggle=\"tooltip\"\r\n                        data-bs-placement=\"left\"\r\n                        data-bs-title=\"Çıkış yap!\"\r\n                    >\r\n                        <FontAwesomeIcon icon={faRightFromBracket} />\r\n                    </button>\r\n                </li>\r\n                <li className=\"nav-item text-nowrap d-md-none\">\r\n                    <button\r\n                        className=\"nav-link px-3\"\r\n                        type=\"button\"\r\n                        data-bs-toggle=\"offcanvas\"\r\n                        data-bs-target=\"#yanMenu\"\r\n                        aria-controls=\"yanMenu\"\r\n                        aria-expanded=\"false\"\r\n                        aria-label=\"menu aç/kapa\"\r\n                    >\r\n                        <FontAwesomeIcon icon={faBars} />\r\n                    </button>\r\n                </li>\r\n            </ul>\r\n        </header>\r\n    );\r\n};\r\n\r\nexport default Header; ", "import React, { useState, useEffect } from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { \r\n  faBell, faExclamationTriangle, faThermometerHalf, faBatteryEmpty,\r\n  faDroplet, faDoorOpen, faWifi, faCheck, faTrash, faFilter,\r\n  faCalendarAlt, faMapMarkerAlt, faEye, faCog\r\n} from '@fortawesome/free-solid-svg-icons';\r\nimport Header from '../components/Header';\r\nimport Sidebar from '../components/Sidebar';\r\nimport Footer from '../components/Footer';\r\nimport LoadingSpinner from '../components/LoadingSpinner';\r\nimport ErrorMessage from '../components/ErrorMessage';\r\n\r\nconst Notifications = () => {\r\n  const [notifications, setNotifications] = useState([]);\r\n  const [filteredNotifications, setFilteredNotifications] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [filter, setFilter] = useState('all');\r\n  const [selectedNotifications, setSelectedNotifications] = useState([]);\r\n  const [showSettings, setShowSettings] = useState(false);\r\n\r\n  useEffect(() => {\r\n    fetchNotifications();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    filterNotifications();\r\n  }, [notifications, filter]);\r\n\r\n  const fetchNotifications = async () => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      // Demo veriler - gerçek API ile değiştirilecek\r\n      const demoNotifications = [\r\n        {\r\n          id: 1,\r\n          type: 'temperature',\r\n          severity: 'high',\r\n          title: 'Yüksek Sıcaklık Alarmı',\r\n          message: 'Cihaz MGZ-001 sıcaklık değeri 12°C (Normal: 2-8°C)',\r\n          deviceId: 'MGZ-001',\r\n          shipmentId: 'SVK-2024-001',\r\n          timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 dakika önce\r\n          read: false,\r\n          location: 'Ankara - İstanbul Yolu',\r\n          resolved: false\r\n        },\r\n        {\r\n          id: 2,\r\n          type: 'battery',\r\n          severity: 'medium',\r\n          title: 'Düşük Batarya',\r\n          message: 'Cihaz MGZ-002 batarya seviyesi %15',\r\n          deviceId: 'MGZ-002',\r\n          shipmentId: 'SVK-2024-002',\r\n          timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 dakika önce\r\n          read: false,\r\n          location: 'İzmir - Manisa Yolu',\r\n          resolved: false\r\n        },\r\n        {\r\n          id: 3,\r\n          type: 'humidity',\r\n          severity: 'low',\r\n          title: 'Nem Seviyesi Uyarısı',\r\n          message: 'Cihaz MGZ-003 nem seviyesi %85 (Normal: 40-60%)',\r\n          deviceId: 'MGZ-003',\r\n          shipmentId: 'SVK-2024-003',\r\n          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 saat önce\r\n          read: true,\r\n          location: 'Bursa - Eskişehir Yolu',\r\n          resolved: true\r\n        },\r\n        {\r\n          id: 4,\r\n          type: 'door',\r\n          severity: 'high',\r\n          title: 'Kapı Açılması',\r\n          message: 'Cihaz MGZ-004 kapı sensörü tetiklendi',\r\n          deviceId: 'MGZ-004',\r\n          shipmentId: 'SVK-2024-004',\r\n          timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 saat önce\r\n          read: true,\r\n          location: 'Adana - Mersin Yolu',\r\n          resolved: false\r\n        },\r\n        {\r\n          id: 5,\r\n          type: 'connection',\r\n          severity: 'medium',\r\n          title: 'Bağlantı Kaybı',\r\n          message: 'Cihaz MGZ-005 ile bağlantı kesildi',\r\n          deviceId: 'MGZ-005',\r\n          shipmentId: 'SVK-2024-005',\r\n          timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 saat önce\r\n          read: true,\r\n          location: 'Antalya - Konya Yolu',\r\n          resolved: true\r\n        }\r\n      ];\r\n\r\n      setNotifications(demoNotifications);\r\n      setLoading(false);\r\n    } catch (error) {\r\n      console.error('Bildirim verileri alınırken hata:', error);\r\n      setError('Bildirim verileri yüklenirken hata oluştu: ' + error.message);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const filterNotifications = () => {\r\n    let filtered = [...notifications];\r\n\r\n    switch (filter) {\r\n      case 'unread':\r\n        filtered = filtered.filter(n => !n.read);\r\n        break;\r\n      case 'high':\r\n        filtered = filtered.filter(n => n.severity === 'high');\r\n        break;\r\n      case 'medium':\r\n        filtered = filtered.filter(n => n.severity === 'medium');\r\n        break;\r\n      case 'low':\r\n        filtered = filtered.filter(n => n.severity === 'low');\r\n        break;\r\n      case 'resolved':\r\n        filtered = filtered.filter(n => n.resolved);\r\n        break;\r\n      case 'unresolved':\r\n        filtered = filtered.filter(n => !n.resolved);\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n\r\n    setFilteredNotifications(filtered);\r\n  };\r\n\r\n  const formatTimeAgo = (timestamp) => {\r\n    const now = new Date();\r\n    const diff = now - timestamp;\r\n    const minutes = Math.floor(diff / (1000 * 60));\r\n    const hours = Math.floor(diff / (1000 * 60 * 60));\r\n    const days = Math.floor(diff / (1000 * 60 * 60 * 24));\r\n\r\n    if (minutes < 60) {\r\n      return `${minutes} dakika önce`;\r\n    } else if (hours < 24) {\r\n      return `${hours} saat önce`;\r\n    } else {\r\n      return `${days} gün önce`;\r\n    }\r\n  };\r\n\r\n  const getNotificationIcon = (type) => {\r\n    switch (type) {\r\n      case 'temperature':\r\n        return faThermometerHalf;\r\n      case 'battery':\r\n        return faBatteryEmpty;\r\n      case 'humidity':\r\n        return faDroplet;\r\n      case 'door':\r\n        return faDoorOpen;\r\n      case 'connection':\r\n        return faWifi;\r\n      default:\r\n        return faBell;\r\n    }\r\n  };\r\n\r\n  const getSeverityBadge = (severity) => {\r\n    switch (severity) {\r\n      case 'high':\r\n        return <span className=\"badge bg-danger\">Yüksek</span>;\r\n      case 'medium':\r\n        return <span className=\"badge bg-warning\">Orta</span>;\r\n      case 'low':\r\n        return <span className=\"badge bg-info\">Düşük</span>;\r\n      default:\r\n        return <span className=\"badge bg-secondary\">Bilinmiyor</span>;\r\n    }\r\n  };\r\n\r\n  const markAsRead = (id) => {\r\n    setNotifications(notifications.map(n => \r\n      n.id === id ? { ...n, read: true } : n\r\n    ));\r\n  };\r\n\r\n  const markAsResolved = (id) => {\r\n    setNotifications(notifications.map(n => \r\n      n.id === id ? { ...n, resolved: true } : n\r\n    ));\r\n  };\r\n\r\n  const deleteNotification = (id) => {\r\n    setNotifications(notifications.filter(n => n.id !== id));\r\n  };\r\n\r\n  const toggleNotificationSelection = (id) => {\r\n    setSelectedNotifications(prev => \r\n      prev.includes(id) \r\n        ? prev.filter(nId => nId !== id)\r\n        : [...prev, id]\r\n    );\r\n  };\r\n\r\n  const markSelectedAsRead = () => {\r\n    setNotifications(notifications.map(n => \r\n      selectedNotifications.includes(n.id) ? { ...n, read: true } : n\r\n    ));\r\n    setSelectedNotifications([]);\r\n  };\r\n\r\n  const deleteSelected = () => {\r\n    setNotifications(notifications.filter(n => !selectedNotifications.includes(n.id)));\r\n    setSelectedNotifications([]);\r\n  };\r\n\r\n  const getFilterCount = (filterType) => {\r\n    switch (filterType) {\r\n      case 'unread':\r\n        return notifications.filter(n => !n.read).length;\r\n      case 'high':\r\n        return notifications.filter(n => n.severity === 'high').length;\r\n      case 'medium':\r\n        return notifications.filter(n => n.severity === 'medium').length;\r\n      case 'low':\r\n        return notifications.filter(n => n.severity === 'low').length;\r\n      case 'resolved':\r\n        return notifications.filter(n => n.resolved).length;\r\n      case 'unresolved':\r\n        return notifications.filter(n => !n.resolved).length;\r\n      default:\r\n        return notifications.length;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Header />\r\n      <div className=\"container-fluid\">\r\n        <div className=\"row\">\r\n          <Sidebar />\r\n          \r\n          <main className=\"col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white\">\r\n            <div className=\"d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom\">\r\n              <h1 className=\"h2\">\r\n                <FontAwesomeIcon icon={faBell} className=\"me-2\" />\r\n                Bildirimler\r\n                {notifications.filter(n => !n.read).length > 0 && (\r\n                  <span className=\"badge bg-danger ms-2\">\r\n                    {notifications.filter(n => !n.read).length}\r\n                  </span>\r\n                )}\r\n              </h1>\r\n              <button\r\n                className=\"btn btn-outline-secondary\"\r\n                onClick={() => setShowSettings(!showSettings)}\r\n              >\r\n                <FontAwesomeIcon icon={faCog} className=\"me-2\" />\r\n                Ayarlar\r\n              </button>\r\n            </div>\r\n\r\n            {loading ? (\r\n              <LoadingSpinner size=\"lg\" variant=\"primary\" message=\"Bildirimler yükleniyor...\" centered={true} />\r\n            ) : error ? (\r\n              <ErrorMessage \r\n                message={error} \r\n                variant=\"danger\"\r\n                title=\"Veri Yükleme Hatası\"\r\n                dismissible={true}\r\n                onDismiss={() => setError('')}\r\n              >\r\n                <button className=\"btn btn-primary btn-sm mt-2\" onClick={fetchNotifications}>\r\n                  Yeniden Dene\r\n                </button>\r\n              </ErrorMessage>\r\n            ) : (\r\n              <>\r\n                {/* Filtreler */}\r\n                <div className=\"card mb-4\">\r\n                  <div className=\"card-body\">\r\n                    <div className=\"row g-2\">\r\n                      <div className=\"col-auto\">\r\n                        <button\r\n                          className={`btn btn-sm ${filter === 'all' ? 'btn-primary' : 'btn-outline-primary'}`}\r\n                          onClick={() => setFilter('all')}\r\n                        >\r\n                          Tümü ({getFilterCount('all')})\r\n                        </button>\r\n                      </div>\r\n                      <div className=\"col-auto\">\r\n                        <button\r\n                          className={`btn btn-sm ${filter === 'unread' ? 'btn-primary' : 'btn-outline-primary'}`}\r\n                          onClick={() => setFilter('unread')}\r\n                        >\r\n                          Okunmamış ({getFilterCount('unread')})\r\n                        </button>\r\n                      </div>\r\n                      <div className=\"col-auto\">\r\n                        <button\r\n                          className={`btn btn-sm ${filter === 'high' ? 'btn-danger' : 'btn-outline-danger'}`}\r\n                          onClick={() => setFilter('high')}\r\n                        >\r\n                          Yüksek ({getFilterCount('high')})\r\n                        </button>\r\n                      </div>\r\n                      <div className=\"col-auto\">\r\n                        <button\r\n                          className={`btn btn-sm ${filter === 'medium' ? 'btn-warning' : 'btn-outline-warning'}`}\r\n                          onClick={() => setFilter('medium')}\r\n                        >\r\n                          Orta ({getFilterCount('medium')})\r\n                        </button>\r\n                      </div>\r\n                      <div className=\"col-auto\">\r\n                        <button\r\n                          className={`btn btn-sm ${filter === 'low' ? 'btn-info' : 'btn-outline-info'}`}\r\n                          onClick={() => setFilter('low')}\r\n                        >\r\n                          Düşük ({getFilterCount('low')})\r\n                        </button>\r\n                      </div>\r\n                      <div className=\"col-auto\">\r\n                        <button\r\n                          className={`btn btn-sm ${filter === 'unresolved' ? 'btn-warning' : 'btn-outline-warning'}`}\r\n                          onClick={() => setFilter('unresolved')}\r\n                        >\r\n                          Çözülmemiş ({getFilterCount('unresolved')})\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n                    \r\n                    {selectedNotifications.length > 0 && (\r\n                      <div className=\"mt-3 d-flex gap-2\">\r\n                        <button className=\"btn btn-sm btn-outline-success\" onClick={markSelectedAsRead}>\r\n                          <FontAwesomeIcon icon={faCheck} className=\"me-1\" />\r\n                          Okundu İşaretle ({selectedNotifications.length})\r\n                        </button>\r\n                        <button className=\"btn btn-sm btn-outline-danger\" onClick={deleteSelected}>\r\n                          <FontAwesomeIcon icon={faTrash} className=\"me-1\" />\r\n                          Sil ({selectedNotifications.length})\r\n                        </button>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Bildirim Listesi */}\r\n                <div className=\"card\">\r\n                  <div className=\"card-body p-0\">\r\n                    {filteredNotifications.length === 0 ? (\r\n                      <div className=\"text-center py-5\">\r\n                        <FontAwesomeIcon icon={faBell} className=\"fa-3x text-muted mb-3\" />\r\n                        <h5 className=\"text-muted\">Bildirim Bulunamadı</h5>\r\n                        <p className=\"text-muted\">Seçili filtreye uygun bildirim bulunamadı.</p>\r\n                      </div>\r\n                    ) : (\r\n                      <div className=\"list-group list-group-flush\">\r\n                        {filteredNotifications.map((notification) => (\r\n                          <div\r\n                            key={notification.id}\r\n                            className={`list-group-item ${!notification.read ? 'list-group-item-light' : ''}`}\r\n                          >\r\n                            <div className=\"d-flex align-items-start\">\r\n                              <div className=\"me-3\">\r\n                                <input\r\n                                  type=\"checkbox\"\r\n                                  className=\"form-check-input\"\r\n                                  checked={selectedNotifications.includes(notification.id)}\r\n                                  onChange={() => toggleNotificationSelection(notification.id)}\r\n                                />\r\n                              </div>\r\n                              \r\n                              <div className=\"me-3\">\r\n                                <div className={`notification-icon ${notification.severity}`}>\r\n                                  <FontAwesomeIcon \r\n                                    icon={getNotificationIcon(notification.type)} \r\n                                    className={`text-${notification.severity === 'high' ? 'danger' : \r\n                                      notification.severity === 'medium' ? 'warning' : 'info'}`}\r\n                                  />\r\n                                </div>\r\n                              </div>\r\n                              \r\n                              <div className=\"flex-grow-1\">\r\n                                <div className=\"d-flex justify-content-between align-items-start\">\r\n                                  <div>\r\n                                    <h6 className=\"mb-1\">\r\n                                      {notification.title}\r\n                                      {!notification.read && (\r\n                                        <span className=\"badge bg-primary ms-2\">Yeni</span>\r\n                                      )}\r\n                                      {notification.resolved && (\r\n                                        <span className=\"badge bg-success ms-2\">Çözüldü</span>\r\n                                      )}\r\n                                    </h6>\r\n                                    <p className=\"mb-1\">{notification.message}</p>\r\n                                    <div className=\"small text-muted\">\r\n                                      <FontAwesomeIcon icon={faMapMarkerAlt} className=\"me-1\" />\r\n                                      {notification.location}\r\n                                      <span className=\"mx-2\">•</span>\r\n                                      <FontAwesomeIcon icon={faCalendarAlt} className=\"me-1\" />\r\n                                      {formatTimeAgo(notification.timestamp)}\r\n                                    </div>\r\n                                  </div>\r\n                                  <div className=\"text-end\">\r\n                                    {getSeverityBadge(notification.severity)}\r\n                                  </div>\r\n                                </div>\r\n                                \r\n                                <div className=\"mt-2\">\r\n                                  <div className=\"btn-group btn-group-sm\">\r\n                                    <Link\r\n                                      to={`/view/${notification.shipmentId}`}\r\n                                      className=\"btn btn-outline-primary\"\r\n                                    >\r\n                                      <FontAwesomeIcon icon={faEye} className=\"me-1\" />\r\n                                      Detay\r\n                                    </Link>\r\n                                    {!notification.read && (\r\n                                      <button\r\n                                        className=\"btn btn-outline-success\"\r\n                                        onClick={() => markAsRead(notification.id)}\r\n                                      >\r\n                                        <FontAwesomeIcon icon={faCheck} className=\"me-1\" />\r\n                                        Okundu\r\n                                      </button>\r\n                                    )}\r\n                                    {!notification.resolved && (\r\n                                      <button\r\n                                        className=\"btn btn-outline-warning\"\r\n                                        onClick={() => markAsResolved(notification.id)}\r\n                                      >\r\n                                        <FontAwesomeIcon icon={faCheck} className=\"me-1\" />\r\n                                        Çözüldü\r\n                                      </button>\r\n                                    )}\r\n                                    <button\r\n                                      className=\"btn btn-outline-danger\"\r\n                                      onClick={() => deleteNotification(notification.id)}\r\n                                    >\r\n                                      <FontAwesomeIcon icon={faTrash} />\r\n                                    </button>\r\n                                  </div>\r\n                                </div>\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        ))}\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </>\r\n            )}\r\n          </main>\r\n          \r\n          <Footer />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Ayarlar Modalı */}\r\n      {showSettings && (\r\n        <div className=\"modal show d-block\" tabIndex=\"-1\" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>\r\n          <div className=\"modal-dialog\">\r\n            <div className=\"modal-content\">\r\n              <div className=\"modal-header\">\r\n                <h5 className=\"modal-title\">Bildirim Ayarları</h5>\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"btn-close\"\r\n                  onClick={() => setShowSettings(false)}\r\n                ></button>\r\n              </div>\r\n              <div className=\"modal-body\">\r\n                <div className=\"form-check\">\r\n                  <input className=\"form-check-input\" type=\"checkbox\" id=\"emailNotifications\" defaultChecked />\r\n                  <label className=\"form-check-label\" htmlFor=\"emailNotifications\">\r\n                    E-posta bildirimleri\r\n                  </label>\r\n                </div>\r\n                <div className=\"form-check\">\r\n                  <input className=\"form-check-input\" type=\"checkbox\" id=\"smsNotifications\" />\r\n                  <label className=\"form-check-label\" htmlFor=\"smsNotifications\">\r\n                    SMS bildirimleri\r\n                  </label>\r\n                </div>\r\n                <div className=\"form-check\">\r\n                  <input className=\"form-check-input\" type=\"checkbox\" id=\"pushNotifications\" defaultChecked />\r\n                  <label className=\"form-check-label\" htmlFor=\"pushNotifications\">\r\n                    Push bildirimleri\r\n                  </label>\r\n                </div>\r\n                <hr />\r\n                <h6>Bildirim Türleri</h6>\r\n                <div className=\"form-check\">\r\n                  <input className=\"form-check-input\" type=\"checkbox\" id=\"tempAlerts\" defaultChecked />\r\n                  <label className=\"form-check-label\" htmlFor=\"tempAlerts\">\r\n                    Sıcaklık alarmları\r\n                  </label>\r\n                </div>\r\n                <div className=\"form-check\">\r\n                  <input className=\"form-check-input\" type=\"checkbox\" id=\"batteryAlerts\" defaultChecked />\r\n                  <label className=\"form-check-label\" htmlFor=\"batteryAlerts\">\r\n                    Batarya alarmları\r\n                  </label>\r\n                </div>\r\n                <div className=\"form-check\">\r\n                  <input className=\"form-check-input\" type=\"checkbox\" id=\"doorAlerts\" defaultChecked />\r\n                  <label className=\"form-check-label\" htmlFor=\"doorAlerts\">\r\n                    Kapı alarmları\r\n                  </label>\r\n                </div>\r\n              </div>\r\n              <div className=\"modal-footer\">\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"btn btn-secondary\"\r\n                  onClick={() => setShowSettings(false)}\r\n                >\r\n                  İptal\r\n                </button>\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"btn btn-primary\"\r\n                  onClick={() => setShowSettings(false)}\r\n                >\r\n                  Kaydet\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Notifications;"], "names": ["Sidebar", "location", "useLocation", "isAdmin", "getUserRole", "_user$user", "_user$user2", "user", "JSON", "parse", "localStorage", "getItem", "role", "gorev", "_unused", "_jsx", "className", "children", "_jsxs", "tabIndex", "id", "type", "Link", "concat", "pathname", "to", "FontAwesomeIcon", "icon", "faDolly", "faFolderPlus", "faAnchor", "faFlag<PERSON><PERSON><PERSON><PERSON>", "faDesktop", "faBell", "faUser", "faStreetView", "faCreditCard", "faFileLines", "Footer", "Header", "navigate", "useNavigate", "userName", "setUserName", "useState", "loading", "setLoading", "useEffect", "async", "_storedUser$user", "_storedUser$user2", "_storedUser$user3", "_storedUser$user4", "storedUser", "userId", "musteri_ID", "name", "musteri_adi", "console", "warn", "userData", "kullaniciService", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setItem", "stringify", "apiError", "error", "fetchUserData", "src", "alt", "height", "onClick", "handleLogout", "removeItem", "faRightFromBracket", "faBars", "Notifications", "notifications", "setNotifications", "filteredNotifications", "setFilteredNotifications", "setError", "filter", "setFilter", "selectedNotifications", "setSelectedNotifications", "showSettings", "setShowSettings", "fetchNotifications", "filterNotifications", "demoNotifications", "severity", "title", "message", "deviceId", "shipmentId", "timestamp", "Date", "now", "read", "resolved", "filtered", "n", "formatTimeAgo", "diff", "minutes", "Math", "floor", "hours", "days", "getNotificationIcon", "faThermometerHalf", "faBatteryEmpty", "faDroplet", "faDoorOpen", "faWifi", "getSeverityBadge", "getFilterCount", "filterType", "length", "_Fragment", "faCog", "LoadingSpinner", "size", "variant", "centered", "ErrorMessage", "dismissible", "on<PERSON><PERSON><PERSON>", "markSelectedAsRead", "map", "includes", "_objectSpread", "faCheck", "deleteSelected", "faTrash", "notification", "checked", "onChange", "toggleNotificationSelection", "prev", "nId", "faMapMarkerAlt", "faCalendarAlt", "faEye", "mark<PERSON><PERSON><PERSON>", "markAsResolved", "deleteNotification", "style", "backgroundColor", "defaultChecked", "htmlFor"], "sourceRoot": ""}