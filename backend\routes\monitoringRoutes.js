import express from 'express';
import mysql from 'mysql2/promise';
import authMiddleware from '../middleware/auth.js';

const router = express.Router();

// Database connection pool
const pool = mysql.createPool({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'mgz24db',
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
});

// Genel sistem durumu
router.get('/system-overview', authMiddleware, async (req, res) => {
    try {
        const musteriID = req.user.musteri_ID || req.user.id;
        
        // Sistem genel durumu
        const [systemStats] = await pool.execute(`
            SELECT 
                COUNT(DISTINCT c.cihaz_kodu) as total_devices,
                COUNT(DISTINCT CASE WHEN c.sevkiyat_id IS NOT NULL THEN c.cihaz_kodu END) as active_devices,
                COUNT(DISTINCT CASE WHEN c.sevkiyat_id IS NULL THEN c.cihaz_kodu END) as idle_devices,
                COUNT(DISTINCT CASE WHEN c.pil_seviyesi < 20 THEN c.cihaz_kodu END) as low_battery_devices,
                COUNT(DISTINCT CASE WHEN TIMESTAMPDIFF(MINUTE, c.son_kontrol, NOW()) > 60 THEN c.cihaz_kodu END) as offline_devices,
                COUNT(DISTINCT s.id) as active_shipments,
                COUNT(DISTINCT CASE WHEN s.durum = 'tamamlandı' THEN s.id END) as completed_shipments,
                AVG(c.pil_seviyesi) as avg_battery_level,
                MAX(c.son_kontrol) as last_data_received
            FROM cihazBilgi c
            LEFT JOIN sevkiyatlar s ON c.sevkiyat_id = s.id
            LEFT JOIN cihazID ci ON c.cihaz_kodu = ci.CihazID
            WHERE ci.MusteriID = ? OR ? = 'admin'
        `, [musteriID, req.user.role]);

        // Son 24 saat veri akışı
        const [dataFlowStats] = await pool.execute(`
            SELECT 
                DATE_FORMAT(zaman, '%H:00') as hour,
                COUNT(*) as data_count,
                COUNT(DISTINCT cihaz_kodu) as device_count
            FROM cihazBilgi c
            LEFT JOIN cihazID ci ON c.cihaz_kodu = ci.CihazID
            WHERE 
                c.zaman >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                AND (ci.MusteriID = ? OR ? = 'admin')
            GROUP BY DATE_FORMAT(zaman, '%H:00')
            ORDER BY hour
        `, [musteriID, req.user.role]);

        // Alarm istatistikleri
        const [alarmStats] = await pool.execute(`
            SELECT 
                'temperature' as alarm_type,
                COUNT(*) as count
            FROM cihazBilgi c
            LEFT JOIN sevkiyatlar s ON c.sevkiyat_id = s.id
            LEFT JOIN cihazID ci ON c.cihaz_kodu = ci.CihazID
            WHERE 
                c.sicaklik < 0 OR c.sicaklik > 10
                AND (ci.MusteriID = ? OR ? = 'admin')
                AND c.zaman >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            UNION ALL
            SELECT 
                'battery' as alarm_type,
                COUNT(*) as count
            FROM cihazBilgi c
            LEFT JOIN cihazID ci ON c.cihaz_kodu = ci.CihazID
            WHERE 
                c.pil_seviyesi < 20
                AND (ci.MusteriID = ? OR ? = 'admin')
            UNION ALL
            SELECT 
                'offline' as alarm_type,
                COUNT(*) as count
            FROM cihazBilgi c
            LEFT JOIN cihazID ci ON c.cihaz_kodu = ci.CihazID
            WHERE 
                TIMESTAMPDIFF(MINUTE, c.son_kontrol, NOW()) > 60
                AND (ci.MusteriID = ? OR ? = 'admin')
        `, [musteriID, req.user.role, musteriID, req.user.role, musteriID, req.user.role]);

        res.json({
            success: true,
            data: {
                systemStats: systemStats[0],
                dataFlowStats: dataFlowStats,
                alarmStats: alarmStats.reduce((acc, alarm) => {
                    acc[alarm.alarm_type] = alarm.count;
                    return acc;
                }, {})
            }
        });

    } catch (error) {
        console.error('Error fetching system overview:', error);
        res.status(500).json({
            success: false,
            message: 'Sistem durumu alınırken hata oluştu: ' + error.message
        });
    }
});

// Cihaz durumu listesi
router.get('/device-status', authMiddleware, async (req, res) => {
    try {
        const musteriID = req.user.musteri_ID || req.user.id;
        const { status, sortBy = 'son_kontrol', sortOrder = 'DESC', limit = 50 } = req.query;
        
        let whereClause = 'WHERE (ci.MusteriID = ? OR ? = "admin")';
        let params = [musteriID, req.user.role];

        // Durum filtresi
        if (status) {
            switch (status) {
                case 'online':
                    whereClause += ' AND TIMESTAMPDIFF(MINUTE, c.son_kontrol, NOW()) <= 10';
                    break;
                case 'offline':
                    whereClause += ' AND TIMESTAMPDIFF(MINUTE, c.son_kontrol, NOW()) > 60';
                    break;
                case 'low_battery':
                    whereClause += ' AND c.pil_seviyesi < 20';
                    break;
                case 'assigned':
                    whereClause += ' AND c.sevkiyat_id IS NOT NULL';
                    break;
                case 'available':
                    whereClause += ' AND c.sevkiyat_id IS NULL';
                    break;
            }
        }

        const [devices] = await pool.execute(`
            SELECT 
                c.cihaz_kodu,
                c.sicaklik,
                c.nem,
                c.pil_seviyesi,
                c.enlem,
                c.boylam,
                c.son_kontrol,
                c.sevkiyat_id,
                c.durum,
                s.sevkiyat_adi,
                s.plaka_no,
                ci.GoldCihaz,
                ci.KalanSure,
                ci.MusteriID,
                TIMESTAMPDIFF(MINUTE, c.son_kontrol, NOW()) as minutes_since_update,
                CASE 
                    WHEN TIMESTAMPDIFF(MINUTE, c.son_kontrol, NOW()) <= 10 THEN 'online'
                    WHEN TIMESTAMPDIFF(MINUTE, c.son_kontrol, NOW()) <= 60 THEN 'warning'
                    ELSE 'offline'
                END as connection_status,
                CASE 
                    WHEN c.pil_seviyesi > 60 THEN 'good'
                    WHEN c.pil_seviyesi > 30 THEN 'medium'
                    ELSE 'low'
                END as battery_status
            FROM cihazBilgi c
            LEFT JOIN sevkiyatlar s ON c.sevkiyat_id = s.id
            LEFT JOIN cihazID ci ON c.cihaz_kodu = ci.CihazID
            ${whereClause}
            ORDER BY ${sortBy} ${sortOrder}
            LIMIT ?
        `, [...params, parseInt(limit)]);

        res.json({
            success: true,
            data: devices
        });

    } catch (error) {
        console.error('Error fetching device status:', error);
        res.status(500).json({
            success: false,
            message: 'Cihaz durumu alınırken hata oluştu: ' + error.message
        });
    }
});

// Aktif alarmlar
router.get('/active-alerts', authMiddleware, async (req, res) => {
    try {
        const musteriID = req.user.musteri_ID || req.user.id;
        const { severity, limit = 100 } = req.query;
        
        let alerts = [];

        // Sıcaklık alarmları
        const [tempAlerts] = await pool.execute(`
            SELECT 
                c.cihaz_kodu,
                c.sicaklik,
                c.son_kontrol,
                s.sevkiyat_adi,
                s.sicaklik_araligi,
                'temperature' as alert_type,
                CASE 
                    WHEN c.sicaklik < -10 OR c.sicaklik > 15 THEN 'critical'
                    WHEN c.sicaklik < 0 OR c.sicaklik > 10 THEN 'high'
                    ELSE 'medium'
                END as severity,
                CONCAT('Sıcaklık aralık dışında: ', c.sicaklik, '°C') as message
            FROM cihazBilgi c
            LEFT JOIN sevkiyatlar s ON c.sevkiyat_id = s.id
            LEFT JOIN cihazID ci ON c.cihaz_kodu = ci.CihazID
            WHERE 
                (ci.MusteriID = ? OR ? = 'admin')
                AND c.sicaklik IS NOT NULL
                AND (c.sicaklik < 0 OR c.sicaklik > 10)
                AND c.son_kontrol >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
            ORDER BY c.son_kontrol DESC
        `, [musteriID, req.user.role]);

        // Batarya alarmları
        const [batteryAlerts] = await pool.execute(`
            SELECT 
                c.cihaz_kodu,
                c.pil_seviyesi,
                c.son_kontrol,
                s.sevkiyat_adi,
                NULL as sicaklik_araligi,
                'battery' as alert_type,
                CASE 
                    WHEN c.pil_seviyesi < 5 THEN 'critical'
                    WHEN c.pil_seviyesi < 15 THEN 'high'
                    ELSE 'medium'
                END as severity,
                CONCAT('Düşük batarya: ', c.pil_seviyesi, '%') as message
            FROM cihazBilgi c
            LEFT JOIN sevkiyatlar s ON c.sevkiyat_id = s.id
            LEFT JOIN cihazID ci ON c.cihaz_kodu = ci.CihazID
            WHERE 
                (ci.MusteriID = ? OR ? = 'admin')
                AND c.pil_seviyesi < 20
            ORDER BY c.pil_seviyesi ASC
        `, [musteriID, req.user.role]);

        // Bağlantı alarmları
        const [connectionAlerts] = await pool.execute(`
            SELECT 
                c.cihaz_kodu,
                c.pil_seviyesi,
                c.son_kontrol,
                s.sevkiyat_adi,
                NULL as sicaklik_araligi,
                'connection' as alert_type,
                CASE 
                    WHEN TIMESTAMPDIFF(HOUR, c.son_kontrol, NOW()) > 6 THEN 'critical'
                    WHEN TIMESTAMPDIFF(HOUR, c.son_kontrol, NOW()) > 2 THEN 'high'
                    ELSE 'medium'
                END as severity,
                CONCAT('Bağlantı kesildi: ', TIMESTAMPDIFF(MINUTE, c.son_kontrol, NOW()), ' dakika') as message
            FROM cihazBilgi c
            LEFT JOIN sevkiyatlar s ON c.sevkiyat_id = s.id
            LEFT JOIN cihazID ci ON c.cihaz_kodu = ci.CihazID
            WHERE 
                (ci.MusteriID = ? OR ? = 'admin')
                AND TIMESTAMPDIFF(MINUTE, c.son_kontrol, NOW()) > 60
            ORDER BY c.son_kontrol ASC
        `, [musteriID, req.user.role]);

        // Tüm alarmları birleştir
        alerts = [...tempAlerts, ...batteryAlerts, ...connectionAlerts];

        // Severity filtresi
        if (severity) {
            alerts = alerts.filter(alert => alert.severity === severity);
        }

        // Limit uygula
        alerts = alerts.slice(0, parseInt(limit));

        res.json({
            success: true,
            data: {
                alerts: alerts,
                summary: {
                    total: alerts.length,
                    critical: alerts.filter(a => a.severity === 'critical').length,
                    high: alerts.filter(a => a.severity === 'high').length,
                    medium: alerts.filter(a => a.severity === 'medium').length,
                    types: {
                        temperature: alerts.filter(a => a.alert_type === 'temperature').length,
                        battery: alerts.filter(a => a.alert_type === 'battery').length,
                        connection: alerts.filter(a => a.alert_type === 'connection').length
                    }
                }
            }
        });

    } catch (error) {
        console.error('Error fetching active alerts:', error);
        res.status(500).json({
            success: false,
            message: 'Aktif alarmlar alınırken hata oluştu: ' + error.message
        });
    }
});

// Performans metrikleri
router.get('/performance-metrics', authMiddleware, async (req, res) => {
    try {
        const musteriID = req.user.musteri_ID || req.user.id;
        const { period = '24h' } = req.query;
        
        let timeInterval;
        switch (period) {
            case '1h':
                timeInterval = 'INTERVAL 1 HOUR';
                break;
            case '24h':
                timeInterval = 'INTERVAL 24 HOUR';
                break;
            case '7d':
                timeInterval = 'INTERVAL 7 DAY';
                break;
            case '30d':
                timeInterval = 'INTERVAL 30 DAY';
                break;
            default:
                timeInterval = 'INTERVAL 24 HOUR';
        }

        // Veri akış metrikleri
        const [dataFlowMetrics] = await pool.execute(`
            SELECT 
                COUNT(*) as total_data_points,
                COUNT(DISTINCT cihaz_kodu) as active_devices,
                AVG(pil_seviyesi) as avg_battery_level,
                MIN(pil_seviyesi) as min_battery_level,
                MAX(pil_seviyesi) as max_battery_level,
                AVG(sicaklik) as avg_temperature,
                MIN(sicaklik) as min_temperature,
                MAX(sicaklik) as max_temperature,
                COUNT(DISTINCT DATE_FORMAT(zaman, '%Y-%m-%d %H')) as active_hours
            FROM cihazBilgi c
            LEFT JOIN cihazID ci ON c.cihaz_kodu = ci.CihazID
            WHERE 
                c.zaman >= DATE_SUB(NOW(), ${timeInterval})
                AND (ci.MusteriID = ? OR ? = 'admin')
        `, [musteriID, req.user.role]);

        // Cihaz performansı (en aktif cihazlar)
        const [devicePerformance] = await pool.execute(`
            SELECT 
                c.cihaz_kodu,
                COUNT(*) as data_points,
                AVG(c.pil_seviyesi) as avg_battery,
                MIN(c.son_kontrol) as first_update,
                MAX(c.son_kontrol) as last_update,
                s.sevkiyat_adi
            FROM cihazBilgi c
            LEFT JOIN sevkiyatlar s ON c.sevkiyat_id = s.id
            LEFT JOIN cihazID ci ON c.cihaz_kodu = ci.CihazID
            WHERE 
                c.zaman >= DATE_SUB(NOW(), ${timeInterval})
                AND (ci.MusteriID = ? OR ? = 'admin')
            GROUP BY c.cihaz_kodu
            ORDER BY data_points DESC
            LIMIT 10
        `, [musteriID, req.user.role]);

        // Sistem sağlığı metrikleri
        const [systemHealth] = await pool.execute(`
            SELECT 
                COUNT(DISTINCT CASE WHEN TIMESTAMPDIFF(MINUTE, c.son_kontrol, NOW()) <= 10 THEN c.cihaz_kodu END) as healthy_devices,
                COUNT(DISTINCT CASE WHEN TIMESTAMPDIFF(MINUTE, c.son_kontrol, NOW()) > 60 THEN c.cihaz_kodu END) as unhealthy_devices,
                COUNT(DISTINCT CASE WHEN c.pil_seviyesi < 20 THEN c.cihaz_kodu END) as low_battery_devices,
                COUNT(DISTINCT CASE WHEN c.error_count > 5 THEN c.cihaz_kodu END) as error_prone_devices,
                AVG(TIMESTAMPDIFF(MINUTE, c.son_kontrol, NOW())) as avg_last_update_minutes
            FROM cihazBilgi c
            LEFT JOIN cihazID ci ON c.cihaz_kodu = ci.CihazID
            WHERE (ci.MusteriID = ? OR ? = 'admin')
        `, [musteriID, req.user.role]);

        res.json({
            success: true,
            data: {
                period: period,
                dataFlowMetrics: dataFlowMetrics[0],
                devicePerformance: devicePerformance,
                systemHealth: systemHealth[0],
                timestamp: new Date()
            }
        });

    } catch (error) {
        console.error('Error fetching performance metrics:', error);
        res.status(500).json({
            success: false,
            message: 'Performans metrikleri alınırken hata oluştu: ' + error.message
        });
    }
});

// Cihaz detaylı analiz
router.get('/device-analysis/:deviceId', authMiddleware, async (req, res) => {
    try {
        const { deviceId } = req.params;
        const musteriID = req.user.musteri_ID || req.user.id;
        const { period = '24h' } = req.query;
        
        let timeInterval;
        switch (period) {
            case '1h':
                timeInterval = 'INTERVAL 1 HOUR';
                break;
            case '24h':
                timeInterval = 'INTERVAL 24 HOUR';
                break;
            case '7d':
                timeInterval = 'INTERVAL 7 DAY';
                break;
            default:
                timeInterval = 'INTERVAL 24 HOUR';
        }

        // Yetki kontrolü
        const [authCheck] = await pool.execute(`
            SELECT ci.MusteriID 
            FROM cihazID ci 
            WHERE ci.CihazID = ?
        `, [deviceId]);

        if (authCheck.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Cihaz bulunamadı'
            });
        }

        if (authCheck[0].MusteriID !== musteriID && req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Bu cihaza erişim yetkiniz yok'
            });
        }

        // Cihaz temel bilgileri
        const [deviceInfo] = await pool.execute(`
            SELECT 
                c.cihaz_kodu,
                c.sicaklik,
                c.nem,
                c.pil_seviyesi,
                c.enlem,
                c.boylam,
                c.son_kontrol,
                c.sevkiyat_id,
                c.durum,
                s.sevkiyat_adi,
                s.plaka_no,
                ci.GoldCihaz,
                ci.KalanSure,
                TIMESTAMPDIFF(MINUTE, c.son_kontrol, NOW()) as minutes_since_update
            FROM cihazBilgi c
            LEFT JOIN sevkiyatlar s ON c.sevkiyat_id = s.id
            LEFT JOIN cihazID ci ON c.cihaz_kodu = ci.CihazID
            WHERE c.cihaz_kodu = ?
            ORDER BY c.son_kontrol DESC
            LIMIT 1
        `, [deviceId]);

        // Zaman serisi verileri
        const [timeSeriesData] = await pool.execute(`
            SELECT 
                c.sicaklik,
                c.nem,
                c.pil_seviyesi,
                c.enlem,
                c.boylam,
                c.zaman,
                DATE_FORMAT(c.zaman, '%Y-%m-%d %H:%i:%s') as formatted_time
            FROM cihazBilgi c
            WHERE 
                c.cihaz_kodu = ?
                AND c.zaman >= DATE_SUB(NOW(), ${timeInterval})
            ORDER BY c.zaman ASC
        `, [deviceId]);

        // İstatistikler
        const [statistics] = await pool.execute(`
            SELECT 
                COUNT(*) as total_readings,
                AVG(sicaklik) as avg_temperature,
                MIN(sicaklik) as min_temperature,
                MAX(sicaklik) as max_temperature,
                AVG(nem) as avg_humidity,
                MIN(nem) as min_humidity,
                MAX(nem) as max_humidity,
                AVG(pil_seviyesi) as avg_battery,
                MIN(pil_seviyesi) as min_battery,
                MAX(pil_seviyesi) as max_battery,
                MIN(zaman) as first_reading,
                MAX(zaman) as last_reading
            FROM cihazBilgi c
            WHERE 
                c.cihaz_kodu = ?
                AND c.zaman >= DATE_SUB(NOW(), ${timeInterval})
        `, [deviceId]);

        res.json({
            success: true,
            data: {
                deviceInfo: deviceInfo[0] || null,
                timeSeriesData: timeSeriesData,
                statistics: statistics[0] || null,
                period: period,
                dataPointCount: timeSeriesData.length
            }
        });

    } catch (error) {
        console.error(`Error fetching device analysis for ${req.params.deviceId}:`, error);
        res.status(500).json({
            success: false,
            message: 'Cihaz analizi alınırken hata oluştu: ' + error.message
        });
    }
});

export default router;