// MySQL2 uyarılarını sustur
process.env.NODE_NO_WARNINGS = '1';

import express from 'express';
import cors from 'cors';
import mysql from 'mysql2/promise';
import dotenv from 'dotenv';
import bodyParser from 'body-parser';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import axios from 'axios';
import PDFDocument from 'pdfkit';
import ExcelJS from 'exceljs';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { body, validationResult } from 'express-validator';
import crypto from 'crypto';

// .env dosyasından ortam değişkenlerini yükle
dotenv.config();

// ES6 modules için __dirname tanımla
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// External API Services
import ExternalApiService from './services/externalApiService.js';
import DeviceAssignmentService from './services/deviceAssignmentService.js';
import RealTimePollingService from './services/realTimePollingService.js';

// Routes
import externalApiRoutes from './routes/externalApiRoutes.js';
import monitoringRoutes from './routes/monitoringRoutes.js';

const app = express();
const port = process.env.PORT || 3000;

// Middleware
app.use(cors({
  origin: ['https://mgz24.com', 'https://www.mgz24.com', 'http://localhost:3000'], // Sadece güvenilir domainlere izin ver
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}));
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Veritabanı bağlantı ayarları
const dbConfig = {
  host: process.env.DB_HOST || '************',
  user: process.env.DB_USER || 'mehmet',
  password: process.env.DB_PASSWORD || 'Inka.mS_335265933s.tech',
  database: process.env.DB_NAME || 'mgz24db',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  connectTimeout: 10000 // 10 saniye
  // MySQL2'de desteklenmeyen seçenekler kaldırıldı:
  // acquireTimeout, timeout, enableKeepAlive, keepAliveInitialDelay, idleTimeout, socketTimeout
};

// Veritabanı bağlantı havuzu oluştur
const pool = mysql.createPool(dbConfig);

// Bağlantı hataları için olay dinleyicisi
pool.on('error', (err) => {
  console.error('Veritabanı havuzu hatası:', err);
  if (err.code === 'PROTOCOL_CONNECTION_LOST' || err.code === 'ECONNRESET') {
    console.log('Bağlantı yeniden kurulmaya çalışılıyor...');
  }
});

// Service instance'ları oluştur
const externalApiService = new ExternalApiService();
const deviceAssignmentService = new DeviceAssignmentService();
const pollingService = new RealTimePollingService();

// Uygulama başlatılırken servisleri başlat
let servicesInitialized = false;

const initializeServices = async () => {
  if (servicesInitialized) return;

  try {
    console.log('Initializing external API services...');

    // Polling servisini başlat (geçici olarak devre dışı)
    // await pollingService.startPolling();

    // Polling servisinin olaylarını dinle
    pollingService.on('criticalAlerts', (data) => {
      console.log(`Critical alerts for device ${data.device}:`, data.alerts);
      // Buraya alert notification logic eklenebilir
    });

    pollingService.on('deviceUpdated', (data) => {
      console.log(`Device ${data.device} updated at ${data.timestamp}`);
    });

    pollingService.on('pollingError', (error) => {
      console.error('Polling service error:', error);
    });

    servicesInitialized = true;
    console.log('External API services initialized successfully');

  } catch (error) {
    console.error('Error initializing external API services:', error);
  }
};

// Uygulama kapatılırken servisleri temizle
const cleanupServices = async () => {
  try {
    console.log('Cleaning up external API services...');

    await pollingService.stopPolling();
    await externalApiService.closeConnection();
    await deviceAssignmentService.closeConnection();
    await pollingService.closeConnection();

    console.log('External API services cleaned up successfully');

  } catch (error) {
    console.error('Error cleaning up external API services:', error);
  }
};

// Process termination handler'ları
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, gracefully shutting down...');
  await cleanupServices();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('SIGINT received, gracefully shutting down...');
  await cleanupServices();
  process.exit(0);
});

// Uncaught exception handler
process.on('uncaughtException', (error) => {
  console.error('Uncaught exception:', error);
  cleanupServices().then(() => {
    process.exit(1);
  });
});

// JWT Authentication Middleware (Zorunlu)
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Access token gerekli'
    });
  }

  const JWT_SECRET = process.env.JWT_SECRET || 'mgz24_production_secret_key_2024_very_secure_random_string_3d8f9a2b1c4e5f6g7h8i9j0k';
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({
        success: false,
        message: 'Geçersiz veya süresi dolmuş token'
      });
    }
    req.user = user;
    next();
  });
};

// JWT Authentication Middleware (Opsiyonel) - Token varsa kullan, yoksa devam et
const optionalAuthentication = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (token) {
    const JWT_SECRET = process.env.JWT_SECRET || 'mgz24_production_secret_key_2024_very_secure_random_string_3d8f9a2b1c4e5f6g7h8i9j0k';
    jwt.verify(token, JWT_SECRET, (err, user) => {
      if (!err) {
        req.user = user;
      }
      // Hata olsa bile devam et
      next();
    });
  } else {
    // Token yok, devam et
    next();
  }
};

// Role-based Authorization Middleware
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Oturum açmanız gerekli'
      });
    }

    const userRole = req.user.role;
    if (!roles.includes(userRole)) {
      return res.status(403).json({
        success: false,
        message: 'Bu işlem için yetkiniz bulunmamaktadır'
      });
    }

    next();
  };
};

// Admin check middleware
const requireAdmin = requireRole(['admin']);

// Check if user is admin
const isAdmin = (req) => {
  return req.user && req.user.role === 'admin';
};

// JWT Token Oluşturma Yardımcı Fonksiyonu
const generateToken = (user) => {
  const JWT_SECRET = process.env.JWT_SECRET || 'mgz24_production_secret_key_2024_very_secure_random_string_3d8f9a2b1c4e5f6g7h8i9j0k';
  return jwt.sign(
    {
      userId: user.musteri_ID,
      email: user.email,
      role: user.gorev
    },
    JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
  );
};

// Validation Middleware - Validation hatalarını kontrol et
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    console.error('Validation hatası detayları:', {
      requestBody: req.body,
      validationErrors: errors.array()
    });

    // Türkçe hata mesajları ile yanıt ver
    const detailedErrors = errors.array().map(error => ({
      field: error.path,
      value: error.value,
      message: error.msg
    }));

    return res.status(400).json({
      success: false,
      message: 'Validation hatası',
      errors: detailedErrors,
      details: 'Zorunlu alanlar: MGZ24 ID, Sevkiyat adı, Plaka no, Çıkış yeri, Varış yeri ve Müşteri ID (sayı formatında) doldurulmalıdır.'
    });
  }
  next();
};

// Yardımcı fonksiyon: Sorgu çalıştırma (yeniden deneme mantığı ile)
const executeQuery = async (query, params = [], maxRetries = 3) => {
  let attempts = 0;

  while (attempts < maxRetries) {
    try {
      const connection = await pool.getConnection();
      try {
        const result = await connection.query(query, params);
        return result;
      } finally {
        connection.release();
      }
    } catch (error) {
      attempts++;
      console.error(`Sorgu hatası (Deneme ${attempts}/${maxRetries}):`, error);

      if (error.code === 'ECONNRESET' || error.code === 'PROTOCOL_CONNECTION_LOST') {
        // Bağlantı hatası durumunda kısa bir bekleme
        await new Promise(resolve => setTimeout(resolve, 1000));
      } else if (attempts >= maxRetries) {
        throw error; // Maksimum deneme sayısına ulaşıldı, hatayı ilet
      } else {
        throw error; // Diğer hataları hemen ilet
      }
    }
  }
};

// Sevkiyatlar tablosu şemasını kontrol et
app.get('/api/debug/sevkiyatlar-schema', async (req, res) => {
  try {
    const [columns] = await executeQuery('DESCRIBE sevkiyatlar');
    res.json({
      success: true,
      columns: columns,
      message: 'Sevkiyatlar tablosu sütun bilgileri'
    });
  } catch (error) {
    console.error('Tablo şeması alınırken hata:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Eksik sütunları eklemek için SQL çalıştır
app.post('/api/debug/add-missing-columns', async (req, res) => {
  try {
    // Önce mevcut sütunları kontrol et
    const [existingColumns] = await executeQuery('DESCRIBE sevkiyatlar');
    const columnNames = existingColumns.map(col => col.Field);

    const sqlCommands = [];

    // Eksik sütunları kontrol et ve ekle
    if (!columnNames.includes('cikis_lokasyon')) {
      sqlCommands.push('ALTER TABLE sevkiyatlar ADD COLUMN cikis_lokasyon VARCHAR(100) NULL');
    }

    if (!columnNames.includes('varis_lokasyon')) {
      sqlCommands.push('ALTER TABLE sevkiyatlar ADD COLUMN varis_lokasyon VARCHAR(100) NULL');
    }

    if (!columnNames.includes('urun_bilgisi')) {
      sqlCommands.push('ALTER TABLE sevkiyatlar ADD COLUMN urun_bilgisi VARCHAR(100) NULL');
    }

    if (!columnNames.includes('nakliyeci')) {
      sqlCommands.push('ALTER TABLE sevkiyatlar ADD COLUMN nakliyeci VARCHAR(100) NULL');
    }

    // SQL komutlarını çalıştır
    for (const sql of sqlCommands) {
      console.log('Çalıştırılıyor:', sql);
      await executeQuery(sql);
    }

    // Güncel şemayı kontrol et
    const [newColumns] = await executeQuery('DESCRIBE sevkiyatlar');

    res.json({
      success: true,
      message: `${sqlCommands.length} sütun eklendi`,
      addedColumns: sqlCommands,
      currentSchema: newColumns
    });
  } catch (error) {
    console.error('Sütun ekleme hatası:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Bağlantıyı test et
app.get('/api/test-connection', async (req, res) => {
  try {
    // Basit bir sorgu ile bağlantıyı test et
    const [result] = await executeQuery('SELECT 1 AS test, NOW() AS server_time, VERSION() AS mysql_version');

    res.json({
      success: true,
      message: 'Veritabanı bağlantısı başarılı',
      server_time: new Date().toISOString(),
      database_time: result[0].server_time,
      mysql_version: result[0].mysql_version,
      test_query: result[0].test
    });
  } catch (error) {
    console.error('Veritabanı bağlantı hatası:', error);
    res.status(500).json({
      success: false,
      message: 'Veritabanı bağlantı hatası',
      error: error.message,
      code: error.code,
      errno: error.errno,
      sqlState: error.sqlState,
      sqlMessage: error.sqlMessage,
      server_time: new Date().toISOString()
    });
  }
});

// ====== API Rotaları ======

// Şifreleri hash'le (migration için)
app.post('/api/admin/hash-passwords', async (req, res) => {
  try {
    // Tüm kullanıcıları al
    const [users] = await executeQuery('SELECT musteri_ID, email, password FROM kullanicilar');

    let updatedCount = 0;

    for (const user of users) {
      // Eğer şifre zaten hash'lenmiş ise (bcrypt hash'leri $ ile başlar) atla
      if (user.password.startsWith('$2')) {
        continue;
      }

      // Şifreyi hash'le
      const hashedPassword = await bcrypt.hash(user.password, 10);

      // Veritabanında güncelle
      await executeQuery(
        'UPDATE kullanicilar SET password = ? WHERE musteri_ID = ?',
        [hashedPassword, user.musteri_ID]
      );

      updatedCount++;
      console.log(`Kullanıcı ${user.email} şifresi hashlendi`);
    }

    res.json({
      success: true,
      message: `${updatedCount} kullanıcının şifresi başarıyla hashlendi`,
      totalUsers: users.length,
      updatedUsers: updatedCount
    });

  } catch (error) {
    console.error('Şifre hashleme hatası:', error);
    res.status(500).json({
      success: false,
      message: 'Şifre hashleme işleminde hata',
      error: error.message
    });
  }
});

// Database'yi başlat ve gerekli kullanıcıları ekle
app.get('/api/init-db', async (req, res) => {
  try {
    // <EMAIL> kullanıcısını kontrol et ve yoksa ekle
    const [existingUser] = await executeQuery(
      'SELECT * FROM kullanicilar WHERE email = ?',
      ['<EMAIL>']
    );

    if (existingUser.length === 0) {
      // Şifreyi hash'le
      const hashedPassword = await bcrypt.hash('mgz24321', 10);

      // Kullanıcıyı ekle
      await executeQuery(
        `INSERT INTO kullanicilar (
          kullanici, email, password, musteri_adi, tel, firma, adres, gorev
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          'admin24',
          '<EMAIL>',
          hashedPassword,
          'MGZ24 Admin',
          '5054340211',
          'MGZ24',
          '',
          'admin'
        ]
      );

      res.json({
        success: true,
        message: '<EMAIL> kullanıcısı başarıyla eklendi'
      });
    } else {
      res.json({
        success: true,
        message: '<EMAIL> kullanıcısı zaten mevcut'
      });
    }

  } catch (error) {
    console.error('Database init hatası:', error);
    res.status(500).json({
      success: false,
      message: 'Database init hatası',
      error: error.message
    });
  }
});

// Kimlik Doğrulama Rotaları
app.post('/api/auth/login', [
  body('email').isEmail().withMessage('Geçerli bir e-posta adresi girin'),
  body('password').isLength({ min: 4 }).withMessage('Şifre en az 4 karakter olmalıdır'),
  handleValidationErrors
], async (req, res) => {
  try {
    const { email, password } = req.body;

    // Kullanıcıyı veritabanından bul
    const [users] = await executeQuery(
      'SELECT * FROM kullanicilar WHERE email = ?',
      [email]
    );

    if (users.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'Kullanıcı bulunamadı'
      });
    }

    const user = users[0];

    // Şifre kontrolü - bcrypt ile hash karşılaştırması
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: 'Geçersiz şifre'
      });
    }

    // Başarılı giriş - JWT token oluştur
    const userData = {
      musteri_ID: user.musteri_ID,
      kullanici: user.kullanici,
      email: user.email,
      musteri_adi: user.musteri_adi,
      tel: user.tel,
      firma: user.firma,
      adres: user.adres,
      gorev: user.gorev
    };

    const token = generateToken(user);

    res.json({
      success: true,
      message: 'Giriş başarılı',
      token: token,
      user: userData
    });

  } catch (error) {
    console.error('Login hatası:', error);
    res.status(500).json({
      success: false,
      message: 'Sunucu hatası',
      error: error.message
    });
  }
});

// Sevkiyat Rotaları (JWT korumalı)
app.get('/api/sevkiyatlar', authenticateToken, async (req, res) => {
  try {
    const { musteri_ID } = req.query;
    let query = `
            SELECT s.*
            FROM sevkiyatlar s`;
    const params = [];

    // WHERE koşulları
    const whereConditions = [];

    // Sadece tamamlanmamış sevkiyatları getir (aktif sevkiyatlar)
    whereConditions.push('(s.tamamlandi_mi = 0 OR s.tamamlandi_mi IS NULL)');

    // Role-based filtering
    if (!isAdmin(req)) {
      // Non-admin users can only see their own shipments
      whereConditions.push('s.musteri_ID = ?');
      params.push(req.user.userId);
    } else if (musteri_ID) {
      // Admin users can filter by specific customer if provided
      whereConditions.push('s.musteri_ID = ?');
      params.push(musteri_ID);
    }

    // WHERE koşullarını ekle
    if (whereConditions.length > 0) {
      query += ' WHERE ' + whereConditions.join(' AND ');
    }

    // Sonuçları oluşturma zamanına göre azalan sırayla getir (en yeniden en eskiye)
    query += ' ORDER BY s.olusturma_zamani DESC';

    const [rows] = await executeQuery(query, params);

    // Sonuçları doğrudan döndür
    res.json(rows);
  } catch (error) {
    console.error('Sevkiyatlar alınırken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

// Geçmiş (tamamlanmış) sevkiyatları getir
app.get('/api/sevkiyatlar/gecmis', authenticateToken, async (req, res) => {
  try {
    const { musteri_ID } = req.query;

    let query = `
            SELECT s.*
            FROM sevkiyatlar s`;
    const params = [];

    // WHERE koşulları
    const whereConditions = [];

    // Sadece tamamlanmış sevkiyatları getir
    whereConditions.push('s.tamamlandi_mi = 1');

    // Role-based filtering
    if (!isAdmin(req)) {
      // Non-admin users can only see their own shipments
      whereConditions.push('s.musteri_ID = ?');
      params.push(req.user.userId);
    } else if (musteri_ID && musteri_ID !== 'undefined' && musteri_ID !== 'null') {
      // Admin users can filter by specific customer if provided
      whereConditions.push('s.musteri_ID = ?');
      params.push(musteri_ID);
    }

    // WHERE koşullarını ekle
    if (whereConditions.length > 0) {
      query += ' WHERE ' + whereConditions.join(' AND ');
    }

    // Sonuçları tamamlanma zamanına göre azalan sırayla getir (en yeniden en eskiye)
    query += ' ORDER BY s.tamamlanma_zamani DESC, s.olusturma_zamani DESC';

    const [rows] = await executeQuery(query, params);

    // Sonuçları doğrudan döndür
    res.json(rows);
  } catch (error) {
    console.error('Geçmiş sevkiyatlar alınırken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/sevkiyatlar/:id', authenticateToken, async (req, res) => {
  try {
    const sevkiyatId = req.params.id;

    // Önce aktif sevkiyatları kontrol et
    const [activeRows] = await executeQuery(`
            SELECT s.*
            FROM sevkiyatlar s
            WHERE s.id = ?`, [sevkiyatId]);

    if (activeRows.length > 0) {
      return res.json(activeRows[0]);
    }

    // Aktif sevkiyat bulunamadı, geçmiş sevkiyatlara bak
    const [historyRows] = await executeQuery(`
            SELECT 
              sg.id,
              COALESCE(s.sevkiyat_ID, CONCAT('GS-', LPAD(sg.id, 6, '0'))) as sevkiyat_ID,
              sg.mgz24_kodu,
              sg.sevkiyat_adi,
              sg.plaka_no,
              sg.cikis_lokasyon,
              sg.varis_lokasyon,
              sg.nakliyeci as surucu_adi,
              sg.urun as urun_bilgisi,
              sg.palet_sayisi,
              sg.net_agirlik,
              sg.brut_agirlik,
              sg.sicaklik_araligi,
              sg.baslangic_zamani as olusturma_zamani,
              sg.bitis_zamani as tamamlanma_zamani,
              1 as tamamlandi_mi,
              sg.durum,
              sg.musteri_ID,
              sg.olusturma_zamani as kayit_olusturma_zamani,
              sg.guncelleme_zamani,
              sg.gonderen_firma,
              sg.alici_firma
            FROM sevkiyatGecmis sg
            LEFT JOIN sevkiyatlar s ON sg.sevkiyat_ID = s.id
            WHERE sg.id = ?`, [sevkiyatId]);

    if (historyRows.length === 0) {
      return res.status(404).json({ message: 'Sevkiyat bulunamadı' });
    }

    res.json(historyRows[0]);
  } catch (error) {
    console.error('Sevkiyat alınırken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/sevkiyatlar', [
  optionalAuthentication,
  body('mgz24_kodu').optional().isString().withMessage('MGZ24 kodu metin olmalıdır'),
  body('sevkiyat_adi').notEmpty().withMessage('Sevkiyat adı zorunludur'),
  body('plaka_no').notEmpty().withMessage('Plaka numarası zorunludur'),
  body('cikis_lokasyon').notEmpty().withMessage('Çıkış lokasyonu zorunludur'),
  body('varis_lokasyon').notEmpty().withMessage('Varış lokasyonu zorunludur'),
  body('surucu_telefon').optional().isString().withMessage('Şoför telefonu metin olmalıdır'),
  body('urun_bilgisi').optional().isString().withMessage('Ürün bilgisi metin olmalıdır'),
  handleValidationErrors
], async (req, res) => {
  try {
    const {
      mgz24_kodu, sevkiyat_adi, plaka_no, cikis_lokasyon, varis_lokasyon,
      surucu_adi, surucu_telefon, urun_bilgisi, palet_sayisi,
      net_agirlik, brut_agirlik, sicaklik_araligi, musteri_ID,
      gonderen_firma_id, alici_firma_id
    } = req.body;

    // Debug: Gelen veriyi logla
    console.log('Sevkiyat POST isteği - Gelen veri:', {
      mgz24_kodu, sevkiyat_adi, plaka_no, cikis_lokasyon, varis_lokasyon,
      urun_bilgisi, musteri_ID, surucu_telefon
    });

    // Validation express-validator tarafından yapılıyor

    // Sevkiyat ID'si oluştur (örnek: SV-202408001)
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');

    // Aynı ay içindeki sevkiyat sayısını kontrol et ve sıra numarası oluştur
    const [countResult] = await executeQuery(
      'SELECT COUNT(*) as count FROM sevkiyatlar WHERE YEAR(olusturma_zamani) = ? AND MONTH(olusturma_zamani) = ?',
      [year, date.getMonth() + 1]
    );
    const count = countResult[0].count + 1;
    const sequenceNumber = String(count).padStart(3, '0');

    const sevkiyatID = `SV-${year}${month}${sequenceNumber}`;

    // Gerçek database şemasına göre düzeltilmiş INSERT query
    const [result] = await executeQuery(
      `INSERT INTO sevkiyatlar (
                sevkiyat_ID, mgz24_kodu, sevkiyat_adi, plaka_no, 
                cikis_lokasyon, varis_lokasyon, musteri_ID, 
                gonderen_firma_id, alici_firma_id, surucu_adi, surucu_telefon, 
                urun_bilgisi, palet_sayisi, net_agirlik, brut_agirlik, 
                sicaklik_araligi, status, durum
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        sevkiyatID,
        mgz24_kodu || null,
        sevkiyat_adi,
        plaka_no,
        cikis_lokasyon,
        varis_lokasyon,
        musteri_ID || null,
        gonderen_firma_id || null,
        alici_firma_id || null,
        surucu_adi || null,
        surucu_telefon || null,
        urun_bilgisi || null,
        palet_sayisi || null,
        net_agirlik || null,
        brut_agirlik || null,
        sicaklik_araligi || null,
        'hazırlanıyor',  // status enum değeri
        'hazirlaniyor'   // durum enum değeri
      ]
    );

    console.log('Sevkiyat başarıyla eklendi, ID:', result.insertId);

    // Cihaz varsa cihazBilgi tablosunu güncelle: aktif=1 ve sevkiyat_ID ata
    if (mgz24_kodu) {
      try {
        console.log(`Cihaz ${mgz24_kodu} aktif hale getiriliyor ve sevkiyat ID'si atanıyor...`);

        const [updateResult] = await executeQuery(
          `UPDATE cihazBilgi 
           SET aktif = 1, 
               sevkiyat_ID = ?,
               guncelleme_zamani = NOW()
           WHERE mgz24_kodu = ?`,
          [result.insertId, mgz24_kodu]
        );

        if (updateResult.affectedRows > 0) {
          console.log(`Cihaz ${mgz24_kodu} başarıyla güncellendi: aktif=1, sevkiyat_ID=${result.insertId}`);
        } else {
          console.warn(`Cihaz ${mgz24_kodu} bulunamadı - güncelleme yapılamadı`);
        }
      } catch (updateError) {
        console.error(`Cihaz ${mgz24_kodu} güncellenirken hata:`, updateError);
        // Sevkiyat oluşturuldu ama cihaz güncellenemedi - bu critical değil
      }
    }

    res.status(201).json({
      success: true,
      id: result.insertId,
      sevkiyat_ID: sevkiyatID,
      message: 'Sevkiyat başarıyla oluşturuldu'
    });
  } catch (error) {
    console.error('Sevkiyat eklenirken hata:', error);

    // Daha detaylı hata bilgisi
    if (error.code === 'ER_DATA_TOO_LONG') {
      return res.status(400).json({
        success: false,
        message: 'Veri çok uzun - alan boyut limitlerini kontrol edin',
        error: error.message,
        sqlMessage: error.sqlMessage
      });
    }

    if (error.code === 'ER_BAD_NULL_ERROR') {
      return res.status(400).json({
        success: false,
        message: 'Zorunlu alan eksik',
        error: error.message,
        sqlMessage: error.sqlMessage
      });
    }

    res.status(500).json({
      success: false,
      message: 'Sevkiyat ekleme hatası',
      error: error.message,
      code: error.code
    });
  }
});

app.put('/api/sevkiyatlar/:id', async (req, res) => {
  try {
    const {
      mgz24_kodu, sevkiyat_adi, plaka_no, cikis_lokasyon, varis_lokasyon,
      surucu_adi, surucu_telefon, urun_bilgisi, palet_sayisi,
      net_agirlik, brut_agirlik, sicaklik_araligi, status, durum,
      gonderen_firma_id, alici_firma_id
    } = req.body;

    // Debug: Gelen güncelleme verisi
    console.log('Sevkiyat PUT isteği - Gelen veri:', {
      id: req.params.id,
      mgz24_kodu, sevkiyat_adi, plaka_no, cikis_lokasyon, varis_lokasyon,
      urun_bilgisi, status, durum
    });

    // Gerçek database şemasına göre düzeltilmiş UPDATE query
    const [result] = await executeQuery(
      `UPDATE sevkiyatlar SET 
              mgz24_kodu = ?, sevkiyat_adi = ?, plaka_no = ?, 
              cikis_lokasyon = ?, varis_lokasyon = ?, surucu_adi = ?,
              surucu_telefon = ?, urun_bilgisi = ?, palet_sayisi = ?, 
              net_agirlik = ?, brut_agirlik = ?, sicaklik_araligi = ?,
              status = ?, durum = ?, gonderen_firma_id = ?, alici_firma_id = ?,
              guncelleme_zamani = CURRENT_TIMESTAMP
          WHERE id = ?`,
      [
        mgz24_kodu, sevkiyat_adi, plaka_no,
        cikis_lokasyon, varis_lokasyon, surucu_adi,
        surucu_telefon, urun_bilgisi, palet_sayisi,
        net_agirlik, brut_agirlik, sicaklik_araligi,
        status || 'hazırlanıyor', durum || 'hazirlaniyor',
        gonderen_firma_id, alici_firma_id,
        req.params.id
      ]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'Sevkiyat bulunamadı'
      });
    }

    res.json({
      success: true,
      message: 'Sevkiyat başarıyla güncellendi'
    });
  } catch (error) {
    console.error('Sevkiyat güncellenirken hata:', error);

    // Daha detaylı hata bilgisi
    if (error.code === 'ER_DATA_TOO_LONG') {
      return res.status(400).json({
        success: false,
        message: 'Veri çok uzun - alan boyut limitlerini kontrol edin',
        error: error.message,
        sqlMessage: error.sqlMessage
      });
    }

    res.status(500).json({
      success: false,
      message: 'Sevkiyat güncelleme hatası',
      error: error.message,
      code: error.code
    });
  }
});

// Sensör Veri Rotaları - Artık uzak API kullanıyoruz, bu endpoint kaldırıldı
// Bu endpoint artık kullanılmıyor, sensör verileri uzak API'den alınıyor

// Sevkiyat geçmişi rotaları
app.get('/api/sevkiyat-gecmis', async (req, res) => {
  try {
    const { musteri_ID, mgz24_kodu } = req.query;
    let query = `
      SELECT sg.*, 
        s.sevkiyat_adi,
        k.musteri_adi
      FROM sevkiyatGecmis sg
      LEFT JOIN sevkiyatlar s ON sg.sevkiyat_ID = s.id
      LEFT JOIN kullanicilar k ON sg.musteri_ID = k.musteri_ID`;

    const params = [];
    const conditions = [];

    // Filtreleme koşulları
    if (musteri_ID) {
      conditions.push('sg.musteri_ID = ?');
      params.push(musteri_ID);
    }

    if (mgz24_kodu) {
      conditions.push('sg.mgz24_kodu = ?');
      params.push(mgz24_kodu);
    }

    // Koşulları sorguya ekle
    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    // Sonuçları başlangıç zamanına göre azalan sırayla getir (en yeniden en eskiye)
    query += ' ORDER BY sg.baslangic_zamani DESC';

    const [rows] = await executeQuery(query, params);
    res.json(rows);
  } catch (error) {
    console.error('Sevkiyat geçmişi alınırken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/sevkiyat-gecmis/:id', async (req, res) => {
  try {
    const [rows] = await executeQuery(`
      SELECT sg.*, 
        s.sevkiyat_adi,
        k.musteri_adi
      FROM sevkiyatGecmis sg
      LEFT JOIN sevkiyatlar s ON sg.sevkiyat_ID = s.id
      LEFT JOIN kullanicilar k ON sg.musteri_ID = k.musteri_ID
      WHERE sg.id = ?`, [req.params.id]);

    if (rows.length === 0) {
      return res.status(404).json({ message: 'Sevkiyat geçmişi bulunamadı' });
    }
    res.json(rows[0]);
  } catch (error) {
    console.error('Sevkiyat geçmişi alınırken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/sevkiyat-gecmis', async (req, res) => {
  try {
    const {
      cihaz_kodu, sevkiyat_ID, baslangic_zamani, bitis_zamani,
      gonderen_firma_id, alici_firma_id, sicaklik_min, sicaklik_max,
      nem_min, nem_max, durum, musteri_ID, notlar
    } = req.body;

    // Gerekli alanların kontrolü
    if (!cihaz_kodu || !sevkiyat_ID || !baslangic_zamani) {
      return res.status(400).json({ error: 'Cihaz kodu, sevkiyat ID ve başlangıç zamanı zorunludur' });
    }

    // sevkiyat_ID'nin sayı olduğundan emin ol
    const sevkiyatIDInt = parseInt(sevkiyat_ID, 10);
    if (isNaN(sevkiyatIDInt)) {
      return res.status(400).json({ error: 'Sevkiyat ID geçerli bir sayı olmalıdır' });
    }

    // Foreign key kontrollerini geçici olarak devre dışı bırak
    await executeQuery('SET FOREIGN_KEY_CHECKS=0');

    try {
      const [result] = await executeQuery(
        `INSERT INTO sevkiyatGecmis (
          cihaz_kodu, sevkiyat_ID, baslangic_zamani, bitis_zamani, 
          gonderen_firma_id, alici_firma_id, sicaklik_min, sicaklik_max,
          nem_min, nem_max, durum, musteri_ID, notlar
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          cihaz_kodu, sevkiyatIDInt, baslangic_zamani, bitis_zamani,
          gonderen_firma_id, alici_firma_id, sicaklik_min, sicaklik_max,
          nem_min, nem_max, durum, musteri_ID, notlar
        ]
      );

      res.status(201).json({
        id: result.insertId,
        message: 'Sevkiyat geçmişi başarıyla oluşturuldu'
      });
    } finally {
      // Foreign key kontrollerini tekrar etkinleştir
      await executeQuery('SET FOREIGN_KEY_CHECKS=1');
    }
  } catch (error) {
    console.error('Sevkiyat geçmişi eklenirken hata:', error);

    // Foreign key hatası
    if (error.code === 'ER_NO_REFERENCED_ROW_2') {
      return res.status(400).json({
        error: 'Referans verilen cihaz kodu, sevkiyat ID veya müşteri ID bulunamadı'
      });
    }

    res.status(500).json({ error: error.message });
  }
});

app.put('/api/sevkiyat-gecmis/:id', async (req, res) => {
  try {
    const {
      bitis_zamani, sicaklik_min, sicaklik_max, nem_min, nem_max, durum, notlar
    } = req.body;

    const [result] = await executeQuery(
      `UPDATE sevkiyatGecmis SET 
        bitis_zamani = ?, sicaklik_min = ?, sicaklik_max = ?,
        nem_min = ?, nem_max = ?, durum = ?, notlar = ?
      WHERE id = ?`,
      [
        bitis_zamani, sicaklik_min, sicaklik_max,
        nem_min, nem_max, durum, notlar, req.params.id
      ]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ message: 'Sevkiyat geçmişi bulunamadı' });
    }

    res.json({ message: 'Sevkiyat geçmişi başarıyla güncellendi' });
  } catch (error) {
    console.error('Sevkiyat geçmişi güncellenirken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

app.delete('/api/sevkiyat-gecmis/:id', async (req, res) => {
  try {
    const [result] = await executeQuery('DELETE FROM sevkiyatGecmis WHERE id = ?', [req.params.id]);

    if (result.affectedRows === 0) {
      return res.status(404).json({ message: 'Sevkiyat geçmişi bulunamadı' });
    }

    res.json({ message: 'Sevkiyat geçmişi başarıyla silindi' });
  } catch (error) {
    console.error('Sevkiyat geçmişi silinirken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

// Cihaz bilgisi rotaları
app.get('/api/cihaz-bilgi', async (req, res) => {
  try {
    const { mgz24_kodu, aktif } = req.query;
    let query = 'SELECT * FROM cihazBilgi';
    const params = [];
    const conditions = [];

    // Filtreleme koşulları
    if (mgz24_kodu) {
      conditions.push('mgz24_kodu = ?');
      params.push(mgz24_kodu);
    }

    // Aktif cihazları filtrele
    if (aktif === 'true') {
      conditions.push('aktif = 1');
    } else if (aktif === 'false') {
      conditions.push('aktif = 0');
    }

    // Koşulları sorguya ekle
    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    // Sonuçları cihaz koduna göre sırala
    query += ' ORDER BY mgz24_kodu';

    const [rows] = await executeQuery(query, params);
    res.json(rows);
  } catch (error) {
    console.error('Cihaz bilgisi alınırken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

// Cihaz durumunu güncelle (aktif/inaktif)
app.put('/api/cihaz-bilgi/:cihazKodu/status', async (req, res) => {
  try {
    const { cihazKodu } = req.params;
    const { aktif, notlar } = req.body;

    // Aktif durumunu kontrol et
    if (aktif === undefined) {
      return res.status(400).json({ error: 'Aktif durumu belirtilmedi' });
    }

    // Cihazı güncelle
    const [result] = await executeQuery(
      `UPDATE cihazBilgi 
       SET aktif = ?, 
           notlar = IFNULL(?, notlar), 
           guncelleme_zamani = NOW() 
       WHERE mgz24_kodu = ?`,
      [aktif ? 1 : 0, notlar, cihazKodu]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Cihaz bulunamadı' });
    }

    res.json({
      success: true,
      message: `Cihaz ${cihazKodu} durumu başarıyla güncellendi`,
      aktif: aktif ? 1 : 0
    });
  } catch (error) {
    console.error('Cihaz durumu güncellenirken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

// KALDIRILAN ENDPOINT: Sıcaklık alarm rotası artık uzak API'den alınıyor
// Bu endpoint cihazBilgi tablosunda 'zaman' field olmadığı için çalışmıyor
// Sıcaklık verileri artık https://ffl21.fun:3001 API'den alınıyor
/*
app.get('/api/alarms/temperature/:sevkiyatId', async (req, res) => {
  try {
    const [shipment] = await executeQuery(
      'SELECT sicaklik_araligi FROM sevkiyatlar WHERE id = ?',
      [req.params.sevkiyatId]
    );

    if (shipment.length === 0) {
      return res.status(404).json({ message: 'Sevkiyat bulunamadı' });
    }

    // Sıcaklık aralığını parse et
    const tempRange = shipment[0].sicaklik_araligi;
    const matches = tempRange.match(/(-?\d+(?:\.\d+)?)°C\s*-\s*(-?\d+(?:\.\d+)?)°C/);

    if (!matches) {
      return res.status(400).json({ message: 'Geçersiz sıcaklık aralığı formatı' });
    }

    const minTemp = parseFloat(matches[1]);
    const maxTemp = parseFloat(matches[2]);

    const [alarms] = await executeQuery(
      `SELECT * FROM cihazBilgi
             WHERE sevkiyat_id = ? AND okuma_tipi = 'sicaklik' AND (sicaklik < ? OR sicaklik > ?)
             ORDER BY olusturma_zamani DESC`,
      [req.params.sevkiyatId, minTemp, maxTemp]
    );

    res.json(alarms);
  } catch (error) {
    console.error('Sıcaklık alarmları alınırken hata:', error);
    res.status(500).json({ error: error.message });
  }
});
*/

// Müşteri Rotaları
app.get('/api/musteriler', async (req, res) => {
  try {
    const [rows] = await executeQuery('SELECT * FROM musteriler');
    res.json(rows);
  } catch (error) {
    console.error('Müşteriler alınırken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/musteriler/:id', async (req, res) => {
  try {
    const [rows] = await executeQuery('SELECT * FROM musteriler WHERE musteri_id = ?', [req.params.id]);
    if (rows.length === 0) {
      return res.status(404).json({ message: 'Müşteri bulunamadı' });
    }
    res.json(rows[0]);
  } catch (error) {
    console.error('Müşteri alınırken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/musteriler', async (req, res) => {
  try {
    const {
      ad, soyad, e_posta, telefon, sifre, kullanici_seviyesi, firma_ad, firma_adres, kategori
    } = req.body;

    const [result] = await executeQuery(
      `INSERT INTO musteriler (
                ad, soyad, e_posta, telefon, sifre, kullanici_seviyesi, firma_ad, firma_adres, kategori
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        ad, soyad, e_posta, telefon, sifre, kullanici_seviyesi, firma_ad, firma_adres, kategori
      ]
    );

    res.status(201).json({
      id: result.insertId,
      message: 'Müşteri başarıyla oluşturuldu'
    });
  } catch (error) {
    console.error('Müşteri eklenirken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

app.put('/api/musteriler/:id', async (req, res) => {
  try {
    const {
      ad, soyad, e_posta, telefon, sifre, kullanici_seviyesi, firma_ad, firma_adres, kategori
    } = req.body;

    const [result] = await executeQuery(
      `UPDATE musteriler SET 
                ad = ?, soyad = ?, e_posta = ?, telefon = ?, sifre = ?, 
                kullanici_seviyesi = ?, firma_ad = ?, firma_adres = ?, kategori = ?
            WHERE musteri_id = ?`,
      [
        ad, soyad, e_posta, telefon, sifre, kullanici_seviyesi,
        firma_ad, firma_adres, kategori, req.params.id
      ]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ message: 'Müşteri bulunamadı' });
    }

    res.json({ message: 'Müşteri başarıyla güncellendi' });
  } catch (error) {
    console.error('Müşteri güncellenirken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

app.delete('/api/musteriler/:id', async (req, res) => {
  try {
    const [result] = await executeQuery('DELETE FROM musteriler WHERE musteri_id = ?', [req.params.id]);

    if (result.affectedRows === 0) {
      return res.status(404).json({ message: 'Müşteri bulunamadı' });
    }

    res.json({ message: 'Müşteri başarıyla silindi' });
  } catch (error) {
    console.error('Müşteri silinirken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

// Kullanıcılar (kullanicilar) tablosu için endpointler

// Tüm kullanıcıları getir (sadece admin rolüne sahip kullanıcılar için)
app.get('/api/kullanicilar', async (req, res) => {
  try {
    const [users] = await executeQuery(
      `SELECT musteri_ID, kullanici, email, musteri_adi, tel, firma, adres, gorev, 
             olusturma_tarihi, guncelleme_tarihi 
             FROM kullanicilar`
    );
    res.json(users);
  } catch (error) {
    console.error('Kullanıcılar getirilirken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

// ID'ye göre kullanıcı getir
app.get('/api/kullanicilar/:id', async (req, res) => {
  try {
    const [users] = await executeQuery(
      `SELECT musteri_ID, kullanici, email, musteri_adi, tel, firma, adres, gorev, 
             olusturma_tarihi, guncelleme_tarihi 
             FROM kullanicilar 
             WHERE musteri_ID = ?`,
      [req.params.id]
    );

    if (users.length === 0) {
      return res.status(404).json({ message: 'Kullanıcı bulunamadı' });
    }

    res.json(users[0]);
  } catch (error) {
    console.error('Kullanıcı bilgileri getirilirken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

// Kullanıcı güncelle
app.put('/api/kullanicilar/:id', async (req, res) => {
  try {
    const {
      kullanici, email, musteri_adi, tel, firma, adres,
      current_password, password
    } = req.body;

    // Şifre değişikliği varsa
    if (current_password && password) {
      // Önce mevcut şifre kontrolü yap
      const [users] = await executeQuery(
        'SELECT password FROM kullanicilar WHERE musteri_ID = ?',
        [req.params.id]
      );

      if (users.length === 0) {
        return res.status(404).json({ message: 'Kullanıcı bulunamadı' });
      }

      const storedPassword = users[0].password;

      // Burada şifre doğrulama işlemi yapılabilir (bcrypt vb.)
      // Örnek olarak doğrudan karşılaştırma kullanıyoruz
      if (storedPassword !== current_password) {
        return res.status(401).json({ message: 'Mevcut şifre yanlış' });
      }

      // Şifre güncellemesi yap
      const [result] = await executeQuery(
        `UPDATE kullanicilar SET 
                    kullanici = ?, email = ?, musteri_adi = ?, tel = ?, 
                    firma = ?, adres = ?, password = ?, guncelleme_tarihi = NOW()
                WHERE musteri_ID = ?`,
        [kullanici, email, musteri_adi, tel, firma, adres, password, req.params.id]
      );

      if (result.affectedRows === 0) {
        return res.status(404).json({ message: 'Kullanıcı bulunamadı' });
      }
    } else {
      // Şifre değişikliği yoksa sadece diğer bilgileri güncelle
      const [result] = await executeQuery(
        `UPDATE kullanicilar SET 
                    kullanici = ?, email = ?, musteri_adi = ?, tel = ?, 
                    firma = ?, adres = ?, guncelleme_tarihi = NOW()
                WHERE musteri_ID = ?`,
        [kullanici, email, musteri_adi, tel, firma, adres, req.params.id]
      );

      if (result.affectedRows === 0) {
        return res.status(404).json({ message: 'Kullanıcı bulunamadı' });
      }
    }

    res.json({ message: 'Kullanıcı başarıyla güncellendi' });
  } catch (error) {
    console.error('Kullanıcı güncellenirken hata:', error);

    // Benzersizlik kısıtlaması hatası
    if (error.code === 'ER_DUP_ENTRY') {
      return res.status(409).json({
        message: 'Bu e-posta adresi veya kullanıcı adı başka bir kullanıcı tarafından kullanılıyor.'
      });
    }

    res.status(500).json({ error: error.message });
  }
});

// Ödeme ve Paket Rotaları
app.get('/api/payments/packages', async (req, res) => {
  try {
    // Burada veritabanından paketler çekilebilir
    // Şimdilik sabit veri döndürüyoruz
    const packages = [
      { id: 0, days: 1, priceEUR: 0.03, name: 'Test Paketi (1 Gün)', priceTL: 1, isTest: true },
      { id: 1, days: 5, priceEUR: 3, name: '5 Gün Kullanım' },
      { id: 2, days: 10, priceEUR: 5, name: '10 Gün Kullanım' },
      { id: 3, days: 20, priceEUR: 6, name: '20 Gün Kullanım' },
      { id: 4, days: 30, priceEUR: 7, name: '30 Gün Kullanım' },
      { id: 5, days: 90, priceEUR: 18, name: '3 Ay Kullanım' },
      { id: 6, days: 180, priceEUR: 33, name: '6 Ay Kullanım' },
      { id: 7, days: 360, priceEUR: 60, name: '1 Yıl Kullanım' }
    ];

    res.json(packages);
  } catch (error) {
    console.error('Paketler alınırken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/payments/exchange-rate', async (req, res) => {
  try {
    // TCMB'den canlı EUR kuru al
    const fetch = (await import('node-fetch')).default;
    const response = await fetch('https://www.tcmb.gov.tr/kurlar/today.xml');
    const xmlText = await response.text();

    // XML'i parse et
    const { DOMParser } = await import('xmldom');
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xmlText, 'text/xml');

    // EUR kurunun satış değerini bul
    const currencies = xmlDoc.getElementsByTagName('Currency');
    let eurSelling = null;
    let eurBuying = null;

    for (let i = 0; i < currencies.length; i++) {
      const currency = currencies[i];
      const code = currency.getAttribute('CurrencyCode');

      if (code === 'EUR') {
        const sellingElement = currency.getElementsByTagName('BanknoteSelling')[0];
        const buyingElement = currency.getElementsByTagName('BanknoteBuying')[0];

        if (sellingElement) {
          eurSelling = parseFloat(sellingElement.textContent);
        }
        if (buyingElement) {
          eurBuying = parseFloat(buyingElement.textContent);
        }
        break;
      }
    }

    if (eurSelling) {
      res.json({
        EUR: {
          selling: eurSelling,
          buying: eurBuying || eurSelling - 0.10,
          updated: new Date().toISOString()
        }
      });
    } else {
      throw new Error('EUR kuru TCMB\'den alınamadı');
    }
  } catch (error) {
    console.error('Döviz kuru alınırken hata:', error);
    // Hata durumunda fallback değer döndür
    const fallbackRate = 34.98;
    res.json({
      EUR: {
        selling: fallbackRate,
        buying: fallbackRate - 0.10,
        updated: new Date().toISOString(),
        fallback: true,
        error: error.message
      }
    });
  }
});

app.get('/api/payments/history/:userId', async (req, res) => {
  try {
    const userId = req.params.userId;

    // Veritabanından kullanıcının ödeme geçmişini çek
    const [rows] = await executeQuery(
      `SELECT * FROM kullanici_odemeleri WHERE kullanici_id = ? ORDER BY odeme_tarihi DESC`,
      [userId]
    );

    // Sonuçları düzenle ve gönder
    const formattedPayments = rows.map(payment => ({
      id: payment.islem_id,
      packageName: payment.paket_adi,
      amountEUR: parseFloat(payment.tutar_eur),
      amountTRY: parseFloat(payment.tutar_tl),
      exchangeRate: parseFloat(payment.doviz_kuru),
      date: payment.odeme_tarihi,
      status: payment.durum
    }));

    res.json(formattedPayments);
  } catch (error) {
    console.error('Ödeme geçmişi alınırken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/users/:userId/usage', async (req, res) => {
  try {
    const userId = req.params.userId;

    // Kullanıcının cihazlarından en uzun kalan süreyi hesapla
    const [rows] = await executeQuery(
      `SELECT kontor_sonu FROM cihazBilgi 
       WHERE kullanici_ID = ? AND kontor_sonu IS NOT NULL 
       ORDER BY kontor_sonu DESC LIMIT 1`,
      [userId]
    );

    if (rows.length === 0) {
      return res.json({
        remainingDays: 0,
        expiryDate: null,
        status: 'inactive'
      });
    }

    const device = rows[0];
    const expiryDate = new Date(device.kontor_sonu);
    const today = new Date();

    // Kalan gün sayısını hesapla (InactiveDevices.js'deki hesaplama ile aynı)
    const diffTime = expiryDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    res.json({
      remainingDays: diffDays > 0 ? diffDays : 0,
      expiryDate: device.kontor_sonu,
      status: diffDays > 0 ? 'active' : 'expired'
    });
  } catch (error) {
    console.error('Kullanım süresi alınırken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/payments/process', async (req, res) => {
  try {
    const {
      userId,
      packageId,
      cardDetails,
      amount
    } = req.body;

    // Gerçek uygulamada burada ödeme işlemi gerçekleştirilir
    // Test için başarılı ödeme simüle ediyoruz

    // Yeni işlem ID'si oluştur
    const transactionId = 'TRX-' + Math.random().toString(36).substring(2, 10).toUpperCase();

    // Paketi bul
    const [packages] = await executeQuery('SELECT * FROM paketler WHERE id = ?', [packageId]);

    if (packages.length === 0) {
      return res.status(404).json({ success: false, message: 'Paket bulunamadı' });
    }

    const selectedPackage = packages[0];

    // Ödeme kaydını veritabanına ekle
    await executeQuery(
      `INSERT INTO kullanici_odemeleri (
        islem_id, kullanici_id, paket_id, paket_adi, tutar_eur, tutar_tl, 
        doviz_kuru, odeme_tarihi, durum, satin_alinan_gun
      ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), 'tamamlandi', ?)`,
      [
        transactionId,
        userId,
        packageId,
        selectedPackage.name,
        amount.eur,
        amount.try,
        amount.exchangeRate,
        selectedPackage.days
      ]
    );

    // Not: Kredi cihaza atanacak, bu endpoint sadece ödeme kaydını tutar
    // Gerçek kredi ataması frontend'de selectedDevice ile /api/cihaz-bilgi/:cihazKodu/add-credit endpoint'ine yapılır

    res.json({
      success: true,
      message: 'Ödeme başarıyla gerçekleştirildi',
      transactionId: transactionId,
      packageInfo: selectedPackage,
      date: new Date()
    });
  } catch (error) {
    console.error('Ödeme işlemi sırasında hata:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Düzgün kapatma işlemleri
process.on('SIGINT', async () => {
  console.log('Sunucu kapatılıyor...');

  try {
    // Bağlantı havuzunu kapat
    await pool.end();
    console.log('Veritabanı bağlantısı kapatıldı');
  } catch (err) {
    console.error('Veritabanı havuzu kapatılırken hata:', err);
  }

  process.exit(0);
});

// Beklenmeyen hatalar için
process.on('uncaughtException', (err) => {
  console.error('Beklenmeyen hata:', err);
  // Kritik bir hata durumunda bile düzgün kapanma denemesi
  try {
    pool.end().then(() => {
      console.log('Veritabanı bağlantısı kapatıldı');
      process.exit(1);
    }).catch(() => {
      process.exit(1);
    });
  } catch (e) {
    process.exit(1);
  }
});

// =========================
// CİHAZ ID YÖNETİMİ API'LERİ
// =========================

// Müşteriye atanan tüm cihazları getir (admin + user)
app.get('/api/cihaz-id', optionalAuthentication, async (req, res) => {
  try {
    const { musteriId, aktif, krediVar } = req.query;

    let query = `
      SELECT 
        cb.id as ID, cb.mgz24_kodu as CihazID, 
        '' as ICCID, 1 as TestBitti, 1 as GoldCihaz,
        cb.olusturma_zamani as SistemeEklemeTarihi, cb.olusturma_zamani as BaslamaTarihi, 
        DATE_ADD(cb.olusturma_zamani, INTERVAL 30 DAY) as BitisTarihi,
        CASE WHEN cb.aktif = 0 THEN 1 ELSE 0 END as KullanimBitti,
        CASE WHEN cb.aktif = 1 THEN 2592000 ELSE 0 END as KalanSure,
        s.musteri_ID as MusteriID, 30 as CihazGun,
        '' as Notlar, '' as Notlaradmin,
        u.musteri_adi, u.firma,
        CASE 
          WHEN cb.aktif = 1 THEN 'var'
          ELSE 'yok'
        END as kredi_durumu,
        CASE 
          WHEN cb.aktif = 1 THEN 30.0
          ELSE 0
        END as kredi_gun,
        CASE 
          WHEN cb.sevkiyat_ID IS NOT NULL THEN 'aktif'
          ELSE 'inaktif'
        END as cihaz_durumu,
        cb.pil_seviyesi, cb.durum as sensör_durumu
      FROM cihazBilgi cb
      LEFT JOIN sevkiyatlar s ON cb.sevkiyat_ID = s.id
      LEFT JOIN kullanicilar u ON s.musteri_ID = u.musteri_ID
      WHERE cb.aktif = 1
    `;

    const params = [];

    if (musteriId) {
      query += ' AND s.musteri_ID = ?';
      params.push(musteriId);
    }

    if (aktif === 'true') {
      query += ' AND cb.sevkiyat_ID IS NOT NULL';
    } else if (aktif === 'false') {
      query += ' AND cb.sevkiyat_ID IS NULL';
    }

    if (krediVar === 'true') {
      query += ' AND cb.aktif = 1';
    } else if (krediVar === 'false') {
      query += ' AND cb.aktif = 0';
    }

    query += ' ORDER BY cb.olusturma_zamani DESC';

    const [cihazlar] = await executeQuery(query, params);

    res.json(cihazlar);
  } catch (error) {
    console.error('Cihazlar listelenirken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

// İnaktif cihazları getir (cihazBilgi tablosundan)
app.get('/api/cihaz-id/inaktif', async (req, res) => {
  try {
    const { musteriId } = req.query;

    if (!musteriId) {
      return res.status(400).json({ error: 'Müşteri ID gereklidir' });
    }

    // cihazBilgi tablosundan inaktif cihazları getir (aktif = 0 olanlar)
    const query = `
      SELECT 
        cb.id as ID,
        cb.mgz24_kodu as CihazID,
        cb.pil_seviyesi,
        cb.kontor_sonu,
        CASE 
          WHEN cb.kontor_sonu IS NOT NULL AND cb.kontor_sonu > NOW() THEN
            CASE 
              WHEN TIMESTAMPDIFF(DAY, NOW(), cb.kontor_sonu) > 0 THEN
                CONCAT('Kredi: ', TIMESTAMPDIFF(DAY, NOW(), cb.kontor_sonu), ' gün')
              WHEN TIMESTAMPDIFF(HOUR, NOW(), cb.kontor_sonu) > 0 THEN
                CONCAT('Kredi: ', TIMESTAMPDIFF(HOUR, NOW(), cb.kontor_sonu), ' saat')
              ELSE 'Kredi: 0 gün'
            END
          ELSE 'Kredi: 0 gün'
        END as kredi_gun,
        CASE 
          WHEN cb.kontor_sonu IS NOT NULL AND cb.kontor_sonu > NOW() THEN 'var'
          ELSE 'yok'
        END as kredi_durumu,
        'inaktif' as cihaz_durumu
      FROM cihazBilgi cb
      WHERE cb.aktif = 0 
        AND cb.kullanici_ID = ?
        AND cb.kontor_sonu IS NOT NULL 
        AND cb.kontor_sonu > NOW()
      ORDER BY cb.olusturma_zamani DESC
    `;

    const [cihazlar] = await executeQuery(query, [musteriId]);

    res.json(cihazlar);
  } catch (error) {
    console.error('İnaktif cihazlar listelenirken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

// Admin: Cihazı müşteriye ata
app.post('/api/cihaz-id/assign', authenticateToken, async (req, res) => {
  try {
    const { cihazId, musteriId } = req.body;

    if (!cihazId || !musteriId) {
      return res.status(400).json({ error: 'Cihaz ID ve Müşteri ID gereklidir' });
    }

    // Cihazın var olup olmadığını kontrol et
    const [cihazCheck] = await executeQuery(
      'SELECT id, mgz24_kodu, sevkiyat_ID FROM cihazBilgi WHERE mgz24_kodu = ?',
      [cihazId]
    );

    if (cihazCheck.length === 0) {
      return res.status(404).json({ error: 'Cihaz bulunamadı' });
    }

    if (cihazCheck[0].sevkiyat_ID !== null) {
      return res.status(400).json({ error: 'Cihaz zaten bir sevkiyata atanmış' });
    }

    // Yeni sevkiyat oluştur ve cihazı aktif et
    const [sevkiyatResult] = await executeQuery(
      `INSERT INTO sevkiyatlar (musteri_ID, olusturma_zamani, status) VALUES (?, NOW(), 'aktif')`,
      [musteriId]
    );

    // Cihazı sevkiyata ata ve aktif et
    const [updateResult] = await executeQuery(
      `UPDATE cihazBilgi 
       SET sevkiyat_ID = ?, 
           aktif = 1,
           guncelleme_zamani = NOW()
       WHERE mgz24_kodu = ?`,
      [sevkiyatResult.insertId, cihazId]
    );

    if (updateResult.affectedRows === 0) {
      return res.status(400).json({ error: 'Cihaz ataması yapılamadı' });
    }

    res.json({
      success: true,
      message: 'Cihaz başarıyla müşteriye atandı',
      cihazId: cihazId,
      musteriId: musteriId,
      krediGun: 10
    });

  } catch (error) {
    console.error('Cihaz atama hatası:', error);
    res.status(500).json({ error: error.message });
  }
});

// Cihaz kredisi satın al
app.post('/api/cihaz-id/buy-credit', authenticateToken, async (req, res) => {
  try {
    const { cihazId, krediGun, odemeDetaylari } = req.body;

    if (!cihazId || !krediGun) {
      return res.status(400).json({ error: 'Cihaz ID ve kredi günü gereklidir' });
    }

    // Cihazın kullanıcıya ait olup olmadığını kontrol et
    const userId = req.user.musteri_ID;
    const [cihazCheck] = await executeQuery(
      'SELECT cb.id, cb.mgz24_kodu, cb.aktif, s.musteri_ID FROM cihazBilgi cb LEFT JOIN sevkiyatlar s ON cb.sevkiyat_ID = s.id WHERE cb.mgz24_kodu = ? AND s.musteri_ID = ?',
      [cihazId, userId]
    );

    if (cihazCheck.length === 0) {
      return res.status(404).json({ error: 'Cihaz bulunamadı veya size ait değil' });
    }

    // Ödeme işlemi burada gerçekleştirilecek (demo olarak başarılı kabul ediyoruz)
    // Gerçek uygulamada payment gateway entegrasyonu yapılmalı

    // Cihazı aktif et (kredi satın alındığı için)
    const [updateResult] = await executeQuery(
      `UPDATE cihazBilgi 
       SET aktif = 1
       WHERE cihaz_kodu = ?`,
      [cihazId]
    );

    if (updateResult.affectedRows === 0) {
      return res.status(400).json({ error: 'Kredi güncellenemedi' });
    }

    res.json({
      success: true,
      message: `${krediGun} günlük kredi başarıyla satın alındı`,
      cihazId: cihazId,
      cihazAktif: true,
      eklenenGun: krediGun
    });

  } catch (error) {
    console.error('Kredi satın alma hatası:', error);
    res.status(500).json({ error: error.message });
  }
});

// =========================
// GERÇEK ZAMANLI CİHAZ VERİSİ
// =========================

// Harici API'den cihaz verisi çekme fonksiyonu
const fetchExternalDeviceData = async (cihazID) => {
  try {
    const response = await axios.get(`${process.env.EXTERNAL_API_URL}/${cihazID}/data`, {
      headers: {
        'Authorization': `Bearer ${process.env.EXTERNAL_API_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: parseInt(process.env.EXTERNAL_API_TIMEOUT) || 3000
    });

    return {
      success: true,
      data: response.data,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error(`Harici API hatası (${cihazID}):`, error.message);

    if (error.code === 'ECONNABORTED') {
      return {
        success: false,
        error: 'API_TIMEOUT',
        message: 'Dış API zaman aşımına uğradı'
      };
    }

    if (error.response) {
      return {
        success: false,
        error: 'API_ERROR',
        message: `API Hatası: ${error.response.status} - ${error.response.data?.message || 'Bilinmeyen hata'}`,
        status: error.response.status
      };
    }

    return {
      success: false,
      error: 'NETWORK_ERROR',
      message: 'Ağ bağlantı hatası'
    };
  }
};

// Fallback demo data function
const getFallbackDeviceData = (cihazID) => {
  return {
    success: true,
    data: {
      cihaz_kodu: cihazID,
      sicaklik: (Math.random() * 20 - 5).toFixed(1), // -5 ile 15 arası
      nem: (Math.random() * 100).toFixed(1),
      isik: (Math.random() * 1000).toFixed(1),
      pil_seviyesi: (Math.random() * 100).toFixed(1),
      enlem: (41.0082 + (Math.random() - 0.5) * 0.1).toFixed(6),
      boylam: (28.9784 + (Math.random() - 0.5) * 0.1).toFixed(6),
      durum: Math.random() > 0.8 ? 'pasif' : 'aktif',
      son_guncelleme: new Date().toISOString()
    },
    source: 'fallback',
    timestamp: new Date().toISOString()
  };
};

// Gerçek zamanlı cihaz verisi endpoint'i
app.get('/api/cihaz-veri/:cihazID', optionalAuthentication, async (req, res) => {
  try {
    const { cihazID } = req.params;
    const { fallback = 'true' } = req.query;

    // Cihaz ID validasyonu
    if (!cihazID || cihazID.length < 3) {
      return res.status(400).json({
        success: false,
        error: 'INVALID_DEVICE_ID',
        message: 'Geçersiz cihaz ID formatı'
      });
    }

    // Veritabanından cihaz bilgilerini kontrol et
    const [deviceCheck] = await executeQuery(
      'SELECT mgz24_kodu, aktif FROM cihazBilgi WHERE mgz24_kodu = ? LIMIT 1',
      [cihazID]
    );

    if (deviceCheck.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'DEVICE_NOT_FOUND',
        message: 'Cihaz bulunamadı'
      });
    }

    // Harici API'den veri çekmeye çalış
    const externalData = await fetchExternalDeviceData(cihazID);

    // Harici API başarılı ise sonucu döndür
    if (externalData.success) {
      // KALDIRILAN: Veriyi veritabanına kaydetme - uzak API kullanılıyor
      // Bu INSERT cihazBilgi tablosunda 'zaman' field olmadığı için çalışmıyor
      // Sensor verileri artık tamamen uzak API'den alınıyor, lokal kayıt yapılmıyor
      /*
      try {
        await executeQuery(
          `INSERT INTO cihazBilgi (
            mgz24_kodu, sicaklik, nem, isik, pil_seviyesi, 
            enlem, boylam, durum, okuma_tipi
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            cihazID,
            externalData.data.sicaklik || null,
            externalData.data.nem || null,
            externalData.data.isik || null,
            externalData.data.pil_seviyesi || null,
            externalData.data.enlem || null,
            externalData.data.boylam || null,
            externalData.data.durum || 'aktif',
            'gercek_zamanli'
          ]
        );
      } catch (dbError) {
        console.error('Veritabanı kayıt hatası:', dbError);
        // Veritabanı hatası olsa bile external data'yı döndür
      }
      */

      return res.json({
        success: true,
        data: externalData.data,
        source: 'external_api',
        timestamp: externalData.timestamp
      });
    }

    // Harici API başarısız ise fallback kullan
    if (fallback === 'true') {
      const fallbackData = getFallbackDeviceData(cihazID);

      return res.json({
        success: true,
        data: fallbackData.data,
        source: 'fallback',
        timestamp: fallbackData.timestamp,
        warning: 'Harici API mevcut değil, demo veri kullanılıyor',
        external_error: externalData.message
      });
    }

    // Fallback kapalı ise hata döndür
    return res.status(503).json({
      success: false,
      error: externalData.error,
      message: externalData.message,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Cihaz veri endpoint hatası:', error);
    res.status(500).json({
      success: false,
      error: 'INTERNAL_ERROR',
      message: 'Sunucu hatası',
      timestamp: new Date().toISOString()
    });
  }
});

// Cihaz detay bilgileri endpoint'i
app.get('/api/cihaz-detay/:cihazID', optionalAuthentication, async (req, res) => {
  try {
    const { cihazID } = req.params;

    // Cihaz ID validasyonu
    if (!cihazID || cihazID.length < 3) {
      return res.status(400).json({
        success: false,
        error: 'INVALID_DEVICE_ID',
        message: 'Geçersiz cihaz ID formatı'
      });
    }

    // Veritabanından cihaz detaylarını al
    const [deviceDetails] = await executeQuery(
      `SELECT 
        cb.id as ID, cb.mgz24_kodu as CihazID, '' as ICCID, 
        1 as TestBitti, 1 as GoldCihaz,
        cb.olusturma_zamani as SistemeEklemeTarihi, cb.olusturma_zamani as BaslamaTarihi, 
        DATE_ADD(cb.olusturma_zamani, INTERVAL 30 DAY) as BitisTarihi,
        CASE WHEN cb.aktif = 0 THEN 1 ELSE 0 END as KullanimBitti,
        CASE WHEN cb.aktif = 1 THEN 2592000 ELSE 0 END as KalanSure,
        s.musteri_ID as MusteriID, 30 as CihazGun,
        '' as Notlar, '' as Notlaradmin,
        u.musteri_adi, u.firma,
        CASE 
          WHEN cb.aktif = 1 THEN 30.0
          ELSE 0
        END as kredi_gun,
        CASE 
          WHEN cb.aktif = 1 THEN 'var'
          ELSE 'yok'
        END as kredi_durumu,
        cb.pil_seviyesi, cb.durum as sensor_durumu,
        cb.guncelleme_zamani as son_veri_zamani
      FROM cihazBilgi cb
      LEFT JOIN sevkiyatlar s ON cb.sevkiyat_ID = s.id
      LEFT JOIN kullanicilar u ON s.musteri_ID = u.musteri_ID
      WHERE cb.mgz24_kodu = ?
      ORDER BY cb.olusturma_zamani DESC
      LIMIT 1`,
      [cihazID]
    );

    if (deviceDetails.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'DEVICE_NOT_FOUND',
        message: 'Cihaz bulunamadı'
      });
    }

    const device = deviceDetails[0];

    // Kalan süreyi hesapla
    const kalanGun = device.KalanSure > 0 ? Math.floor(device.KalanSure / 86400) : 0;
    const kalanSaat = device.KalanSure > 0 ? Math.floor((device.KalanSure % 86400) / 3600) : 0;

    // Üretim tarihini hesapla (sistem ekleme tarihinden tahmin)
    const uretimTarihi = new Date(device.SistemeEklemeTarihi);
    uretimTarihi.setMonth(uretimTarihi.getMonth() - 1); // Tahmini üretim tarihi

    const response = {
      success: true,
      data: {
        cihaz_id: device.CihazID,
        iccid: device.ICCID,
        uretim_tarihi: uretimTarihi.toISOString().split('T')[0],
        sisteme_ekleme_tarihi: device.SistemeEklemeTarihi,
        baslama_tarihi: device.BaslamaTarihi,
        bitis_tarihi: device.BitisTarihi,
        musteri_id: device.MusteriID,
        musteri_adi: device.musteri_adi,
        firma: device.firma,
        gold_cihaz: device.GoldCihaz === 1,
        test_bitti: device.TestBitti === 1,
        kullanim_bitti: device.KullanimBitti === 1,
        kalan_sure: {
          saniye: device.KalanSure,
          gun: kalanGun,
          saat: kalanSaat,
          durum: device.kredi_durumu,
          yuzde: Math.min(100, (device.KalanSure / 3628800) * 100) // 42 günlük max süre
        },
        pil_seviyesi: device.pil_seviyesi || 0,
        sensor_durumu: device.sensor_durumu || 'bilinmiyor',
        son_veri_zamani: device.son_veri_zamani,
        notlar: device.Notlar,
        admin_notlari: device.Notlaradmin
      },
      timestamp: new Date().toISOString()
    };

    res.json(response);

  } catch (error) {
    console.error('Cihaz detay endpoint hatası:', error);
    res.status(500).json({
      success: false,
      error: 'INTERNAL_ERROR',
      message: 'Sunucu hatası',
      timestamp: new Date().toISOString()
    });
  }
});

// =========================
// RAPORLAMA SİSTEMİ - KALDIRILAN ENDPOİNTLER
// =========================
// Bu endpointler cihazBilgi tablosunda 'zaman' field olmadığı için çalışmıyor
// Rapor verilerinin uzak API'den alınması gerekiyor

/*
// PDF raporu oluştur
app.get('/api/rapor/pdf/:cihazID', authenticateToken, async (req, res) => {
  try {
    const { cihazID } = req.params;
    const { startDate, endDate } = req.query;

    // Tarih validasyonu
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const end = endDate ? new Date(endDate) : new Date();

    // Cihaz verilerini çek
    const [deviceData] = await executeQuery(
      `SELECT cb.*, c.CihazID, c.MusteriID, u.musteri_adi, u.firma
       FROM cihazBilgi cb
       LEFT JOIN cihazID c ON cb.cihaz_kodu = c.CihazID
       LEFT JOIN kullanicilar u ON c.MusteriID = u.musteri_ID
       WHERE cb.cihaz_kodu = ? AND cb.zaman BETWEEN ? AND ?
       ORDER BY cb.zaman ASC`,
      [cihazID, start, end]
    );

    if (deviceData.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Belirtilen tarih aralığında cihaz verisi bulunamadı'
      });
    }

    // PDF oluştur
    const doc = new PDFDocument({
      margin: 50,
      autoFirstPage: true,
      bufferPages: true
    });
    const fileName = `MGZ24_Rapor_${cihazID}_${Date.now()}.pdf`;
    const filePath = path.join(__dirname, 'temp', fileName);

    // Temp klasörünü oluştur
    const tempDir = path.join(__dirname, 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // PDF'i dosyaya yaz
    doc.pipe(fs.createWriteStream(filePath));

    // Türkçe karakter desteği için font ayarları
    doc.font('Helvetica');

    // PDF içeriği
    doc.fontSize(20).text('MGZ24 Cihaz Raporu', { align: 'center' });
    doc.moveDown();

    // Cihaz bilgileri
    doc.fontSize(14).text(`Cihaz ID: ${cihazID}`);
    if (deviceData[0].musteri_adi) {
      doc.text(`Müşteri: ${deviceData[0].musteri_adi}`);
    }
    if (deviceData[0].firma) {
      doc.text(`Firma: ${deviceData[0].firma}`);
    }
    doc.text(`Rapor Tarihi: ${start.toLocaleDateString('tr-TR')} - ${end.toLocaleDateString('tr-TR')}`);
    doc.text(`Oluşturulma: ${new Date().toLocaleString('tr-TR')}`);
    doc.moveDown();

    // Özet istatistikler
    const avgTemp = deviceData.reduce((sum, item) => sum + (parseFloat(item.sicaklik) || 0), 0) / deviceData.length;
    const avgHumidity = deviceData.reduce((sum, item) => sum + (parseFloat(item.nem) || 0), 0) / deviceData.length;
    const minTemp = Math.min(...deviceData.map(item => parseFloat(item.sicaklik) || 0));
    const maxTemp = Math.max(...deviceData.map(item => parseFloat(item.sicaklik) || 0));

    doc.fontSize(16).text('Özet İstatistikler', { underline: true });
    doc.fontSize(12);
    doc.text(`Toplam Ölçüm: ${deviceData.length}`);
    doc.text(`Ortalama Sıcaklık: ${avgTemp.toFixed(2)}°C`);
    doc.text(`Minimum Sıcaklık: ${minTemp}°C`);
    doc.text(`Maksimum Sıcaklık: ${maxTemp}°C`);
    doc.text(`Ortalama Nem: ${avgHumidity.toFixed(2)}%`);
    doc.moveDown();

    // Veri tablosu
    doc.fontSize(16).text('Detaylı Veriler', { underline: true });
    doc.fontSize(10);

    let yPosition = doc.y;
    const tableTop = yPosition + 20;
    const itemHeight = 20;

    // Tablo başlıkları
    doc.text('Tarih/Saat', 50, tableTop);
    doc.text('Sıcaklık', 150, tableTop);
    doc.text('Nem', 220, tableTop);
    doc.text('Işık', 280, tableTop);
    doc.text('Pil', 340, tableTop);
    doc.text('Konum', 400, tableTop);

    // Tablo verileri
    deviceData.slice(0, 30).forEach((item, index) => {
      const y = tableTop + (index + 1) * itemHeight;

      if (y > 700) {
        doc.addPage();
        yPosition = 50;
      }

      doc.text(new Date(item.zaman).toLocaleString('tr-TR'), 50, y);
      doc.text(`${item.sicaklik}°C`, 150, y);
      doc.text(`${item.nem}%`, 220, y);
      doc.text(`${item.isik}`, 280, y);
      doc.text(`${item.pil_seviyesi}%`, 340, y);
      doc.text(`${item.enlem}, ${item.boylam}`, 400, y);
    });


    doc.end();

    // PDF hazır olduğunda gönder
    doc.on('end', () => {
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);

      const fileStream = fs.createReadStream(filePath);
      fileStream.pipe(res);

      // Dosyayı 5 dakika sonra sil
      setTimeout(() => {
        fs.unlink(filePath, (err) => {
          if (err) console.error('Temp PDF silinirken hata:', err);
        });
      }, 5 * 60 * 1000);
    });

  } catch (error) {
    console.error('PDF raporu oluşturulurken hata:', error);
    res.status(500).json({
      success: false,
      error: 'PDF_GENERATION_ERROR',
      message: 'PDF raporu oluşturulamadı'
    });
  }
});

// Excel raporu oluştur
app.get('/api/rapor/excel/:cihazID', authenticateToken, async (req, res) => {
  try {
    const { cihazID } = req.params;
    const { startDate, endDate } = req.query;

    // Tarih validasyonu
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const end = endDate ? new Date(endDate) : new Date();

    // Cihaz verilerini çek
    const [deviceData] = await executeQuery(
      `SELECT cb.*, c.CihazID, c.MusteriID, u.musteri_adi, u.firma
       FROM cihazBilgi cb
       LEFT JOIN cihazID c ON cb.cihaz_kodu = c.CihazID
       LEFT JOIN kullanicilar u ON c.MusteriID = u.musteri_ID
       WHERE cb.cihaz_kodu = ? AND cb.zaman BETWEEN ? AND ?
       ORDER BY cb.zaman ASC`,
      [cihazID, start, end]
    );

    if (deviceData.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Belirtilen tarih aralığında cihaz verisi bulunamadı'
      });
    }

    // Excel dosyası oluştur
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Cihaz Raporu');

    // Başlık stilleri
    const headerStyle = {
      fill: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF4472C4' } },
      font: { color: { argb: 'FFFFFFFF' }, bold: true },
      alignment: { horizontal: 'center', vertical: 'middle' }
    };

    // Bilgi bölümü
    worksheet.mergeCells('A1:H1');
    worksheet.getCell('A1').value = 'MGZ24 Cihaz Raporu';
    worksheet.getCell('A1').style = {
      font: { size: 16, bold: true },
      alignment: { horizontal: 'center' }
    };

    worksheet.getCell('A3').value = 'Cihaz ID:';
    worksheet.getCell('B3').value = cihazID;
    worksheet.getCell('A4').value = 'Müşteri:';
    worksheet.getCell('B4').value = deviceData[0].musteri_adi || 'Bilinmiyor';
    worksheet.getCell('A5').value = 'Firma:';
    worksheet.getCell('B5').value = deviceData[0].firma || 'Bilinmiyor';
    worksheet.getCell('A6').value = 'Rapor Tarihi:';
    worksheet.getCell('B6').value = `${start.toLocaleDateString('tr-TR')} - ${end.toLocaleDateString('tr-TR')}`;

    // Tablo başlıkları
    const headers = ['Tarih/Saat', 'Sıcaklık (°C)', 'Nem (%)', 'Işık', 'Pil (%)', 'Enlem', 'Boylam', 'Durum'];
    worksheet.getRow(8).values = headers;
    worksheet.getRow(8).eachCell((cell) => {
      cell.style = headerStyle;
    });

    // Veriler
    deviceData.forEach((item, index) => {
      const row = worksheet.getRow(9 + index);
      row.values = [
        new Date(item.zaman).toLocaleString('tr-TR'),
        parseFloat(item.sicaklik) || 0,
        parseFloat(item.nem) || 0,
        parseFloat(item.isik) || 0,
        parseFloat(item.pil_seviyesi) || 0,
        parseFloat(item.enlem) || 0,
        parseFloat(item.boylam) || 0,
        item.durum || 'aktif'
      ];
    });

    // Sütun genişlikleri
    worksheet.columns = [
      { width: 20 }, // Tarih/Saat
      { width: 15 }, // Sıcaklık
      { width: 10 }, // Nem
      { width: 10 }, // Işık
      { width: 10 }, // Pil
      { width: 15 }, // Enlem
      { width: 15 }, // Boylam
      { width: 15 }  // Durum
    ];

    // Dosya adı ve yolu
    const fileName = `MGZ24_Rapor_${cihazID}_${Date.now()}.xlsx`;
    const filePath = path.join(__dirname, 'temp', fileName);

    // Temp klasörünü oluştur
    const tempDir = path.join(__dirname, 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Excel dosyasını yaz
    await workbook.xlsx.writeFile(filePath);

    // Dosyayı gönder
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);

    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);

    // Dosyayı 5 dakika sonra sil
    setTimeout(() => {
      fs.unlink(filePath, (err) => {
        if (err) console.error('Temp Excel silinirken hata:', err);
      });
    }, 5 * 60 * 1000);

  } catch (error) {
    console.error('Excel raporu oluşturulurken hata:', error);
    res.status(500).json({
      success: false,
      error: 'EXCEL_GENERATION_ERROR',
      message: 'Excel raporu oluşturulamadı'
    });
  }
});
*/

// Sevkiyat tamamlama endpoint'i
app.post('/api/sevkiyatlar/complete', authenticateToken, async (req, res) => {
  try {
    const { sevkiyat_id, tamamlayan_kullanici_id, notlar } = req.body;

    // Parametreleri doğrula
    if (!sevkiyat_id || !tamamlayan_kullanici_id) {
      return res.status(400).json({
        success: false,
        error: 'Sevkiyat ID ve kullanıcı ID gereklidir'
      });
    }

    // Önce sevkiyatın var olup olmadığını kontrol et
    const [sevkiyatCheck] = await executeQuery(
      'SELECT id, status FROM sevkiyatlar WHERE id = ?',
      [sevkiyat_id]
    );

    if (sevkiyatCheck.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Sevkiyat bulunamadı'
      });
    }

    if (sevkiyatCheck[0].status === 'tamamlandı') {
      return res.status(400).json({
        success: false,
        error: 'Sevkiyat zaten tamamlanmış'
      });
    }

    // Transaction başlat
    const connection = await pool.getConnection();
    await connection.beginTransaction();

    try {
      // Sevkiyatı tamamlandı olarak işaretle
      const [updateResult] = await connection.execute(
        `UPDATE sevkiyatlar 
         SET status = 'tamamlandı', 
             tamamlanma_zamani = NOW(),
             tamamlayan_kullanici_id = ?,
             tamamlandi_mi = 1,
             guncelleme_zamani = NOW()
         WHERE id = ?`,
        [tamamlayan_kullanici_id, sevkiyat_id]
      );

      if (updateResult.affectedRows === 0) {
        throw new Error('Sevkiyat güncellenemedi');
      }

      // Sevkiyat bilgilerini al (sevkiyatGecmis tablosu için)
      const [sevkiyatBilgi] = await connection.execute(
        `SELECT s.sevkiyat_ID, s.musteri_ID, s.olusturma_zamani, 
                cb.mgz24_kodu
         FROM sevkiyatlar s
         LEFT JOIN cihazBilgi cb ON cb.sevkiyat_ID = s.id
         WHERE s.id = ?
         LIMIT 1`,
        [sevkiyat_id]
      );

      // Sevkiyat geçmişi tablosuna kayıt ekle (notlar ile birlikte)
      if (sevkiyatBilgi.length > 0 && sevkiyatBilgi[0].cihaz_kodu) {
        await connection.execute(
          `INSERT INTO sevkiyatGecmis (
             cihaz_kodu, sevkiyat_ID, baslangic_zamani, bitis_zamani,
             musteri_ID, durum, notlar
           ) VALUES (?, ?, ?, NOW(), ?, 'tamamlandi', ?)`,
          [
            sevkiyatBilgi[0].cihaz_kodu,
            sevkiyat_id,
            sevkiyatBilgi[0].olusturma_zamani,
            sevkiyatBilgi[0].musteri_ID,
            notlar || 'Sevkiyat başarıyla tamamlandı'
          ]
        );
      }

      // İlgili cihazları inaktif yap (notlar kolonu yok, sadece temel alanları güncelle)
      await connection.execute(
        `UPDATE cihazBilgi 
         SET aktif = FALSE, 
             son_kullanim_tarihi = NOW(),
             guncelleme_zamani = NOW()
         WHERE sevkiyat_ID = ?`,
        [sevkiyat_id]
      );

      // Transaction'ı tamamla
      await connection.commit();

      res.json({
        success: true,
        message: 'Sevkiyat başarıyla tamamlandı',
        sevkiyat_id: sevkiyat_id,
        tamamlanma_zamani: new Date().toISOString()
      });

    } catch (error) {
      // Hata durumunda rollback yap
      await connection.rollback();
      throw error;
    } finally {
      // Bağlantıyı serbest bırak
      connection.release();
    }

  } catch (error) {
    console.error('Sevkiyat tamamlanırken hata:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// =========================
// EXTERNAL API ROUTES
// =========================

// External API routes'ları ekle
app.use('/api/external', externalApiRoutes);

// Monitoring routes'ları ekle
app.use('/api/monitoring', monitoringRoutes);

// =========================
// LEGACY API INTEGRATION
// =========================

// Mevcut API'lerin external API ile entegrasyonu
app.get('/api/cihaz-veri/:cihazID', authenticateToken, async (req, res) => {
  try {
    const { cihazID } = req.params;
    const musteriID = req.user.musteri_ID || req.user.id;

    // Önce external API'den güncel veriyi getir
    const externalData = await externalApiService.fetchDeviceStatus(cihazID);

    // Lokal database'i güncelle
    await externalApiService.syncDeviceToLocal(cihazID);

    // Yetki kontrolü
    const [authCheck] = await pool.execute(
      'SELECT s.musteri_ID FROM cihazBilgi cb LEFT JOIN sevkiyatlar s ON cb.sevkiyat_ID = s.id WHERE cb.mgz24_kodu = ?',
      [cihazID]
    );

    if (authCheck.length === 0 || authCheck[0].musteri_ID !== musteriID) {
      return res.status(403).json({
        success: false,
        message: 'Bu cihaza erişim yetkiniz yok'
      });
    }

    // Lokal database'den veriyi getir
    const [localData] = await pool.execute(
      `SELECT * FROM cihazBilgi 
       WHERE cihaz_kodu = ? 
       ORDER BY son_kontrol DESC 
       LIMIT 1`,
      [cihazID]
    );

    res.json({
      success: true,
      data: {
        external: externalData,
        local: localData[0] || null,
        timestamp: new Date()
      }
    });

  } catch (error) {
    console.error(`Error fetching device data for ${req.params.cihazID}:`, error);
    res.status(500).json({
      success: false,
      message: 'Cihaz verisi alınırken hata oluştu: ' + error.message
    });
  }
});

// Cihaz detayları - external API entegrasyonu
app.get('/api/cihaz-detay/:cihazID', authenticateToken, async (req, res) => {
  try {
    const { cihazID } = req.params;
    const musteriID = req.user.musteri_ID || req.user.id;

    // External API'den detaylı bilgi getir
    const externalDetail = await externalApiService.fetchDeviceDetail(cihazID);

    // Yetki kontrolü
    if (externalDetail.simBilgisi && externalDetail.simBilgisi.musteriID !== musteriID) {
      return res.status(403).json({
        success: false,
        message: 'Bu cihaza erişim yetkiniz yok'
      });
    }

    // Lokal database'i güncelle
    await externalApiService.syncDeviceToLocal(cihazID);

    // Cihaz atama durumunu kontrol et
    const [assignmentInfo] = await pool.execute(
      `SELECT 
        cb.sevkiyat_id,
        s.sevkiyat_adi,
        s.plaka_no,
        s.durum as sevkiyat_durum
       FROM cihazBilgi cb
       LEFT JOIN sevkiyatlar s ON cb.sevkiyat_id = s.id
       WHERE cb.mgz24_kodu = ?`,
      [cihazID]
    );

    res.json({
      success: true,
      data: {
        ...externalDetail,
        assignmentInfo: assignmentInfo[0] || null
      }
    });

  } catch (error) {
    console.error(`Error fetching device detail for ${req.params.cihazID}:`, error);
    res.status(500).json({
      success: false,
      message: 'Cihaz detayı alınırken hata oluştu: ' + error.message
    });
  }
});

// Cihaz bilgisi getirme endpoint (cihazBilgi tablosundan)
app.get('/api/cihaz-bilgi/:cihazKodu', /* authenticateToken, */ async (req, res) => {
  try {
    const { cihazKodu } = req.params;

    // cihazBilgi tablosundan kullanıcı bilgileri ile birlikte getir
    const [rows] = await pool.execute(
      `SELECT 
        cb.mgz24_kodu,
        cb.kullanici_ID,
        cb.aktif,
        cb.pil_seviyesi,
        cb.kontor_sonu,
        k.musteri_adi,
        k.firma
      FROM cihazBilgi cb
      LEFT JOIN kullanicilar k ON cb.kullanici_ID = k.musteri_ID
      WHERE cb.mgz24_kodu = ?`,
      [cihazKodu]
    );

    if (rows.length === 0) {
      return res.json({
        success: true,
        data: null,
        message: 'Cihaz cihazBilgi tablosunda bulunamadı'
      });
    }

    const cihazBilgi = rows[0];

    // Kontor sonu tarihini DD/MM/YY HH:MM formatına çevir
    let formattedKontorSonu = null;
    if (cihazBilgi.kontor_sonu) {
      const date = new Date(cihazBilgi.kontor_sonu);
      const dd = String(date.getDate()).padStart(2, '0');
      const mm = String(date.getMonth() + 1).padStart(2, '0');
      const yy = String(date.getFullYear()).slice(-2);
      const hh = String(date.getHours()).padStart(2, '0');
      const min = String(date.getMinutes()).padStart(2, '0');
      formattedKontorSonu = `${dd}/${mm}/${yy} ${hh}:${min}`;
    }

    res.json({
      success: true,
      data: {
        cihazKodu: cihazBilgi.cihaz_kodu,
        kullaniciID: cihazBilgi.kullanici_ID,
        kullaniciAdi: cihazBilgi.musteri_adi,
        firma: cihazBilgi.firma,
        kullanim: cihazBilgi.aktif ? 'Aktif' : 'İnaktif',
        pilSeviyesi: cihazBilgi.pil_seviyesi,
        sonKullanimTarihi: formattedKontorSonu
      }
    });

  } catch (error) {
    console.error(`Cihaz bilgisi getirme hatası (${req.params.cihazKodu}):`, error);
    res.status(500).json({
      success: false,
      message: 'Cihaz bilgisi alınırken hata oluştu: ' + error.message
    });
  }
});

// Tüm cihazlar listesi endpoint (cihazBilgi + sevkiyatlar JOIN) - Admin only
app.get('/api/all-devices', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const [rows] = await pool.execute(
      `SELECT 
        cb.mgz24_kodu,
        cb.kullanici_ID,
        cb.sevkiyat_ID,
        cb.aktif,
        cb.pil_seviyesi,
        cb.kontor_sonu,
        k.musteri_adi as kullanici_adi,
        k.firma,
        s.sevkiyat_adi,
        s.plaka_no,
        s.cikis_lokasyon,
        s.varis_lokasyon,
        s.durum as sevkiyat_durum,
        s.olusturma_zamani
      FROM cihazBilgi cb
      LEFT JOIN kullanicilar k ON cb.kullanici_ID = k.musteri_ID
      LEFT JOIN sevkiyatlar s ON cb.sevkiyat_ID = s.id
      ORDER BY cb.mgz24_kodu`
    );

    // Kontor sonu tarihlerini formatla
    const formattedDevices = rows.map(device => {
      let formattedKontorSonu = null;
      if (device.kontor_sonu) {
        const date = new Date(device.kontor_sonu);
        const dd = String(date.getDate()).padStart(2, '0');
        const mm = String(date.getMonth() + 1).padStart(2, '0');
        const yy = String(date.getFullYear()).slice(-2);
        const hh = String(date.getHours()).padStart(2, '0');
        const min = String(date.getMinutes()).padStart(2, '0');
        formattedKontorSonu = `${dd}/${mm}/${yy} ${hh}:${min}`;
      }

      let formattedOlusturmaTarihi = null;
      if (device.olusturma_zamani) {
        const date = new Date(device.olusturma_zamani);
        const dd = String(date.getDate()).padStart(2, '0');
        const mm = String(date.getMonth() + 1).padStart(2, '0');
        const yy = String(date.getFullYear()).slice(-2);
        formattedOlusturmaTarihi = `${dd}/${mm}/${yy}`;
      }

      return {
        cihazKodu: device.mgz24_kodu,
        kullaniciID: device.kullanici_ID,
        sevkiyatID: device.sevkiyat_ID,
        aktif: device.aktif,
        pilSeviyesi: device.pil_seviyesi,
        kontorSonu: formattedKontorSonu,
        kullaniciAdi: device.kullanici_adi,
        firma: device.firma,
        sevkiyatAdi: device.sevkiyat_adi,
        plakaNo: device.plaka_no,
        cikisLokasyon: device.cikis_lokasyon,
        varisLokasyon: device.varis_lokasyon,
        sevkiyatDurum: device.sevkiyat_durum,
        olusturmaTarihi: formattedOlusturmaTarihi,
        nakliyeci: null
      };
    });

    res.json({
      success: true,
      data: formattedDevices,
      count: formattedDevices.length
    });

  } catch (error) {
    console.error('Tüm cihazlar getirme hatası:', error);
    res.status(500).json({
      success: false,
      message: 'Cihazlar alınırken hata oluştu: ' + error.message
    });
  }
});

// Pil gerilimini yüzdeye çeviren fonksiyon
function calculateBatteryPercentage(voltage) {
  if (!voltage || voltage <= 0) return null;

  // 4.2V = %100, 3.4V = %30
  // %30'un altında cihaz çalışmaz
  const maxVoltage = 4.2;  // %100
  const minVoltage = 3.4;  // %30
  const minPercentage = 30;
  const maxPercentage = 100;

  // Gerilim 3.4V'un altındaysa %0
  if (voltage < minVoltage) {
    return 0;
  }

  // Gerilim 4.2V'un üstündeyse %100
  if (voltage >= maxVoltage) {
    return 100;
  }

  // Linear interpolation: 3.4V-4.2V arası %30-100 arası
  const voltageRange = maxVoltage - minVoltage; // 0.8V
  const percentageRange = maxPercentage - minPercentage; // 70%

  const percentage = minPercentage + ((voltage - minVoltage) / voltageRange) * percentageRange;

  return Math.round(percentage);
}

// İnaktif cihazlar listesi endpoint - detaylı bilgilerle
app.get('/api/inactive-devices', authenticateToken, async (req, res) => {
  try {
    let query = `SELECT 
        cb.mgz24_kodu,
        cb.cihaz_adi,
        cb.pil_seviyesi,
        cb.sevkiyat_ID,
        cb.olusturma_zamani,
        cb.guncelleme_zamani,
        cb.aktif,
        cb.son_kullanim_tarihi,
        cb.kullanici_ID,
        cb.kontor_sonu,
        s.sevkiyat_ID as sevkiyat_no,
        s.cikis_lokasyon,
        s.varis_lokasyon,
        s.urun_bilgisi,
        s.surucu_adi as nakliyeci,
        s.olusturma_zamani as sevkiyat_tarihi,
        s.tamamlanma_zamani as sevkiyat_bitis_tarihi
      FROM cihazBilgi cb
      LEFT JOIN sevkiyatlar s ON cb.sevkiyat_ID = s.id
      WHERE cb.aktif = 0`;

    const params = [];

    // Role-based filtering - non-admin users can only see devices assigned to them
    if (!isAdmin(req)) {
      query += ` AND cb.kullanici_ID = ?`;
      params.push(req.user.userId);
    }

    query += ` ORDER BY cb.son_kullanim_tarihi DESC, cb.mgz24_kodu`;

    const [rows] = await pool.execute(query, params);

    // Verileri formatla
    const formattedDevices = rows.map(device => {
      // Kontor sonu tarihini formatla
      let formattedKontorSonu = null;
      if (device.kontor_sonu) {
        const date = new Date(device.kontor_sonu);
        const dd = String(date.getDate()).padStart(2, '0');
        const mm = String(date.getMonth() + 1).padStart(2, '0');
        const yy = String(date.getFullYear()).slice(-2);
        const hh = String(date.getHours()).padStart(2, '0');
        const min = String(date.getMinutes()).padStart(2, '0');
        formattedKontorSonu = `${dd}/${mm}/${yy} ${hh}:${min}`;
      }

      // Son kullanım tarihini formatla
      let formattedSonKullanim = null;
      if (device.son_kullanim_tarihi) {
        const date = new Date(device.son_kullanim_tarihi);
        const dd = String(date.getDate()).padStart(2, '0');
        const mm = String(date.getMonth() + 1).padStart(2, '0');
        const yy = String(date.getFullYear()).slice(-2);
        const hh = String(date.getHours()).padStart(2, '0');
        const min = String(date.getMinutes()).padStart(2, '0');
        formattedSonKullanim = `${dd}/${mm}/${yy} ${hh}:${min}`;
      }

      // Sevkiyat tarihini formatla
      let formattedSevkiyatTarihi = null;
      if (device.sevkiyat_tarihi) {
        const date = new Date(device.sevkiyat_tarihi);
        const dd = String(date.getDate()).padStart(2, '0');
        const mm = String(date.getMonth() + 1).padStart(2, '0');
        const yy = String(date.getFullYear()).slice(-2);
        formattedSevkiyatTarihi = `${dd}/${mm}/${yy}`;
      }

      // Sevkiyat bitiş tarihini formatla
      let formattedSevkiyatBitis = null;
      if (device.sevkiyat_bitis_tarihi) {
        const date = new Date(device.sevkiyat_bitis_tarihi);
        const dd = String(date.getDate()).padStart(2, '0');
        const mm = String(date.getMonth() + 1).padStart(2, '0');
        const yy = String(date.getFullYear()).slice(-2);
        formattedSevkiyatBitis = `${dd}/${mm}/${yy}`;
      }

      return {
        cihazKodu: device.mgz24_kodu,
        cihazAdi: device.cihaz_adi || 'İsimsiz Cihaz',
        model: 'MGZ24 Tracker', // Tablo yapısında model kolonu yok, sabit değer
        durum: device.aktif ? 'aktif' : 'pasif',
        pilSeviyesi: calculateBatteryPercentage(device.pil_seviyesi),
        pilGerilimi: device.pil_seviyesi, // Ham gerilim değeri de gönderelim
        sicaklik: null, // Tablo yapısında yok
        nem: null, // Tablo yapısında yok
        isik: null, // Tablo yapısında yok
        konum: {
          enlem: null, // Tablo yapısında yok
          boylam: null, // Tablo yapısında yok
          yukseklik: null, // Tablo yapısında yok
          dogruluk: null // Tablo yapısında yok
        },
        darbeBuyuklugu: null, // Tablo yapısında yok
        kapiDurumu: null, // Tablo yapısında yok
        kapiAcikKalmaSuresi: null, // Tablo yapısında yok
        kontorSonu: device.kontor_sonu, // JavaScript Date için raw tarih
        sonKontrol: formattedKontorSonu,
        sonKullanim: formattedSonKullanim,
        enSonSevkiyat: device.sevkiyat_ID ? {
          sevkiyatNo: device.sevkiyat_no || device.sevkiyat_ID,
          cikisLokasyon: device.cikis_lokasyon,
          varisLokasyon: device.varis_lokasyon,
          urunBilgisi: device.urun_bilgisi,
          nakliyeci: device.nakliyeci,
          sevkiyatTarihi: formattedSevkiyatTarihi,
          bitisTarihi: formattedSevkiyatBitis
        } : null
      };
    });

    res.json({
      success: true,
      data: formattedDevices,
      count: formattedDevices.length
    });

  } catch (error) {
    console.error('İnaktif cihazlar getirme hatası:', error);
    res.status(500).json({
      success: false,
      message: 'İnaktif cihazlar alınırken hata oluştu: ' + error.message
    });
  }
});

// Cihaz kullanıcı atama endpoint
app.put('/api/cihaz-bilgi/:cihazKodu/assign', /* authenticateToken, */ async (req, res) => {
  try {
    const { cihazKodu } = req.params;
    const { musteriId, pilSeviyesi } = req.body;

    console.log('=== Cihaz Atama İsteği Başladı ===');
    console.log('Cihaz kodu:', cihazKodu);
    console.log('Müşteri ID:', musteriId);
    console.log('Pil seviyesi:', pilSeviyesi);
    console.log('Gelen token:', req.headers['authorization']);
    console.log('Decoded user:', req.user);

    // Yetki kontrolü - geçici olarak devre dışı
    // if (req.user.gorev !== 'admin') {
    //   return res.status(403).json({
    //     success: false,
    //     message: 'Bu işlem için admin yetkisi gereklidir'
    //   });
    // }

    // Kullanıcının var olup olmadığını kontrol et
    console.log('Kullanıcı kontrolü yapılıyor...');
    const [userCheck] = await pool.execute(
      'SELECT musteri_ID, musteri_adi FROM kullanicilar WHERE musteri_ID = ?',
      [musteriId]
    );

    console.log('Kullanıcı kontrol sonucu:', userCheck);

    if (userCheck.length === 0) {
      console.log('Kullanıcı bulunamadı!');
      return res.status(404).json({
        success: false,
        message: 'Kullanıcı bulunamadı'
      });
    }

    // Cihaz bilgisini güncelle veya yeni kayıt ekle
    console.log('Cihaz bilgisi güncelleniyor...');

    // pilSeviyesi undefined ise null olarak ayarla
    const pilSeviyesiValue = pilSeviyesi !== undefined ? pilSeviyesi : null;

    const [updateResult] = await pool.execute(
      `INSERT INTO cihazBilgi (mgz24_kodu, kullanici_ID, pil_seviyesi, son_kullanim_tarihi, aktif) 
       VALUES (?, ?, ?, NOW(), 0)
       ON DUPLICATE KEY UPDATE 
       kullanici_ID = VALUES(kullanici_ID),
       pil_seviyesi = VALUES(pil_seviyesi), 
       son_kullanim_tarihi = NOW()`,
      [cihazKodu, musteriId, pilSeviyesiValue]
    );

    console.log('Güncelleme sonucu:', updateResult);

    const response = {
      success: true,
      message: `Cihaz ${cihazKodu} kullanıcı ${userCheck[0].musteri_adi} (ID: ${musteriId}) tarafından atandı`,
      data: {
        cihazKodu: cihazKodu,
        musteriId: musteriId,
        musteriAdi: userCheck[0].musteri_adi
      }
    };

    console.log('Yanıt gönderiliyor:', response);
    res.json(response);

  } catch (error) {
    console.error('=== Cihaz Atama Hatası ===');
    console.error('Hata mesajı:', error.message);
    console.error('Hata stack:', error.stack);
    console.error('Hata detayları:', error);

    res.status(500).json({
      success: false,
      message: 'Cihaz atama işlemi sırasında hata oluştu: ' + error.message
    });
  }
});

// External API proxy endpoint
app.get('/external/cihaz/:cihazID', async (req, res) => {
  try {
    const { cihazID } = req.params;

    // External API'den cihaz bilgisini proxy ile getir
    const externalDetail = await externalApiService.fetchDeviceDetail(cihazID);

    res.json({
      success: true,
      data: externalDetail
    });

  } catch (error) {
    console.error(`External API proxy error for device ${req.params.cihazID}:`, error);
    res.status(500).json({
      success: false,
      message: 'External API proxy hatası: ' + error.message
    });
  }
});

// Kullanıcının tüm cihazlarını getir (aktif + inaktif)
app.get('/api/user-devices', authenticateToken, async (req, res) => {
  try {
    const { userId } = req.query;
    const requestUserId = userId || req.user.musteri_ID || req.user.id;

    console.log('User devices request for userId:', requestUserId);

    if (!requestUserId) {
      return res.status(400).json({
        error: 'User ID gerekli',
        debug: { userId, userFromToken: req.user }
      });
    }

    // Kullanıcının tüm cihazlarını getir (aktif ve inaktif)
    const query = `
      SELECT 
        cb.id as ID,
        cb.mgz24_kodu as CihazID,
        cb.aktif,
        cb.pil_seviyesi,
        cb.kontor_sonu,
        cb.cihaz_adi,
        CASE 
          WHEN cb.aktif = 1 THEN 'Aktif'
          ELSE 'İnaktif'
        END as durum,
        CASE 
          WHEN cb.kontor_sonu IS NULL OR cb.kontor_sonu <= NOW() THEN 0
          ELSE DATEDIFF(cb.kontor_sonu, NOW())
        END as kredi_gun
      FROM cihazBilgi cb
      WHERE cb.kullanici_ID = ?
      ORDER BY cb.aktif DESC, cb.mgz24_kodu ASC
    `;

    const [rows] = await executeQuery(query, [requestUserId]);

    console.log(`Found ${rows.length} devices for user ${requestUserId}`);

    res.json(rows);
  } catch (error) {
    console.error('Kullanıcı cihazları alınırken hata:', error);
    res.status(500).json({ error: error.message });
  }
});

// Test endpoint right after last working endpoint
app.get('/api/debug-route-test', (req, res) => {
  res.json({ success: true, message: 'Debug route test working' });
});

// PayTR Ödeme Sistemi Entegrasyonu
// ========================================

// PayTR ayarlarını veritabanından al
const getPayTRSettings = async () => {
  try {
    const [rows] = await executeQuery(
      `SELECT ayar_anahtari, ayar_degeri, ayar_tipi FROM sistem_ayarlari 
       WHERE ayar_anahtari LIKE 'paytr_%'`
    );

    const settings = {};
    rows.forEach(row => {
      let value = row.ayar_degeri;

      // Değer tipine göre dönüştür
      if (row.ayar_tipi === 'boolean') {
        value = value === '1' || value === 'true';
      } else if (row.ayar_tipi === 'sayi') {
        value = parseInt(value);
      }

      settings[row.ayar_anahtari] = value;
    });

    return settings;
  } catch (error) {
    console.error('PayTR ayarları alınırken hata:', error);
    throw new Error('PayTR ayarları alınamadı');
  }
};

// PayTR hash oluştur - Iframe API için
const createPayTRHash = (data, merchantSalt, merchantKey) => {
  // Test merchant bilgileri
  const testMerchantKey = 'P2inRKCu37NEMmQG';
  const testMerchantSalt = 'auPuTfWWLTP57TH3';

  // PayTR normal iframe API hash formatı (resmi örneğe göre - payment_type YOK)
  const hashSTR = data.merchant_id +
    data.user_ip +
    data.merchant_oid +
    data.email +
    data.payment_amount +
    data.user_basket +
    data.no_installment +
    data.max_installment +
    data.currency +
    data.test_mode;

  console.log('PayTR Iframe API Hash Debug:');
  console.log('merchant_id:', data.merchant_id);
  console.log('user_ip:', data.user_ip);
  console.log('merchant_oid:', data.merchant_oid);
  console.log('email:', data.email);
  console.log('payment_amount:', data.payment_amount);
  console.log('user_basket:', data.user_basket);
  console.log('no_installment:', data.no_installment);
  console.log('max_installment:', data.max_installment);
  console.log('currency:', data.currency);
  console.log('test_mode:', data.test_mode);
  console.log('Hash STR:', hashSTR);

  // Token format: hashSTR + merchant_salt
  const paytr_token = hashSTR + (data.test_mode === '1' ? testMerchantSalt : merchantSalt);
  console.log('PayTR Token String:', paytr_token);

  // HMAC-SHA256 with merchant_key
  const hash = crypto.createHmac('sha256', data.test_mode === '1' ? testMerchantKey : merchantKey)
    .update(paytr_token)
    .digest('base64');

  console.log('Generated hash (Iframe API):', hash);

  return hash;
};

// Test endpoint for debugging
app.post('/api/test-paytr', (req, res) => {
  res.json({ success: true, message: 'PayTR test endpoint working' });
});

// PayTR ödeme token oluştur
app.post('/api/payments/paytr/create-token', async (req, res) => {
  try {
    const {
      user_id,
      device_id,
      package_info,
      amount,
      currency,
      user_name,
      user_email,
      user_phone,
      user_basket
    } = req.body;

    // PayTR ayarlarını al
    const paytrSettings = await getPayTRSettings();

    console.log('PayTR Settings:', {
      merchant_id: paytrSettings.paytr_merchant_id ? 'SET' : 'MISSING',
      merchant_key: paytrSettings.paytr_merchant_key ? 'SET' : 'MISSING',
      merchant_salt: paytrSettings.paytr_merchant_salt ? 'SET' : 'MISSING',
      test_mode: paytrSettings.paytr_test_mode
    });

    // PayTR ayarları kontrolü - gerçek merchant bilgileri varsa kullan
    const hasRealMerchantInfo = paytrSettings.paytr_merchant_id && 
                               paytrSettings.paytr_merchant_key && 
                               paytrSettings.paytr_merchant_salt;
    
    // Gerçek merchant bilgileri varsa test modu false yap
    const isTestMode = hasRealMerchantInfo ? (paytrSettings.paytr_test_mode === true) : true;
    
    console.log('PayTR Merchant Info Check:', {
      hasRealMerchantInfo,
      isTestMode,
      test_mode_setting: paytrSettings.paytr_test_mode
    });
    
    if (!hasRealMerchantInfo && !isTestMode) {
      return res.status(500).json({
        success: false,
        message: 'PayTR merchant bilgileri eksik.',
        missing: {
          merchant_id: !paytrSettings.paytr_merchant_id,
          merchant_key: !paytrSettings.paytr_merchant_key,
          merchant_salt: !paytrSettings.paytr_merchant_salt
        }
      });
    }

    // Benzersiz sipariş ID oluştur (sadece alfanumerik karakterler)
    const merchant_oid = 'MGZ24' + Date.now() + Math.random().toString(36).substring(2, 8).toUpperCase();

    // Kullanıcının IP adresini al
    const user_ip = req.ip || req.connection.remoteAddress || '127.0.0.1';

    // PayTR Iframe API için ödeme verilerini hazırla (Base64 encoded user_basket gerekli)
    const userBasket = Buffer.from(JSON.stringify([
      [package_info?.name || 'MGZ24 Kullanım Paketi', (amount).toFixed(2), 1]
    ])).toString('base64');

    const paymentData = {
      merchant_id: isTestMode ? '170578' : paytrSettings.paytr_merchant_id,
      merchant_key: isTestMode ? 'P2inRKCu37NEMmQG' : paytrSettings.paytr_merchant_key,
      merchant_salt: isTestMode ? 'auPuTfWWLTP57TH3' : paytrSettings.paytr_merchant_salt,
      user_ip: user_ip.replace('::ffff:', ''), // IPv6 prefix'ini temizle
      merchant_oid: merchant_oid,
      email: user_email || '<EMAIL>',
      payment_amount: Math.round(amount * 100), // Kuruş cinsinden - number olarak
      user_basket: userBasket, // Base64 encoded basket
      no_installment: '0', // String olarak
      max_installment: (paytrSettings.paytr_installment_limit || 0).toString(),
      currency: 'TL', // PayTR sadece TL destekler
      test_mode: isTestMode ? '1' : '0', // String olarak
      user_name: user_name || 'Müşteri',
      user_address: 'Türkiye',
      user_phone: user_phone || '5555555555',
      merchant_ok_url: `${process.env.FRONTEND_URL || 'https://mgz24.com'}/#/payment?status=success&order_id=${merchant_oid}`,
      merchant_fail_url: `${process.env.FRONTEND_URL || 'https://mgz24.com'}/#/payment?status=failed&order_id=${merchant_oid}`,
      timeout_limit: (paytrSettings.paytr_timeout_limit || 30).toString(),
      debug_on: paytrSettings.paytr_debug_on ? '1' : '0',
      lang: paytrSettings.paytr_lang || 'tr'
    };

    // Hash oluştur (tüm parametreler hazır olduktan sonra)
    const merchantSalt = isTestMode ? 'auPuTfWWLTP57TH3' : paytrSettings.paytr_merchant_salt;
    const merchantKey = isTestMode ? 'P2inRKCu37NEMmQG' : paytrSettings.paytr_merchant_key;
    
    paymentData.paytr_token = createPayTRHash(paymentData, merchantSalt, merchantKey);

    console.log('PayTR Iframe API\'ye gönderilecek veri:', {
      merchant_id: paymentData.merchant_id,
      user_ip: paymentData.user_ip,
      merchant_oid: paymentData.merchant_oid,
      email: paymentData.email,
      payment_amount: paymentData.payment_amount,
      currency: paymentData.currency,
      test_mode: paymentData.test_mode,
      user_name: paymentData.user_name,
      paytr_token: paymentData.paytr_token ? 'SET' : 'MISSING'
    });

    // PayTR Iframe API'den token al
    
    try {
      // FormData oluştur (PayTR resmi iFrame API dokümantasyonuna göre)
      const formData = new URLSearchParams();
      formData.append('merchant_id', paymentData.merchant_id);
      formData.append('user_ip', paymentData.user_ip);
      formData.append('merchant_oid', paymentData.merchant_oid);
      formData.append('email', paymentData.email);
      formData.append('payment_amount', paymentData.payment_amount);
      formData.append('currency', paymentData.currency);
      formData.append('user_basket', paymentData.user_basket);
      formData.append('no_installment', paymentData.no_installment);
      formData.append('max_installment', paymentData.max_installment);
      formData.append('test_mode', paymentData.test_mode);
      formData.append('paytr_token', paymentData.paytr_token);

      // PayTR resmi Node.js örneğinde payment_type parametresi YOK
      
      // Opsiyonel parametreler (PayTR resmi Node.js örneğine göre)
      formData.append('user_name', paymentData.user_name || '');
      formData.append('user_address', paymentData.user_address || '');
      formData.append('user_phone', paymentData.user_phone || '');
      formData.append('merchant_ok_url', paymentData.merchant_ok_url || '');
      formData.append('merchant_fail_url', paymentData.merchant_fail_url || '');
      formData.append('timeout_limit', paymentData.timeout_limit || '30');
      formData.append('debug_on', paymentData.debug_on || '1');
      formData.append('lang', paymentData.lang || 'tr');

      console.log('PayTR\'ye gönderilen tüm parametreler:');
      for (const [key, value] of formData.entries()) {
        console.log(`${key}: ${value}`);
      }

      const response = await fetch('https://www.paytr.com/odeme/api/get-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: formData.toString()
      });

      const responseData = await response.json();
      console.log('PayTR API yanıtı:', responseData);

      // PayTR API yanıtını kontrol et
      if (!response.ok) {
        console.error('PayTR API HTTP hatası:', response.status, response.statusText);
        console.error('PayTR API hata yanıtı:', responseData);
        throw new Error(`PayTR API HTTP ${response.status}: ${responseData.reason || response.statusText}`);
      }

      if (responseData.status === 'success') {
        // Ödeme kaydını veritabanına ekle (bekleme durumunda)
        await executeQuery(
          `INSERT INTO kullanici_odemeleri (
            islem_id, kullanici_id, paket_id, paket_adi, tutar_eur, tutar_tl, 
            doviz_kuru, odeme_tarihi, durum, satin_alinan_gun, odeme_yontemi, saglayici_islem_id
          ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), 'beklemede', ?, 'paytr', ?)`,
          [
            merchant_oid,
            user_id,
            package_info?.id || 1,
            package_info?.name || 'MGZ24 Kullanım Paketi',
            currency === 'EUR' ? amount : 0,
            amount,
            1,
            package_info?.days || 30,
            'iframe_' + merchant_oid
          ]
        );

        // Iframe token döndür
        res.json({
          success: true,
          merchant_oid: merchant_oid,
          test_mode: isTestMode,
          iframe_token: responseData.token
        });
      } else {
        console.error('PayTR token oluşturma başarısız:', responseData);
        throw new Error(responseData.reason || 'PayTR token alınamadı');
      }
    } catch (apiError) {
      console.error('PayTR API hatası:', apiError.message);
      throw new Error('PayTR API isteği başarısız: ' + apiError.message);
    }

  } catch (error) {
    console.error('PayTR token oluşturma hatası:', error);
    console.error('Hata detayı:', error.response?.data || error.message);
    res.status(500).json({
      success: false,
      message: error.message || 'PayTR ödeme token oluşturulamadı',
      error_detail: error.response?.data || error.message
    });
  }
});

// PayTR başarılı ödeme callback
app.post('/api/payments/paytr/callback', async (req, res) => {
  try {
    const {
      merchant_oid,
      status,
      total_amount,
      hash,
      failed_reason_code,
      failed_reason_msg,
      test_mode,
      payment_type,
      currency,
      payment_amount
    } = req.body;

    console.log('PayTR callback alındı:', {
      merchant_oid,
      status,
      total_amount,
      hash: hash ? 'SET' : 'MISSING',
      failed_reason_msg,
      payment_type,
      currency,
      payment_amount,
      test_mode,
      failed_reason_code
    });

    // Tüm callback verilerini logla
    console.log('PayTR callback tüm veriler:', req.body);

    // PayTR ayarlarını al
    const paytrSettings = await getPayTRSettings();

    // Hash doğrulama (PayTR resmi dokümantasyonuna göre)
    const merchantSalt = test_mode === '1' ? 'auPuTfWWLTP57TH3' : paytrSettings.paytr_merchant_salt;
    const merchantKey = test_mode === '1' ? 'P2inRKCu37NEMmQG' : paytrSettings.paytr_merchant_key;
    
    if (hash) {
      // PayTR callback hash formatı: merchant_oid + merchant_salt + status + total_amount
      const paytr_token = merchant_oid + merchantSalt + status + total_amount;
      const expectedHash = crypto.createHmac('sha256', merchantKey)
        .update(paytr_token)
        .digest('base64');

      if (hash !== expectedHash) {
        console.error('PayTR hash doğrulama başarısız');
        console.error('Expected hash:', expectedHash);
        console.error('Received hash:', hash);
        console.error('Test mode:', test_mode);
        console.error('Token string:', paytr_token);
        console.error('Merchant salt:', merchantSalt);
        console.error('Merchant key:', merchantKey ? 'SET' : 'MISSING');
        return res.status(400).send('FAIL');
      }
      console.log('PayTR hash doğrulama başarılı');
    } else {
      console.log('PayTR callback - hash bilgisi yok, devam ediliyor');
    }

    // Ödeme kaydını bul ve güncelle
    const [paymentRows] = await executeQuery(
      'SELECT * FROM kullanici_odemeleri WHERE islem_id = ?',
      [merchant_oid]
    );

    if (paymentRows.length === 0) {
      console.error('PayTR callback: Ödeme kaydı bulunamadı:', merchant_oid);
      return res.status(404).send('FAIL');
    }

    const payment = paymentRows[0];

    if (status === 'success') {
      // Ödeme başarılı - kaydı güncelle
      await executeQuery(
        'UPDATE kullanici_odemeleri SET durum = ?, guncelleme_tarihi = NOW() WHERE islem_id = ?',
        ['tamamlandi', merchant_oid]
      );

      console.log(`PayTR ödeme başarılı: ${merchant_oid}, Tutar: ${total_amount}`);
      res.send('OK');
    } else {
      // Ödeme başarısız
      const errorMessage = failed_reason_msg || 'Bilinmeyen hata';

      await executeQuery(
        'UPDATE kullanici_odemeleri SET durum = ?, guncelleme_tarihi = NOW(), hata_mesaji = ? WHERE islem_id = ?',
        ['basarisiz', errorMessage, merchant_oid]
      );

      console.log(`PayTR ödeme başarısız: ${merchant_oid}, Hata: ${errorMessage}`);
      res.send('OK');
    }

  } catch (error) {
    console.error('PayTR callback hatası:', error);
    res.status(500).send('FAIL');
  }
});

// PayTR ödeme durumu kontrol endpoint'i
app.get('/api/payments/paytr/status/:merchant_oid', authenticateToken, async (req, res) => {
  const { merchant_oid } = req.params;

  try {
    // Ödeme durumunu kontrol et
    const [paymentRows] = await executeQuery(
      'SELECT * FROM kullanici_odemeleri WHERE islem_id = ?',
      [merchant_oid]
    );

    if (paymentRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Ödeme kaydı bulunamadı'
      });
    }

    const payment = paymentRows[0];

    res.json({
      success: true,
      status: payment.durum,
      order_id: merchant_oid,
      amount: payment.tutar_tl,
      currency: 'TL',
      date: payment.odeme_tarihi,
      message: payment.durum === 'tamamlandi' ? 'Ödeme başarıyla tamamlandı' :
        payment.durum === 'basarisiz' ? 'Ödeme başarısız' : 'Ödeme işlemi beklemede'
    });
  } catch (error) {
    console.error('PayTR ödeme durumu kontrol hatası:', error);
    res.status(500).json({
      success: false,
      message: 'Ödeme durumu kontrol edilirken hata oluştu'
    });
  }
});

// PayTR başarı sayfası (redirect için)
app.get('/api/payments/paytr/success', async (req, res) => {
  const { merchant_oid } = req.query;

  if (!merchant_oid) {
    return res.redirect(`${process.env.FRONTEND_URL || 'https://mgz24.com'}/#/payment?status=error&message=Geçersiz sipariş`);
  }

  try {
    // Ödeme durumunu kontrol et
    const [paymentRows] = await executeQuery(
      'SELECT * FROM kullanici_odemeleri WHERE islem_id = ?',
      [merchant_oid]
    );

    if (paymentRows.length > 0 && paymentRows[0].durum === 'tamamlandi') {
      // Frontend'e yönlendir
      res.redirect(`${process.env.FRONTEND_URL || 'https://mgz24.com'}/#/payment?status=success&order_id=${merchant_oid}`);
    } else {
      res.redirect(`${process.env.FRONTEND_URL || 'https://mgz24.com'}/#/payment?status=pending&order_id=${merchant_oid}`);
    }
  } catch (error) {
    console.error('PayTR success sayfası hatası:', error);
    res.redirect(`${process.env.FRONTEND_URL || 'https://mgz24.com'}/#/payment?status=error&message=Sistem hatası`);
  }
});

// PayTR hata sayfası (redirect için)
app.get('/api/payments/paytr/fail', async (req, res) => {
  const { merchant_oid } = req.query;

  try {
    if (merchant_oid) {
      // Ödeme durumunu başarısız olarak güncelle
      await executeQuery(
        'UPDATE kullanici_odemeleri SET durum = ?, guncelleme_tarihi = NOW() WHERE islem_id = ?',
        ['basarisiz', merchant_oid]
      );
    }

    // Frontend'e yönlendir
    res.redirect(`${process.env.FRONTEND_URL || 'https://mgz24.com'}/#/payment?status=failed&order_id=${merchant_oid || ''}`);
  } catch (error) {
    console.error('PayTR fail sayfası hatası:', error);
    res.redirect(`${process.env.FRONTEND_URL || 'https://mgz24.com'}/#/payment?status=error&message=Sistem hatası`);
  }
});

// Server'ı başlat
app.listen(port, '0.0.0.0', async () => {
  console.log(`🚀 MGZ24 Backend Server ${port} portunda başlatıldı`);
  console.log(`📡 Sunucu tüm arayüzlerden erişilebilir (0.0.0.0:${port})`);
  console.log(`🌐 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔗 CORS: mgz24.com domainlerine izin verildi`);
  console.log(`⚠️  Apache proxy yapılandırması: /api → localhost:${port}/api`);
  console.log('✅ Backend hazır ve çalışıyor!');

  // External API servislerini başlat
  await initializeServices();
});
