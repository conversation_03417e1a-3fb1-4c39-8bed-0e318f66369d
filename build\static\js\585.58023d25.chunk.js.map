{"version": 3, "file": "static/js/585.58023d25.chunk.js", "mappings": "gKASA,MAwGA,EAxGgBA,KACZ,MAAMC,GAAWC,EAAAA,EAAAA,MAaXC,EAAuB,UAVTC,MAChB,IAAK,IAADC,EAAAC,EACA,MAAMC,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C,OAAW,OAAJJ,QAAI,IAAJA,GAAU,QAANF,EAAJE,EAAMA,YAAI,IAAAF,OAAN,EAAJA,EAAYO,QAAY,OAAJL,QAAI,IAAJA,GAAU,QAAND,EAAJC,EAAMA,YAAI,IAAAD,OAAN,EAAJA,EAAYO,QAAS,MACpD,CAAE,MAAAC,GACE,MAAO,MACX,GAGaV,GAGjB,OACIW,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC1DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6BAA6BG,SAAS,KAAKC,GAAG,UAAU,kBAAgB,UAASH,SAAA,EAC5FC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,EAC7BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,kBAAkBI,GAAG,UAASH,SAAC,gBAC7CF,EAAAA,EAAAA,KAAA,UAAQM,KAAK,SAASL,UAAU,YAAY,kBAAgB,YAAY,iBAAe,WAAW,aAAW,cAEjHE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mEAAkEC,SAAA,EAC7EF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,kCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,MAAtBtB,EAASuB,SAAmB,SAAW,IAAMC,GAAG,IAAGR,SAAA,EAC5GF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMC,EAAAA,MAAW,0BAG1Cb,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,SAAtBtB,EAASuB,SAAsB,SAAW,IAAMC,GAAG,OAAMR,SAAA,EAClHF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAME,EAAAA,MAAgB,mCAG/Cd,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,sBAAtBtB,EAASuB,SAAmC,SAAW,IAAMC,GAAG,oBAAmBR,SAAA,EAC5IF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMG,EAAAA,MAAY,8BAG3Cf,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMI,EAAAA,MAAmB,sCAKtDhB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wBACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,CAEpCd,IACGY,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMK,EAAAA,MAAa,0BAIhDjB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,mBAAtBtB,EAASuB,SAAgC,SAAW,IAAMC,GAAG,iBAAgBR,SAAA,EACtIF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMM,EAAAA,MAAU,uBAK7ClB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,uCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMO,EAAAA,MAAU,qCAGzCnB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMQ,EAAAA,MAAgB,6CAKnDpB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMS,EAAAA,MAAgB,qBAG/CrB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,cAAtBtB,EAASuB,SAA2B,SAAW,IAAMC,GAAG,YAAWR,SAAA,EAC5HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMU,EAAAA,MAAe,4BAKlDtB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wBAG1I,C,sDC3Gd,MAQA,EAReqB,KAEPvB,EAAAA,EAAAA,KAAA,UAAQC,UAAU,kBAAiBC,UAC/BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,cAAaC,SAAC,iD,qGCEvC,MA6GA,EA7GesB,KACX,MAAMC,GAAWC,EAAAA,EAAAA,OACVC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,YAClCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAGvCG,EAAAA,EAAAA,YAAU,KACgBC,WAClB,IAAK,IAADC,EAAAC,EAAAC,EAAAC,EAEA,MAAMC,EAAa7C,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C2C,GAAmB,OAAVD,QAAU,IAAVA,GAAgB,QAANJ,EAAVI,EAAY9C,YAAI,IAAA0C,OAAN,EAAVA,EAAkBM,cAAwB,OAAVF,QAAU,IAAVA,GAAgB,QAANH,EAAVG,EAAY9C,YAAI,IAAA2C,OAAN,EAAVA,EAAkB9B,IAC3DsB,GAAqB,OAAVW,QAAU,IAAVA,GAAgB,QAANF,EAAVE,EAAY9C,YAAI,IAAA4C,OAAN,EAAVA,EAAkBK,QAAkB,OAAVH,QAAU,IAAVA,GAAgB,QAAND,EAAVC,EAAY9C,YAAI,IAAA6C,OAAN,EAAVA,EAAkBK,aAE7D,IAAKH,EAID,OAHAI,QAAQC,KAAK,kCACbhB,EAAY,gBACZG,GAAW,GAKf,GAAIJ,EAGA,OAFAC,EAAYD,QACZI,GAAW,GAKf,IACI,MAAMc,QAAiBC,EAAAA,GAAiBC,aAAaR,GAGjDM,GAAYA,EAASH,cACrBd,EAAYiB,EAASH,aAGP,OAAVJ,QAAU,IAAVA,GAAAA,EAAY9C,OACZ8C,EAAW9C,KAAKiD,KAAOI,EAASH,YAChC/C,aAAaqD,QAAQ,OAAQvD,KAAKwD,UAAUX,KAGxD,CAAE,MAAOY,GACLP,QAAQC,KAAK,gGAEbhB,EAAYD,GAAY,sBAC5B,CACJ,CAAE,MAAOwB,GACLR,QAAQQ,MAAM,0DAAuCA,GAErDvB,EAAY,sBAChB,CAAC,QACGG,GAAW,EACf,GAGJqB,EAAe,GAChB,IAWH,OACIjD,EAAAA,EAAAA,MAAA,UAAQF,UAAU,0DAA0D,gBAAc,OAAMC,SAAA,EAC5FF,EAAAA,EAAAA,KAACO,EAAAA,GAAI,CAACN,UAAU,gEAAgES,GAAG,IAAGR,UAClFF,EAAAA,EAAAA,KAAA,OAAKqD,I,60RAAeC,IAAI,aAAaC,OAAO,UAEhDpD,EAAAA,EAAAA,MAAA,MAAIF,UAAU,yCAAwCC,SAAA,EAClDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8CAA6CC,UACvDC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,aAAYC,SAAA,EACxBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMO,EAAAA,IAAQlB,UAAU,SACxC6B,EAAU,mBAAkBH,QAGrC3B,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4BAA2BC,UACrCF,EAAAA,EAAAA,KAAA,UACIwD,QAtBCC,KAEjB9D,aAAa+D,WAAW,QAGxBjC,EAAS,SAAS,EAkBFxB,UAAU,gBACV,iBAAe,UACf,oBAAkB,OAClB,gBAAc,+BAAYC,UAE1BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM+C,EAAAA,WAG/B3D,EAAAA,EAAAA,KAAA,MAAIC,UAAU,iCAAgCC,UAC1CF,EAAAA,EAAAA,KAAA,UACIC,UAAU,gBACVK,KAAK,SACL,iBAAe,YACf,iBAAe,WACf,gBAAc,UACd,gBAAc,QACd,aAAW,kBAAcJ,UAEzBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMgD,EAAAA,eAI9B,C,oJChGjB,MA2WA,EA3WwBC,KAEpB,MAAOC,EAAWC,IAAgBlC,EAAAA,EAAAA,UAAS,KACpCmC,EAAmBC,IAAwBpC,EAAAA,EAAAA,UAAS,KACpDC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCsB,EAAOe,IAAYrC,EAAAA,EAAAA,UAAS,OAI5BsC,EAASC,IAAcvC,EAAAA,EAAAA,UAAS,CACnCwC,SAAU,GACVC,cAAe,GACfC,gBAAiB,GACjBC,UAAW,GACXC,QAAS,GACTC,QAAS,KAIPC,EAAahF,aAAaC,QAAQ,QAClCiD,EAAW8B,EAAalF,KAAKC,MAAMiF,GAAc,KACjDnF,EAAOqD,EAAWA,EAASrD,KAAO,KAClCoF,EAAYpF,EAAOA,EAAKgD,WAAa,MAG3CR,EAAAA,EAAAA,YAAU,KACiBC,WACnB,IACIF,GAAW,GACXmC,EAAS,MAGT,MAAMW,QAAaC,EAAAA,GAAgBC,gCAAgCH,GACnEb,EAAac,GACbZ,EAAqBY,GACrB9C,GAAW,EACf,CAAE,MAAOoB,GACLR,QAAQQ,MAAM,qDAAoCA,GAClDe,EAAS,iEAAmDf,EAAM6B,SAClEjD,GAAW,EACf,GAGJkD,EAAgB,GAYjB,CAACL,IAGJ,MAAMM,EAAsBC,IACxB,MAAM,KAAE1C,EAAI,MAAE2C,GAAUD,EAAEE,OAC1BjB,GAAWkB,IAAWC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfD,GAAW,IACd,CAAC7C,GAAO2C,KACT,GAIPpD,EAAAA,EAAAA,YAAU,KACN,IAAIwD,EAAW,IAAI1B,GAGfK,EAAQE,WACRmB,EAAWA,EAASC,QAAOC,IAAM,IAAAC,EAAA,OACZ,QADYA,EAC7BD,EAAOE,kBAAU,IAAAD,OAAA,EAAjBA,EAAmBE,cAAcC,SAAS3B,EAAQE,SAASwB,cAAc,KAK7E1B,EAAQG,gBACRkB,EAAWA,EAASC,QAAOC,IAAM,IAAAK,EAAA,OACJ,QADIA,EAC7BL,EAAOM,0BAAkB,IAAAD,OAAA,EAAzBA,EAA2BF,cAAcC,SAAS3B,EAAQG,cAAcuB,cAAc,KAK1F1B,EAAQI,kBACRiB,EAAWA,EAASC,QAAOC,IAAM,IAAAO,EAAA,OACP,QADOA,EAC7BP,EAAOQ,uBAAe,IAAAD,OAAA,EAAtBA,EAAwBJ,cAAcC,SAAS3B,EAAQI,gBAAgBsB,cAAc,KAKzF1B,EAAQK,WAAaL,EAAQM,QAC7Be,EAAWA,EAASC,QAAOC,IACvB,MAAMS,EAAkB,IAAIC,KAAKV,EAAOW,kBAClCC,EAAgB,IAAIF,KAAKV,EAAOa,cAChCC,EAAkB,IAAIJ,KAAKjC,EAAQK,WAIzC,OAAQ2B,GAHc,IAAIC,KAAKjC,EAAQM,UAGK6B,GAAiBE,CAAe,IAEzErC,EAAQK,UACfgB,EAAWA,EAASC,QAAOC,GACD,IAAIU,KAAKV,EAAOa,eACd,IAAIH,KAAKjC,EAAQK,aAGtCL,EAAQM,UACfe,EAAWA,EAASC,QAAOC,GACC,IAAIU,KAAKV,EAAOW,mBAClB,IAAID,KAAKjC,EAAQM,YAK/CR,EAAqBuB,EAAS,GAC/B,CAACrB,EAASL,IAGb,MA4BM2C,EAAcC,IAChB,IAAKA,EAAY,MAAO,GAExB,OADa,IAAIN,KAAKM,GACVC,eAAe,QAAS,CAChCC,IAAK,UACLC,MAAO,UACPC,KAAM,UACNC,KAAM,UACNC,OAAQ,WACV,EAGN,OACI7G,EAAAA,EAAAA,MAAA8G,EAAAA,SAAA,CAAA/G,SAAA,EACIF,EAAAA,EAAAA,KAACwB,EAAAA,EAAM,KACPxB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAChBF,EAAAA,EAAAA,KAACf,EAAAA,EAAO,KAERkB,EAAAA,EAAAA,MAAA,QAAMF,UAAU,iFAAgFC,SAAA,EAC5FF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAAmCC,UAC9CC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,eAAcC,SAAA,EACxBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMsG,EAAAA,IAAWjH,UAAU,SAAS,mCAM7DE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qBAAoBC,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,cAAaC,UACxBC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,kBAAiBC,SAAA,EAC3BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMuG,EAAAA,IAAUlH,UAAU,SAAS,kBAI5DE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACtBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,UAASC,SAAA,EACpBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACrBF,EAAAA,EAAAA,KAAA,SAAOoH,QAAQ,WAAWnH,UAAU,aAAYC,SAAC,gBACjDF,EAAAA,EAAAA,KAAA,SACIM,KAAK,OACLL,UAAU,eACVI,GAAG,WACHoC,KAAK,WACL2C,MAAOjB,EAAQE,SACfgD,SAAUnC,EACVoC,YAAY,mBAIpBnH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACrBF,EAAAA,EAAAA,KAAA,SAAOoH,QAAQ,gBAAgBnH,UAAU,aAAYC,SAAC,uBACtDF,EAAAA,EAAAA,KAAA,SACIM,KAAK,OACLL,UAAU,eACVI,GAAG,gBACHoC,KAAK,gBACL2C,MAAOjB,EAAQG,cACf+C,SAAUnC,EACVoC,YAAY,0BAIpBnH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACrBF,EAAAA,EAAAA,KAAA,SAAOoH,QAAQ,kBAAkBnH,UAAU,aAAYC,SAAC,gBACxDF,EAAAA,EAAAA,KAAA,SACIM,KAAK,OACLL,UAAU,eACVI,GAAG,kBACHoC,KAAK,kBACL2C,MAAOjB,EAAQI,gBACf8C,SAAUnC,EACVoC,YAAY,mBAIpBnH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACrBF,EAAAA,EAAAA,KAAA,SAAOoH,QAAQ,YAAYnH,UAAU,aAAYC,SAAC,mCAClDF,EAAAA,EAAAA,KAAA,SACIM,KAAK,OACLL,UAAU,eACVI,GAAG,YACHoC,KAAK,YACL2C,MAAOjB,EAAQK,UACf6C,SAAUnC,QAIlB/E,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACrBF,EAAAA,EAAAA,KAAA,SAAOoH,QAAQ,UAAUnH,UAAU,aAAYC,SAAC,uBAChDF,EAAAA,EAAAA,KAAA,SACIM,KAAK,OACLL,UAAU,eACVI,GAAG,UACHoC,KAAK,UACL2C,MAAOjB,EAAQM,QACf4C,SAAUnC,WAKtB/E,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kCAAiCC,SAAA,EAC5CC,EAAAA,EAAAA,MAAA,UACIF,UAAU,iCACVuD,QApIf+D,KACjBnD,EAAW,CACPC,SAAU,GACVC,cAAe,GACfC,gBAAiB,GACjBC,UAAW,GACXC,QAAS,GACTC,QAAS,IACX,EA4HoDxE,SAAA,EAEtBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM4G,EAAAA,IAAQvH,UAAU,SAAS,mCAItDE,EAAAA,EAAAA,MAAA,UAAQF,UAAU,kBAAiBC,SAAA,EAC/BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM6G,EAAAA,IAAUxH,UAAU,SAAS,kBAQnE6B,GACG3B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qCAAoCC,SAAA,EAC/CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8BAA8BJ,KAAK,SAAQK,UACtDF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,kBAAiBC,SAAC,wBAEtCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,OAAMC,SAAC,iDAE3BiD,GACAhD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qBAAoBC,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,gBAAeC,SAAC,UAC9BF,EAAAA,EAAAA,KAAA,KAAAE,SAAIiD,KACJnD,EAAAA,EAAAA,KAAA,UACAA,EAAAA,EAAAA,KAAA,UAAQC,UAAU,kBAAkBuD,QAASA,IAAMkE,OAAOxI,SAASyI,SAASzH,SAAC,qBAKjFF,EAAAA,EAAAA,KAAAiH,EAAAA,SAAA,CAAA/G,SACkC,IAA7B8D,EAAkB4D,QACfzH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sBAAqBC,SAAA,EAChCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMiH,EAAAA,IAAuB5H,UAAU,SAAS,0EAIrEE,EAAAA,EAAAA,MAAA8G,EAAAA,SAAA,CAAA/G,SAAA,EACIC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBC,SAAA,EAClCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMkH,EAAAA,IAAS7H,UAAU,UAC1CD,EAAAA,EAAAA,KAAA,UAAAE,SAAS8D,EAAkB4D,SAAgB,6CAG/C5H,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,UAC7BC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,cAAaC,UAC1BC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACIF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,iBACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,uBACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,cACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,aACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,YACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,eACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,gBACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,qBACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,WACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,SACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,aACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,aACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,6BAGZF,EAAAA,EAAAA,KAAA,SAAAE,SACK8D,EAAkB+D,KAAKrC,IACpBvF,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACIC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACIF,EAAAA,EAAAA,KAAA,UAAAE,SAASwF,EAAOsC,aAAW,MAAAxH,OAAUyH,OAAOvC,EAAOrF,IAAI6H,SAAS,EAAG,QAClExC,EAAOE,aACJ5F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,SAAEwF,EAAOE,iBAGlDzF,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACIF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,UAASC,SAAEwF,EAAOyC,cAAgB,gBACjDnI,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,SAAEwF,EAAO0C,MAAQ,+BAEtDpI,EAAAA,EAAAA,KAAA,MAAAE,UACIF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gBAAeC,SAAEwF,EAAO2C,UAAY,SAExDrI,EAAAA,EAAAA,KAAA,MAAAE,SAAKwF,EAAO4C,gBAAkB5C,EAAOM,oBAAsB,gBAC3DhG,EAAAA,EAAAA,KAAA,MAAAE,SAAKwF,EAAO6C,gBAAkB7C,EAAOQ,iBAAmB,gBACxDlG,EAAAA,EAAAA,KAAA,MAAAE,SAAKwF,EAAO8C,WAAa,gBACzBxI,EAAAA,EAAAA,KAAA,MAAAE,SAAKwF,EAAO0C,MAAQ,gBACpBpI,EAAAA,EAAAA,KAAA,MAAAE,SAAKwF,EAAOE,YAAc,OAC1B5F,EAAAA,EAAAA,KAAA,MAAAE,SAAKwF,EAAO+C,cAAgB,OAC5BzI,EAAAA,EAAAA,KAAA,MAAAE,SAAKwF,EAAOgD,aAAe,OAC3B1I,EAAAA,EAAAA,KAAA,MAAAE,SAAKwF,EAAOiD,cAAgB,OAC5B3I,EAAAA,EAAAA,KAAA,MAAAE,SAAKuG,EAAWf,EAAOW,mBAAqBI,EAAWf,EAAOkD,qBAC9D5I,EAAAA,EAAAA,KAAA,MAAAE,UACIF,EAAAA,EAAAA,KAACO,EAAAA,GAAI,CAACG,GAAE,SAAAF,OAAWkF,EAAOrF,IAAMJ,UAAU,iCAAgCC,SAAC,iBAxB1EwF,EAAOrF,2BAwC5DL,EAAAA,EAAAA,KAACuB,EAAAA,EAAM,MACR,C", "sources": ["components/Sidebar.js", "components/Footer.js", "components/Header.js", "pages/ShipmentHistory.js"], "sourcesContent": ["import React from 'react';\r\nimport { Link, useLocation } from 'react-router-dom';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport {\r\n    faDolly, faFolderPlus, faAnchor, faFlag<PERSON>he<PERSON>ed,\r\n    faUser, faStreetView, faCreditCard, faFileLines,\r\n    faBell, faDesktop\r\n} from '@fortawesome/free-solid-svg-icons';\r\n\r\nconst Sidebar = () => {\r\n    const location = useLocation();\r\n    \r\n    // Get user role from localStorage\r\n    const getUserRole = () => {\r\n        try {\r\n            const user = JSON.parse(localStorage.getItem('user'));\r\n            return user?.user?.role || user?.user?.gorev || 'user';\r\n        } catch {\r\n            return 'user';\r\n        }\r\n    };\r\n    \r\n    const userRole = getUserRole();\r\n    const isAdmin = userRole === 'admin';\r\n\r\n    return (\r\n        <div className=\"sidebar col-md-4 col-lg-2 p-0 bg-light-subtle\">\r\n            <div className=\"offcanvas-md offcanvas-end\" tabIndex=\"-1\" id=\"yanMenu\" aria-labelledby=\"yanMenu\">\r\n                <div className=\"offcanvas-header\">\r\n                    <h5 className=\"offcanvas-title\" id=\"yanMenu\">MGZ24 Gold</h5>\r\n                    <button type=\"button\" className=\"btn-close\" data-bs-dismiss=\"offcanvas\" data-bs-target=\"#yanMenu\" aria-label=\"Kapat\"></button>\r\n                </div>\r\n                <div className=\"offcanvas-body d-md-flex flex-column p-0 pt-lg-3 overflow-y-auto\">\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Sevkiyat İşlemleri</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/' ? 'active' : ''}`} to=\"/\">\r\n                                <FontAwesomeIcon icon={faDolly} />Aktif Sevkiyatlar\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/add' ? 'active' : ''}`} to=\"/add\">\r\n                                <FontAwesomeIcon icon={faFolderPlus} />Yeni Sevkiyat Oluştur\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/inactive-devices' ? 'active' : ''}`} to=\"/inactive-devices\">\r\n                                <FontAwesomeIcon icon={faAnchor} />İnaktif Cihazlar\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/history' ? 'active' : ''}`} to=\"/history\">\r\n                                <FontAwesomeIcon icon={faFlagCheckered} />Geçmiş Sevkiyatlar\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Sistem Yönetimi</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        {/* Cihaz Yönetimi - Sadece admin kullanıcılarına göster */}\r\n                        {isAdmin && (\r\n                            <li className=\"nav-item\">\r\n                                <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/devices' ? 'active' : ''}`} to=\"/devices\">\r\n                                    <FontAwesomeIcon icon={faDesktop} />Cihaz Yönetimi\r\n                                </Link>\r\n                            </li>\r\n                        )}\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/notifications' ? 'active' : ''}`} to=\"/notifications\">\r\n                                <FontAwesomeIcon icon={faBell} />Bildirimler\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Kullanıcı Ayarları</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/profile' ? 'active' : ''}`} to=\"/profile\">\r\n                                <FontAwesomeIcon icon={faUser} />Kullanıcı Profilim\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/viewers' ? 'active' : ''}`} to=\"/viewers\">\r\n                                <FontAwesomeIcon icon={faStreetView} />İzleyici İşlemleri\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Ödeme ve Yapılandırma</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/payment' ? 'active' : ''}`} to=\"/payment\">\r\n                                <FontAwesomeIcon icon={faCreditCard} />Ödeme Yap\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/invoices' ? 'active' : ''}`} to=\"/invoices\">\r\n                                <FontAwesomeIcon icon={faFileLines} />Faturalarım\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-body-secondary text-uppercase\">Kredi Durumu</h6>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Sidebar;", "import React from 'react';\r\n\r\nconst Footer = () => {\r\n    return (\r\n        <footer className=\"py-5 border-top\">\r\n            <p className=\"text-center\">&copy;2025 Inkatech Ölçüm Sistemleri</p>\r\n        </footer>\r\n    );\r\n};\r\n\r\nexport default Footer; ", "import React, { useState, useEffect } from 'react';\r\nimport { Link, useNavigate } from 'react-router-dom';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { faRightFromBracket, faBars, faUser } from '@fortawesome/free-solid-svg-icons';\r\nimport logoDark from '../assets/img/logo.png';\r\nimport { kullaniciService } from '../api/dbService';\r\n\r\nconst Header = () => {\r\n    const navigate = useNavigate();\r\n    const [userName, setUserName] = useState('Misafir');\r\n    const [loading, setLoading] = useState(true);\r\n\r\n    // Kullanıcı bilgilerini veritabanından al\r\n    useEffect(() => {\r\n        const fetchUserData = async () => {\r\n            try {\r\n                // Local storage'dan kullanıcı bilgisini al\r\n                const storedUser = JSON.parse(localStorage.getItem('user'));\r\n                const userId = storedUser?.user?.musteri_ID || storedUser?.user?.id;\r\n                const userName = storedUser?.user?.name || storedUser?.user?.musteri_adi;\r\n\r\n                if (!userId) {\r\n                    console.warn('Oturum bilgisi bulunamadı');\r\n                    setUserName('Misafir');\r\n                    setLoading(false);\r\n                    return;\r\n                }\r\n\r\n                // Eğer localStorage'da isim varsa direkt kullan\r\n                if (userName) {\r\n                    setUserName(userName);\r\n                    setLoading(false);\r\n                    return;\r\n                }\r\n\r\n                // API'den kullanıcı bilgilerini getir (backend çalışıyorsa)\r\n                try {\r\n                    const userData = await kullaniciService.getKullanici(userId);\r\n\r\n                    // musteri_adi alanını kullan\r\n                    if (userData && userData.musteri_adi) {\r\n                        setUserName(userData.musteri_adi);\r\n\r\n                        // localStorage'ı güncelle\r\n                        if (storedUser?.user) {\r\n                            storedUser.user.name = userData.musteri_adi;\r\n                            localStorage.setItem('user', JSON.stringify(storedUser));\r\n                        }\r\n                    }\r\n                } catch (apiError) {\r\n                    console.warn('API\\'den kullanıcı bilgisi alınamadı, localStorage kullanılıyor');\r\n                    // API hatası durumunda localStorage'daki adı kullan\r\n                    setUserName(userName || 'Kullanıcı');\r\n                }\r\n            } catch (error) {\r\n                console.error('Kullanıcı bilgileri alınırken hata:', error);\r\n                // Hata durumunda varsayılan isim kullan\r\n                setUserName('Kullanıcı');\r\n            } finally {\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        fetchUserData();\r\n    }, []);\r\n\r\n    // Çıkış işlemi\r\n    const handleLogout = () => {\r\n        // Local storage'dan kullanıcı bilgilerini temizle\r\n        localStorage.removeItem('user');\r\n\r\n        // Login sayfasına yönlendir\r\n        navigate('/login');\r\n    };\r\n\r\n    return (\r\n        <header className=\"navbar sticky-top bg-darkblue flex-md-nowrap p-0 shadow\" data-bs-theme=\"dark\">\r\n            <Link className=\"navbar-brand col-md-4 col-lg-2 me-0 px-3 py-2 fs-6 text-white\" to=\"/\">\r\n                <img src={logoDark} alt=\"MGZ24 Logo\" height=\"40\" />\r\n            </Link>\r\n            <ul className=\"navbar-nav flex-row align-items-center\">\r\n                <li className=\"nav-item text-nowrap d-none d-md-block me-3\">\r\n                    <span className=\"text-white\">\r\n                        <FontAwesomeIcon icon={faUser} className=\"me-2\" />\r\n                        {loading ? 'Yükleniyor...' : userName}\r\n                    </span>\r\n                </li>\r\n                <li className=\"nav-item text-nowrap px-1\">\r\n                    <button\r\n                        onClick={handleLogout}\r\n                        className=\"nav-link px-3\"\r\n                        data-bs-toggle=\"tooltip\"\r\n                        data-bs-placement=\"left\"\r\n                        data-bs-title=\"Çıkış yap!\"\r\n                    >\r\n                        <FontAwesomeIcon icon={faRightFromBracket} />\r\n                    </button>\r\n                </li>\r\n                <li className=\"nav-item text-nowrap d-md-none\">\r\n                    <button\r\n                        className=\"nav-link px-3\"\r\n                        type=\"button\"\r\n                        data-bs-toggle=\"offcanvas\"\r\n                        data-bs-target=\"#yanMenu\"\r\n                        aria-controls=\"yanMenu\"\r\n                        aria-expanded=\"false\"\r\n                        aria-label=\"menu aç/kapa\"\r\n                    >\r\n                        <FontAwesomeIcon icon={faBars} />\r\n                    </button>\r\n                </li>\r\n            </ul>\r\n        </header>\r\n    );\r\n};\r\n\r\nexport default Header; ", "import React, { useState, useEffect } from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport Header from '../components/Header';\r\nimport Sidebar from '../components/Sidebar';\r\nimport Footer from '../components/Footer';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport {\r\n    faCheck,\r\n    faHistory,\r\n    faExclamationTriangle,\r\n    faFilter,\r\n    faUndo,\r\n    faSearch\r\n} from '@fortawesome/free-solid-svg-icons';\r\nimport { sevkiyatService } from '../api/dbService';\r\n\r\nconst ShipmentHistory = () => {\r\n    // State tanımlamaları\r\n    const [gecmisler, setGecmisler] = useState([]);\r\n    const [filteredGecmisler, setFilteredGecmisler] = useState([]);\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState(null);\r\n    // const [userRole, setUserRole] = useState('viewer'); // Gerekirse eklenecek\r\n\r\n    // Filtreleme için state\r\n    const [filters, setFilters] = useState({\r\n        deviceId: '',\r\n        senderCompany: '',\r\n        receiverCompany: '',\r\n        startDate: '',\r\n        endDate: '',\r\n        product: ''\r\n    });\r\n\r\n    // Kullanıcı bilgisini localStorage'dan al\r\n    const userString = localStorage.getItem('user');\r\n    const userData = userString ? JSON.parse(userString) : null;\r\n    const user = userData ? userData.user : null;\r\n    const musteriID = user ? user.musteri_ID : null;\r\n\r\n    // Geçmiş sevkiyatları yükle\r\n    useEffect(() => {\r\n        const fetchGecmisler = async () => {\r\n            try {\r\n                setLoading(true);\r\n                setError(null);\r\n\r\n                // Tamamlanmış sevkiyatları API'den çek (tamamlandi_mi = 1)\r\n                const data = await sevkiyatService.getGecmisSevkiyatlarByMusteriId(musteriID);\r\n                setGecmisler(data);\r\n                setFilteredGecmisler(data);\r\n                setLoading(false);\r\n            } catch (error) {\r\n                console.error('Sevkiyat geçmişi alınırken hata:', error);\r\n                setError('Sevkiyat geçmişi yüklenirken bir hata oluştu: ' + error.message);\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        fetchGecmisler();\r\n\r\n        // Kullanıcı rolü kontrolü (gerekirse eklenecek)\r\n        // const checkUserRole = () => {\r\n        //     const storedUser = JSON.parse(localStorage.getItem('user'));\r\n        //     if (storedUser && storedUser.role) {\r\n        //         setUserRole(storedUser.role);\r\n        //     } else {\r\n        //         setUserRole('viewer');\r\n        //     }\r\n        // };\r\n        // checkUserRole();\r\n    }, [musteriID]);\r\n\r\n    // Filtre değişikliklerini işle\r\n    const handleFilterChange = (e) => {\r\n        const { name, value } = e.target;\r\n        setFilters(prevFilters => ({\r\n            ...prevFilters,\r\n            [name]: value\r\n        }));\r\n    };\r\n\r\n    // Filtreleri uygula\r\n    useEffect(() => {\r\n        let filtered = [...gecmisler];\r\n\r\n        // Cihaz ID filtresi\r\n        if (filters.deviceId) {\r\n            filtered = filtered.filter(gecmis =>\r\n                gecmis.mgz24_kodu?.toLowerCase().includes(filters.deviceId.toLowerCase())\r\n            );\r\n        }\r\n\r\n        // Gönderen firma filtresi\r\n        if (filters.senderCompany) {\r\n            filtered = filtered.filter(gecmis =>\r\n                gecmis.gonderen_firma_adi?.toLowerCase().includes(filters.senderCompany.toLowerCase())\r\n            );\r\n        }\r\n\r\n        // Alan firma filtresi\r\n        if (filters.receiverCompany) {\r\n            filtered = filtered.filter(gecmis =>\r\n                gecmis.alici_firma_adi?.toLowerCase().includes(filters.receiverCompany.toLowerCase())\r\n            );\r\n        }\r\n\r\n        // Tarih aralığı filtresi\r\n        if (filters.startDate && filters.endDate) {\r\n            filtered = filtered.filter(gecmis => {\r\n                const gecmisStartDate = new Date(gecmis.baslangic_zamani);\r\n                const gecmisEndDate = new Date(gecmis.bitis_zamani);\r\n                const filterStartDate = new Date(filters.startDate);\r\n                const filterEndDate = new Date(filters.endDate);\r\n\r\n                // Sevkiyat tarihleri ile filtreleme tarihleri arasında çakışma varsa göster\r\n                return (gecmisStartDate <= filterEndDate && gecmisEndDate >= filterStartDate);\r\n            });\r\n        } else if (filters.startDate) {\r\n            filtered = filtered.filter(gecmis => {\r\n                const gecmisEndDate = new Date(gecmis.bitis_zamani);\r\n                const filterStartDate = new Date(filters.startDate);\r\n                return gecmisEndDate >= filterStartDate;\r\n            });\r\n        } else if (filters.endDate) {\r\n            filtered = filtered.filter(gecmis => {\r\n                const gecmisStartDate = new Date(gecmis.baslangic_zamani);\r\n                const filterEndDate = new Date(filters.endDate);\r\n                return gecmisStartDate <= filterEndDate;\r\n            });\r\n        }\r\n\r\n        setFilteredGecmisler(filtered);\r\n    }, [filters, gecmisler]);\r\n\r\n    // Filtreleri sıfırla\r\n    const resetFilters = () => {\r\n        setFilters({\r\n            deviceId: '',\r\n            senderCompany: '',\r\n            receiverCompany: '',\r\n            startDate: '',\r\n            endDate: '',\r\n            product: ''\r\n        });\r\n    };\r\n\r\n    // Durum etiketi gösterimi\r\n    const renderStatus = (durum) => {\r\n        switch (durum) {\r\n            case 'tamamlandi':\r\n                return <span className=\"badge bg-success\">Tamamlandı</span>;\r\n            case 'iptal_edildi':\r\n                return <span className=\"badge bg-danger\">İptal Edildi</span>;\r\n            case 'iade_edildi':\r\n                return <span className=\"badge bg-info\">İade Edildi</span>;\r\n            case 'ariza_bildirildi':\r\n                return <span className=\"badge bg-warning\">Arıza Bildirildi</span>;\r\n            default:\r\n                return <span className=\"badge bg-secondary\">Bilinmiyor</span>;\r\n        }\r\n    };\r\n\r\n    // Tarih formatını düzenleme\r\n    const formatDate = (dateString) => {\r\n        if (!dateString) return '';\r\n        const date = new Date(dateString);\r\n        return date.toLocaleString('tr-TR', {\r\n            day: '2-digit',\r\n            month: '2-digit',\r\n            year: 'numeric',\r\n            hour: '2-digit',\r\n            minute: '2-digit'\r\n        });\r\n    };\r\n\r\n    return (\r\n        <>\r\n            <Header />\r\n            <div className=\"container-fluid\">\r\n                <div className=\"row\">\r\n                    <Sidebar />\r\n\r\n                    <main className=\"col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white\">\r\n                        <div className=\"pt-3 pb-1 mt-3 mb-3 border-bottom\">\r\n                            <h1 className=\"h4 text-dark\">\r\n                                <FontAwesomeIcon icon={faHistory} className=\"me-2\" />\r\n                                Geçmiş Sevkiyatlar\r\n                            </h1>\r\n                        </div>\r\n\r\n                        {/* Filtreler */}\r\n                        <div className=\"card bg-light mb-4\">\r\n                            <div className=\"card-header\">\r\n                                <h5 className=\"card-title mb-0\">\r\n                                    <FontAwesomeIcon icon={faFilter} className=\"me-2\" />\r\n                                    Filtreler\r\n                                </h5>\r\n                            </div>\r\n                            <div className=\"card-body\">\r\n                                <div className=\"row g-3\">\r\n                                    <div className=\"col-md-2\">\r\n                                        <label htmlFor=\"deviceId\" className=\"form-label\">Cihaz Kodu</label>\r\n                                        <input\r\n                                            type=\"text\"\r\n                                            className=\"form-control\"\r\n                                            id=\"deviceId\"\r\n                                            name=\"deviceId\"\r\n                                            value={filters.deviceId}\r\n                                            onChange={handleFilterChange}\r\n                                            placeholder=\"Cihaz kodu\"\r\n                                        />\r\n                                    </div>\r\n\r\n                                    <div className=\"col-md-2\">\r\n                                        <label htmlFor=\"senderCompany\" className=\"form-label\">Gönderen Firma</label>\r\n                                        <input\r\n                                            type=\"text\"\r\n                                            className=\"form-control\"\r\n                                            id=\"senderCompany\"\r\n                                            name=\"senderCompany\"\r\n                                            value={filters.senderCompany}\r\n                                            onChange={handleFilterChange}\r\n                                            placeholder=\"Gönderen firma\"\r\n                                        />\r\n                                    </div>\r\n\r\n                                    <div className=\"col-md-2\">\r\n                                        <label htmlFor=\"receiverCompany\" className=\"form-label\">Alan Firma</label>\r\n                                        <input\r\n                                            type=\"text\"\r\n                                            className=\"form-control\"\r\n                                            id=\"receiverCompany\"\r\n                                            name=\"receiverCompany\"\r\n                                            value={filters.receiverCompany}\r\n                                            onChange={handleFilterChange}\r\n                                            placeholder=\"Alan firma\"\r\n                                        />\r\n                                    </div>\r\n\r\n                                    <div className=\"col-md-2\">\r\n                                        <label htmlFor=\"startDate\" className=\"form-label\">Başlangıç Tarihi</label>\r\n                                        <input\r\n                                            type=\"date\"\r\n                                            className=\"form-control\"\r\n                                            id=\"startDate\"\r\n                                            name=\"startDate\"\r\n                                            value={filters.startDate}\r\n                                            onChange={handleFilterChange}\r\n                                        />\r\n                                    </div>\r\n\r\n                                    <div className=\"col-md-2\">\r\n                                        <label htmlFor=\"endDate\" className=\"form-label\">Bitiş Tarihi</label>\r\n                                        <input\r\n                                            type=\"date\"\r\n                                            className=\"form-control\"\r\n                                            id=\"endDate\"\r\n                                            name=\"endDate\"\r\n                                            value={filters.endDate}\r\n                                            onChange={handleFilterChange}\r\n                                        />\r\n                                    </div>\r\n                                </div>\r\n\r\n                                <div className=\"d-flex justify-content-end mt-3\">\r\n                                    <button\r\n                                        className=\"btn btn-outline-secondary me-2\"\r\n                                        onClick={resetFilters}\r\n                                    >\r\n                                        <FontAwesomeIcon icon={faUndo} className=\"me-1\" />\r\n                                        Filtreleri Sıfırla\r\n                                    </button>\r\n\r\n                                    <button className=\"btn btn-primary\">\r\n                                        <FontAwesomeIcon icon={faSearch} className=\"me-1\" />\r\n                                        Ara\r\n                                    </button>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        {/* Ana İçerik */}\r\n                        {loading ? (\r\n                            <div className=\"d-flex justify-content-center my-5\">\r\n                                <div className=\"spinner-border text-primary\" role=\"status\">\r\n                                    <span className=\"visually-hidden\">Yükleniyor...</span>\r\n                                </div>\r\n                                <span className=\"ms-2\">Sevkiyat geçmişi yükleniyor...</span>\r\n                            </div>\r\n                        ) : error ? (\r\n                            <div className=\"alert alert-danger\">\r\n                                <h5 className=\"alert-heading\">Hata</h5>\r\n                                <p>{error}</p>\r\n                                <hr />\r\n                                <button className=\"btn btn-primary\" onClick={() => window.location.reload()}>\r\n                                    Yeniden Dene\r\n                                </button>\r\n                            </div>\r\n                        ) : (\r\n                            <>\r\n                                {filteredGecmisler.length === 0 ? (\r\n                                    <div className=\"alert alert-warning\">\r\n                                        <FontAwesomeIcon icon={faExclamationTriangle} className=\"me-2\" />\r\n                                        Arama kriterlerinize uygun geçmiş sevkiyat bulunamadı.\r\n                                    </div>\r\n                                ) : (\r\n                                    <>\r\n                                        <div className=\"alert alert-info mb-3\">\r\n                                            <FontAwesomeIcon icon={faCheck} className=\"me-2\" />\r\n                                            <strong>{filteredGecmisler.length}</strong> geçmiş sevkiyat listeleniyor.\r\n                                        </div>\r\n\r\n                                        <div className=\"table-responsive\">\r\n                                            <table className=\"table table-striped table-hover border\">\r\n                                                <thead className=\"table-light\">\r\n                                                    <tr>\r\n                                                        <th>Sevkiyat ID</th>\r\n                                                        <th>Sevkiyat Adı</th>\r\n                                                        <th>Plaka No</th>\r\n                                                        <th>Nereden</th>\r\n                                                        <th>Nereye</th>\r\n                                                        <th>Nakliyeci</th>\r\n                                                        <th>Ürün</th>\r\n                                                        <th>Sipariş No</th>\r\n                                                        <th>Palet</th>\r\n                                                        <th>Net</th>\r\n                                                        <th>Brüt</th>\r\n                                                        <th>Eklenme</th>\r\n                                                        <th>İşlemler</th>\r\n                                                    </tr>\r\n                                                </thead>\r\n                                                <tbody>\r\n                                                    {filteredGecmisler.map((gecmis) => (\r\n                                                        <tr key={gecmis.id}>\r\n                                                            <td>\r\n                                                                <strong>{gecmis.sevkiyat_ID || `GS-${String(gecmis.id).padStart(6, '0')}`}</strong>\r\n                                                                {gecmis.mgz24_kodu && (\r\n                                                                    <div className=\"small text-muted\">{gecmis.mgz24_kodu}</div>\r\n                                                                )}\r\n                                                            </td>\r\n                                                            <td>\r\n                                                                <div className=\"fw-bold\">{gecmis.sevkiyat_adi || 'Bilinmiyor'}</div>\r\n                                                                <div className=\"small text-muted\">{gecmis.urun || 'Ürün bilgisi yok'}</div>\r\n                                                            </td>\r\n                                                            <td>\r\n                                                                <span className=\"badge bg-dark\">{gecmis.plaka_no || '-'}</span>\r\n                                                            </td>\r\n                                                            <td>{gecmis.cikis_lokasyon || gecmis.gonderen_firma_adi || 'Bilinmiyor'}</td>\r\n                                                            <td>{gecmis.varis_lokasyon || gecmis.alici_firma_adi || 'Bilinmiyor'}</td>\r\n                                                            <td>{gecmis.nakliyeci || 'Bilinmiyor'}</td>\r\n                                                            <td>{gecmis.urun || 'Bilinmiyor'}</td>\r\n                                                            <td>{gecmis.mgz24_kodu || '-'}</td>\r\n                                                            <td>{gecmis.palet_sayisi || '-'}</td>\r\n                                                            <td>{gecmis.net_agirlik || '-'}</td>\r\n                                                            <td>{gecmis.brut_agirlik || '-'}</td>\r\n                                                            <td>{formatDate(gecmis.baslangic_zamani) || formatDate(gecmis.olusturma_zamani)}</td>\r\n                                                            <td>\r\n                                                                <Link to={`/view/${gecmis.id}`} className=\"btn btn-sm btn-outline-primary\">\r\n                                                                    Detaylar\r\n                                                                </Link>\r\n                                                            </td>\r\n                                                        </tr>\r\n                                                    ))}\r\n                                                </tbody>\r\n                                            </table>\r\n                                        </div>\r\n                                    </>\r\n                                )}\r\n                            </>\r\n                        )}\r\n                    </main>\r\n                </div>\r\n            </div>\r\n            <Footer />\r\n        </>\r\n    );\r\n};\r\n\r\nexport default ShipmentHistory;"], "names": ["Sidebar", "location", "useLocation", "isAdmin", "getUserRole", "_user$user", "_user$user2", "user", "JSON", "parse", "localStorage", "getItem", "role", "gorev", "_unused", "_jsx", "className", "children", "_jsxs", "tabIndex", "id", "type", "Link", "concat", "pathname", "to", "FontAwesomeIcon", "icon", "faDolly", "faFolderPlus", "faAnchor", "faFlag<PERSON><PERSON><PERSON><PERSON>", "faDesktop", "faBell", "faUser", "faStreetView", "faCreditCard", "faFileLines", "Footer", "Header", "navigate", "useNavigate", "userName", "setUserName", "useState", "loading", "setLoading", "useEffect", "async", "_storedUser$user", "_storedUser$user2", "_storedUser$user3", "_storedUser$user4", "storedUser", "userId", "musteri_ID", "name", "musteri_adi", "console", "warn", "userData", "kullaniciService", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setItem", "stringify", "apiError", "error", "fetchUserData", "src", "alt", "height", "onClick", "handleLogout", "removeItem", "faRightFromBracket", "faBars", "ShipmentHistory", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setFilteredGecmisler", "setError", "filters", "setFilters", "deviceId", "senderCompany", "receiverCompany", "startDate", "endDate", "product", "userString", "musteriID", "data", "sevkiyatService", "getGecmisSevkiyatlarByMusteriId", "message", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "handleFilterChange", "e", "value", "target", "prevFilters", "_objectSpread", "filtered", "filter", "gec<PERSON>", "_gecmis$mgz24_kodu", "mgz24_kodu", "toLowerCase", "includes", "_gecmis$gonderen_firm", "gonderen_firma_adi", "_gecmis$alici_firma_a", "alici_firma_adi", "gecmisStartDate", "Date", "baslang<PERSON>_zamani", "gecmisEndDate", "bitis_zamani", "filterStartDate", "formatDate", "dateString", "toLocaleString", "day", "month", "year", "hour", "minute", "_Fragment", "faHistory", "faFilter", "htmlFor", "onChange", "placeholder", "resetFilters", "faUndo", "faSearch", "window", "reload", "length", "faExclamationTriangle", "faCheck", "map", "sevkiyat_ID", "String", "padStart", "sevkiyat_adi", "urun", "plaka_no", "cikis_lokasyon", "varis_lokasyon", "<PERSON><PERSON><PERSON><PERSON>", "palet_sayisi", "net_agirlik", "brut_agirlik", "olusturma_zamani"], "sourceRoot": ""}