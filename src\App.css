.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* Sensor Data Container Styles */
.sensor-data-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
}

.sensor-item {
  flex: 1;
  min-width: 200px;
  padding: 1rem;
  border-right: 1px solid #e9ecef;
  position: relative;
}

.sensor-item:last-child {
  border-right: none;
}

.sensor-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: -0.5rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 60%;
  background: linear-gradient(to bottom, transparent, #dee2e6, transparent);
}

@media (max-width: 768px) {
  .sensor-data-container {
    flex-direction: column;
    gap: 0.5rem;
  }

  .sensor-item {
    border-right: none;
    border-bottom: 1px solid #e9ecef;
    width: 100%;
  }

  .sensor-item:last-child {
    border-bottom: none;
  }

  .sensor-item:not(:last-child)::after {
    display: none;
  }
}

/* Device Real Time Card Styles */
.device-realtime-card {
  border: 1px solid #dee2e6;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.device-realtime-card .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

/* Data Table Styles */
.table-responsive {
  max-height: 400px;
  overflow-y: auto;
}

.table th {
  position: sticky;
  top: 0;
  background-color: #343a40;
  color: white;
  z-index: 10;
}

/* Loading and Error States */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.error-container {
  padding: 1rem;
  text-align: center;
}