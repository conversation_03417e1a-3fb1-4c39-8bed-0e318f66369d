import mysql from 'mysql2/promise';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

// .env dosyasını yükle
dotenv.config({ path: './backend/.env' });

// Veritabanı bağlantı ayarları
const dbConfig = {
  host: process.env.DB_HOST || '************',
  user: process.env.DB_USER || 'mehmet',
  password: process.env.DB_PASSWORD || 'Inka.mS_335265933s.tech',
  database: process.env.DB_NAME || 'mgz24db',
  multipleStatements: true
};

async function applyDatabaseOptimizations() {
  let connection;
  
  try {
    console.log('🔌 Veritabanına bağlanılıyor...');
    connection = await mysql.createConnection(dbConfig);
    
    console.log('✅ Veritabanı bağlantısı başarılı');
    
    // SQL dosyasını oku
    const sqlFilePath = path.join(process.cwd(), 'database_foreign_keys_indexes.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    console.log('📄 SQL script dosyası okundu');
    
    // SQL script'ini çalıştır
    console.log('🚀 Foreign key constraints ve index'ler ekleniyor...');
    console.log('⚠️  Bu işlem birkaç dakika sürebilir...');
    
    const statements = sqlContent.split(';').filter(stmt => stmt.trim().length > 0);
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim();
      if (statement.length === 0) continue;
      
      try {
        await connection.execute(statement);
        console.log(`✅ Statement ${i + 1}/${statements.length} başarıyla çalıştırıldı`);
      } catch (error) {
        // Bazı constraint'ler zaten varsa veya tablo yoksa, hata göster ama devam et
        if (error.code === 'ER_DUP_KEYNAME' || 
            error.code === 'ER_FK_DUP_NAME' ||
            error.code === 'ER_NO_SUCH_TABLE') {
          console.log(`⚠️  Statement ${i + 1}: ${error.message} (Atlayarak devam ediliyor)`);
        } else {
          console.error(`❌ Statement ${i + 1} hatası:`, error.message);
          // Kritik olmayan hatalar için devam et
        }
      }
    }
    
    console.log('\n🎉 Veritabanı optimizasyonu tamamlandı!');
    console.log('\n📊 Index durumunu kontrol etmek için:');
    console.log('SHOW INDEX FROM sevkiyatlar;');
    console.log('SHOW INDEX FROM cihazBilgi;');
    
    console.log('\n🔗 Foreign key durumunu kontrol etmek için:');
    console.log(`SELECT TABLE_NAME, COLUMN_NAME, CONSTRAINT_NAME, REFERENCED_TABLE_NAME 
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE REFERENCED_TABLE_SCHEMA = '${dbConfig.database}' 
AND REFERENCED_TABLE_NAME IS NOT NULL;`);
    
  } catch (error) {
    console.error('❌ Hata:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Veritabanı bağlantısı kapatıldı');
    }
  }
}

// Script'i çalıştır
console.log('🗄️  MGZ24 Veritabanı Optimizasyon Script\'i');
console.log('=====================================\n');

applyDatabaseOptimizations()
  .then(() => {
    console.log('\n✨ İşlem başarıyla tamamlandı!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 İşlem başarısız:', error.message);
    process.exit(1);
  });