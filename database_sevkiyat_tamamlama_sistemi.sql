-- ===================================================================
-- MGZ24 Sevkiyat Tamamlama Sistemi için Veritabanı Düzenlemeleri
-- ===================================================================

USE mgz24db;

-- 1. SEVKİYATGECMIS TABLOSU YAPISI KONTROLÜ VE GÜNCELLEMESİ
-- ===================================================================

-- Mevcut sevkiyatGecmis tablosunu kontrol et ve gerekli kolonları ekle
-- Bu tablo, tama<PERSON>lanmış sevkiyatların geçmişini tutacak

-- Eğer sevkiyatGecmis tablosu yoksa oluştur
CREATE TABLE IF NOT EXISTS sevkiyatGecmis (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sevkiyat_ID INT NOT NULL,
    sevkiyat_adi VARCHAR(255) NOT NULL,
    cihaz_kodu VARCHAR(50) NOT NULL,
    plaka_no VARCHAR(20),
    
    -- Firma bilgileri
    gonderen_firma_id INT,
    gonderen_firma_adi VARCHAR(255),
    alici_firma_id INT, 
    alici_firma_adi VARCHAR(255),
    
    -- Tarih bilgileri
    baslangic_zamani DATETIME NOT NULL,
    bitis_zamani DATETIME,
    tamamlanma_zamani DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- Ağırlık ve ürün bilgileri
    net_agirlik DECIMAL(10,2),
    brut_agirlik DECIMAL(10,2),
    urun_id INT,
    urun_adi VARCHAR(255),
    palet_sayisi INT,
    
    -- Sıcaklık analizi
    sicaklik_min DECIMAL(5,2),
    sicaklik_max DECIMAL(5,2),
    sicaklik_ortalama DECIMAL(5,2),
    sicaklik_araligi VARCHAR(50),
    
    -- Nem analizi  
    nem_min DECIMAL(5,2),
    nem_max DECIMAL(5,2),
    nem_ortalama DECIMAL(5,2),
    
    -- Lokasyon bilgileri
    cikis_lokasyon_id INT,
    cikis_lokasyon VARCHAR(255),
    varis_lokasyon_id INT,
    varis_lokasyon VARCHAR(255),
    
    -- Durum ve notlar
    durum ENUM('tamamlandi', 'iade_edildi', 'ariza_bildirildi', 'kayip') DEFAULT 'tamamlandi',
    musteri_ID INT NOT NULL,
    musteri_adi VARCHAR(255),
    nakliyeci_id INT,
    nakliyeci_adi VARCHAR(255),
    
    -- Ek bilgiler
    notlar TEXT,
    tamamlayan_kullanici_id INT,
    toplam_mesafe DECIMAL(10,2),
    toplam_sure_saat DECIMAL(8,2),
    
    -- Timestamp
    olusturma_zamani DATETIME DEFAULT CURRENT_TIMESTAMP,
    guncelleme_zamani DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Index'ler
    INDEX idx_sevkiyat_gecmis_musteri (musteri_ID),
    INDEX idx_sevkiyat_gecmis_cihaz (cihaz_kodu),
    INDEX idx_sevkiyat_gecmis_tarih (baslangic_zamani DESC),
    INDEX idx_sevkiyat_gecmis_durum (durum),
    INDEX idx_sevkiyat_gecmis_gonderen (gonderen_firma_adi),
    INDEX idx_sevkiyat_gecmis_alici (alici_firma_adi)
);

-- 2. SEVKİYATLAR TABLOSUNA EK KOLONLAR
-- ===================================================================

-- Sevkiyat durumu için enum güncelle (eğer yoksa)
ALTER TABLE sevkiyatlar 
MODIFY COLUMN durum ENUM('hazirlaniyor', 'yolda', 'tamamlandi', 'iptal_edildi') DEFAULT 'hazirlaniyor';

-- Tamamlanma zamanı kolonu ekle (eğer yoksa)
ALTER TABLE sevkiyatlar 
ADD COLUMN IF NOT EXISTS tamamlanma_zamani DATETIME NULL;

-- Tamamlayan kullanıcı bilgisi ekle (eğer yoksa)
ALTER TABLE sevkiyatlar 
ADD COLUMN IF NOT EXISTS tamamlayan_kullanici_id INT NULL;

-- 3. CİHAZBİLGİ TABLOSUNA AKTIFLIK DURUMU
-- ===================================================================

-- Cihazın aktiflik durumu için kolon ekle (eğer yoksa)
ALTER TABLE cihazBilgi 
ADD COLUMN IF NOT EXISTS aktif BOOLEAN DEFAULT TRUE;

-- Son kullanım tarihi ekle (eğer yoksa)
ALTER TABLE cihazBilgi 
ADD COLUMN IF NOT EXISTS son_kullanim_tarihi DATETIME NULL;

-- 4. KULLANICILAR TABLOSUNA YETKİ SEVİYESİ
-- ===================================================================

-- Kullanıcı yetki seviyesi ekle (eğer yoksa)
ALTER TABLE kullanicilar 
MODIFY COLUMN gorev ENUM('admin', 'kullanici', 'viewer', 'firma_yoneticisi') DEFAULT 'kullanici';

-- 5. TETİKLEYİCİLER (TRIGGERS) 
-- ===================================================================

-- Sevkiyat tamamlandığında otomatik geçmiş kaydı oluşturan trigger
DELIMITER //

CREATE TRIGGER IF NOT EXISTS trigger_sevkiyat_tamamlandi
    AFTER UPDATE ON sevkiyatlar
    FOR EACH ROW
BEGIN
    -- Eğer durum 'tamamlandi' olarak değiştirildiyse
    IF NEW.durum = 'tamamlandi' AND OLD.durum != 'tamamlandi' THEN
        
        -- sevkiyatGecmis tablosuna kayıt ekle
        INSERT INTO sevkiyatGecmis (
            sevkiyat_ID, sevkiyat_adi, cihaz_kodu, plaka_no,
            gonderen_firma_id, alici_firma_id,
            baslangic_zamani, bitis_zamani, tamamlanma_zamani,
            net_agirlik, brut_agirlik, urun_id, palet_sayisi,
            sicaklik_araligi, cikis_lokasyon_id, varis_lokasyon_id,
            durum, musteri_ID, nakliyeci_id, 
            tamamlayan_kullanici_id, notlar
        ) VALUES (
            NEW.id, NEW.sevkiyat_adi, 
            (SELECT cihaz_kodu FROM cihazBilgi WHERE sevkiyat_id = NEW.id LIMIT 1),
            NEW.plaka_no,
            NEW.gonderen_firma_id, NEW.alici_firma_id,
            NEW.olusturma_zamani, NEW.tamamlanma_zamani, NOW(),
            NEW.net_agirlik, NEW.brut_agirlik, NEW.urun_id, NEW.palet_sayisi,
            NEW.sicaklik_araligi, NEW.cikis_lokasyon_id, NEW.varis_lokasyon_id,
            'tamamlandi', NEW.musteri_ID, NEW.nakliyeci_id,
            NEW.tamamlayan_kullanici_id, NEW.notlar
        );
        
        -- İlgili cihazları inaktif yap
        UPDATE cihazBilgi 
        SET aktif = FALSE, 
            son_kullanim_tarihi = NOW()
        WHERE sevkiyat_id = NEW.id;
        
    END IF;
END//

DELIMITER ;

-- 6. SEVKİYAT TAMAMLAMA STORED PROCEDURE
-- ===================================================================

DELIMITER //

CREATE PROCEDURE IF NOT EXISTS sp_sevkiyat_tamamla(
    IN p_sevkiyat_id INT,
    IN p_tamamlayan_kullanici_id INT,
    IN p_notlar TEXT
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- Sevkiyatı tamamla
    UPDATE sevkiyatlar 
    SET durum = 'tamamlandi',
        tamamlanma_zamani = NOW(),
        tamamlayan_kullanici_id = p_tamamlayan_kullanici_id,
        notlar = CONCAT(IFNULL(notlar, ''), '\n', 'Tamamlama: ', IFNULL(p_notlar, '')),
        guncelleme_zamani = NOW()
    WHERE id = p_sevkiyat_id;
    
    -- Eğer güncelleme başarılıysa commit
    IF ROW_COUNT() > 0 THEN
        COMMIT;
        SELECT 'Sevkiyat başarıyla tamamlandı' as sonuc, p_sevkiyat_id as sevkiyat_id;
    ELSE
        ROLLBACK;
        SELECT 'Sevkiyat bulunamadı' as sonuc, p_sevkiyat_id as sevkiyat_id;
    END IF;
    
END//

DELIMITER ;

-- 7. GEÇMİŞ SEVKİYAT SORGULARI İÇİN VİEW
-- ===================================================================

CREATE OR REPLACE VIEW view_gecmis_sevkiyatlar AS
SELECT 
    sg.id,
    sg.sevkiyat_ID,
    sg.sevkiyat_adi,
    sg.cihaz_kodu,
    sg.gonderen_firma_adi,
    sg.alici_firma_adi,
    sg.baslangic_zamani,
    sg.bitis_zamani,
    sg.tamamlanma_zamani,
    sg.net_agirlik,
    sg.brut_agirlik,
    sg.urun_adi,
    sg.durum,
    sg.musteri_ID,
    sg.musteri_adi,
    sg.nakliyeci_adi,
    sg.notlar,
    -- Süre hesaplama
    TIMESTAMPDIFF(HOUR, sg.baslangic_zamani, sg.bitis_zamani) as toplam_sure_saat,
    -- Sıcaklık bilgileri
    sg.sicaklik_min,
    sg.sicaklik_max,
    sg.sicaklik_ortalama
FROM sevkiyatGecmis sg
ORDER BY sg.tamamlanma_zamani DESC;

-- 8. YETKİ KONTROL FONKSİYONU
-- ===================================================================

DELIMITER //

CREATE FUNCTION IF NOT EXISTS fn_gecmis_sevkiyat_erisim_kontrolu(
    p_kullanici_id INT,
    p_sevkiyat_musteri_id INT
) RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_kullanici_gorev VARCHAR(50);
    DECLARE v_kullanici_musteri_id INT;
    
    -- Kullanıcı bilgilerini al
    SELECT gorev, musteri_ID 
    INTO v_kullanici_gorev, v_kullanici_musteri_id
    FROM kullanicilar 
    WHERE id = p_kullanici_id;
    
    -- Admin ise her şeye erişebilir
    IF v_kullanici_gorev = 'admin' THEN
        RETURN TRUE;
    END IF;
    
    -- Aynı müşteriye aitse erişebilir
    IF v_kullanici_musteri_id = p_sevkiyat_musteri_id THEN
        RETURN TRUE;
    END IF;
    
    -- Diğer durumda erişemez
    RETURN FALSE;
END//

DELIMITER ;

-- 9. ÖRNEK VERİ KONTROLÜ
-- ===================================================================

-- Mevcut aktif sevkiyatların listesi
/*
SELECT 
    id, sevkiyat_adi, durum, olusturma_zamani,
    CASE 
        WHEN durum != 'tamamlandi' THEN 'Aktif'
        ELSE 'Tamamlandı'
    END as sevkiyat_durumu
FROM sevkiyatlar
ORDER BY olusturma_zamani DESC
LIMIT 10;
*/

-- Tamamlanmış sevkiyatların geçmiş kayıtları
/*
SELECT 
    sevkiyat_ID, sevkiyat_adi, cihaz_kodu, 
    gonderen_firma_adi, alici_firma_adi,
    baslangic_zamani, tamamlanma_zamani, durum
FROM sevkiyatGecmis
ORDER BY tamamlanma_zamani DESC
LIMIT 10;
*/

-- Script tamamlandı
SELECT 'Sevkiyat Tamamlama Sistemi veritabanı düzenlemeleri tamamlandı!' as Status;

-- ===================================================================
-- KULLANIM TALİMATLARI
-- ===================================================================

-- Sevkiyat tamamlamak için stored procedure kullanımı:
-- CALL sp_sevkiyat_tamamla(sevkiyat_id, kullanici_id, 'Tamamlama notları');

-- Geçmiş sevkiyatları sorgulamak için view kullanımı:
-- SELECT * FROM view_gecmis_sevkiyatlar WHERE musteri_ID = [kullanici_musteri_id];

-- Yetki kontrolü için fonksiyon kullanımı:
-- SELECT fn_gecmis_sevkiyat_erisim_kontrolu(kullanici_id, sevkiyat_musteri_id);