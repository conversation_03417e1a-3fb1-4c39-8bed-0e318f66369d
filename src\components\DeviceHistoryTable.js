import React, { useState, useEffect, useCallback } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faHistory, faSync, faThermometerHalf, faDroplet, faBatteryHalf, faMapMarkerAlt, faCalendarAlt } from '@fortawesome/free-solid-svg-icons';

const DeviceHistoryTable = ({ cihazID, onHistoryDataChange, dateRange = {} }) => {
    const [historyData, setHistoryData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [selectedLimit, setSelectedLimit] = useState(10);
    const [totalRecords, setTotalRecords] = useState(0);

    // Pil voltajını yüzdeye çevirme (2.5V = %30, 3.4V = %100)
    const calculateBatteryPercentage = (voltage) => {
        const volt = parseFloat(voltage);
        if (isNaN(volt)) return 0;
        
        const minVoltage = 2.5;
        const maxVoltage = 3.4;
        const minPercentage = 30;
        const maxPercentage = 100;
        
        const percentage = minPercentage + ((volt - minVoltage) / (maxVoltage - minVoltage)) * (maxPercentage - minPercentage);
        return Math.round(Math.max(minPercentage, Math.min(maxPercentage, percentage)));
    };

    // Pil renk belirleme
    const getBatteryColor = (batteryPercentage) => {
        const bat = parseFloat(batteryPercentage);
        if (bat < 35) return 'text-danger';
        if (bat < 60) return 'text-warning';
        return 'text-success';
    };

    // Cihaz geçmişini API'den getir - useCallback ile optimize edildi
    const fetchDeviceHistory = useCallback(async () => {
        if (!cihazID) {
            setError('Cihaz ID bulunamadı');
            return;
        }

        try {
            setLoading(true);
            setError(null);
            
            const params = new URLSearchParams();
            
            // Tarih aralığı varsa öncelikle tarihleri ekle (API'nin beklediği parametre isimleri)
            if (dateRange.baslangic_tarihi) {
                params.append('baslangicTarihi', dateRange.baslangic_tarihi);
                console.log('DeviceHistoryTable - Başlangıç tarihi:', dateRange.baslangic_tarihi);
            }
            if (dateRange.bitis_tarihi) {
                params.append('bitisTarihi', dateRange.bitis_tarihi);
                console.log('DeviceHistoryTable - Bitiş tarihi:', dateRange.bitis_tarihi);
            }
            
            // Limit ekle (tarih aralığı ile birlikte çalışır)
            if (selectedLimit !== 'all') {
                params.append('limit', selectedLimit);
            }
            
            const queryString = params.toString() ? `?${params.toString()}` : '';
            console.log('🔍 DeviceHistoryTable - API URL:', `https://ffl21.fun:3001/api/cihaz/${cihazID}/history${queryString}`);
            console.log('📅 DeviceHistoryTable - Tarih Aralığı Kontrolü:', {
                baslangic: dateRange.baslangic_tarihi,
                bitis: dateRange.bitis_tarihi,
                expectedResult: `${dateRange.baslangic_tarihi} ile ${dateRange.bitis_tarihi} arası veriler`,
                dateRangeEmpty: Object.keys(dateRange).length === 0
            });
            
            // CRITICAL: Eğer dateRange boşsa tarih filtresi uygulanmaz!
            if (!dateRange.baslangic_tarihi || !dateRange.bitis_tarihi) {
                console.error('❌ TARİH FİLTRESİ UYGULANMIYOR! dateRange eksik:', dateRange);
            }
            const response = await fetch(`https://ffl21.fun:3001/api/cihaz/${cihazID}/history${queryString}`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            
            if (data.success && data.data) {
                // API'den gelen veri yapısına göre güncelle
                const historyRecords = data.data.gecmisVeriler || [];
                console.log('🆕 DeviceHistoryTable - API\'den gelen yeni veriler:', {
                    kayitSayisi: historyRecords.length,
                    ilkKayit: historyRecords[0]?.tarih,
                    sonKayit: historyRecords[historyRecords.length - 1]?.tarih,
                    totalRecords: data.data.pagination?.totalRecords
                });
                setHistoryData(historyRecords);
                setTotalRecords(data.data.pagination?.totalRecords || 0);
                
                // Parent componente konum verilerini gönder
                if (onHistoryDataChange) {
                    onHistoryDataChange(historyRecords);
                }
            } else {
                throw new Error(data.message || 'Cihaz geçmişi alınamadı');
            }
        } catch (err) {
            console.error('🚨 Cihaz geçmişi getirme hatası:', err);
            setError(err.message);
            setHistoryData([]);
        } finally {
            setLoading(false);
        }
    }, [cihazID, selectedLimit, dateRange?.baslangic_tarihi, dateRange?.bitis_tarihi, onHistoryDataChange]);

    // İlk yükleme ve dependency'ler değiştiğinde veri getir
    useEffect(() => {
        fetchDeviceHistory();
    }, [fetchDeviceHistory]);

    // Tarih formatla
    const formatDate = (dateString) => {
        if (!dateString) return 'Bilinmiyor';
        const date = new Date(dateString);
        return date.toLocaleString('tr-TR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    // Limit değişikliği
    const handleLimitChange = (e) => {
        setSelectedLimit(e.target.value === 'all' ? 'all' : parseInt(e.target.value));
    };

    if (!cihazID) {
        return null;
    }

    return (
        <div className="row mb-4">
            <div className="col-12">
                <div className="card border-0 shadow-sm">
                    <div className="card-header bg-light d-flex justify-content-between align-items-center">
                        <h6 className="mb-0">
                            <FontAwesomeIcon icon={faHistory} className="me-2 text-primary" />
                            Cihaz Veri Geçmişi - {cihazID}
                        </h6>
                        <div className="d-flex gap-2 align-items-center">
                            <select 
                                className="form-select form-select-sm" 
                                style={{ width: 'auto' }}
                                value={selectedLimit}
                                onChange={handleLimitChange}
                                disabled={loading}
                            >
                                <option value={10}>Son 10</option>
                                <option value={50}>Son 50</option>
                                <option value={100}>Son 100</option>
                                <option value={500}>Son 500</option>
                                <option value="all">Tümü</option>
                            </select>
                            <button 
                                className="btn btn-sm btn-outline-primary"
                                onClick={fetchDeviceHistory}
                                disabled={loading}
                                title="Yenile"
                            >
                                <FontAwesomeIcon icon={faSync} className={loading ? 'fa-spin' : ''} />
                            </button>
                        </div>
                    </div>
                    <div className="card-body p-0">
                        {loading && (
                            <div className="text-center py-4">
                                <div className="spinner-border text-primary" role="status">
                                    <span className="visually-hidden">Veriler yükleniyor...</span>
                                </div>
                                <div className="mt-2">Cihaz geçmişi yükleniyor...</div>
                            </div>
                        )}

                        {error && (
                            <div className="alert alert-warning m-3">
                                <strong>Veri alınamadı:</strong> {error}
                                <br />
                                <small>API endpointi kontrol edilsin: https://ffl21.fun:3001/api/cihaz/{cihazID}/history</small>
                            </div>
                        )}

                        {!loading && !error && historyData.length === 0 && (
                            <div className="alert alert-info m-3">
                                <FontAwesomeIcon icon={faHistory} className="me-2" />
                                Bu cihaz için henüz veri kaydı bulunmuyor.
                            </div>
                        )}

                        {!loading && !error && historyData.length > 0 && (
                            <>
                                <div className="table-responsive">
                                    <table className="table table-hover mb-0">
                                        <thead className="table-dark">
                                            <tr>
                                                <th scope="col">
                                                    <FontAwesomeIcon icon={faCalendarAlt} className="me-1" />
                                                    Tarih
                                                </th>
                                                <th scope="col">
                                                    <FontAwesomeIcon icon={faThermometerHalf} className="me-1" />
                                                    Sıcaklık
                                                </th>
                                                <th scope="col">
                                                    <FontAwesomeIcon icon={faDroplet} className="me-1" />
                                                    Nem
                                                </th>
                                                <th scope="col">
                                                    <FontAwesomeIcon icon={faBatteryHalf} className="me-1" />
                                                    Pil
                                                </th>
                                                <th scope="col">
                                                    <FontAwesomeIcon icon={faMapMarkerAlt} className="me-1" />
                                                    Konum
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {historyData.map((record, index) => {
                                                // İlk kayıt için debug log
                                                if (index === 0) {
                                                    console.log('🔄 Tabloda render edilen ilk kayıt:', {
                                                        tarih: record.tarih,
                                                        sicaklik: record.sensorler?.sicaklik,
                                                        index: index
                                                    });
                                                }
                                                
                                                const batteryPercentage = calculateBatteryPercentage(record.sensorler?.pil || 0);
                                                const sicaklik = record.sensorler?.sicaklik || 0;
                                                const nem = record.sensorler?.nem || 0;
                                                const enlem = record.konum?.enlem;
                                                const boylam = record.konum?.boylam;
                                                
                                                return (
                                                    <tr key={index}>
                                                        <td>
                                                            <small>{formatDate(record.tarih)}</small>
                                                        </td>
                                                        <td>
                                                            <span className={sicaklik < 0 ? 'text-primary' : sicaklik > 30 ? 'text-danger' : 'text-success'}>
                                                                {sicaklik}°C
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <span className={nem > 80 ? 'text-danger' : nem > 60 ? 'text-warning' : 'text-success'}>
                                                                {nem}%
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <span className={getBatteryColor(batteryPercentage)}>
                                                                {batteryPercentage}%
                                                            </span>
                                                            {batteryPercentage < 35 && (
                                                                <small className="text-danger d-block">⚠️ Düşük</small>
                                                            )}
                                                        </td>
                                                        <td>
                                                            {enlem && boylam ? (
                                                                <small className="text-muted">
                                                                    {parseFloat(enlem).toFixed(4)}, {parseFloat(boylam).toFixed(4)}
                                                                </small>
                                                            ) : (
                                                                <small className="text-muted">Konum yok</small>
                                                            )}
                                                        </td>
                                                    </tr>
                                                );
                                            })}
                                        </tbody>
                                    </table>
                                </div>
                                <div className="card-footer bg-light">
                                    <div className="d-flex justify-content-between align-items-center">
                                        <small className="text-muted">
                                            {dateRange.baslangic_tarihi && dateRange.bitis_tarihi ? (
                                                `Sevkiyat dönemi kayıtları, gösterilen: ${historyData.length}`
                                            ) : totalRecords > 0 ? (
                                                `Toplam ${totalRecords} kayıt, gösterilen: ${historyData.length}`
                                            ) : (
                                                `${historyData.length} kayıt gösteriliyor`
                                            )}
                                        </small>
                                        <small className="text-muted">
                                            Son güncelleme: {new Date().toLocaleTimeString('tr-TR')}
                                        </small>
                                    </div>
                                </div>
                            </>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default DeviceHistoryTable;