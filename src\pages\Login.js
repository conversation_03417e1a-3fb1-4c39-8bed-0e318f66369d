import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import logoDark from '../assets/img/logo-dark.png';
import backgroundImg from '../assets/img/on-the-road.jpg';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faEye, faEyeSlash } from '@fortawesome/free-solid-svg-icons';
import { authService } from '../api/dbService';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';

const Login = () => {
    const navigate = useNavigate();
    const [formData, setFormData] = useState({
        email: '',
        password: '',
        rememberMe: false
    });

    const [showPassword, setShowPassword] = useState(false);
    const [error, setError] = useState('');
    const [loading, setLoading] = useState(false);

    const handleChange = (e) => {
        const { name, value, type, checked } = e.target;
        setFormData({
            ...formData,
            [name]: type === 'checkbox' ? checked : value
        });
    };

    const togglePasswordVisibility = () => {
        setShowPassword(!showPassword);
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setError('');
        setLoading(true);

        try {
            // API üzerinden giriş yap
            const response = await authService.login(formData.email, formData.password);

            if (response.success && response.user && response.token) {
                console.log('Giriş başarılı:', response.user);

                // Kullanıcı bilgilerini standardize ederek localStorage'a kaydet
                const storageData = {
                    id: response.user.musteri_ID,
                    musteri_ID: response.user.musteri_ID,
                    name: response.user.musteri_adi || 'Kullanıcı',
                    email: response.user.email,
                    username: response.user.kullanici,
                    role: response.user.gorev || 'user',
                    tel: response.user.tel,
                    firma: response.user.firma,
                    adres: response.user.adres
                };

                // localStorage'a user objesi ve token'ı kaydet
                const userDataToStore = { 
                    user: storageData,
                    token: response.token
                };
                localStorage.setItem('user', JSON.stringify(userDataToStore));
                localStorage.setItem('auth_token', response.token);

                // Debug için localStorage'a kaydedilen veriyi logla
                console.log('=== Login.js Debug ===');
                console.log('Storing to localStorage:', userDataToStore);
                console.log('Final stored data:', JSON.parse(localStorage.getItem('user')));

                // Ana sayfaya yönlendir
                navigate('/');
            } else {
                throw new Error(response.message || 'Giriş işlemi başarısız');
            }
        } catch (error) {
            console.error('Giriş hatası:', error);

            // Hata mesajını kullanıcı dostu hale getir
            let errorMessage = 'E-posta adresi veya parola hatalı!';

            if (error.response?.status === 401) {
                errorMessage = 'E-posta adresi veya parola hatalı!';
            } else if (error.response?.status === 500) {
                errorMessage = 'Sunucu hatası. Lütfen daha sonra tekrar deneyin.';
            } else if (error.message) {
                errorMessage = error.message;
            }

            setError(errorMessage);
        } finally {
            setLoading(false);
        }
    };

    // Stil tanımlamaları
    const styles = {
        // Arkaplan stili
        loginContainer: {
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '100vh',
            backgroundImage: `url(${backgroundImg})`,
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'center center',
            backgroundSize: 'cover',
            position: 'relative'
        },
        // Arkaplan kaplama stili
        overlay: {
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(0, 0, 0, 0.1)',
            zIndex: 1
        },
        // Kart stili
        card: {
            width: '100%',
            maxWidth: '450px',
            padding: '30px 25px',
            backgroundColor: '#fff',
            borderRadius: '8px',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.2)',
            border: '1px solid #e0e0e0',
            position: 'relative',
            zIndex: 2
        },
        // Logo stili
        logo: {
            maxWidth: '250px',
            marginBottom: '1rem'
        },
        // Form kontrol stili
        formControl: {
            height: '48px',
            borderRadius: '4px',
            border: '1px solid #ced4da',
            padding: '10px 15px',
            fontSize: '16px',
            width: '100%'
        },
        // Giriş butonu stili
        btnInfo: {
            backgroundColor: '#00c3f7',
            borderColor: '#00c3f7',
            color: '#fff',
            fontWeight: 500,
            borderRadius: '4px',
            height: '48px',
            fontSize: '16px',
            width: '100%',
            border: 'none',
            cursor: 'pointer',
            marginBottom: '1rem'
        },
        // Form grubu stili
        formGroup: {
            marginBottom: '1rem',
            position: 'relative'
        },
        // Check box container
        checkGroup: {
            marginBottom: '1rem',
            display: 'flex',
            alignItems: 'center',
            backgroundColor: '#fff'
        },
        // Check box input
        checkInput: {
            marginRight: '8px'
        },
        // Password toggle icon
        passwordToggle: {
            position: 'absolute',
            right: '15px',
            top: '15px',
            cursor: 'pointer',
            color: '#777'
        },
        // Error message
        errorMessage: {
            color: '#dc3545',
            fontSize: '14px',
            marginBottom: '15px',
            textAlign: 'center',
            backgroundColor: '#f8d7da',
            padding: '8px',
            borderRadius: '4px',
            border: '1px solid #f5c6cb'
        },
        // Kullanıcı bilgileri alanı
        tempUserInfo: {
            marginTop: '20px',
            padding: '10px',
            backgroundColor: '#f8f9fa',
            borderRadius: '4px',
            border: '1px solid #dee2e6',
            fontSize: '14px',
            color: '#6c757d',
            textAlign: 'center'
        }
    };

    return (
        <div style={styles.loginContainer}>
            <div style={styles.overlay}></div>
            <div style={styles.card}>
                <div style={{ textAlign: 'center', marginBottom: '1.5rem' }}>
                    <img src={logoDark} alt="MGZ24 Logo" style={styles.logo} />
                </div>

                <form onSubmit={handleSubmit}>
                    {error && (
                        <ErrorMessage 
                            message={error} 
                            variant="danger" 
                            dismissible={true}
                            onDismiss={() => setError('')}
                        />
                    )}

                    <div style={styles.formGroup}>
                        <input
                            type="email"
                            style={styles.formControl}
                            placeholder="Email adresiniz"
                            name="email"
                            value={formData.email}
                            onChange={handleChange}
                            required
                            autoComplete="username"
                        />
                    </div>

                    <div style={styles.formGroup}>
                        <input
                            type={showPassword ? "text" : "password"}
                            style={styles.formControl}
                            placeholder="Parola"
                            name="password"
                            value={formData.password}
                            onChange={handleChange}
                            required
                            autoComplete="current-password"
                        />
                        <FontAwesomeIcon
                            icon={showPassword ? faEyeSlash : faEye}
                            style={styles.passwordToggle}
                            onClick={togglePasswordVisibility}
                        />
                    </div>

                    <div style={styles.checkGroup}>
                        <input
                            type="checkbox"
                            style={styles.checkInput}
                            id="rememberMe"
                            name="rememberMe"
                            checked={formData.rememberMe}
                            onChange={handleChange}
                        />
                        <label htmlFor="rememberMe" style={{ color: '#333', backgroundColor: 'transparent' }}>
                            Beni hatırla
                        </label>
                    </div>

                    <button
                        type="submit"
                        style={styles.btnInfo}
                        disabled={loading}
                    >
                        {loading ? (
                            <LoadingSpinner 
                                size="sm" 
                                variant="light" 
                                message="Giriş yapılıyor..." 
                                centered={false}
                            />
                        ) : (
                            'Giriş'
                        )}
                    </button>


                    <hr />

                    <div style={{ textAlign: 'center', color: '#6c757d' }}>
                        &copy; 2025 Inkatech Ölçüm Sistemleri
                    </div>
                </form>
            </div>
        </div>
    );
};

export default Login;