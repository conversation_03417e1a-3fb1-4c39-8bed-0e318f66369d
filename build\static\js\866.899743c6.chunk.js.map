{"version": 3, "file": "static/js/866.899743c6.chunk.js", "mappings": "gKASA,MAwGA,EAxGgBA,KACZ,MAAMC,GAAWC,EAAAA,EAAAA,MAaXC,EAAuB,UAVTC,MAChB,IAAK,IAADC,EAAAC,EACA,MAAMC,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C,OAAW,OAAJJ,QAAI,IAAJA,GAAU,QAANF,EAAJE,EAAMA,YAAI,IAAAF,OAAN,EAAJA,EAAYO,QAAY,OAAJL,QAAI,IAAJA,GAAU,QAAND,EAAJC,EAAMA,YAAI,IAAAD,OAAN,EAAJA,EAAYO,QAAS,MACpD,CAAE,MAAAC,GACE,MAAO,MACX,GAGaV,GAGjB,OACIW,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC1DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6BAA6BG,SAAS,KAAKC,GAAG,UAAU,kBAAgB,UAASH,SAAA,EAC5FC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,EAC7BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,kBAAkBI,GAAG,UAASH,SAAC,gBAC7CF,EAAAA,EAAAA,KAAA,UAAQM,KAAK,SAASL,UAAU,YAAY,kBAAgB,YAAY,iBAAe,WAAW,aAAW,cAEjHE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mEAAkEC,SAAA,EAC7EF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,kCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,MAAtBtB,EAASuB,SAAmB,SAAW,IAAMC,GAAG,IAAGR,SAAA,EAC5GF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMC,EAAAA,MAAW,0BAG1Cb,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,SAAtBtB,EAASuB,SAAsB,SAAW,IAAMC,GAAG,OAAMR,SAAA,EAClHF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAME,EAAAA,MAAgB,mCAG/Cd,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,sBAAtBtB,EAASuB,SAAmC,SAAW,IAAMC,GAAG,oBAAmBR,SAAA,EAC5IF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMG,EAAAA,MAAY,8BAG3Cf,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMI,EAAAA,MAAmB,sCAKtDhB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wBACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,CAEpCd,IACGY,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMK,EAAAA,MAAa,0BAIhDjB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,mBAAtBtB,EAASuB,SAAgC,SAAW,IAAMC,GAAG,iBAAgBR,SAAA,EACtIF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMM,EAAAA,MAAU,uBAK7ClB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,uCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMO,EAAAA,MAAU,qCAGzCnB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMQ,EAAAA,MAAgB,6CAKnDpB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMS,EAAAA,MAAgB,qBAG/CrB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,cAAtBtB,EAASuB,SAA2B,SAAW,IAAMC,GAAG,YAAWR,SAAA,EAC5HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMU,EAAAA,MAAe,4BAKlDtB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wBAG1I,C,sDC3Gd,MAQA,EAReqB,KAEPvB,EAAAA,EAAAA,KAAA,UAAQC,UAAU,kBAAiBC,UAC/BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,cAAaC,SAAC,iD,qGCEvC,MA6GA,EA7GesB,KACX,MAAMC,GAAWC,EAAAA,EAAAA,OACVC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,YAClCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAGvCG,EAAAA,EAAAA,YAAU,KACgBC,WAClB,IAAK,IAADC,EAAAC,EAAAC,EAAAC,EAEA,MAAMC,EAAa7C,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C2C,GAAmB,OAAVD,QAAU,IAAVA,GAAgB,QAANJ,EAAVI,EAAY9C,YAAI,IAAA0C,OAAN,EAAVA,EAAkBM,cAAwB,OAAVF,QAAU,IAAVA,GAAgB,QAANH,EAAVG,EAAY9C,YAAI,IAAA2C,OAAN,EAAVA,EAAkB9B,IAC3DsB,GAAqB,OAAVW,QAAU,IAAVA,GAAgB,QAANF,EAAVE,EAAY9C,YAAI,IAAA4C,OAAN,EAAVA,EAAkBK,QAAkB,OAAVH,QAAU,IAAVA,GAAgB,QAAND,EAAVC,EAAY9C,YAAI,IAAA6C,OAAN,EAAVA,EAAkBK,aAE7D,IAAKH,EAID,OAHAI,QAAQC,KAAK,kCACbhB,EAAY,gBACZG,GAAW,GAKf,GAAIJ,EAGA,OAFAC,EAAYD,QACZI,GAAW,GAKf,IACI,MAAMc,QAAiBC,EAAAA,GAAiBC,aAAaR,GAGjDM,GAAYA,EAASH,cACrBd,EAAYiB,EAASH,aAGP,OAAVJ,QAAU,IAAVA,GAAAA,EAAY9C,OACZ8C,EAAW9C,KAAKiD,KAAOI,EAASH,YAChC/C,aAAaqD,QAAQ,OAAQvD,KAAKwD,UAAUX,KAGxD,CAAE,MAAOY,GACLP,QAAQC,KAAK,gGAEbhB,EAAYD,GAAY,sBAC5B,CACJ,CAAE,MAAOwB,GACLR,QAAQQ,MAAM,0DAAuCA,GAErDvB,EAAY,sBAChB,CAAC,QACGG,GAAW,EACf,GAGJqB,EAAe,GAChB,IAWH,OACIjD,EAAAA,EAAAA,MAAA,UAAQF,UAAU,0DAA0D,gBAAc,OAAMC,SAAA,EAC5FF,EAAAA,EAAAA,KAACO,EAAAA,GAAI,CAACN,UAAU,gEAAgES,GAAG,IAAGR,UAClFF,EAAAA,EAAAA,KAAA,OAAKqD,I,60RAAeC,IAAI,aAAaC,OAAO,UAEhDpD,EAAAA,EAAAA,MAAA,MAAIF,UAAU,yCAAwCC,SAAA,EAClDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8CAA6CC,UACvDC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,aAAYC,SAAA,EACxBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMO,EAAAA,IAAQlB,UAAU,SACxC6B,EAAU,mBAAkBH,QAGrC3B,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4BAA2BC,UACrCF,EAAAA,EAAAA,KAAA,UACIwD,QAtBCC,KAEjB9D,aAAa+D,WAAW,QAGxBjC,EAAS,SAAS,EAkBFxB,UAAU,gBACV,iBAAe,UACf,oBAAkB,OAClB,gBAAc,+BAAYC,UAE1BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM+C,EAAAA,WAG/B3D,EAAAA,EAAAA,KAAA,MAAIC,UAAU,iCAAgCC,UAC1CF,EAAAA,EAAAA,KAAA,UACIC,UAAU,gBACVK,KAAK,SACL,iBAAe,YACf,iBAAe,WACf,gBAAc,UACd,gBAAc,QACd,aAAW,kBAAcJ,UAEzBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMgD,EAAAA,eAI9B,C,8JC1GjB,MAwKA,EAxKwBC,IAAgD,IAA/C,QAAEC,EAAO,UAAEC,EAAS,kBAAEC,GAAmBH,EAEhE,MAAOI,EAAcC,IAAmBrC,EAAAA,EAAAA,WAAS,IAC1CsB,EAAOgB,IAAYtC,EAAAA,EAAAA,UAAS,MAyHnC,OACE7B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,CAEvBiD,IACCnD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qBAAoBC,UACjCF,EAAAA,EAAAA,KAAA,SAAAE,SAAQiD,OAKZhD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oDAAmDC,SAAA,EAChEF,EAAAA,EAAAA,KAAA,OAAAE,UACEF,EAAAA,EAAAA,KAAA,UACEC,UAAU,iBACVuD,QArIcvB,UACxB,IAIE,GAHAiC,GAAgB,GAChBC,EAAS,OAEJJ,GAAkC,IAArBA,EAAUK,OAE1B,YADAD,EAAS,uDAKX,MAAME,EAAM,IAAIC,EAAAA,GAGhBD,EAAIE,QAAQ,YAAa,UAGzBF,EAAIG,YAAY,MAGhB,MAAMC,EAAmBC,GAChBA,EACJC,QAAQ,UAAM,KACdA,QAAQ,UAAM,KACdA,QAAQ,QAAM,KACdA,QAAQ,QAAM,KACdA,QAAQ,UAAM,KACdA,QAAQ,UAAM,KACdA,QAAQ,UAAM,KACdA,QAAQ,UAAM,KACdA,QAAQ,QAAM,KACdA,QAAQ,QAAM,KACdA,QAAQ,QAAM,KACdA,QAAQ,QAAM,KAInBN,EAAIO,YAAY,IAChBP,EAAIE,QAAQ,YAAa,QACzB,MAAMM,EAAYR,EAAIS,SAASC,SAASC,MAClCC,EAAQR,EAAgB,2BAExBS,GAAUL,EADGR,EAAIc,aAAaF,IACM,EAC1CZ,EAAIK,KAAKO,EAAOC,EAAQ,IAGxBb,EAAIO,YAAY,IAChBP,EAAIE,QAAQ,YAAa,UACzB,MAAMa,GAAc,IAAIC,MAAOC,mBAAmB,SAC5CC,EAAcxB,EAAUK,OACxBoB,EAAWf,EAAgB,aAADjE,OAAcsD,EAAO,sBAAAtD,OAAqB4E,EAAW,sBAAA5E,OAAqB+E,IAEpGE,GAAaZ,EADGR,EAAIc,aAAaK,IACS,EAChDnB,EAAIK,KAAKc,EAAUC,EAAW,IAG9B,MAAMC,EAA8BC,IAClC,MAAMC,EAAOC,WAAWF,GACxB,GAAIG,MAAMF,GAAO,OAAO,EAExB,MAKMG,EAHgB,IAGeH,EALlB,MACA,IADA,KAKgE,GACnF,OAAOI,KAAKC,MAAMD,KAAKE,IAJD,GAIoBF,KAAKG,IAHzB,IAG4CJ,IAAa,EAI3EK,EAAe,CAAC,QAAS,WAAY,MAAO,UAAW,QAAS,UAAUC,KAAIC,GAAU7B,EAAgB6B,KACxGC,EAAYxC,EAAUsC,KAAIG,IAAG,IAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,MAAI,CACrCL,EAAIM,OAASN,EAAIO,QAAS,IAAI1B,MAAOC,mBAAmB,SAC3C,QAAbmB,EAAAD,EAAIQ,iBAAS,IAAAP,GAAbA,EAAeQ,SAAQ,GAAAzG,OAAMgG,EAAIQ,UAAUC,SAAQ,SAAO,IAC7C,QAAbP,EAAAF,EAAIQ,iBAAS,IAAAN,GAAbA,EAAeQ,IAAG,GAAA1G,OAAMgG,EAAIQ,UAAUE,IAAG,KAAM,IAClC,QAAbP,EAAAH,EAAIQ,iBAAS,IAAAL,GAAbA,EAAeQ,IAAG,GAAA3G,OAAMkF,EAA2Bc,EAAIQ,UAAUG,KAAI,KAAM,KAClE,QAATP,EAAAJ,EAAIY,aAAK,IAAAR,OAAA,EAATA,EAAWS,QAAS,KACX,QAATR,EAAAL,EAAIY,aAAK,IAAAP,OAAA,EAATA,EAAWS,SAAU,IACtB,KAGDC,EAAAA,EAAAA,IAAUlD,EAAK,CACbmD,KAAM,CAACpB,GACPqB,KAAMlB,EACNmB,OAAQ,GACRC,OAAQ,CACNC,SAAU,EACVC,YAAa,GAEfC,WAAY,CACVC,UAAW,CAAC,GAAI,IAAK,KACrBC,UAAW,IACXC,UAAW,QAEbC,mBAAoB,CAClBH,UAAW,CAAC,IAAK,IAAK,MAExBI,OAAQ,CAAEC,IAAK,GAAIC,KAAM,GAAIC,MAAO,MAItC,MAAMC,EAAQ,sBAAA/H,OAAyBsD,EAAO,KAAAtD,QAAI,IAAI6E,MAAOmD,cAAcC,MAAM,KAAK,GAAE,QACxFpE,EAAIqE,KAAKH,GAGLvE,GACFA,EAAkB,MAAOuE,EAG7B,CAAE,MAAOpF,GACPR,QAAQQ,MAAM,wCAA+BA,GAC7CgB,EAAS,oDAA4ChB,EAAMwF,QAC7D,CAAC,QACCzE,GAAgB,EAClB,GAoBU0E,SAAU3E,EAAa/D,SAEtB+D,GACC9D,EAAAA,EAAAA,MAAA0I,EAAAA,SAAA,CAAA3I,SAAA,EACEF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMkI,EAAAA,IAAW7I,UAAU,iBAAiB,4BAI/DE,EAAAA,EAAAA,MAAA0I,EAAAA,SAAA,CAAA3I,SAAA,EACEF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMmI,EAAAA,IAAW9I,UAAU,SAAS,yBAQ7DD,EAAAA,EAAAA,KAAA,OAAAE,UACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,aAAYC,SAAC,2DAMhC,E,cCrKV,MAiRA,EAjR2B2D,IAAkC,IAAjC,QAAEC,EAAO,UAAEkF,EAAY,CAAC,GAAGnF,EACnD,MAAOoF,EAAYC,IAAiBrH,EAAAA,EAAAA,UAAS,OACtCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCsB,EAAOgB,IAAYtC,EAAAA,EAAAA,UAAS,OAC5BsH,EAAYC,IAAiBvH,EAAAA,EAAAA,UAAS,MAGvCwH,EAAkBpH,UACpB,IAAK6B,EAGD,OAFAK,EAAS,iCACTpC,GAAW,GAIf,IACIoC,EAAS,MACT,MAAMmF,QAAiBC,MAAM,oCAAD/I,OAAqCsD,IAEjE,IAAKwF,EAASE,GACV,MAAM,IAAIC,MAAM,QAADjJ,OAAS8I,EAASI,OAAM,MAAAlJ,OAAK8I,EAASK,aAGzD,MAAMC,QAAaN,EAASO,OAE5B,IAAID,EAAKE,UAAWF,EAAKA,KAarB,MAAM,IAAIH,MAAMG,EAAKjB,SAAW,oCAbL,CAAC,IAADoB,EAAAC,EAAAC,EAAAC,EAAAC,EAE3B,MAAMC,GAAaC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACZT,EAAKA,MAAI,IACZ3C,UAAgC,QAAtB8C,EAAAH,EAAKA,KAAKU,oBAAY,IAAAP,OAAA,EAAtBA,EAAwB9C,WAAY2C,EAAKA,KAAK3C,SACxDC,KAA2B,QAAtB8C,EAAAJ,EAAKA,KAAKU,oBAAY,IAAAN,OAAA,EAAtBA,EAAwB9C,MAAO0C,EAAKA,KAAK1C,IAC9CC,KAA2B,QAAtB8C,EAAAL,EAAKA,KAAKU,oBAAY,IAAAL,OAAA,EAAtBA,EAAwB9C,MAAOyC,EAAKA,KAAKzC,IAC9CoD,MAA4B,QAAtBL,EAAAN,EAAKA,KAAKU,oBAAY,IAAAJ,OAAA,EAAtBA,EAAwBK,OAAQX,EAAKA,KAAKW,KAChDC,QAA8B,QAAtBL,EAAAP,EAAKA,KAAKU,oBAAY,IAAAH,OAAA,EAAtBA,EAAwBK,SAAUZ,EAAKA,KAAKY,SAExDtB,EAAckB,GACdhB,GAAc,IAAI/D,MAAOoF,qBAC7B,CAGJ,CAAE,MAAOC,GACL/H,QAAQQ,MAAM,oCAAgCuH,GAC9CvG,EAASuG,EAAI/B,QACjB,CAAC,QACG5G,GAAW,EACf,IAIJC,EAAAA,EAAAA,YAAU,KACNqH,GAAiB,GAClB,CAACvF,IAKJ,MAAM6G,EAAuBC,IACzB,MAAMC,EAAchF,WAAW+E,GAC/B,OAAIC,EAAc,EAAU,eACxBA,EAAc,GAAW,YACzBA,EAAc,GAAW,cACtB,cAAc,EAInBC,EAAoBC,IACtB,MAAMC,EAAMnF,WAAWkF,GACvB,OAAIC,EAAM,GAAW,cACjBA,EAAM,GAAW,eACd,cAAc,EAInBtF,EAA8BC,IAChC,MAAMC,EAAOC,WAAWF,GACxB,GAAIG,MAAMF,GAAO,OAAO,EAExB,MAMMG,EAJgB,IAIeH,EANlB,MACA,IADA,KAMgE,GAGnF,OAAOI,KAAKC,MAAMD,KAAKE,IAPD,GAOoBF,KAAKG,IANzB,IAM4CJ,IAAa,EAI7EkF,EAAmBC,IACrB,MAAMC,EAAMtF,WAAWqF,GACvB,OAAIC,EAAM,GAAW,cACjBA,EAAM,GAAW,eACd,cAAc,EAGzB,OAAKrH,EAIDhC,GAEI9B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACrBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,SAAQC,UACnBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0BAAyBC,UACpCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6BAA4BC,SAAA,EACvCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8BAA8BJ,KAAK,SAAQK,UACtDF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,kBAAiBC,SAAC,uCAEtCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,SAAC,6CAQ1CiD,GAEInD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACrBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,SAAQC,UACnBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0BAAyBC,SAAA,EACpCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qFAAoFC,SAAA,EAC/FC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,OAAMC,SAAA,EAChBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMwK,EAAAA,IAAmBnL,UAAU,SAAS,oBAC3C6D,MAEtB3D,EAAAA,EAAAA,MAAA,OAAKF,UAAU,eAAcC,SAAA,EACzBF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,kBAAiBC,SAAC,UAClCF,EAAAA,EAAAA,KAAA,UACIC,UAAU,8BACVuD,QAAS6F,EACTpE,MAAM,eAAc/E,UAEpBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMyK,EAAAA,eAInCrL,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACtBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2BAA0BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,8BAAwB,IAAEiD,GAClCnD,EAAAA,EAAAA,KAAA,UACAG,EAAAA,EAAAA,MAAA,SAAAD,SAAA,CAAO,mEAAiE4D,kBAUhG9D,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACrBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,SAAQC,UACnBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0BAAyBC,SAAA,EACpCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,+FAA8FC,SAAA,EACzGC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,OAAMC,SAAA,EAChBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMwK,EAAAA,IAAmBnL,UAAU,SAAS,oBAC3C6D,MAEtB3D,EAAAA,EAAAA,MAAA,OAAKF,UAAU,eAAcC,SAAA,EACzBF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mBAAkBC,SAAC,gBACnCF,EAAAA,EAAAA,KAAA,UACIC,UAAU,+BACVuD,QAAS6F,EACTpE,MAAM,SAAQ/E,UAEdF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMyK,EAAAA,eAInClL,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACtBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAChBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC1BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2CAA0CC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACjBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CACZC,KAAMwK,EAAAA,IACNnL,UAAW0K,EAAoB1B,EAAWhC,UAAY,KACtDqE,KAAK,UAGbnL,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACIF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,SAAC,wBAClCC,EAAAA,EAAAA,MAAA,OAAKF,UAAS,WAAAO,OAAamK,EAAoB1B,EAAWhC,UAAY,MAAO/G,SAAA,CACxE+I,EAAWhC,UAAY,IAAI,oBAK5CjH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC1BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2CAA0CC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACjBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CACZC,KAAM2K,EAAAA,IACNtL,UAAW6K,EAAiB7B,EAAW/B,KAAO,KAC9CoE,KAAK,UAGbnL,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACIF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,SAAC,SAClCC,EAAAA,EAAAA,MAAA,OAAKF,UAAS,WAAAO,OAAasK,EAAiB7B,EAAW/B,KAAO,MAAOhH,SAAA,CAAC,IAChE+I,EAAW/B,KAAO,gBAKpClH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC1BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2CAA0CC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACjBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CACZC,KAAM4K,EAAAA,IACNvL,UAAWgL,EAAgBvF,EAA2BuD,EAAW9B,KAAO,MACxEmE,KAAK,UAGbnL,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACIF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,SAAC,SAClCC,EAAAA,EAAAA,MAAA,OAAKF,UAAS,WAAAO,OAAayK,EAAgBvF,EAA2BuD,EAAW9B,KAAO,OAAQjH,SAAA,CAAC,IAC3FwF,EAA2BuD,EAAW9B,KAAO,QAElDzB,EAA2BuD,EAAW9B,KAAO,KAAO,KACjDnH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,4BAA2BC,SAAC,wDAO3DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC1BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gCAA+BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACjBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM6K,EAAAA,IAAaxL,UAAU,eAAeqL,KAAK,UAEtEnL,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACIF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,SAAC,oBAClCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uBAAsBC,SAChC+I,EAAWsB,MAAQ,kBAMxCpK,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2BAA0BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACrBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4BAA2BC,SAAA,EACtCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM8K,EAAAA,IAAgBzL,UAAU,uBACjDD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,aAAYC,SAAC,YAC7BF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,4BAA2BC,SACtC+I,EAAW5B,OAAS4B,EAAW3B,OAAM,GAAA9G,OAC7BqF,WAAWoD,EAAW5B,OAAOsE,QAAQ,GAAE,MAAAnL,OAAKqF,WAAWoD,EAAW3B,QAAQqE,QAAQ,IACrF,4BAKlBxL,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAC9BF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,aAAYC,SAAC,YAC7BF,EAAAA,EAAAA,KAAA,QAAMC,UAAS,cAAAO,OAAgByI,EAAW2C,MAAQ,aAAe,gBAAiB1L,SAC7E+I,EAAW2C,MAAQ,QAAU,UAEjCzC,IACGhJ,EAAAA,EAAAA,MAAA,QAAMF,UAAU,kBAAiBC,SAAA,CAAC,sBAAiBiJ,qBArKxE,IA4KD,EC6Bd,EA3S2BtF,IAAuD,IAAtD,QAAEC,EAAO,oBAAE+H,EAAmB,UAAE7C,EAAY,CAAC,GAAGnF,EACxE,MAAOiI,EAAaC,IAAkBlK,EAAAA,EAAAA,UAAS,KACxCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCsB,EAAOgB,IAAYtC,EAAAA,EAAAA,UAAS,OAC5BmK,EAAeC,IAAoBpK,EAAAA,EAAAA,UAAS,KAC5CqK,EAAcC,IAAmBtK,EAAAA,EAAAA,UAAS,GAiB3CoJ,EAAmBC,IACrB,MAAMC,EAAMtF,WAAWqF,GACvB,OAAIC,EAAM,GAAW,cACjBA,EAAM,GAAW,eACd,cAAc,EAInBiB,GAAqBC,EAAAA,EAAAA,cAAYpK,UACnC,GAAK6B,EAKL,IACI/B,GAAW,GACXoC,EAAS,MAET,MAAMmI,EAAS,IAAIC,gBAGfvD,EAAUwD,mBACVF,EAAOG,OAAO,kBAAmBzD,EAAUwD,kBAC3C7J,QAAQ+J,IAAI,sDAA0C1D,EAAUwD,mBAEhExD,EAAU2D,eACVL,EAAOG,OAAO,cAAezD,EAAU2D,cACvChK,QAAQ+J,IAAI,0CAAsC1D,EAAU2D,eAI1C,QAAlBX,GACAM,EAAOG,OAAO,QAAST,GAG3B,MAAMY,EAAcN,EAAOO,WAAU,IAAArM,OAAO8L,EAAOO,YAAe,GAClElK,QAAQ+J,IAAI,6CAAmC,oCAADlM,OAAsCsD,EAAO,YAAAtD,OAAWoM,IACtGjK,QAAQ+J,IAAI,8EAAmD,CAC3DI,UAAW9D,EAAUwD,iBACrBO,MAAO/D,EAAU2D,aACjBK,eAAe,GAADxM,OAAKwI,EAAUwD,iBAAgB,SAAAhM,OAAQwI,EAAU2D,aAAY,uBAC3EM,eAAkD,IAAlCC,OAAOC,KAAKnE,GAAW5E,SAItC4E,EAAUwD,kBAAqBxD,EAAU2D,cAC1ChK,QAAQQ,MAAM,sEAAmD6F,GAErE,MAAMM,QAAiBC,MAAM,oCAAD/I,OAAqCsD,EAAO,YAAAtD,OAAWoM,IAEnF,IAAKtD,EAASE,GACV,MAAM,IAAIC,MAAM,QAADjJ,OAAS8I,EAASI,OAAM,MAAAlJ,OAAK8I,EAASK,aAGzD,MAAMC,QAAaN,EAASO,OAE5B,IAAID,EAAKE,UAAWF,EAAKA,KAiBrB,MAAM,IAAIH,MAAMG,EAAKjB,SAAW,6CAjBL,CAAC,IAADyE,EAAAC,EAAAC,EAAAC,EAE3B,MAAMC,EAAiB5D,EAAKA,KAAK6D,eAAiB,GAClD9K,QAAQ+J,IAAI,gEAAwD,CAChEgB,YAAaF,EAAepJ,OAC5BuJ,SAA2B,QAAnBP,EAAEI,EAAe,UAAE,IAAAJ,OAAA,EAAjBA,EAAmBtG,MAC7B8G,SAAmD,QAA3CP,EAAEG,EAAeA,EAAepJ,OAAS,UAAE,IAAAiJ,OAAA,EAAzCA,EAA2CvG,MACrDoF,aAAkC,QAAtBoB,EAAE1D,EAAKA,KAAKiE,kBAAU,IAAAP,OAAA,EAApBA,EAAsBpB,eAExCH,EAAeyB,GACfrB,GAAoC,QAApBoB,EAAA3D,EAAKA,KAAKiE,kBAAU,IAAAN,OAAA,EAApBA,EAAsBrB,eAAgB,GAGlDL,GACAA,EAAoB2B,EAE5B,CAGJ,CAAE,MAAO9C,GACL/H,QAAQQ,MAAM,0DAAoCuH,GAClDvG,EAASuG,EAAI/B,SACboD,EAAe,GACnB,CAAC,QACGhK,GAAW,EACf,MAvEIoC,EAAS,2BAuEb,GACD,CAACL,EAASkI,EAAwB,OAAThD,QAAS,IAATA,OAAS,EAATA,EAAWwD,iBAA2B,OAATxD,QAAS,IAATA,OAAS,EAATA,EAAW2D,aAAcd,KAGlF7J,EAAAA,EAAAA,YAAU,KACNoK,GAAoB,GACrB,CAACA,IAGJ,MAAM0B,EAAcC,IAChB,IAAKA,EAAY,MAAO,aAExB,OADa,IAAI1I,KAAK0I,GACVC,eAAe,QAAS,CAChCC,IAAK,UACLC,MAAO,UACPC,KAAM,UACNC,KAAM,UACNC,OAAQ,WACV,EAQN,OAAKvK,GAKD9D,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACrBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,SAAQC,UACnBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0BAAyBC,SAAA,EACpCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yEAAwEC,SAAA,EACnFC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,OAAMC,SAAA,EAChBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM0N,EAAAA,IAAWrO,UAAU,sBAAsB,gCAC5C6D,MAE1B3D,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kCAAiCC,SAAA,EAC5CC,EAAAA,EAAAA,MAAA,UACIF,UAAU,6BACVsO,MAAO,CAAEvJ,MAAO,QAChBwJ,MAAOxC,EACPyC,SAtBDC,IACvBzC,EAAoC,QAAnByC,EAAEC,OAAOH,MAAkB,MAAQI,SAASF,EAAEC,OAAOH,OAAO,EAsBrD5F,SAAU9G,EAAQ5B,SAAA,EAElBF,EAAAA,EAAAA,KAAA,UAAQwO,MAAO,GAAGtO,SAAC,YACnBF,EAAAA,EAAAA,KAAA,UAAQwO,MAAO,GAAGtO,SAAC,YACnBF,EAAAA,EAAAA,KAAA,UAAQwO,MAAO,IAAItO,SAAC,aACpBF,EAAAA,EAAAA,KAAA,UAAQwO,MAAO,IAAItO,SAAC,aACpBF,EAAAA,EAAAA,KAAA,UAAQwO,MAAM,MAAKtO,SAAC,mBAExBF,EAAAA,EAAAA,KAAA,UACIC,UAAU,iCACVuD,QAAS4I,EACTxD,SAAU9G,EACVmD,MAAM,SAAQ/E,UAEdF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMyK,EAAAA,IAAQpL,UAAW6B,EAAU,UAAY,cAI5E3B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeC,SAAA,CACzB4B,IACG3B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,EAC7BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8BAA8BJ,KAAK,SAAQK,UACtDF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,kBAAiBC,SAAC,gCAEtCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,SAAC,8CAI7BiD,IACGhD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0BAAyBC,SAAA,EACpCF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,8BAAwB,IAAEiD,GAClCnD,EAAAA,EAAAA,KAAA,UACAG,EAAAA,EAAAA,MAAA,SAAAD,SAAA,CAAO,mEAAiE4D,EAAQ,kBAItFhC,IAAYqB,GAAgC,IAAvB2I,EAAY1H,SAC/BjE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACjCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM0N,EAAAA,IAAWrO,UAAU,SAAS,4DAK3D6B,IAAYqB,GAAS2I,EAAY1H,OAAS,IACxCjE,EAAAA,EAAAA,MAAA0I,EAAAA,SAAA,CAAA3I,SAAA,EACIF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,UAC7BC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,yBAAwBC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,aAAYC,UACzBC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACIC,EAAAA,EAAAA,MAAA,MAAI0O,MAAM,MAAK3O,SAAA,EACXF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMkO,EAAAA,IAAe7O,UAAU,SAAS,YAG7DE,EAAAA,EAAAA,MAAA,MAAI0O,MAAM,MAAK3O,SAAA,EACXF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMwK,EAAAA,IAAmBnL,UAAU,SAAS,yBAGjEE,EAAAA,EAAAA,MAAA,MAAI0O,MAAM,MAAK3O,SAAA,EACXF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM2K,EAAAA,IAAWtL,UAAU,SAAS,UAGzDE,EAAAA,EAAAA,MAAA,MAAI0O,MAAM,MAAK3O,SAAA,EACXF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM4K,EAAAA,IAAevL,UAAU,SAAS,UAG7DE,EAAAA,EAAAA,MAAA,MAAI0O,MAAM,MAAK3O,SAAA,EACXF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM8K,EAAAA,IAAgBzL,UAAU,SAAS,iBAKtED,EAAAA,EAAAA,KAAA,SAAAE,SACK4L,EAAYzF,KAAI,CAAC0I,EAAQC,KAAW,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAEfC,EAAH,IAAVN,GACArM,QAAQ+J,IAAI,qDAAuC,CAC/C5F,MAAOiI,EAAOjI,MACdG,SAA0B,QAAlBqI,EAAEP,EAAO/H,iBAAS,IAAAsI,OAAA,EAAhBA,EAAkBrI,SAC5B+H,MAAOA,IAIf,MAAM9D,EA9NdvF,KAChC,MAAMC,EAAOC,WAAWF,GACxB,GAAIG,MAAMF,GAAO,OAAO,EAExB,MAKMG,EAHgB,IAGeH,EALlB,MACA,IADA,KAKgE,GACnF,OAAOI,KAAKC,MAAMD,KAAKE,IAJD,GAIoBF,KAAKG,IAHzB,IAG4CJ,IAAa,EAoNbL,EAA2C,QAAhBuJ,EAAAF,EAAO/H,iBAAS,IAAAiI,OAAA,EAAhBA,EAAkB9H,MAAO,GACxEF,GAA2B,QAAhBiI,EAAAH,EAAO/H,iBAAS,IAAAkI,OAAA,EAAhBA,EAAkBjI,WAAY,EACzCC,GAAsB,QAAhBiI,EAAAJ,EAAO/H,iBAAS,IAAAmI,OAAA,EAAhBA,EAAkBjI,MAAO,EAC/BG,EAAoB,QAAf+H,EAAGL,EAAO3H,aAAK,IAAAgI,OAAA,EAAZA,EAAc/H,MACtBC,EAAqB,QAAf+H,EAAGN,EAAO3H,aAAK,IAAAiI,OAAA,EAAZA,EAAc/H,OAE7B,OACInH,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACIF,EAAAA,EAAAA,KAAA,MAAAE,UACIF,EAAAA,EAAAA,KAAA,SAAAE,SAAQ4N,EAAWiB,EAAOjI,YAE9B9G,EAAAA,EAAAA,KAAA,MAAAE,UACIC,EAAAA,EAAAA,MAAA,QAAMF,UAAWgH,EAAW,EAAI,eAAiBA,EAAW,GAAK,cAAgB,eAAe/G,SAAA,CAC3F+G,EAAS,cAGlBjH,EAAAA,EAAAA,KAAA,MAAAE,UACIC,EAAAA,EAAAA,MAAA,QAAMF,UAAWiH,EAAM,GAAK,cAAgBA,EAAM,GAAK,eAAiB,eAAehH,SAAA,CAClFgH,EAAI,UAGb/G,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACIC,EAAAA,EAAAA,MAAA,QAAMF,UAAWgL,EAAgBC,GAAmBhL,SAAA,CAC/CgL,EAAkB,OAEtBA,EAAoB,KACjBlL,EAAAA,EAAAA,KAAA,SAAOC,UAAU,sBAAqBC,SAAC,sCAG/CF,EAAAA,EAAAA,KAAA,MAAAE,SACKmH,GAASC,GACNnH,EAAAA,EAAAA,MAAA,SAAOF,UAAU,aAAYC,SAAA,CACxB2F,WAAWwB,GAAOsE,QAAQ,GAAG,KAAG9F,WAAWyB,GAAQqE,QAAQ,OAGhE3L,EAAAA,EAAAA,KAAA,SAAOC,UAAU,aAAYC,SAAC,kBA5BjC8O,EA+BJ,YAMzBhP,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uBAAsBC,UACjCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oDAAmDC,SAAA,EAC9DF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,aAAYC,SACxB8I,EAAUwD,kBAAoBxD,EAAU2D,aAAY,0DAAAnM,OACPsL,EAAY1H,QACtD8H,EAAe,EAAC,UAAA1L,OACN0L,EAAY,gCAAA1L,OAAuBsL,EAAY1H,QAAM,GAAA5D,OAE5DsL,EAAY1H,OAAM,kCAG7BjE,EAAAA,EAAAA,MAAA,SAAOF,UAAU,aAAYC,SAAA,CAAC,uBACT,IAAImF,MAAOoF,mBAAmB,8BA5J5E,IAqKD,E,wBC5Qd,MAAM8E,EAAY,CAAC,UA07BnB,EAx7BqBC,KAAO,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EACvB,MAAM,GAAE5P,IAAO6P,EAAAA,EAAAA,KACTzO,GAAWC,EAAAA,EAAAA,OACVI,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCsB,EAAOgB,IAAYtC,EAAAA,EAAAA,UAAS,MAG7BsO,EAAaxQ,aAAaC,QAAQ,QAClCiD,EAAWsN,EAAa1Q,KAAKC,MAAMyQ,GAAc,KACjD3Q,EAAOqD,EAAWA,EAASrD,KAAO,MAMjC4Q,EAAWC,IAAgBxO,EAAAA,EAAAA,UAAS,CAAEyO,IAAK,QAASC,IAAK,WACzDC,EAAMC,IAAW5O,EAAAA,EAAAA,UAAS,GAC3B6O,GAASC,EAAAA,EAAAA,QAAO,OAGfC,EAAgBC,IAAqBhP,EAAAA,EAAAA,WAAS,IAC9CiP,EAAkBC,IAAuBlP,EAAAA,EAAAA,UAAS,KAMlDmP,EAAgBC,IAAqBpP,EAAAA,EAAAA,UAAS,OAG9CqP,EAAcC,IAAmBtP,EAAAA,EAAAA,WAAS,IAC1CuP,EAAmBC,IAAwBxP,EAAAA,EAAAA,WAAS,IACpDyP,EAAeC,IAAoB1P,EAAAA,EAAAA,UAAS,KAG5C2P,EAAkBC,IAAuB5P,EAAAA,EAAAA,UAAS,KAClD6P,EAAkBC,IAAuB9P,EAAAA,EAAAA,WAAS,IAGnD,SAAE+P,GAAQ,UAAEC,KAAcC,EAAAA,EAAAA,IAAe,CAC3CzR,GAAI,oBACJ0R,iBA7BW,0CA8BXxC,UAAWA,IAITyC,IAAY3F,EAAAA,EAAAA,cAAahG,IAC3BqK,EAAOuB,QAAU5L,EAGbA,GACAA,EAAI6L,YAAY,gBAAgB,KAC5BzB,EAAQpK,EAAI8L,UAAU,GAE9B,GACD,IAIGC,IAAoB/F,EAAAA,EAAAA,cAAagG,IACnC1P,QAAQ+J,IAAI,6CAA8C2F,GAC1DZ,EAAoBY,EAAU,GAC/B,IAGGC,IAAkBC,EAAAA,EAAAA,UAAQ,KAC5B,GAAmB,OAAdvB,QAAc,IAAdA,IAAAA,EAAgBwB,gBAEjB,OADA7P,QAAQC,KAAK,4EACN,CAAC,EAIZ,MAAM6P,EAAoB1E,IACtB,IAAKA,EAAY,OAAO,KAKxB,MAAM2E,EAAO,IAAIrN,KAAK0I,GAEhB4E,EADe,IAAItN,KAAKqN,EAAKE,UAAa,MACjBpK,cAAc7D,QAAQ,IAAK,KAAK8D,MAAM,KAAK,GAS1E,OAPA9F,QAAQ+J,IAAI,yDAA0B,CAClCmG,SAAU9E,EACV+E,OAAQJ,EAAKlK,cACbuK,aAAcJ,EACdK,WAAY,kDAGTL,CAAS,EAGpB,IAAIM,EAAWC,EAsBf,OApBIlC,EAAemC,aAAenC,EAAeoC,kBAE7CH,EAAYR,EAAiBzB,EAAewB,iBAC5CU,EAAUT,EAAiBzB,EAAeoC,oBAG1CH,EAAYR,EAAiBzB,EAAewB,iBAC5CU,EAAUT,GAAiB,IAAIpN,MAAOmD,gBAG1C7F,QAAQ+J,IAAI,uEAA+C,CACvDF,iBAAkByG,EAClBtG,aAAcuG,EACdC,YAAanC,EAAemC,YAC5BE,cAAerC,EAAewB,gBAC9Bc,YAAatC,EAAeoC,iBAC5BG,aAAcvC,EAAemC,YAAc,0BAAoB,iBAC/DK,kBAAoBxC,EAAewB,kBAGhC,CACHhG,iBAAkByG,EAClBtG,aAAcuG,EACjB,GACF,CAAe,OAAdlC,QAAc,IAAdA,OAAc,EAAdA,EAAgBwB,gBAA+B,OAAdxB,QAAc,IAAdA,OAAc,EAAdA,EAAgBoC,iBAAgC,OAAdpC,QAAc,IAAdA,OAAc,EAAdA,EAAgBmC,eAGvFnR,EAAAA,EAAAA,YAAU,KACYC,WACd,IACIF,GAAW,GACXoC,EAAS,MAGT,MAAMsP,QAAyBC,EAAAA,GAAgBC,gBAAgBtT,GAG/D,IAAIuT,EAAiB,GACrB,IACI,GAAIH,EAAiBI,WAAY,CAE7B,MAAMvK,QAAiBC,MAAM,oCAAD/I,OAAqCiT,EAAiBI,aAClF,GAAIvK,EAASE,GAAI,CACb,MAAMI,QAAaN,EAASO,OACxBD,EAAKE,SAAWF,EAAKA,OACrBgK,EAAiB,CAAChK,EAAKA,MAE/B,CACJ,CACJ,CAAE,MAAOkK,GACLnR,QAAQC,KAAK,kDAADpC,OAAsCiT,EAAiBI,WAAU,MAAMC,EAEvF,CAGA,MAAMC,EAAmBH,GAAkBA,EAAexP,OAAS,EAC7DwP,EAAe,GACf,KAoBN,GAjBAjR,QAAQ+J,IAAI,uBAAwBkH,GACpCjR,QAAQ+J,IAAI,sBAAuBqH,GACf,OAAhBA,QAAgB,IAAhBA,GAAAA,EAAkBC,UAClBrR,QAAQ+J,IAAI,oBAAqBqH,EAAiBC,UAEtDrR,QAAQ+J,IAAI,uCAAmC+G,EAAiBQ,iBAAkB,cAAeR,EAAiBS,mBAI7GT,EAAiBQ,kBAClBtR,QAAQQ,MAAM,2CAAuC,CACjD8Q,iBAAkBR,EAAiBQ,iBACnCE,YAAaV,EAAiBpT,IAAMA,IAKxCoT,EAAkB,CAAC,IAADW,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAElB,MAAMC,EAAW1B,EAAiB2B,SAAW3B,EAAiB4B,gBAAkB5B,EAAiB6B,gBAAkB,aAC7GC,EAAS9B,EAAiB+B,QAAU/B,EAAiBgC,gBAAkBhC,EAAiBiC,aAAe,aACvGC,EAAclC,EAAiBmC,WAAanC,EAAiBoC,YAAc,aAC3EC,EAAcrC,EAAiBsC,MAAQtC,EAAiBuC,cAAgB,aAGxE7C,EAAiD,IAAnCM,EAAiBwC,eAA0D,MAAnCxC,EAAiBwC,cAE7EhF,EAAkB,CACd5Q,GAAIoT,EAAiBpT,IAAMA,EAC3B6V,WAAYzC,EAAiB0C,aAAe,aAC5CC,QAAS3C,EAAiBI,YAAc,aACxCpR,KAAMgR,EAAiB4C,cAAgB,sBACvCC,MAAO7C,EAAiB8C,UAAY,YACpCC,KAAMrB,EACNzU,GAAI6U,EACJkB,QAASd,EACTe,QAASZ,EACTa,QAASlD,EAAiBI,YAAc,sBACxC+C,QAAqC,QAA7BxC,EAAAX,EAAiBoD,oBAAY,IAAAzC,OAAA,EAA7BA,EAA+BvH,aAAc,IACrDiK,KAAiC,QAA5BzC,EAAAZ,EAAiBsD,mBAAW,IAAA1C,OAAA,EAA5BA,EAA8BxH,aAAc,IACjDmK,OAAoC,QAA7B1C,EAAAb,EAAiBwD,oBAAY,IAAA3C,OAAA,EAA7BA,EAA+BzH,aAAc,IACpDqK,MAAOpJ,GAAW2F,EAAiBQ,mBAAqB,aACxD9K,WAAY2E,GAAW2F,EAAiB0D,mBAAmB,IAAS,aACpEhE,YAAaA,EACbX,gBAAiBiB,EAAiBQ,iBAClCb,iBAAkBK,EAAiBS,kBACnChV,SAAU,CACNoR,IAAqB,OAAhByD,QAAgB,IAAhBA,GAA0B,QAAVQ,EAAhBR,EAAkBC,gBAAQ,IAAAO,GAA1BA,EAA4BlN,MAAQxB,WAAWkO,EAAiBC,SAAS3M,OAAS,QACvFkJ,IAAqB,OAAhBwD,QAAgB,IAAhBA,GAA0B,QAAVS,EAAhBT,EAAkBC,gBAAQ,IAAAQ,GAA1BA,EAA4BlN,OAASzB,WAAWkO,EAAiBC,SAAS1M,QAAU,QACzF8P,QAAS,6BAEbvM,YAAa,CACT2D,OAAuB,OAAhBuF,QAAgB,IAAhBA,GAA8B,QAAdU,EAAhBV,EAAkBzJ,oBAAY,IAAAmK,GAAU,QAAVC,EAA9BD,EAAgCxN,gBAAQ,IAAAyN,OAAxB,EAAhBA,EAA0C7H,aAAc,IAC/D1G,KAAMsN,EAAiB4D,kBAAoB,cAAW5O,MAAM,KAAK,GAAG9D,QAAQ,QAAM,IAAI2S,OACtFpR,KAAmE,QAA9DyO,GAAClB,EAAiB4D,kBAAoB,cAAW5O,MAAM,KAAK,UAAE,IAAAkM,OAAA,EAA9DA,EAAgEhQ,QAAQ,QAAM,IAAI2S,SAAU,MAErGvM,SAAU,CACNyD,OAAuB,OAAhBuF,QAAgB,IAAhBA,GAA8B,QAAda,EAAhBb,EAAkBzJ,oBAAY,IAAAsK,GAAK,QAALC,EAA9BD,EAAgC1N,WAAG,IAAA2N,OAAnB,EAAhBA,EAAqChI,aAAc,IAC1D1G,IAAK,IACLD,IAAK,MAETqR,MAAO,CACH/I,OAAuB,OAAhBuF,QAAgB,IAAhBA,GAA8B,QAAde,EAAhBf,EAAkBzJ,oBAAY,IAAAwK,GAAM,QAANC,EAA9BD,EAAgCvK,YAAI,IAAAwK,OAApB,EAAhBA,EAAsClI,aAAc,IAC3DlE,QAAkD,UAAzB,OAAhBoL,QAAgB,IAAhBA,GAA8B,QAAdiB,EAAhBjB,EAAkBzJ,oBAAY,IAAA0K,OAAd,EAAhBA,EAAgCwC,MAAkB,6BAAkB,uBAKrF7U,QAAQ+J,IAAI,8CAAgC,CACxC8F,gBAAiBiB,EAAiBQ,iBAClCb,iBAAkBK,EAAiBS,kBACnCf,YAAaA,IAIG,OAAhBY,QAAgB,IAAhBA,GAA0B,QAAVkB,EAAhBlB,EAAkBC,gBAAQ,IAAAiB,GAA1BA,EAA4B5N,OAAyB,OAAhB0M,QAAgB,IAAhBA,GAA0B,QAAVmB,EAAhBnB,EAAkBC,gBAAQ,IAAAkB,GAA1BA,EAA4B5N,QACjE+I,EAAa,CACTC,IAAKzK,WAAWkO,EAAiBC,SAAS3M,OAC1CkJ,IAAK1K,WAAWkO,EAAiBC,SAAS1M,SAItD,CAEAvF,GAAW,EACf,CAAE,MAAO2I,GACL/H,QAAQQ,MAAM,4BAAwBuH,GAGtC,IAAI+M,EAAe,sDACfC,EAAY,qBAEZhN,EAAIpB,SAEwB,MAAxBoB,EAAIpB,SAASI,QACbgO,EAAY,6BACZD,EAAe/M,EAAI/B,SAAO,GAAAnI,OAAOH,EAAE,oIAEnCoX,GAAY,wBAAAjX,OAAuBkK,EAAIpB,SAASI,QAC5CgB,EAAIpB,SAASM,MAAQc,EAAIpB,SAASM,KAAKjB,UACvC8O,GAAY,MAAAjX,OAAUkK,EAAIpB,SAASM,KAAKjB,WAG5B,iBAAb+B,EAAIiN,MACXD,EAAY,6BACZD,EAAe,wHACK,eAAb/M,EAAIiN,MACXD,EAAY,iCACZD,EAAe,mJACR/M,EAAIkN,UAEXF,EAAY,sBACZD,EAAe,2GAGnBtT,EAAS,CAAE7D,KAAMoX,EAAW/O,QAAS8O,IACrC1V,GAAW,GA2CXkP,EAxCiB,CACb5Q,GAAIA,GAAM,WACV6V,WAAY,YACZE,QAAS,YACT3T,KAAM,sBACN6T,MAAO,aACPE,KAAM,WACN9V,GAAI,UACJ+V,QAAS,YACTC,QAAS,QACTC,QAAS,YACTC,OAAQ,KACRE,IAAK,OACLE,MAAO,QACPE,MAAO,aACP/N,WAAY,mBACZgK,aAAa,EACbX,gBAAiB,sBACjBY,iBAAkB,KAClBlU,SAAU,CACNoR,IAAK,QACLC,IAAK,QACL6G,QAAS,6BAEbvM,YAAa,CACT2D,MAAO,OACPrI,IAAK,KACLD,IAAK,MAET6E,SAAU,CACNyD,MAAO,KACPrI,IAAK,IACLD,IAAK,MAETqR,MAAO,CACH/I,MAAO,MACP7F,QAAS,sBAKrB,GAGJkP,EAAW,GASZ,CAACxX,KAGJ2B,EAAAA,EAAAA,YAAU,KACN,MAAM8V,EAAenV,QAAQC,KAQ7B,OAPAD,QAAQC,KAAO,WAAc,IAAD,IAAAmV,EAAAC,UAAA5T,OAAT6T,EAAI,IAAAC,MAAAH,GAAAI,EAAA,EAAAA,EAAAJ,EAAAI,IAAJF,EAAIE,GAAAH,UAAAG,GACfF,EAAK,IAAyB,kBAAZA,EAAK,IAAmBA,EAAK,GAAGG,SAAS,qCAG/DN,EAAaO,MAAM1V,QAASsV,EAChC,EAEO,KACHtV,QAAQC,KAAOkV,CAAY,CAC9B,GACF,IAGH,MAAMhK,GAAa,SAACC,GAAqC,IAAzBuK,EAAWN,UAAA5T,OAAA,QAAAmU,IAAAP,UAAA,IAAAA,UAAA,GACvC,IAAKjK,EAAY,MAAO,GAExB,MAAM2E,EAAO,IAAIrN,KAAK0I,GAChBE,EAAMyE,EAAK8F,UAAU3L,WAAW4L,SAAS,EAAG,KAC5CvK,GAASwE,EAAKgG,WAAa,GAAG7L,WAAW4L,SAAS,EAAG,KACrDtK,EAAOuE,EAAKiG,cAElB,GAAIL,EAAa,CACb,MAAMM,EAAQlG,EAAKmG,WAAWhM,WAAW4L,SAAS,EAAG,KAC/CK,EAAUpG,EAAKqG,aAAalM,WAAW4L,SAAS,EAAG,KACzD,MAAM,GAANjY,OAAUyN,EAAG,KAAAzN,OAAI0N,EAAK,KAAA1N,OAAI2N,EAAI,KAAA3N,OAAIoY,EAAK,KAAApY,OAAIsY,EAC/C,CAEA,MAAM,GAANtY,OAAUyN,EAAG,KAAAzN,OAAI0N,EAAK,KAAA1N,OAAI2N,EAC9B,EAwDM6K,GAA4BA,KAC9B,IAAKhI,EAAgB,MAAO,IAC5B,MAAM,IAAEV,EAAG,IAAEC,GAAQS,EAAe9R,SACpC,MAAM,sDAANsB,OAA6D8P,EAAG,KAAA9P,OAAI+P,EAAG,EAI3E,GAAIzO,IAAYkP,EACZ,OACI7Q,EAAAA,EAAAA,MAAA0I,EAAAA,SAAA,CAAA3I,SAAA,EACIF,EAAAA,EAAAA,KAACwB,EAAAA,EAAM,KACPxB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAChBF,EAAAA,EAAAA,KAACf,EAAAA,EAAO,KACRe,EAAAA,EAAAA,KAAA,QAAMC,UAAU,iFAAgFC,UAC5FC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mDAAmDsO,MAAO,CAAEhL,OAAQ,QAASrD,SAAA,EACxFF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8BAA8BJ,KAAK,SAAQK,UACtDF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,kBAAiBC,SAAC,wBAEtCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,OAAMC,SAAC,qDAUnD,GAAIiD,EACA,OACIhD,EAAAA,EAAAA,MAAA0I,EAAAA,SAAA,CAAA3I,SAAA,EACIF,EAAAA,EAAAA,KAACwB,EAAAA,EAAM,KACPxB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAChBF,EAAAA,EAAAA,KAACf,EAAAA,EAAO,KACRkB,EAAAA,EAAAA,MAAA,QAAMF,UAAU,iFAAgFC,SAAA,EAC5FF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAAmCC,UAC9CC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,oBAAmBC,SAAA,EAC7BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMqY,EAAAA,IAAShZ,UAAU,sBAAsB,IAC9DI,EAAG,wBAIbL,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACrBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,SAAQC,UACnBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qBAAoBC,SAAA,EAC/BC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,gBAAeC,SAAA,EACzBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMM,EAAAA,IAAQjB,UAAU,SAAS,yCAGtDD,EAAAA,EAAAA,KAAA,KAAAE,SAAG,8GACHF,EAAAA,EAAAA,KAAA,UACAG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oDAAmDC,SAAA,EAC9DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,OAAMC,SAAC,4EAIpBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACtBC,EAAAA,EAAAA,MAAA,UACIF,UAAU,kBACVuD,QAASA,IAAM0V,OAAOha,SAASia,SAASjZ,SAAA,EAExCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMM,EAAAA,IAAQjB,UAAU,SAAS,8BAUzE+Q,IACGhR,EAAAA,EAAAA,KAAA6I,EAAAA,SAAA,CAAA3I,UAEIF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACrBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,SAAQC,UACnBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACjCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kCAAiCC,UAC5CC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oDAAmDC,SAAA,EAC9DF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,eAAcC,SAAE8Q,EAAevO,QAC7CzC,EAAAA,EAAAA,KAAA,SAAAE,SAAO,oBAGfF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACtBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wBAAuBC,UAClCC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,sDAAqDC,SAAA,EAClEF,EAAAA,EAAAA,KAAA,SAAAE,UACIC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACIF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,eACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,iBACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,gBACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,uBACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,cACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,aACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,YACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,eACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,qBAGZF,EAAAA,EAAAA,KAAA,SAAAE,UACIC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACIF,EAAAA,EAAAA,KAAA,MAAAE,SAAK8Q,EAAe3Q,MACpBL,EAAAA,EAAAA,KAAA,MAAAE,SAAK8Q,EAAekF,cACpBlW,EAAAA,EAAAA,KAAA,MAAAE,UACIF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,8BAA6BC,SACxC8Q,EAAeoF,aAGxBpW,EAAAA,EAAAA,KAAA,MAAAE,SAAK8Q,EAAevO,QACpBzC,EAAAA,EAAAA,KAAA,MAAAE,SAAK8Q,EAAesF,SACpBtW,EAAAA,EAAAA,KAAA,MAAAE,SAAK8Q,EAAewF,QACpBxW,EAAAA,EAAAA,KAAA,MAAAE,SAAK8Q,EAAetQ,MACpBV,EAAAA,EAAAA,KAAA,MAAAE,SAAK8Q,EAAeyF,WACpBzW,EAAAA,EAAAA,KAAA,MAAAE,SAAK8Q,EAAe0F,0CAoEpF,OACIvW,EAAAA,EAAAA,MAAA0I,EAAAA,SAAA,CAAA3I,SAAA,EACIF,EAAAA,EAAAA,KAACwB,EAAAA,EAAM,KACPxB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAChBF,EAAAA,EAAAA,KAACf,EAAAA,EAAO,KAGRkB,EAAAA,EAAAA,MAAA,QAAMF,UAAU,iFAAgFC,SAAA,EAC5FF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAAmCC,UAC9CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oDAAmDC,UAC9DC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,oBAAmBC,SAAA,EAC7BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMqY,EAAAA,IAAShZ,UAAU,sBAAsB,cACtDD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,UAASC,SAAE8Q,EAAekF,aACnDlF,EAAeoF,SAAsC,eAA3BpF,EAAeoF,UACtCjW,EAAAA,EAAAA,MAAA,QAAMF,UAAU,wBAAuBC,SAAA,CAAC,UAAQ8Q,EAAeoF,mBAO/EpW,EAAAA,EAAAA,KAACoZ,EAAkB,CACftV,QAAuB,OAAdkN,QAAc,IAAdA,OAAc,EAAdA,EAAgBoF,QACzBpN,UAAW,CACPwD,iBAAgC,OAAdwE,QAAc,IAAdA,OAAc,EAAdA,EAAgBwB,gBAClC7F,aAA4B,OAAdqE,QAAc,IAAdA,OAAc,EAAdA,EAAgBoC,qBAKtCpT,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACrBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,SAAQC,UACnBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAClDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yEAAwEC,UACnFC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,iBAAgBC,SAAA,EAC1BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMqY,EAAAA,IAAShZ,UAAU,sBAAsB,+BAIxED,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC1BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,UAC7BC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,uCAAsCC,SAAA,EACnDF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,aAAYC,UACzBC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACIF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,eACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,iBACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,gBACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,uBACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,cACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,aACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,YACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,eACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,gBACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,WACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,SACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,aACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,aACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,mCAGZF,EAAAA,EAAAA,KAAA,SAAAE,UACIC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACIF,EAAAA,EAAAA,KAAA,MAAAE,SAAK8Q,EAAe3Q,MACpBL,EAAAA,EAAAA,KAAA,MAAAE,SAAK8Q,EAAekF,cACpBlW,EAAAA,EAAAA,KAAA,MAAAE,UACIF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mBAAkBC,SAC7B8Q,EAAeoF,aAGxBpW,EAAAA,EAAAA,KAAA,MAAAE,SAAK8Q,EAAevO,QACpBzC,EAAAA,EAAAA,KAAA,MAAAE,SAAK8Q,EAAesF,SACpBtW,EAAAA,EAAAA,KAAA,MAAAE,SAAK8Q,EAAewF,QACpBxW,EAAAA,EAAAA,KAAA,MAAAE,SAAK8Q,EAAetQ,MACpBV,EAAAA,EAAAA,KAAA,MAAAE,SAAK8Q,EAAeyF,SAAW,gBAC/BzW,EAAAA,EAAAA,KAAA,MAAAE,SAAK8Q,EAAe0F,WACpB1W,EAAAA,EAAAA,KAAA,MAAAE,SAAK8Q,EAAe4F,UACpB5W,EAAAA,EAAAA,KAAA,MAAAE,SAAK8Q,EAAe8F,OACpB9W,EAAAA,EAAAA,KAAA,MAAAE,SAAK8Q,EAAegG,SACpBhX,EAAAA,EAAAA,KAAA,MAAAE,SAAK4N,GAAWkD,EAAewB,iBAAiB,MAChDxS,EAAAA,EAAAA,KAAA,MAAAE,UACIC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYC,SAAA,EACvBF,EAAAA,EAAAA,KAAA,SACIC,UAAU,mBACVK,KAAK,WACL+Y,QAASrI,EAAemC,YACxB1E,SAlUjC6K,KAC3BjI,GAAqB,EAAK,EAkUkCzI,SAAUsI,GAAgBF,EAAemC,eAE7CnT,EAAAA,EAAAA,KAAA,SAAOC,UAAU,mBAAkBC,SAAC,sDAe5EF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACrBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,SAAQC,UACnBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAuCC,UAClDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAgBsO,MAAO,CAAEhL,OAAQ,OAAQgW,UAAW,qCAAsCrZ,SAAA,CACpG2R,KACG7R,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qBAAoBC,SAAC,qEAKtC0R,IAQEzR,EAAAA,EAAAA,MAAA,OAAKoO,MAAO,CAAEiL,SAAU,WAAYxU,MAAO,OAAQzB,OAAQ,QAASrD,SAAA,CAE/DsR,EAAiBpN,OAAS,IACvBpE,EAAAA,EAAAA,KAAA,OAAKuO,MAAO,CAAEiL,SAAU,WAAYpR,IAAK,GAAIC,KAAM,GAAIoR,OAAQ,GAAIvZ,UAC/DC,EAAAA,EAAAA,MAAA,UACIqD,QAASA,IAAMmO,GAAqBD,GACpCzR,UAAU,mCACVsO,MAAO,CAAE3G,SAAU,QAAS1H,SAAA,EAE5BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM8Y,EAAAA,IAAezZ,UAAU,SAC/CyR,EAAmB,uBAAiB,oCAAAlR,OAA4BgR,EAAiBpN,OAAM,WAKpGjE,EAAAA,EAAAA,MAACwZ,EAAAA,GAAS,CACNC,kBAAmB,CAAE5U,MAAO,OAAQzB,OAAQ,OAAQsW,aAAc,OAClEC,OAAQ1J,EACRI,KAAMA,EACNuJ,OAAQ/H,GACRgI,QAAS,CACLC,UAAW,UACXC,kBAAkB,EAClBC,aAAa,EACbC,gBAAgB,EAChBC,cAAc,EACdC,mBAAmB,EACnBC,eAAe,EACfC,gBAAgB,EAChBC,mBAAmB,EACnBC,gBAAiB,SACjBC,QAAS,EACTC,QAAS,GACTC,mBAAoB,CAChBrB,SAAuB,QAAf/J,EAAEyJ,OAAO4B,cAAM,IAAArL,GAAM,QAANC,EAAbD,EAAesL,YAAI,IAAArL,GAAiB,QAAjBC,EAAnBD,EAAqBsL,uBAAe,IAAArL,OAAvB,EAAbA,EAAsCsL,WAEpDC,yBAA0B,CACtB1B,SAAuB,QAAf5J,EAAEsJ,OAAO4B,cAAM,IAAAlL,GAAM,QAANC,EAAbD,EAAemL,YAAI,IAAAlL,GAAiB,QAAjBC,EAAnBD,EAAqBmL,uBAAe,IAAAlL,OAAvB,EAAbA,EAAsCmL,YAEtD/a,SAAA,EAGFF,EAAAA,EAAAA,KAACmb,EAAAA,GAAM,CACH3B,SAAUxI,EAAe9R,SACzBsE,QAASA,KACLuN,EAAoB,YACpBF,GAAmBD,EAAe,EAEtCwK,UAAwB,QAAfrL,EAAEmJ,OAAO4B,cAAM,IAAA/K,GAAM,QAANC,EAAbD,EAAegL,YAAI,IAAA/K,GAAW,QAAXC,EAAnBD,EAAqBqL,iBAAS,IAAApL,OAAjB,EAAbA,EAAgCqL,KAC3C1a,KAAM,CACF2a,IAAK,oCAAsCC,mBAAmB,0kBAK9DC,WAAYvC,OAAO4B,QAAU5B,OAAO4B,OAAOC,KAAO,IAAI7B,OAAO4B,OAAOC,KAAKW,KAAK,GAAI,SAAMnD,EACxFoD,OAAQzC,OAAO4B,QAAU5B,OAAO4B,OAAOC,KAAO,IAAI7B,OAAO4B,OAAOC,KAAKa,MAAM,GAAI,SAAMrD,GACvFrY,SAED0Q,IACG5Q,EAAAA,EAAAA,KAAC6b,EAAAA,GAAU,CACPrC,SAAUxI,EAAe9R,SACzB4c,aAAcA,IAAMjL,GAAkB,GAAO3Q,UAE7CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAYsO,MAAO,CAAEsL,aAAc,MAAOkC,SAAU,UAAW7b,SA3OlHI,KACpB,OAAQA,GACJ,IAAK,cACD,OACIH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeC,SAAA,EAC1BC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,0BAAyBC,SAAA,EAACF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMob,EAAAA,MAA8B,0BAC7F7b,EAAAA,EAAAA,MAAA,KAAGF,UAAU,qBAAoBC,SAAA,CAAE8Q,EAAenG,YAAY2D,MAAM,aACpErO,EAAAA,EAAAA,MAAA,SAAAD,SAAA,CAAO,YAAU8Q,EAAenG,YAAY1E,IAAI,MAAI6K,EAAenG,YAAY3E,IAAI,eAG/F,IAAK,WACD,OACI/F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeC,SAAA,EAC1BC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,0BAAyBC,SAAA,EAACF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM2K,EAAAA,MAAa,WAC5EpL,EAAAA,EAAAA,MAAA,KAAGF,UAAU,qBAAoBC,SAAA,CAAE8Q,EAAejG,SAASyD,MAAM,SACjErO,EAAAA,EAAAA,MAAA,SAAAD,SAAA,CAAO,YAAU8Q,EAAejG,SAAS5E,IAAI,MAAI6K,EAAejG,SAAS7E,IAAI,WAGzF,IAAK,QACD,OACI/F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeC,SAAA,EAC1BC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,0BAAyBC,SAAA,EAACF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM6K,EAAAA,MAAe,sBAC9EzL,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBAAoBC,SAAE8Q,EAAeuG,MAAM/I,SACxDxO,EAAAA,EAAAA,KAAA,SAAAE,SAAQ8Q,EAAeuG,MAAM5O,aAGzC,IAAK,WACD,OACIxI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeC,SAAA,EAC1BC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,0BAAyBC,SAAA,EAACF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM8Y,EAAAA,MAAiB,aAChF1Z,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBAAoBC,SAAE8Q,EAAe9R,SAASkY,WAC3DjX,EAAAA,EAAAA,MAAA,SAAAD,SAAA,CAAO,sBAAiB8Q,EAAe7H,eACvCnJ,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACjBC,EAAAA,EAAAA,MAAA,KACI8b,KAAMjD,KACNrK,OAAO,SACPuN,IAAI,sBACJjc,UAAU,yBAAwBC,SAAA,EAElCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM8Y,EAAAA,IAAezZ,UAAU,SAAS,yBAK7E,QACI,OAAO,KACf,EA8L6Dkc,CAAerL,SAO/BY,GAAoBF,EAAiBnL,KAAI,CAACnH,EAAU8P,KAAW,IAADoN,EAAAC,EAAAC,EAAAC,EAC3D,MAAMlV,EAAsB,QAAjB+U,EAAGld,EAASkI,aAAK,IAAAgV,OAAA,EAAdA,EAAgB/U,MACxBC,EAAuB,QAAjB+U,EAAGnd,EAASkI,aAAK,IAAAiV,OAAA,EAAdA,EAAgB/U,OAE/B,OADA3E,QAAQ+J,IAAI,YAADlM,OAAawO,EAAK,KAAK9P,EAAU,SAAUmI,EAAO,UAAWC,GACnED,GAAUC,GAGXtH,EAAAA,EAAAA,KAACmb,EAAAA,GAAM,CAEH3B,SAAU,CACNlJ,IAAKzK,WAAWwB,GAChBkJ,IAAK1K,WAAWyB,IAEpB1G,KAAM,CACF2a,IAAK,oCAAsCC,mBAAmB,6lBAM9DC,WAAYvC,OAAO4B,QAAU5B,OAAO4B,OAAOC,KAAO,IAAI7B,OAAO4B,OAAOC,KAAKW,KAAK,GAAI,SAAMnD,EACxFoD,OAAQzC,OAAO4B,QAAU5B,OAAO4B,OAAOC,KAAO,IAAI7B,OAAO4B,OAAOC,KAAKa,MAAM,GAAI,SAAMrD,GAEzFtT,MAAK,GAAAzE,OAAKtB,EAAS4H,MAAK,OAAAtG,QAAwB,QAAlB8b,EAAApd,EAAS8H,iBAAS,IAAAsV,OAAA,EAAlBA,EAAoBrV,WAAY,EAAC,YAAAzG,QAA0B,QAAlB+b,EAAArd,EAAS8H,iBAAS,IAAAuV,OAAA,EAAlBA,EAAoBrV,MAAO,IAf7F8H,GAJiB,IAoBxB,WAvGlB7O,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yDAAwDC,SAAA,EACnEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8BAA8BJ,KAAK,SAAQK,UACtDF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,kBAAiBC,SAAC,+BAEtCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,OAAMC,SAAC,yCAgH/CF,EAAAA,EAAAA,KAACwc,EAAkB,CACf1Y,QAAuB,OAAdkN,QAAc,IAAdA,OAAc,EAAdA,EAAgBoF,QACzBvK,oBAAqBuG,GACrBpJ,UAAWsJ,MAIA,OAAdtB,QAAc,IAAdA,OAAc,EAAdA,EAAgBoF,WACbpW,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACrBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,SAAQC,UACnBF,EAAAA,EAAAA,KAACyc,EAAe,CACZ3Y,QAASkN,EAAeoF,QACxBrS,UAAWyN,EACXxN,kBAAmBA,CAAC0Y,EAAQnU,KACxB5F,QAAQ+J,IAAI,GAADlM,OAAIkc,EAAM,8BAAAlc,OAAwB+H,GAAW,YAUhFvI,EAAAA,EAAAA,KAACuB,EAAAA,EAAM,UAKfvB,EAAAA,EAAAA,KAAA,SAAAE,SAAA,6hBAqBIkR,IACIpR,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAkBsO,MAAO,CAAEoO,QAAS,QAASC,gBAAiB,mBAAqBxc,SAAS,KAAIF,UAC3GF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qCAAoCC,UAC/CC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeC,SAAA,EAC1BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,eAAcC,SAAA,EACzBC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,cAAaC,SAAA,EACvBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMic,EAAAA,IAAS5c,UAAU,sBAAsB,oCAGpED,EAAAA,EAAAA,KAAA,UACIM,KAAK,SACLL,UAAU,YACVuD,QAASA,IAAM6N,GAAqB,GACpCzI,SAAUsI,QAGlB/Q,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYC,SAAA,EACvBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sBAAqBC,SAAA,EAChCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMM,EAAAA,IAAQjB,UAAU,UACzCD,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,YAAgB,uKAE5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACjBF,EAAAA,EAAAA,KAAA,SAAO8c,QAAQ,gBAAgB7c,UAAU,aAAYC,SAAC,gEACtDF,EAAAA,EAAAA,KAAA,YACIK,GAAG,gBACHJ,UAAU,eACV8c,KAAK,IACLvO,MAAO8C,EACP7C,SAAWC,GAAM6C,EAAiB7C,EAAEC,OAAOH,OAC3CwO,YAAY,oFACZpU,SAAUsI,WAItB/Q,EAAAA,EAAAA,MAAA,OAAKF,UAAU,eAAcC,SAAA,EACzBF,EAAAA,EAAAA,KAAA,UACIM,KAAK,SACLL,UAAU,oBACVuD,QAASA,IAAM6N,GAAqB,GACpCzI,SAAUsI,EAAahR,SAC1B,gBAGDF,EAAAA,EAAAA,KAAA,UACIM,KAAK,SACLL,UAAU,kBACVuD,QA9iBJvB,UAC5B,IAAK,IAAD3C,EAAAC,EACA4R,GAAgB,GAEhB,MAAM5O,GAAa,OAAJ/C,QAAI,IAAJA,GAAU,QAANF,EAAJE,EAAMA,YAAI,IAAAF,OAAN,EAAJA,EAAYkD,cAAkB,OAAJhD,QAAI,IAAJA,GAAU,QAAND,EAAJC,EAAMA,YAAI,IAAAD,OAAN,EAAJA,EAAYc,IACrD,IAAKkC,EACD,MAAM,IAAIkH,MAAM,+CAGpB,MAAMwT,QAAevJ,EAAAA,GAAgBwJ,iBACjClM,EAAe3Q,GACfkC,EACA+O,GAGJ,IAAI2L,EAAOnT,QAoBP,MAAM,IAAIL,MAAMwT,EAAOtU,SAAW,gDAlBlC,IAEI,MAAMwU,EAAYnM,EAAeoF,SAAO,KAAA5V,OAASwQ,EAAe3Q,UAC1D+c,EAAAA,GAAkBC,kBAAkBF,EAAW,CACjDvR,OAAO,EACP0R,qBAAqB,IAAIjY,MAAOmD,cAChC+U,OAAO,GAAD/c,OAAKwQ,EAAekF,WAAU,qCAExCvT,QAAQ+J,IAAI,qDAChB,CAAE,MAAO8Q,GACL7a,QAAQC,KAAK,uCAAqC4a,EAEtD,CAGAC,MAAM,6GACNhc,EAAS,IAIjB,CAAE,MAAO0B,GACLR,QAAQQ,MAAM,kCAA8BA,GAC5Csa,MAAM,iDAAyCta,EAAMwF,QACzD,CAAC,QACGwI,GAAgB,GAChBE,GAAqB,GACrBE,EAAiB,GACrB,GAmgBgC3I,SAAUsI,EAAahR,SAEtBgR,GACG/Q,EAAAA,EAAAA,MAAA0I,EAAAA,SAAA,CAAA3I,SAAA,EACIF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAwCJ,KAAK,SAAQK,UAChEF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,kBAAiBC,SAAC,iBAChC,2BAIVC,EAAAA,EAAAA,MAAA0I,EAAAA,SAAA,CAAA3I,SAAA,EACIF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMic,EAAAA,IAAS5c,UAAU,SAAS,0CAWxF,C", "sources": ["components/Sidebar.js", "components/Footer.js", "components/Header.js", "components/ReportGenerator.js", "components/RealTimeDeviceData.js", "components/DeviceHistoryTable.js", "pages/ShipmentView.js"], "sourcesContent": ["import React from 'react';\r\nimport { Link, useLocation } from 'react-router-dom';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport {\r\n    faDolly, faFolderPlus, faAnchor, faFlag<PERSON>he<PERSON>ed,\r\n    faUser, faStreetView, faCreditCard, faFileLines,\r\n    faBell, faDesktop\r\n} from '@fortawesome/free-solid-svg-icons';\r\n\r\nconst Sidebar = () => {\r\n    const location = useLocation();\r\n    \r\n    // Get user role from localStorage\r\n    const getUserRole = () => {\r\n        try {\r\n            const user = JSON.parse(localStorage.getItem('user'));\r\n            return user?.user?.role || user?.user?.gorev || 'user';\r\n        } catch {\r\n            return 'user';\r\n        }\r\n    };\r\n    \r\n    const userRole = getUserRole();\r\n    const isAdmin = userRole === 'admin';\r\n\r\n    return (\r\n        <div className=\"sidebar col-md-4 col-lg-2 p-0 bg-light-subtle\">\r\n            <div className=\"offcanvas-md offcanvas-end\" tabIndex=\"-1\" id=\"yanMenu\" aria-labelledby=\"yanMenu\">\r\n                <div className=\"offcanvas-header\">\r\n                    <h5 className=\"offcanvas-title\" id=\"yanMenu\">MGZ24 Gold</h5>\r\n                    <button type=\"button\" className=\"btn-close\" data-bs-dismiss=\"offcanvas\" data-bs-target=\"#yanMenu\" aria-label=\"Kapat\"></button>\r\n                </div>\r\n                <div className=\"offcanvas-body d-md-flex flex-column p-0 pt-lg-3 overflow-y-auto\">\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Sevkiyat İşlemleri</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/' ? 'active' : ''}`} to=\"/\">\r\n                                <FontAwesomeIcon icon={faDolly} />Aktif Sevkiyatlar\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/add' ? 'active' : ''}`} to=\"/add\">\r\n                                <FontAwesomeIcon icon={faFolderPlus} />Yeni Sevkiyat Oluştur\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/inactive-devices' ? 'active' : ''}`} to=\"/inactive-devices\">\r\n                                <FontAwesomeIcon icon={faAnchor} />İnaktif Cihazlar\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/history' ? 'active' : ''}`} to=\"/history\">\r\n                                <FontAwesomeIcon icon={faFlagCheckered} />Geçmiş Sevkiyatlar\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Sistem Yönetimi</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        {/* Cihaz Yönetimi - Sadece admin kullanıcılarına göster */}\r\n                        {isAdmin && (\r\n                            <li className=\"nav-item\">\r\n                                <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/devices' ? 'active' : ''}`} to=\"/devices\">\r\n                                    <FontAwesomeIcon icon={faDesktop} />Cihaz Yönetimi\r\n                                </Link>\r\n                            </li>\r\n                        )}\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/notifications' ? 'active' : ''}`} to=\"/notifications\">\r\n                                <FontAwesomeIcon icon={faBell} />Bildirimler\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Kullanıcı Ayarları</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/profile' ? 'active' : ''}`} to=\"/profile\">\r\n                                <FontAwesomeIcon icon={faUser} />Kullanıcı Profilim\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/viewers' ? 'active' : ''}`} to=\"/viewers\">\r\n                                <FontAwesomeIcon icon={faStreetView} />İzleyici İşlemleri\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Ödeme ve Yapılandırma</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/payment' ? 'active' : ''}`} to=\"/payment\">\r\n                                <FontAwesomeIcon icon={faCreditCard} />Ödeme Yap\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/invoices' ? 'active' : ''}`} to=\"/invoices\">\r\n                                <FontAwesomeIcon icon={faFileLines} />Faturalarım\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-body-secondary text-uppercase\">Kredi Durumu</h6>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Sidebar;", "import React from 'react';\r\n\r\nconst Footer = () => {\r\n    return (\r\n        <footer className=\"py-5 border-top\">\r\n            <p className=\"text-center\">&copy;2025 Inkatech Ölçüm Sistemleri</p>\r\n        </footer>\r\n    );\r\n};\r\n\r\nexport default Footer; ", "import React, { useState, useEffect } from 'react';\r\nimport { Link, useNavigate } from 'react-router-dom';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { faRightFromBracket, faBars, faUser } from '@fortawesome/free-solid-svg-icons';\r\nimport logoDark from '../assets/img/logo.png';\r\nimport { kullaniciService } from '../api/dbService';\r\n\r\nconst Header = () => {\r\n    const navigate = useNavigate();\r\n    const [userName, setUserName] = useState('Misafir');\r\n    const [loading, setLoading] = useState(true);\r\n\r\n    // Kullanıcı bilgilerini veritabanından al\r\n    useEffect(() => {\r\n        const fetchUserData = async () => {\r\n            try {\r\n                // Local storage'dan kullanıcı bilgisini al\r\n                const storedUser = JSON.parse(localStorage.getItem('user'));\r\n                const userId = storedUser?.user?.musteri_ID || storedUser?.user?.id;\r\n                const userName = storedUser?.user?.name || storedUser?.user?.musteri_adi;\r\n\r\n                if (!userId) {\r\n                    console.warn('Oturum bilgisi bulunamadı');\r\n                    setUserName('Misafir');\r\n                    setLoading(false);\r\n                    return;\r\n                }\r\n\r\n                // Eğer localStorage'da isim varsa direkt kullan\r\n                if (userName) {\r\n                    setUserName(userName);\r\n                    setLoading(false);\r\n                    return;\r\n                }\r\n\r\n                // API'den kullanıcı bilgilerini getir (backend çalışıyorsa)\r\n                try {\r\n                    const userData = await kullaniciService.getKullanici(userId);\r\n\r\n                    // musteri_adi alanını kullan\r\n                    if (userData && userData.musteri_adi) {\r\n                        setUserName(userData.musteri_adi);\r\n\r\n                        // localStorage'ı güncelle\r\n                        if (storedUser?.user) {\r\n                            storedUser.user.name = userData.musteri_adi;\r\n                            localStorage.setItem('user', JSON.stringify(storedUser));\r\n                        }\r\n                    }\r\n                } catch (apiError) {\r\n                    console.warn('API\\'den kullanıcı bilgisi alınamadı, localStorage kullanılıyor');\r\n                    // API hatası durumunda localStorage'daki adı kullan\r\n                    setUserName(userName || 'Kullanıcı');\r\n                }\r\n            } catch (error) {\r\n                console.error('Kullanıcı bilgileri alınırken hata:', error);\r\n                // Hata durumunda varsayılan isim kullan\r\n                setUserName('Kullanıcı');\r\n            } finally {\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        fetchUserData();\r\n    }, []);\r\n\r\n    // Çıkış işlemi\r\n    const handleLogout = () => {\r\n        // Local storage'dan kullanıcı bilgilerini temizle\r\n        localStorage.removeItem('user');\r\n\r\n        // Login sayfasına yönlendir\r\n        navigate('/login');\r\n    };\r\n\r\n    return (\r\n        <header className=\"navbar sticky-top bg-darkblue flex-md-nowrap p-0 shadow\" data-bs-theme=\"dark\">\r\n            <Link className=\"navbar-brand col-md-4 col-lg-2 me-0 px-3 py-2 fs-6 text-white\" to=\"/\">\r\n                <img src={logoDark} alt=\"MGZ24 Logo\" height=\"40\" />\r\n            </Link>\r\n            <ul className=\"navbar-nav flex-row align-items-center\">\r\n                <li className=\"nav-item text-nowrap d-none d-md-block me-3\">\r\n                    <span className=\"text-white\">\r\n                        <FontAwesomeIcon icon={faUser} className=\"me-2\" />\r\n                        {loading ? 'Yükleniyor...' : userName}\r\n                    </span>\r\n                </li>\r\n                <li className=\"nav-item text-nowrap px-1\">\r\n                    <button\r\n                        onClick={handleLogout}\r\n                        className=\"nav-link px-3\"\r\n                        data-bs-toggle=\"tooltip\"\r\n                        data-bs-placement=\"left\"\r\n                        data-bs-title=\"Çıkış yap!\"\r\n                    >\r\n                        <FontAwesomeIcon icon={faRightFromBracket} />\r\n                    </button>\r\n                </li>\r\n                <li className=\"nav-item text-nowrap d-md-none\">\r\n                    <button\r\n                        className=\"nav-link px-3\"\r\n                        type=\"button\"\r\n                        data-bs-toggle=\"offcanvas\"\r\n                        data-bs-target=\"#yanMenu\"\r\n                        aria-controls=\"yanMenu\"\r\n                        aria-expanded=\"false\"\r\n                        aria-label=\"menu aç/kapa\"\r\n                    >\r\n                        <FontAwesomeIcon icon={faBars} />\r\n                    </button>\r\n                </li>\r\n            </ul>\r\n        </header>\r\n    );\r\n};\r\n\r\nexport default Header; ", "import React, { useState } from 'react';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faFilePdf, faSpinner } from '@fortawesome/free-solid-svg-icons';\nimport jsPDF from 'jspdf';\nimport autoTable from 'jspdf-autotable';\n\nconst ReportGenerator = ({ cihazID, tableData, onReportGenerated }) => {\n  \n  const [isGenerating, setIsGenerating] = useState(false);\n  const [error, setError] = useState(null);\n\n  // PDF rapor oluşturma fonksiyonu\n  const generatePDFReport = async () => {\n    try {\n      setIsGenerating(true);\n      setError(null);\n\n      if (!tableData || tableData.length === 0) {\n        setError('Rapor oluşturmak için veri bulunamadı.');\n        return;\n      }\n\n      // PDF oluştur\n      const doc = new jsPDF();\n      \n      // Türkçe karakter desteği için font ayarla\n      doc.setFont('helvetica', 'normal');\n      \n      // UTF-8 encoding kullan\n      doc.setLanguage('tr');\n      \n      // Türkçe karakter desteği için yardımcı fonksiyon\n      const fixTurkishChars = (text) => {\n        return text\n          .replace(/ğ/g, 'g')\n          .replace(/Ğ/g, 'G')\n          .replace(/ü/g, 'u')\n          .replace(/Ü/g, 'U')\n          .replace(/ş/g, 's')\n          .replace(/Ş/g, 'S')\n          .replace(/ı/g, 'i')\n          .replace(/İ/g, 'I')\n          .replace(/ö/g, 'o')\n          .replace(/Ö/g, 'O')\n          .replace(/ç/g, 'c')\n          .replace(/Ç/g, 'C');\n      };\n      \n      // Başlık - üst ortada\n      doc.setFontSize(16);\n      doc.setFont('helvetica', 'bold');\n      const pageWidth = doc.internal.pageSize.width;\n      const title = fixTurkishChars('MGZ24 Cihaz Veri Raporu');\n      const titleWidth = doc.getTextWidth(title);\n      const titleX = (pageWidth - titleWidth) / 2;\n      doc.text(title, titleX, 20);\n      \n      // Alt başlık bilgileri\n      doc.setFontSize(10);\n      doc.setFont('helvetica', 'normal');\n      const currentDate = new Date().toLocaleDateString('tr-TR');\n      const recordCount = tableData.length;\n      const subtitle = fixTurkishChars(`Cihaz ID: ${cihazID}    Rapor Tarihi: ${currentDate}    KAYIT SAYISI: ${recordCount}`);\n      const subtitleWidth = doc.getTextWidth(subtitle);\n      const subtitleX = (pageWidth - subtitleWidth) / 2;\n      doc.text(subtitle, subtitleX, 30);\n\n      // Pil voltajını yüzdeye çevirme fonksiyonu (DeviceHistoryTable ile aynı)\n      const calculateBatteryPercentage = (voltage) => {\n        const volt = parseFloat(voltage);\n        if (isNaN(volt)) return 0;\n        \n        const minVoltage = 2.5;\n        const maxVoltage = 3.4;\n        const minPercentage = 30;\n        const maxPercentage = 100;\n        \n        const percentage = minPercentage + ((volt - minVoltage) / (maxVoltage - minVoltage)) * (maxPercentage - minPercentage);\n        return Math.round(Math.max(minPercentage, Math.min(maxPercentage, percentage)));\n      };\n\n      // Tablo verileri hazırla\n      const tableHeaders = ['Tarih', 'Sicaklik', 'Nem', 'Pil (%)', 'Enlem', 'Boylam'].map(header => fixTurkishChars(header));\n      const tableRows = tableData.map(row => [\n        row.tarih || row.zaman || new Date().toLocaleDateString('tr-TR'),\n        row.sensorler?.sicaklik ? `${row.sensorler.sicaklik}°C` : '-',\n        row.sensorler?.nem ? `${row.sensorler.nem}%` : '-',\n        row.sensorler?.pil ? `${calculateBatteryPercentage(row.sensorler.pil)}%` : '-',\n        row.konum?.enlem || '-',\n        row.konum?.boylam || '-'\n      ]);\n\n      // Tablo oluştur\n      autoTable(doc, {\n        head: [tableHeaders],\n        body: tableRows,\n        startY: 40,\n        styles: {\n          fontSize: 8,\n          cellPadding: 3,\n        },\n        headStyles: {\n          fillColor: [41, 128, 185], // Mavi başlık\n          textColor: 255,\n          fontStyle: 'bold'\n        },\n        alternateRowStyles: {\n          fillColor: [245, 245, 245] // Zebra çizgili\n        },\n        margin: { top: 40, left: 14, right: 14 }\n      });\n\n      // PDF'i indir\n      const fileName = `MGZ24_Cihaz_Raporu_${cihazID}_${new Date().toISOString().split('T')[0]}.pdf`;\n      doc.save(fileName);\n\n      // Başarı callback'i\n      if (onReportGenerated) {\n        onReportGenerated('pdf', fileName);\n      }\n\n    } catch (error) {\n      console.error('PDF rapor oluşturma hatası:', error);\n      setError('PDF raporu oluşturulurken hata oluştu: ' + error.message);\n    } finally {\n      setIsGenerating(false);\n    }\n  };\n\n\n  return (\n    <div className=\"card\">\n      <div className=\"card-body\">\n        {/* Hata Mesajı */}\n        {error && (\n          <div className=\"alert alert-danger\">\n            <small>{error}</small>\n          </div>\n        )}\n\n        {/* PDF İndir Butonu */}\n        <div className=\"d-flex align-items-center justify-content-between\">\n          <div>\n            <button\n              className=\"btn btn-danger\"\n              onClick={generatePDFReport}\n              disabled={isGenerating}\n            >\n              {isGenerating ? (\n                <>\n                  <FontAwesomeIcon icon={faSpinner} className=\"me-2 fa-spin\" />\n                  Oluşturuluyor...\n                </>\n              ) : (\n                <>\n                  <FontAwesomeIcon icon={faFilePdf} className=\"me-2\" />\n                  PDF İndir\n                </>\n              )}\n            </button>\n          </div>\n          \n          {/* Rapor Bilgileri */}\n          <div>\n            <small className=\"text-muted\">\n              Sadece gösterilen veriler raporlanır.\n            </small>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ReportGenerator;", "import React, { useState, useEffect } from 'react';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { faThermometerHalf, faEye, faBatteryHalf, faMapMarkerAlt, faCheck, faSync, faDroplet } from '@fortawesome/free-solid-svg-icons';\r\nimport { faLightbulb } from '@fortawesome/free-regular-svg-icons';\r\n\r\nconst RealTimeDeviceData = ({ cihazID, dateRange = {} }) => {\r\n    const [deviceData, setDeviceData] = useState(null);\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState(null);\r\n    const [lastUpdate, setLastUpdate] = useState(null);\r\n\r\n    // Cihaz verilerini API'den getir\r\n    const fetchDeviceData = async () => {\r\n        if (!cihazID) {\r\n            setError('Cihaz ID bulunamadı');\r\n            setLoading(false);\r\n            return;\r\n        }\r\n\r\n        try {\r\n            setError(null);\r\n            const response = await fetch(`https://ffl21.fun:3001/api/cihaz/${cihazID}`);\r\n            \r\n            if (!response.ok) {\r\n                throw new Error(`HTTP ${response.status}: ${response.statusText}`);\r\n            }\r\n\r\n            const data = await response.json();\r\n            \r\n            if (data.success && data.data) {\r\n                // API'den gelen veriyi işle - sonSensorler objesi içindeki verileri kullan\r\n                const processedData = {\r\n                    ...data.data,\r\n                    sicaklik: data.data.sonSensorler?.sicaklik || data.data.sicaklik,\r\n                    nem: data.data.sonSensorler?.nem || data.data.nem,\r\n                    pil: data.data.sonSensorler?.pil || data.data.pil,\r\n                    isik: data.data.sonSensorler?.isik || data.data.isik,\r\n                    basinc: data.data.sonSensorler?.basinc || data.data.basinc\r\n                };\r\n                setDeviceData(processedData);\r\n                setLastUpdate(new Date().toLocaleTimeString());\r\n            } else {\r\n                throw new Error(data.message || 'Cihaz verisi alınamadı');\r\n            }\r\n        } catch (err) {\r\n            console.error('Cihaz verisi getirme hatası:', err);\r\n            setError(err.message);\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    // İlk yükleme ve cihazID değiştiğinde veri getir\r\n    useEffect(() => {\r\n        fetchDeviceData();\r\n    }, [cihazID]);\r\n\r\n    // Otomatik güncelleme kaldırıldı - sadece manuel yenileme\r\n\r\n    // Sıcaklık renk belirleme\r\n    const getTemperatureColor = (temp) => {\r\n        const temperature = parseFloat(temp);\r\n        if (temperature < 0) return 'text-primary';\r\n        if (temperature < 15) return 'text-info';\r\n        if (temperature > 30) return 'text-danger';\r\n        return 'text-success';\r\n    };\r\n\r\n    // Nem renk belirleme\r\n    const getHumidityColor = (humidity) => {\r\n        const hum = parseFloat(humidity);\r\n        if (hum > 80) return 'text-danger';\r\n        if (hum > 60) return 'text-warning';\r\n        return 'text-success';\r\n    };\r\n\r\n    // Pil voltajını yüzdeye çevirme (2.5V = %30, 3.4V = %100)\r\n    const calculateBatteryPercentage = (voltage) => {\r\n        const volt = parseFloat(voltage);\r\n        if (isNaN(volt)) return 0;\r\n        \r\n        const minVoltage = 2.5; // %30\r\n        const maxVoltage = 3.4; // %100\r\n        const minPercentage = 30;\r\n        const maxPercentage = 100;\r\n        \r\n        // Doğrusal interpolasyon\r\n        const percentage = minPercentage + ((volt - minVoltage) / (maxVoltage - minVoltage)) * (maxPercentage - minPercentage);\r\n        \r\n        // Yüzdeyi 30-100 arasında sınırla\r\n        return Math.round(Math.max(minPercentage, Math.min(maxPercentage, percentage)));\r\n    };\r\n\r\n    // Pil renk belirleme (yüzdeye göre)\r\n    const getBatteryColor = (batteryPercentage) => {\r\n        const bat = parseFloat(batteryPercentage);\r\n        if (bat < 35) return 'text-danger';\r\n        if (bat < 60) return 'text-warning';\r\n        return 'text-success';\r\n    };\r\n\r\n    if (!cihazID) {\r\n        return null;\r\n    }\r\n\r\n    if (loading) {\r\n        return (\r\n            <div className=\"row mb-4\">\r\n                <div className=\"col-12\">\r\n                    <div className=\"card border-0 shadow-sm\">\r\n                        <div className=\"card-body text-center py-4\">\r\n                            <div className=\"spinner-border text-primary\" role=\"status\">\r\n                                <span className=\"visually-hidden\">Cihaz verileri yükleniyor...</span>\r\n                            </div>\r\n                            <div className=\"mt-2\">Cihaz verileri yükleniyor...</div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    if (error) {\r\n        return (\r\n            <div className=\"row mb-4\">\r\n                <div className=\"col-12\">\r\n                    <div className=\"card border-0 shadow-sm\">\r\n                        <div className=\"card-header bg-warning text-dark d-flex justify-content-between align-items-center\">\r\n                            <h6 className=\"mb-0\">\r\n                                <FontAwesomeIcon icon={faThermometerHalf} className=\"me-2\" />\r\n                                Cihaz Verileri - {cihazID}\r\n                            </h6>\r\n                            <div className=\"d-flex gap-2\">\r\n                                <span className=\"badge bg-danger\">Hata</span>\r\n                                <button \r\n                                    className=\"btn btn-sm btn-outline-dark\"\r\n                                    onClick={fetchDeviceData}\r\n                                    title=\"Yeniden Dene\"\r\n                                >\r\n                                    <FontAwesomeIcon icon={faSync} />\r\n                                </button>\r\n                            </div>\r\n                        </div>\r\n                        <div className=\"card-body\">\r\n                            <div className=\"alert alert-warning mb-0\">\r\n                                <strong>Veri alınamadı:</strong> {error}\r\n                                <br />\r\n                                <small>API endpointi kontrol edilsin: https://ffl21.fun:3001/api/cihaz/{cihazID}</small>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <div className=\"row mb-4\">\r\n            <div className=\"col-12\">\r\n                <div className=\"card border-0 shadow-sm\">\r\n                    <div className=\"card-header bg-gradient-primary text-white d-flex justify-content-between align-items-center\">\r\n                        <h6 className=\"mb-0\">\r\n                            <FontAwesomeIcon icon={faThermometerHalf} className=\"me-2\" />\r\n                            En Son Veriler - {cihazID}\r\n                        </h6>\r\n                        <div className=\"d-flex gap-2\">\r\n                            <span className=\"badge bg-success\">Canlı</span>\r\n                            <button \r\n                                className=\"btn btn-sm btn-outline-light\"\r\n                                onClick={fetchDeviceData}\r\n                                title=\"Yenile\"\r\n                            >\r\n                                <FontAwesomeIcon icon={faSync} />\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n                    <div className=\"card-body\">\r\n                        <div className=\"row\">\r\n                            <div className=\"col-md-3 mb-3\">\r\n                                <div className=\"d-flex align-items-center p-3 border-end\">\r\n                                    <div className=\"me-3\">\r\n                                        <FontAwesomeIcon \r\n                                            icon={faThermometerHalf} \r\n                                            className={getTemperatureColor(deviceData.sicaklik || '0')} \r\n                                            size=\"lg\" \r\n                                        />\r\n                                    </div>\r\n                                    <div>\r\n                                        <div className=\"text-muted small\">Sıcaklık</div>\r\n                                        <div className={`fw-bold ${getTemperatureColor(deviceData.sicaklik || '0')}`}>\r\n                                            {deviceData.sicaklik || '0'}°C\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div className=\"col-md-3 mb-3\">\r\n                                <div className=\"d-flex align-items-center p-3 border-end\">\r\n                                    <div className=\"me-3\">\r\n                                        <FontAwesomeIcon \r\n                                            icon={faDroplet} \r\n                                            className={getHumidityColor(deviceData.nem || '0')} \r\n                                            size=\"lg\" \r\n                                        />\r\n                                    </div>\r\n                                    <div>\r\n                                        <div className=\"text-muted small\">Nem</div>\r\n                                        <div className={`fw-bold ${getHumidityColor(deviceData.nem || '0')}`}>\r\n                                            %{deviceData.nem || '0'}\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div className=\"col-md-3 mb-3\">\r\n                                <div className=\"d-flex align-items-center p-3 border-end\">\r\n                                    <div className=\"me-3\">\r\n                                        <FontAwesomeIcon \r\n                                            icon={faBatteryHalf} \r\n                                            className={getBatteryColor(calculateBatteryPercentage(deviceData.pil || '0'))} \r\n                                            size=\"lg\" \r\n                                        />\r\n                                    </div>\r\n                                    <div>\r\n                                        <div className=\"text-muted small\">Pil</div>\r\n                                        <div className={`fw-bold ${getBatteryColor(calculateBatteryPercentage(deviceData.pil || '0'))}`}>\r\n                                            %{calculateBatteryPercentage(deviceData.pil || '0')}\r\n                                        </div>\r\n                                        {calculateBatteryPercentage(deviceData.pil || '0') < 35 && (\r\n                                            <div className=\"text-danger small fw-bold\">\r\n                                                ⚠️ Pil seviyesi düşük\r\n                                            </div>\r\n                                        )}\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div className=\"col-md-3 mb-3\">\r\n                                <div className=\"d-flex align-items-center p-3\">\r\n                                    <div className=\"me-3\">\r\n                                        <FontAwesomeIcon icon={faLightbulb} className=\"text-primary\" size=\"lg\" />\r\n                                    </div>\r\n                                    <div>\r\n                                        <div className=\"text-muted small\">Işık</div>\r\n                                        <div className=\"fw-bold text-primary\">\r\n                                            {deviceData.isik || '0'}\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div className=\"row mt-3 pt-3 border-top\">\r\n                            <div className=\"col-md-6\">\r\n                                <div className=\"d-flex align-items-center\">\r\n                                    <FontAwesomeIcon icon={faMapMarkerAlt} className=\"text-primary me-2\" />\r\n                                    <span className=\"text-muted\">Konum:</span>\r\n                                    <span className=\"ms-2 fw-bold text-primary\">\r\n                                        {deviceData.enlem && deviceData.boylam \r\n                                            ? `${parseFloat(deviceData.enlem).toFixed(6)}, ${parseFloat(deviceData.boylam).toFixed(6)}`\r\n                                            : 'Konum bilgisi yok'\r\n                                        }\r\n                                    </span>\r\n                                </div>\r\n                            </div>\r\n                            <div className=\"col-md-6 text-end\">\r\n                                <span className=\"text-muted\">Durum:</span>\r\n                                <span className={`ms-2 badge ${deviceData.aktif ? 'bg-success' : 'bg-secondary'}`}>\r\n                                    {deviceData.aktif ? 'aktif' : 'pasif'}\r\n                                </span>\r\n                                {lastUpdate && (\r\n                                    <span className=\"ms-3 text-muted\">Son Güncelleme: {lastUpdate}</span>\r\n                                )}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default RealTimeDeviceData;", "import React, { useState, useEffect, useCallback } from 'react';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { faHistory, faSync, faThermometerHalf, faDroplet, faBatteryHalf, faMapMarkerAlt, faCalendarAlt } from '@fortawesome/free-solid-svg-icons';\r\n\r\nconst DeviceHistoryTable = ({ cihazID, onHistoryDataChange, dateRange = {} }) => {\r\n    const [historyData, setHistoryData] = useState([]);\r\n    const [loading, setLoading] = useState(false);\r\n    const [error, setError] = useState(null);\r\n    const [selectedLimit, setSelectedLimit] = useState(10);\r\n    const [totalRecords, setTotalRecords] = useState(0);\r\n\r\n    // Pil voltajını yüzdeye çevirme (2.5V = %30, 3.4V = %100)\r\n    const calculateBatteryPercentage = (voltage) => {\r\n        const volt = parseFloat(voltage);\r\n        if (isNaN(volt)) return 0;\r\n        \r\n        const minVoltage = 2.5;\r\n        const maxVoltage = 3.4;\r\n        const minPercentage = 30;\r\n        const maxPercentage = 100;\r\n        \r\n        const percentage = minPercentage + ((volt - minVoltage) / (maxVoltage - minVoltage)) * (maxPercentage - minPercentage);\r\n        return Math.round(Math.max(minPercentage, Math.min(maxPercentage, percentage)));\r\n    };\r\n\r\n    // Pil renk belirleme\r\n    const getBatteryColor = (batteryPercentage) => {\r\n        const bat = parseFloat(batteryPercentage);\r\n        if (bat < 35) return 'text-danger';\r\n        if (bat < 60) return 'text-warning';\r\n        return 'text-success';\r\n    };\r\n\r\n    // Cihaz geçmişini API'den getir - useCallback ile optimize edildi\r\n    const fetchDeviceHistory = useCallback(async () => {\r\n        if (!cihazID) {\r\n            setError('Cihaz ID bulunamadı');\r\n            return;\r\n        }\r\n\r\n        try {\r\n            setLoading(true);\r\n            setError(null);\r\n            \r\n            const params = new URLSearchParams();\r\n            \r\n            // Tarih aralığı varsa öncelikle tarihleri ekle (API'nin beklediği parametre isimleri)\r\n            if (dateRange.baslangic_tarihi) {\r\n                params.append('baslangicTarihi', dateRange.baslangic_tarihi);\r\n                console.log('DeviceHistoryTable - Başlangıç tarihi:', dateRange.baslangic_tarihi);\r\n            }\r\n            if (dateRange.bitis_tarihi) {\r\n                params.append('bitisTarihi', dateRange.bitis_tarihi);\r\n                console.log('DeviceHistoryTable - Bitiş tarihi:', dateRange.bitis_tarihi);\r\n            }\r\n            \r\n            // Limit ekle (tarih aralığı ile birlikte çalışır)\r\n            if (selectedLimit !== 'all') {\r\n                params.append('limit', selectedLimit);\r\n            }\r\n            \r\n            const queryString = params.toString() ? `?${params.toString()}` : '';\r\n            console.log('🔍 DeviceHistoryTable - API URL:', `https://ffl21.fun:3001/api/cihaz/${cihazID}/history${queryString}`);\r\n            console.log('📅 DeviceHistoryTable - Tarih Aralığı Kontrolü:', {\r\n                baslangic: dateRange.baslangic_tarihi,\r\n                bitis: dateRange.bitis_tarihi,\r\n                expectedResult: `${dateRange.baslangic_tarihi} ile ${dateRange.bitis_tarihi} arası veriler`,\r\n                dateRangeEmpty: Object.keys(dateRange).length === 0\r\n            });\r\n            \r\n            // CRITICAL: Eğer dateRange boşsa tarih filtresi uygulanmaz!\r\n            if (!dateRange.baslangic_tarihi || !dateRange.bitis_tarihi) {\r\n                console.error('❌ TARİH FİLTRESİ UYGULANMIYOR! dateRange eksik:', dateRange);\r\n            }\r\n            const response = await fetch(`https://ffl21.fun:3001/api/cihaz/${cihazID}/history${queryString}`);\r\n            \r\n            if (!response.ok) {\r\n                throw new Error(`HTTP ${response.status}: ${response.statusText}`);\r\n            }\r\n\r\n            const data = await response.json();\r\n            \r\n            if (data.success && data.data) {\r\n                // API'den gelen veri yapısına göre güncelle\r\n                const historyRecords = data.data.gecmisVeriler || [];\r\n                console.log('🆕 DeviceHistoryTable - API\\'den gelen yeni veriler:', {\r\n                    kayitSayisi: historyRecords.length,\r\n                    ilkKayit: historyRecords[0]?.tarih,\r\n                    sonKayit: historyRecords[historyRecords.length - 1]?.tarih,\r\n                    totalRecords: data.data.pagination?.totalRecords\r\n                });\r\n                setHistoryData(historyRecords);\r\n                setTotalRecords(data.data.pagination?.totalRecords || 0);\r\n                \r\n                // Parent componente konum verilerini gönder\r\n                if (onHistoryDataChange) {\r\n                    onHistoryDataChange(historyRecords);\r\n                }\r\n            } else {\r\n                throw new Error(data.message || 'Cihaz geçmişi alınamadı');\r\n            }\r\n        } catch (err) {\r\n            console.error('🚨 Cihaz geçmişi getirme hatası:', err);\r\n            setError(err.message);\r\n            setHistoryData([]);\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    }, [cihazID, selectedLimit, dateRange?.baslangic_tarihi, dateRange?.bitis_tarihi, onHistoryDataChange]);\r\n\r\n    // İlk yükleme ve dependency'ler değiştiğinde veri getir\r\n    useEffect(() => {\r\n        fetchDeviceHistory();\r\n    }, [fetchDeviceHistory]);\r\n\r\n    // Tarih formatla\r\n    const formatDate = (dateString) => {\r\n        if (!dateString) return 'Bilinmiyor';\r\n        const date = new Date(dateString);\r\n        return date.toLocaleString('tr-TR', {\r\n            day: '2-digit',\r\n            month: '2-digit',\r\n            year: 'numeric',\r\n            hour: '2-digit',\r\n            minute: '2-digit'\r\n        });\r\n    };\r\n\r\n    // Limit değişikliği\r\n    const handleLimitChange = (e) => {\r\n        setSelectedLimit(e.target.value === 'all' ? 'all' : parseInt(e.target.value));\r\n    };\r\n\r\n    if (!cihazID) {\r\n        return null;\r\n    }\r\n\r\n    return (\r\n        <div className=\"row mb-4\">\r\n            <div className=\"col-12\">\r\n                <div className=\"card border-0 shadow-sm\">\r\n                    <div className=\"card-header bg-light d-flex justify-content-between align-items-center\">\r\n                        <h6 className=\"mb-0\">\r\n                            <FontAwesomeIcon icon={faHistory} className=\"me-2 text-primary\" />\r\n                            Cihaz Veri Geçmişi - {cihazID}\r\n                        </h6>\r\n                        <div className=\"d-flex gap-2 align-items-center\">\r\n                            <select \r\n                                className=\"form-select form-select-sm\" \r\n                                style={{ width: 'auto' }}\r\n                                value={selectedLimit}\r\n                                onChange={handleLimitChange}\r\n                                disabled={loading}\r\n                            >\r\n                                <option value={10}>Son 10</option>\r\n                                <option value={50}>Son 50</option>\r\n                                <option value={100}>Son 100</option>\r\n                                <option value={500}>Son 500</option>\r\n                                <option value=\"all\">Tümü</option>\r\n                            </select>\r\n                            <button \r\n                                className=\"btn btn-sm btn-outline-primary\"\r\n                                onClick={fetchDeviceHistory}\r\n                                disabled={loading}\r\n                                title=\"Yenile\"\r\n                            >\r\n                                <FontAwesomeIcon icon={faSync} className={loading ? 'fa-spin' : ''} />\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n                    <div className=\"card-body p-0\">\r\n                        {loading && (\r\n                            <div className=\"text-center py-4\">\r\n                                <div className=\"spinner-border text-primary\" role=\"status\">\r\n                                    <span className=\"visually-hidden\">Veriler yükleniyor...</span>\r\n                                </div>\r\n                                <div className=\"mt-2\">Cihaz geçmişi yükleniyor...</div>\r\n                            </div>\r\n                        )}\r\n\r\n                        {error && (\r\n                            <div className=\"alert alert-warning m-3\">\r\n                                <strong>Veri alınamadı:</strong> {error}\r\n                                <br />\r\n                                <small>API endpointi kontrol edilsin: https://ffl21.fun:3001/api/cihaz/{cihazID}/history</small>\r\n                            </div>\r\n                        )}\r\n\r\n                        {!loading && !error && historyData.length === 0 && (\r\n                            <div className=\"alert alert-info m-3\">\r\n                                <FontAwesomeIcon icon={faHistory} className=\"me-2\" />\r\n                                Bu cihaz için henüz veri kaydı bulunmuyor.\r\n                            </div>\r\n                        )}\r\n\r\n                        {!loading && !error && historyData.length > 0 && (\r\n                            <>\r\n                                <div className=\"table-responsive\">\r\n                                    <table className=\"table table-hover mb-0\">\r\n                                        <thead className=\"table-dark\">\r\n                                            <tr>\r\n                                                <th scope=\"col\">\r\n                                                    <FontAwesomeIcon icon={faCalendarAlt} className=\"me-1\" />\r\n                                                    Tarih\r\n                                                </th>\r\n                                                <th scope=\"col\">\r\n                                                    <FontAwesomeIcon icon={faThermometerHalf} className=\"me-1\" />\r\n                                                    Sıcaklık\r\n                                                </th>\r\n                                                <th scope=\"col\">\r\n                                                    <FontAwesomeIcon icon={faDroplet} className=\"me-1\" />\r\n                                                    Nem\r\n                                                </th>\r\n                                                <th scope=\"col\">\r\n                                                    <FontAwesomeIcon icon={faBatteryHalf} className=\"me-1\" />\r\n                                                    Pil\r\n                                                </th>\r\n                                                <th scope=\"col\">\r\n                                                    <FontAwesomeIcon icon={faMapMarkerAlt} className=\"me-1\" />\r\n                                                    Konum\r\n                                                </th>\r\n                                            </tr>\r\n                                        </thead>\r\n                                        <tbody>\r\n                                            {historyData.map((record, index) => {\r\n                                                // İlk kayıt için debug log\r\n                                                if (index === 0) {\r\n                                                    console.log('🔄 Tabloda render edilen ilk kayıt:', {\r\n                                                        tarih: record.tarih,\r\n                                                        sicaklik: record.sensorler?.sicaklik,\r\n                                                        index: index\r\n                                                    });\r\n                                                }\r\n                                                \r\n                                                const batteryPercentage = calculateBatteryPercentage(record.sensorler?.pil || 0);\r\n                                                const sicaklik = record.sensorler?.sicaklik || 0;\r\n                                                const nem = record.sensorler?.nem || 0;\r\n                                                const enlem = record.konum?.enlem;\r\n                                                const boylam = record.konum?.boylam;\r\n                                                \r\n                                                return (\r\n                                                    <tr key={index}>\r\n                                                        <td>\r\n                                                            <small>{formatDate(record.tarih)}</small>\r\n                                                        </td>\r\n                                                        <td>\r\n                                                            <span className={sicaklik < 0 ? 'text-primary' : sicaklik > 30 ? 'text-danger' : 'text-success'}>\r\n                                                                {sicaklik}°C\r\n                                                            </span>\r\n                                                        </td>\r\n                                                        <td>\r\n                                                            <span className={nem > 80 ? 'text-danger' : nem > 60 ? 'text-warning' : 'text-success'}>\r\n                                                                {nem}%\r\n                                                            </span>\r\n                                                        </td>\r\n                                                        <td>\r\n                                                            <span className={getBatteryColor(batteryPercentage)}>\r\n                                                                {batteryPercentage}%\r\n                                                            </span>\r\n                                                            {batteryPercentage < 35 && (\r\n                                                                <small className=\"text-danger d-block\">⚠️ Düşük</small>\r\n                                                            )}\r\n                                                        </td>\r\n                                                        <td>\r\n                                                            {enlem && boylam ? (\r\n                                                                <small className=\"text-muted\">\r\n                                                                    {parseFloat(enlem).toFixed(4)}, {parseFloat(boylam).toFixed(4)}\r\n                                                                </small>\r\n                                                            ) : (\r\n                                                                <small className=\"text-muted\">Konum yok</small>\r\n                                                            )}\r\n                                                        </td>\r\n                                                    </tr>\r\n                                                );\r\n                                            })}\r\n                                        </tbody>\r\n                                    </table>\r\n                                </div>\r\n                                <div className=\"card-footer bg-light\">\r\n                                    <div className=\"d-flex justify-content-between align-items-center\">\r\n                                        <small className=\"text-muted\">\r\n                                            {dateRange.baslangic_tarihi && dateRange.bitis_tarihi ? (\r\n                                                `Sevkiyat dönemi kayıtları, gösterilen: ${historyData.length}`\r\n                                            ) : totalRecords > 0 ? (\r\n                                                `Toplam ${totalRecords} kayıt, gösterilen: ${historyData.length}`\r\n                                            ) : (\r\n                                                `${historyData.length} kayıt gösteriliyor`\r\n                                            )}\r\n                                        </small>\r\n                                        <small className=\"text-muted\">\r\n                                            Son güncelleme: {new Date().toLocaleTimeString('tr-TR')}\r\n                                        </small>\r\n                                    </div>\r\n                                </div>\r\n                            </>\r\n                        )}\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default DeviceHistoryTable;", "import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faTemperatureThreeQuarters, faDroplet, faBell, faLocationDot, faTruck, faCheck } from '@fortawesome/free-solid-svg-icons';\nimport { faLightbulb } from '@fortawesome/free-regular-svg-icons';\nimport Header from '../components/Header';\nimport Sidebar from '../components/Sidebar';\nimport Footer from '../components/Footer';\nimport ReportGenerator from '../components/ReportGenerator';\nimport RealTimeDeviceData from '../components/RealTimeDeviceData';\nimport DeviceHistoryTable from '../components/DeviceHistoryTable';\nimport { GoogleMap, useJsApiLoader, Marker, InfoWindow } from '@react-google-maps/api';\n// import axios from 'axios'; // Gerekirse eklenecek\nimport { sevkiyatService, cihazBilgiService } from '../api/dbService';\n\n// CSS Stil tanımlamaları (gerekirse kullanılacak)\n// const markerStyle = {\n//     background: '#007f97',\n//     borderRadius: '50%',\n//     width: '40px',\n//     height: '40px',\n//     display: 'flex',\n//     alignItems: 'center',\n//     justifyContent: 'center',\n//     color: 'white',\n//     border: '2px solid white',\n//     boxShadow: '0 0 10px rgba(0, 0, 0, 0.3)',\n//     position: 'relative'\n// };\n\n// Google Maps API için kütüphaneleri statik bir değişken olarak tanımla\nconst libraries = ['marker'];\n\nconst ShipmentView = () => {\n    const { id } = useParams();\n    const navigate = useNavigate();\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState(null);\n\n    // Kullanıcı bilgisini localStorage'dan al\n    const userString = localStorage.getItem('user');\n    const userData = userString ? JSON.parse(userString) : null;\n    const user = userData ? userData.user : null;\n\n    // API anahtarı\n    const apiKey = 'AIzaSyA2cfEmiPMyvcGfRiCyB9khWrccCgqpxKs';\n\n    // Harita durumu\n    const [mapCenter, setMapCenter] = useState({ lat: 41.0082, lng: 28.9784 });\n    const [zoom, setZoom] = useState(7);\n    const mapRef = useRef(null);\n\n    // InfoWindow durumu\n    const [showInfoWindow, setShowInfoWindow] = useState(false);\n    const [selectedInfoType, setSelectedInfoType] = useState('');\n\n    // Pulsating marker animasyonu için (gerekirse eklenecek)\n    // const [isPulsing, setIsPulsing] = useState(false);\n\n    // Sevkiyat ve sensör verileri\n    const [shipmentDetail, setShipmentDetail] = useState(null);\n\n    // Sevkiyat tamamlama durumu\n    const [isCompleting, setIsCompleting] = useState(false);\n    const [showCompleteModal, setShowCompleteModal] = useState(false);\n    const [completeNotes, setCompleteNotes] = useState('');\n\n    // Gerçek zamanlı cihaz verileri için state'ler\n    const [historyLocations, setHistoryLocations] = useState([]);\n    const [showAllLocations, setShowAllLocations] = useState(false);\n\n    // Google Maps API'yi yükle\n    const { isLoaded, loadError } = useJsApiLoader({\n        id: 'google-map-script',\n        googleMapsApiKey: apiKey,\n        libraries: libraries\n    });\n\n    // Harita referansını kaydet\n    const onMapLoad = useCallback((map) => {\n        mapRef.current = map;\n\n        // Zoom değişikliğini izle\n        if (map) {\n            map.addListener('zoom_changed', () => {\n                setZoom(map.getZoom());\n            });\n        }\n    }, []);\n\n\n    // DeviceHistoryTable'dan konum verilerini alma callback'i - useCallback ile optimize edildi\n    const handleHistoryData = useCallback((locations) => {\n        console.log('History Locations from DeviceHistoryTable:', locations);\n        setHistoryLocations(locations);\n    }, []);\n\n    // DeviceHistoryTable için tarih aralığı - useMemo ile optimize edildi\n    const deviceDateRange = useMemo(() => {\n        if (!shipmentDetail?.olusturmaZamani) {\n            console.warn('DeviceHistoryTable - olusturmaZamani yok, tarih filtresi uygulanmayacak!');\n            return {};\n        }\n        \n        // API'nin beklediği format: YYYY-MM-DD HH:MM:SS (Database Türkiye saati)\n        const formatDateForAPI = (dateString) => {\n            if (!dateString) return null;\n            \n            // Database Türkiye saatinde ama JavaScript UTC olarak parse ediyor\n            // Türkiye yaz saati UTC+3, ama database zaten +1 saat farkı var\n            // Bu yüzden sadece +2 saat eklemeli\n            const date = new Date(dateString);\n            const turkiyeSaati = new Date(date.getTime() + (2 * 60 * 60 * 1000));\n            const formatted = turkiyeSaati.toISOString().replace('T', ' ').split('.')[0];\n            \n            console.log('📅 Tarih dönüşümü FİX:', {\n                original: dateString,\n                jsDate: date.toISOString(),\n                turkiyeSaati: formatted,\n                expectedDB: 'Database: 17:24:40 → Formatted: 17:24:40'\n            });\n            \n            return formatted;\n        };\n        \n        let startDate, endDate;\n        \n        if (shipmentDetail.isCompleted && shipmentDetail.tamamlanmaZamani) {\n            // Geçmiş sevkiyat: tamamlanma_zamani'ndan geriye olusturma_zamani'na kadar\n            startDate = formatDateForAPI(shipmentDetail.olusturmaZamani);\n            endDate = formatDateForAPI(shipmentDetail.tamamlanmaZamani);\n        } else {\n            // Aktif sevkiyat: şu anki zamandan geriye olusturma_zamani'na kadar\n            startDate = formatDateForAPI(shipmentDetail.olusturmaZamani);\n            endDate = formatDateForAPI(new Date().toISOString());\n        }\n        \n        console.log('🎯 ShipmentView - Hesaplanan tarih aralığı:', {\n            baslangic_tarihi: startDate,\n            bitis_tarihi: endDate,\n            isCompleted: shipmentDetail.isCompleted,\n            originalStart: shipmentDetail.olusturmaZamani,\n            originalEnd: shipmentDetail.tamamlanmaZamani,\n            sevkiyatTipi: shipmentDetail.isCompleted ? 'Geçmiş Sevkiyat' : 'Aktif Sevkiyat',\n            tarihlervMevcut: !!(shipmentDetail.olusturmaZamani)\n        });\n        \n        return {\n            baslangic_tarihi: startDate,\n            bitis_tarihi: endDate\n        };\n    }, [shipmentDetail?.olusturmaZamani, shipmentDetail?.tamamlanmaZamani, shipmentDetail?.isCompleted]);\n\n    // Verileri API'den getir\n    useEffect(() => {\n        const fetchData = async () => {\n            try {\n                setLoading(true);\n                setError(null); // Her yüklemede hata durumunu sıfırla\n\n                // Sevkiyat detaylarını getir\n                const shipmentResponse = await sevkiyatService.getSevkiyatById(id);\n\n                // Sensör verilerini uzak API'den getir\n                let sensorResponse = [];\n                try {\n                    if (shipmentResponse.mgz24_kodu) {\n                        // Uzak API'den cihaz verilerini al\n                        const response = await fetch(`https://ffl21.fun:3001/api/cihaz/${shipmentResponse.mgz24_kodu}`);\n                        if (response.ok) {\n                            const data = await response.json();\n                            if (data.success && data.data) {\n                                sensorResponse = [data.data]; // Array formatında\n                            }\n                        }\n                    }\n                } catch (sensorError) {\n                    console.warn(`Sensör verileri alınamadı (MGZ24: ${shipmentResponse.mgz24_kodu}):`, sensorError);\n                    // Sensör hatası durumunda sadece uyarı ver, ama işleme devam et\n                }\n\n                // Son sensör verilerini bul (en son tarihli)\n                const latestSensorData = sensorResponse && sensorResponse.length > 0\n                    ? sensorResponse[0] // API zaten zamana göre sıralanmış olarak geliyor\n                    : null;\n\n                // Debug: API'den gelen sensor verilerini konsola yazdır\n                console.log('API Sensor Response:', sensorResponse);\n                console.log('Latest Sensor Data:', latestSensorData);\n                if (latestSensorData?.sonKonum) {\n                    console.log('Son Konum Verisi:', latestSensorData.sonKonum);\n                }\n                console.log('Sevkiyat Tarihleri - Oluşturma:', shipmentResponse.olusturma_zamani, 'Tamamlanma:', shipmentResponse.tamamlanma_zamani);\n                // Log işlemini setShipmentDetail'den sonra taşıyacağız\n                \n                // CRITICAL: Eğer olusturmaZamani null/undefined ise filtreleme çalışmaz!\n                if (!shipmentResponse.olusturma_zamani) {\n                    console.error('UYARI: olusturma_zamani değeri yok!', {\n                        olusturma_zamani: shipmentResponse.olusturma_zamani,\n                        sevkiyat_id: shipmentResponse.id || id\n                    });\n                }\n\n                // Verileri işle ve state'e kaydet\n                if (shipmentResponse) {\n                    // Doğrudan sevkiyat nesnesindeki metin alanlarını kullan\n                    const fromName = shipmentResponse.nereden || shipmentResponse.cikis_lokasyon || shipmentResponse.gonderen_firma || 'Bilinmiyor';\n                    const toName = shipmentResponse.nereye || shipmentResponse.varis_lokasyon || shipmentResponse.alici_firma || 'Bilinmiyor';\n                    const carrierName = shipmentResponse.nakliyeci || shipmentResponse.surucu_adi || 'Bilinmiyor';\n                    const productName = shipmentResponse.urun || shipmentResponse.urun_bilgisi || 'Bilinmiyor';\n                    \n                    // Sevkiyatın tamamlanmış olup olmadığını kontrol et\n                    const isCompleted = shipmentResponse.tamamlandi_mi === 1 || shipmentResponse.tamamlandi_mi === '1';\n\n                    setShipmentDetail({\n                        id: shipmentResponse.id || id,\n                        sevkiyatID: shipmentResponse.sevkiyat_ID || 'Bilinmiyor',\n                        mgzKodu: shipmentResponse.mgz24_kodu || 'Bilinmiyor',\n                        name: shipmentResponse.sevkiyat_adi || 'Bilinmeyen Sevkiyat',\n                        plate: shipmentResponse.plaka_no || 'Plaka yok',\n                        from: fromName,\n                        to: toName,\n                        carrier: carrierName,\n                        product: productName,\n                        orderNo: shipmentResponse.mgz24_kodu || 'Sipariş no yok',\n                        pallet: shipmentResponse.palet_sayisi?.toString() || '0',\n                        net: shipmentResponse.net_agirlik?.toString() || '0',\n                        gross: shipmentResponse.brut_agirlik?.toString() || '0',\n                        added: formatDate(shipmentResponse.olusturma_zamani) || 'Bilinmiyor',\n                        lastUpdate: formatDate(shipmentResponse.guncelleme_zamani, true) || 'Bilinmiyor',\n                        isCompleted: isCompleted,\n                        olusturmaZamani: shipmentResponse.olusturma_zamani,\n                        tamamlanmaZamani: shipmentResponse.tamamlanma_zamani,\n                        location: {\n                            lat: latestSensorData?.sonKonum?.enlem ? parseFloat(latestSensorData.sonKonum.enlem) : 41.0082,\n                            lng: latestSensorData?.sonKonum?.boylam ? parseFloat(latestSensorData.sonKonum.boylam) : 28.9784,\n                            address: 'İstanbul, Türkiye' // Gerçek adres için geocoding API kullanılabilir\n                        },\n                        temperature: {\n                            value: latestSensorData?.sonSensorler?.sicaklik?.toString() || '0',\n                            min: (shipmentResponse.sicaklik_araligi || '15-25°C').split('-')[0].replace('°C', '').trim(),\n                            max: (shipmentResponse.sicaklik_araligi || '15-25°C').split('-')[1]?.replace('°C', '').trim() || '25'\n                        },\n                        humidity: {\n                            value: latestSensorData?.sonSensorler?.nem?.toString() || '0',\n                            min: '5',\n                            max: '95'\n                        },\n                        light: {\n                            value: latestSensorData?.sonSensorler?.isik?.toString() || '0',\n                            message: latestSensorData?.sonSensorler?.kapi === 'acik' ? 'Kapak açıldı!' : 'Kapak kapalı'\n                        }\n                    });\n                    \n                    // ShipmentDetail set edildikten sonra log'la\n                    console.log('✅ ShipmentDetail SET EDİLDİ:', {\n                        olusturmaZamani: shipmentResponse.olusturma_zamani,\n                        tamamlanmaZamani: shipmentResponse.tamamlanma_zamani,\n                        isCompleted: isCompleted\n                    });\n\n                    // Harita merkezini güncelle\n                    if (latestSensorData?.sonKonum?.enlem && latestSensorData?.sonKonum?.boylam) {\n                        setMapCenter({\n                            lat: parseFloat(latestSensorData.sonKonum.enlem),\n                            lng: parseFloat(latestSensorData.sonKonum.boylam)\n                        });\n                    }\n\n                }\n\n                setLoading(false);\n            } catch (err) {\n                console.error('Veri getirme hatası:', err);\n\n                // API hata mesajını göster\n                let errorMessage = 'Sevkiyat bilgileri yüklenirken hata oluştu.';\n                let errorType = 'Sunucu Hatası';\n\n                if (err.response) {\n                    // Sunucudan yanıt geldi, ancak hata yanıtı (4xx, 5xx)\n                    if (err.response.status === 404) {\n                        errorType = 'Kayıt Bulunamadı';\n                        errorMessage = err.message || `${id} numaralı sevkiyat kaydı bulunamadı. Silinen veya hiç oluşturulmamış bir kayıt olabilir.`;\n                    } else {\n                        errorMessage += ` Sunucu hatası: ${err.response.status}`;\n                        if (err.response.data && err.response.data.message) {\n                            errorMessage += ` - ${err.response.data.message}`;\n                        }\n                    }\n                } else if (err.code === 'ECONNABORTED') {\n                    errorType = 'Zaman Aşımı';\n                    errorMessage = 'Sunucu yanıt vermedi, istek zaman aşımına uğradı. Lütfen daha sonra tekrar deneyin.';\n                } else if (err.code === 'ECONNRESET') {\n                    errorType = 'Bağlantı Hatası';\n                    errorMessage = 'Sunucu bağlantısı sıfırlandı. Lütfen internet bağlantınızı kontrol edin ve tekrar deneyin.';\n                } else if (err.request) {\n                    // İstek yapıldı ama yanıt alınamadı (ağ hatası vb)\n                    errorType = 'Ağ Hatası';\n                    errorMessage = 'Sunucuya bağlanılamadı. Lütfen ağ bağlantınızı kontrol edin.';\n                }\n\n                setError({ type: errorType, message: errorMessage });\n                setLoading(false);\n\n                // Demo verisi kullan\n                const demoData = {\n                    id: id || '51692580',\n                    sevkiyatID: 'SVK000001',\n                    mgzKodu: '200000780',\n                    name: 'Demo Veri - Moldova',\n                    plate: '34 FH 3581',\n                    from: 'Düzce',\n                    to: 'Moldova',\n                    carrier: 'Transgood',\n                    product: 'Limon',\n                    orderNo: '200000780',\n                    pallet: '46',\n                    net: '8932',\n                    gross: '11061',\n                    added: '30.04.2025',\n                    lastUpdate: '04.05.2025 23:20',\n                    isCompleted: false, // Demo için false varsayalım\n                    olusturmaZamani: '2025-04-30T10:00:00',\n                    tamamlanmaZamani: null, // Demo için null (aktif sevkiyat)\n                    location: {\n                        lat: 41.0082,\n                        lng: 28.9784,\n                        address: 'İstanbul, Türkiye'\n                    },\n                    temperature: {\n                        value: '21.6',\n                        min: '15',\n                        max: '25'\n                    },\n                    humidity: {\n                        value: '45',\n                        min: '5',\n                        max: '95'\n                    },\n                    light: {\n                        value: '0.5',\n                        message: 'Kapak kapalı'\n                    }\n                };\n\n                setShipmentDetail(demoData);\n            }\n        };\n\n        fetchData();\n\n        // Marker pulse animasyonunu başlat (gerekirse eklenecek)\n        // setIsPulsing(true);\n        // const timer = setTimeout(() => {\n        //     setIsPulsing(false);\n        // }, 2000);\n\n        // return () => clearTimeout(timer);\n    }, [id]);\n\n    // Google Maps deprecated uyarısını bastır\n    useEffect(() => {\n        const originalWarn = console.warn;\n        console.warn = (...args) => {\n            if (args[0] && typeof args[0] === 'string' && args[0].includes('google.maps.Marker is deprecated')) {\n                return; // Bu uyarıyı bastır\n            }\n            originalWarn.apply(console, args);\n        };\n\n        return () => {\n            console.warn = originalWarn;\n        };\n    }, []);\n\n    // Tarih formatla\n    const formatDate = (dateString, includeTime = false) => {\n        if (!dateString) return '';\n\n        const date = new Date(dateString);\n        const day = date.getDate().toString().padStart(2, '0');\n        const month = (date.getMonth() + 1).toString().padStart(2, '0');\n        const year = date.getFullYear();\n\n        if (includeTime) {\n            const hours = date.getHours().toString().padStart(2, '0');\n            const minutes = date.getMinutes().toString().padStart(2, '0');\n            return `${day}.${month}.${year} ${hours}:${minutes}`;\n        }\n\n        return `${day}.${month}.${year}`;\n    };\n\n    // Sevkiyat tamamlama onay modalını göster\n    const handleCompleteShipment = () => {\n        setShowCompleteModal(true);\n    };\n\n    // Sevkiyat tamamlama işlemini gerçekleştir\n    const confirmCompleteShipment = async () => {\n        try {\n            setIsCompleting(true);\n\n            const userId = user?.user?.musteri_ID || user?.user?.id;\n            if (!userId) {\n                throw new Error('Kullanıcı bilgisi bulunamadı');\n            }\n\n            const result = await sevkiyatService.completeSevkiyat(\n                shipmentDetail.id,\n                userId,\n                completeNotes\n            );\n\n            if (result.success) {\n                // Cihazı inaktif duruma getir\n                try {\n                    // Demo için cihaz kodu sevkiyat detayından al\n                    const cihazKodu = shipmentDetail.mgzKodu || `D-${shipmentDetail.id}`;\n                    await cihazBilgiService.updateCihazStatus(cihazKodu, {\n                        aktif: false,\n                        son_kullanim_tarihi: new Date().toISOString(),\n                        notlar: `${shipmentDetail.sevkiyatID} sevkiyatı tamamlandı`\n                    });\n                    console.log('Cihaz başarıyla inaktif duruma getirildi');\n                } catch (cihazError) {\n                    console.warn('Cihaz durumu güncellenirken hata:', cihazError);\n                    // Cihaz hatası sevkiyat tamamlama işlemini engellemez\n                }\n\n                // Başarılı olduğunda ana sayfaya yönlendir\n                alert('Sevkiyat başarıyla tamamlandı! Cihaz inaktif cihazlar kategorisine taşındı.');\n                navigate('/');\n            } else {\n                throw new Error(result.message || 'Sevkiyat tamamlanırken hata oluştu');\n            }\n        } catch (error) {\n            console.error('Sevkiyat tamamlama hatası:', error);\n            alert('Sevkiyat tamamlanırken hata oluştu: ' + error.message);\n        } finally {\n            setIsCompleting(false);\n            setShowCompleteModal(false);\n            setCompleteNotes('');\n        }\n    };\n\n    // Konum URL'i oluştur\n    const getGoogleMapsDirectionUrl = () => {\n        if (!shipmentDetail) return '#';\n        const { lat, lng } = shipmentDetail.location;\n        return `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`;\n    };\n\n    // Yükleniyor göstergesi\n    if (loading || !shipmentDetail) {\n        return (\n            <>\n                <Header />\n                <div className=\"container-fluid\">\n                    <div className=\"row\">\n                        <Sidebar />\n                        <main className=\"col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white\">\n                            <div className=\"d-flex justify-content-center align-items-center\" style={{ height: '80vh' }}>\n                                <div className=\"spinner-border text-primary\" role=\"status\">\n                                    <span className=\"visually-hidden\">Yükleniyor...</span>\n                                </div>\n                                <span className=\"ms-2\">Sevkiyat bilgileri yükleniyor...</span>\n                            </div>\n                        </main>\n                    </div>\n                </div>\n            </>\n        );\n    }\n\n    // Hata göstergesi\n    if (error) {\n        return (\n            <>\n                <Header />\n                <div className=\"container-fluid\">\n                    <div className=\"row\">\n                        <Sidebar />\n                        <main className=\"col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white\">\n                            <div className=\"pt-3 pb-1 mt-3 mb-3 border-bottom\">\n                                <h1 className=\"h4 text-dark mb-0\">\n                                    <FontAwesomeIcon icon={faTruck} className=\"me-2 text-primary\" />\n                                    #{id} Sevkiyat Takip\n                                </h1>\n                            </div>\n\n                            <div className=\"row mb-4\">\n                                <div className=\"col-12\">\n                                    <div className=\"alert alert-danger\">\n                                        <h5 className=\"alert-heading\">\n                                            <FontAwesomeIcon icon={faBell} className=\"me-2\" />\n                                            API Bağlantı Hatası\n                                        </h5>\n                                        <p>Sunucuya bağlanılamadı. Lütfen internet bağlantınızı kontrol edin.</p>\n                                        <hr />\n                                        <div className=\"d-flex justify-content-between align-items-center\">\n                                            <p className=\"mb-0\">\n                                                Gerçek veriler için sunucu bağlantısı gereklidir.\n                                            </p>\n\n                                            <div className=\"btn-group\">\n                                                <button\n                                                    className=\"btn btn-primary\"\n                                                    onClick={() => window.location.reload()}\n                                                >\n                                                    <FontAwesomeIcon icon={faBell} className=\"me-2\" />\n                                                    Yeniden Dene\n                                                </button>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n\n                            {/* Sevkiyat verilerini hata durumunda da göster (demo veriler) */}\n                            {shipmentDetail && (\n                                <>\n                                    {/* sevkiyat bilgi */}\n                                    <div className=\"row mb-4\">\n                                        <div className=\"col-12\">\n                                            <div className=\"card bg-light border\">\n                                                <div className=\"card-header bg-dark-subtle py-2\">\n                                                    <div className=\"d-flex justify-content-between align-items-center\">\n                                                        <h5 className=\"mb-0 fw-bold\">{shipmentDetail.name}</h5>\n                                                        <small>Demo Veri</small>\n                                                    </div>\n                                                </div>\n                                                <div className=\"card-body\">\n                                                    <div className=\"table-responsive mb-0\">\n                                                        <table className=\"table table-light table-bordered table-striped mb-0\">\n                                                            <thead>\n                                                                <tr>\n                                                                    <th>Sistem ID</th>\n                                                                    <th>Sevkiyat ID</th>\n                                                                    <th>MGZ24 Kodu</th>\n                                                                    <th>Sevkiyat Adı</th>\n                                                                    <th>Plaka No</th>\n                                                                    <th>Nereden</th>\n                                                                    <th>Nereye</th>\n                                                                    <th>Nakliyeci</th>\n                                                                    <th>Ürün</th>\n                                                                </tr>\n                                                            </thead>\n                                                            <tbody>\n                                                                <tr>\n                                                                    <td>{shipmentDetail.id}</td>\n                                                                    <td>{shipmentDetail.sevkiyatID}</td>\n                                                                    <td>\n                                                                        <span className=\"badge bg-primary text-white\">\n                                                                            {shipmentDetail.mgzKodu}\n                                                                        </span>\n                                                                    </td>\n                                                                    <td>{shipmentDetail.name}</td>\n                                                                    <td>{shipmentDetail.plate}</td>\n                                                                    <td>{shipmentDetail.from}</td>\n                                                                    <td>{shipmentDetail.to}</td>\n                                                                    <td>{shipmentDetail.carrier}</td>\n                                                                    <td>{shipmentDetail.product}</td>\n                                                                </tr>\n                                                            </tbody>\n                                                        </table>\n                                                    </div>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </>\n                            )}\n                        </main>\n                    </div>\n                </div>\n            </>\n        );\n    }\n\n    // Info Window'da gösterilecek içeriği belirle\n    const getInfoContent = (type) => {\n        switch (type) {\n            case 'temperature':\n                return (\n                    <div className=\"card-body p-2\">\n                        <h6 className=\"card-title mb-1 fw-bold\"><FontAwesomeIcon icon={faTemperatureThreeQuarters} /> Sıcaklık</h6>\n                        <p className=\"mb-1 fs-4 fw-light\">{shipmentDetail.temperature.value} °C</p>\n                        <small>Min/Max: {shipmentDetail.temperature.min} - {shipmentDetail.temperature.max} °C</small>\n                    </div>\n                );\n            case 'humidity':\n                return (\n                    <div className=\"card-body p-2\">\n                        <h6 className=\"card-title mb-1 fw-bold\"><FontAwesomeIcon icon={faDroplet} /> Nem</h6>\n                        <p className=\"mb-1 fs-4 fw-light\">{shipmentDetail.humidity.value} %</p>\n                        <small>Min/Max: {shipmentDetail.humidity.min} - {shipmentDetail.humidity.max} %</small>\n                    </div>\n                );\n            case 'light':\n                return (\n                    <div className=\"card-body p-2\">\n                        <h6 className=\"card-title mb-1 fw-bold\"><FontAwesomeIcon icon={faLightbulb} /> Işık</h6>\n                        <p className=\"mb-1 fs-4 fw-light\">{shipmentDetail.light.value}</p>\n                        <small>{shipmentDetail.light.message}</small>\n                    </div>\n                );\n            case 'location':\n                return (\n                    <div className=\"card-body p-2\">\n                        <h6 className=\"card-title mb-1 fw-bold\"><FontAwesomeIcon icon={faLocationDot} /> Konum</h6>\n                        <p className=\"mb-1 fs-5 fw-light\">{shipmentDetail.location.address}</p>\n                        <small>Son Güncelleme: {shipmentDetail.lastUpdate}</small>\n                        <div className=\"mt-2\">\n                            <a\n                                href={getGoogleMapsDirectionUrl()}\n                                target=\"_blank\"\n                                rel=\"noopener noreferrer\"\n                                className=\"btn btn-sm btn-primary\"\n                            >\n                                <FontAwesomeIcon icon={faLocationDot} className=\"me-1\" /> Yol Tarifi Al\n                            </a>\n                        </div>\n                    </div>\n                );\n            default:\n                return null;\n        }\n    };\n\n    return (\n        <>\n            <Header />\n            <div className=\"container-fluid\">\n                <div className=\"row\">\n                    <Sidebar />\n\n                    {/* main sütun */}\n                    <main className=\"col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white\">\n                        <div className=\"pt-3 pb-1 mt-3 mb-3 border-bottom\">\n                            <div className=\"d-flex justify-content-between align-items-center\">\n                                <h1 className=\"h4 text-dark mb-0\">\n                                    <FontAwesomeIcon icon={faTruck} className=\"me-2 text-primary\" />\n                                    Sevkiyat: <span className=\"fw-bold\">{shipmentDetail.sevkiyatID}</span>\n                                    {shipmentDetail.mgzKodu && shipmentDetail.mgzKodu !== 'Bilinmiyor' && (\n                                        <span className=\"ms-2 badge bg-primary\">MGZ24: {shipmentDetail.mgzKodu}</span>\n                                    )}\n                                </h1>\n                            </div>\n                        </div>\n\n                        {/* En Son Veriler Gösterimi */}\n                        <RealTimeDeviceData \n                            cihazID={shipmentDetail?.mgzKodu}\n                            dateRange={{\n                                baslangic_tarihi: shipmentDetail?.olusturmaZamani,\n                                bitis_tarihi: shipmentDetail?.tamamlanmaZamani\n                            }}\n                        />\n\n                        {/* Sevkiyat Bilgisi */}\n                        <div className=\"row mb-4\">\n                            <div className=\"col-12\">\n                                <div className=\"card border-tertiary-subtle px-3 py-3\">\n                                    <div className=\"card-header bg-light d-flex justify-content-between align-items-center\">\n                                        <h5 className=\"mb-0 text-dark\">\n                                            <FontAwesomeIcon icon={faTruck} className=\"me-2 text-primary\" />\n                                            Antalya Sevkiyatı\n                                        </h5>\n                                    </div>\n                                    <div className=\"card-body p-0\">\n                                        <div className=\"table-responsive\">\n                                            <table className=\"table table-striped table-hover mb-0\">\n                                                <thead className=\"table-dark\">\n                                                    <tr>\n                                                        <th>Sistem ID</th>\n                                                        <th>Sevkiyat ID</th>\n                                                        <th>MGZ24 Kodu</th>\n                                                        <th>Sevkiyat Adı</th>\n                                                        <th>Plaka No</th>\n                                                        <th>Nereden</th>\n                                                        <th>Nereye</th>\n                                                        <th>Nakliyeci</th>\n                                                        <th>Ürün</th>\n                                                        <th>Palet</th>\n                                                        <th>Net</th>\n                                                        <th>Brüt</th>\n                                                        <th>Eklenme</th>\n                                                        <th>Sevkiyat Tamamlandı</th>\n                                                    </tr>\n                                                </thead>\n                                                <tbody>\n                                                    <tr>\n                                                        <td>{shipmentDetail.id}</td>\n                                                        <td>{shipmentDetail.sevkiyatID}</td>\n                                                        <td>\n                                                            <span className=\"badge bg-primary\">\n                                                                {shipmentDetail.mgzKodu}\n                                                            </span>\n                                                        </td>\n                                                        <td>{shipmentDetail.name}</td>\n                                                        <td>{shipmentDetail.plate}</td>\n                                                        <td>{shipmentDetail.from}</td>\n                                                        <td>{shipmentDetail.to}</td>\n                                                        <td>{shipmentDetail.carrier || 'Bilinmiyor'}</td>\n                                                        <td>{shipmentDetail.product}</td>\n                                                        <td>{shipmentDetail.pallet}</td>\n                                                        <td>{shipmentDetail.net}</td>\n                                                        <td>{shipmentDetail.gross}</td>\n                                                        <td>{formatDate(shipmentDetail.olusturmaZamani, true)}</td>\n                                                        <td>\n                                                            <div className=\"form-check\">\n                                                                <input\n                                                                    className=\"form-check-input\"\n                                                                    type=\"checkbox\"\n                                                                    checked={shipmentDetail.isCompleted}\n                                                                    onChange={handleCompleteShipment}\n                                                                    disabled={isCompleting || shipmentDetail.isCompleted}\n                                                                />\n                                                                <label className=\"form-check-label\">\n                                                                    Sevkiyat Tamamlandı\n                                                                </label>\n                                                            </div>\n                                                        </td>\n                                                    </tr>\n                                                </tbody>\n                                            </table>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n\n                        {/* Harita */}\n                        <div className=\"row my-4\">\n                            <div className=\"col-12\">\n                                <div className=\"card border-tertiary-subtle px-3 py-3\">\n                                    <div className=\"card-body p-0\" style={{ height: '60vh', boxShadow: 'inset 0 0 20px rgba(0, 0, 0, 0.1)' }}>\n                                        {loadError && (\n                                            <div className=\"alert alert-danger\">\n                                                Google Maps yüklenemedi. Lütfen daha sonra tekrar deneyin.\n                                            </div>\n                                        )}\n\n                                        {!isLoaded ? (\n                                            <div className=\"d-flex justify-content-center align-items-center h-100\">\n                                                <div className=\"spinner-border text-primary\" role=\"status\">\n                                                    <span className=\"visually-hidden\">Harita yükleniyor...</span>\n                                                </div>\n                                                <span className=\"ms-2\">Harita yükleniyor...</span>\n                                            </div>\n                                        ) : (\n                                            <div style={{ position: 'relative', width: '100%', height: '100%' }}>\n                                                {/* Harita kontrolleri */}\n                                                {historyLocations.length > 0 && (\n                                                    <div style={{ position: 'absolute', top: 16, left: 16, zIndex: 2 }}>\n                                                        <button\n                                                            onClick={() => setShowAllLocations(!showAllLocations)}\n                                                            className=\"btn btn-sm btn-primary shadow-sm\"\n                                                            style={{ fontSize: '12px' }}\n                                                        >\n                                                            <FontAwesomeIcon icon={faLocationDot} className=\"me-1\" />\n                                                            {showAllLocations ? 'Konumları Gizle' : `Tüm Konumları Göster (${historyLocations.length})`}\n                                                        </button>\n                                                    </div>\n                                                )}\n\n                                                <GoogleMap\n                                                    mapContainerStyle={{ width: '100%', height: '100%', borderRadius: '4px' }}\n                                                    center={mapCenter}\n                                                    zoom={zoom}\n                                                    onLoad={onMapLoad}\n                                                    options={{\n                                                        mapTypeId: 'roadmap',\n                                                        disableDefaultUI: true,\n                                                        zoomControl: true,\n                                                        mapTypeControl: false,\n                                                        scaleControl: false,\n                                                        streetViewControl: false,\n                                                        rotateControl: false,\n                                                        clickableIcons: true,\n                                                        fullscreenControl: true,\n                                                        gestureHandling: 'greedy',\n                                                        minZoom: 2,\n                                                        maxZoom: 20,\n                                                        zoomControlOptions: {\n                                                            position: window.google?.maps?.ControlPosition?.TOP_RIGHT\n                                                        },\n                                                        fullscreenControlOptions: {\n                                                            position: window.google?.maps?.ControlPosition?.TOP_RIGHT\n                                                        }\n                                                    }}\n                                                >\n                                                    {/* Ana konum marker'ı */}\n                                                    <Marker\n                                                        position={shipmentDetail.location}\n                                                        onClick={() => {\n                                                            setSelectedInfoType('location');\n                                                            setShowInfoWindow(!showInfoWindow);\n                                                        }}\n                                                        animation={window.google?.maps?.Animation?.DROP}\n                                                        icon={{\n                                                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`\n                                                                <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                                                                    <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\" fill=\"#007f97\" stroke=\"#ffffff\" stroke-width=\"1.5\"/>\n                                                                </svg>\n                                                            `),\n                                                            scaledSize: window.google && window.google.maps ? new window.google.maps.Size(32, 32) : undefined,\n                                                            anchor: window.google && window.google.maps ? new window.google.maps.Point(16, 32) : undefined\n                                                        }}\n                                                    >\n                                                        {showInfoWindow && (\n                                                            <InfoWindow\n                                                                position={shipmentDetail.location}\n                                                                onCloseClick={() => setShowInfoWindow(false)}\n                                                            >\n                                                                <div className=\"shadow-sm\" style={{ borderRadius: '8px', overflow: 'hidden' }}>\n                                                                    {getInfoContent(selectedInfoType)}\n                                                                </div>\n                                                            </InfoWindow>\n                                                        )}\n                                                    </Marker>\n\n                                                    {/* Tablodaki konum geçmişi marker'ları */}\n                                                    {showAllLocations && historyLocations.map((location, index) => {\n                                                        const enlem = location.konum?.enlem;\n                                                        const boylam = location.konum?.boylam;\n                                                        console.log(`Location ${index}:`, location, 'Enlem:', enlem, 'Boylam:', boylam);\n                                                        if (!enlem || !boylam) return null;\n                                                        \n                                                        return (\n                                                            <Marker\n                                                                key={index}\n                                                                position={{\n                                                                    lat: parseFloat(enlem),\n                                                                    lng: parseFloat(boylam)\n                                                                }}\n                                                                icon={{\n                                                                    url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`\n                                                                        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                                                                            <circle cx=\"12\" cy=\"12\" r=\"8\" fill=\"#ff6b35\" stroke=\"#ffffff\" stroke-width=\"2\"/>\n                                                                            <circle cx=\"12\" cy=\"12\" r=\"3\" fill=\"#ffffff\"/>\n                                                                        </svg>\n                                                                    `),\n                                                                    scaledSize: window.google && window.google.maps ? new window.google.maps.Size(20, 20) : undefined,\n                                                                    anchor: window.google && window.google.maps ? new window.google.maps.Point(10, 10) : undefined\n                                                                }}\n                                                                title={`${location.tarih} - ${location.sensorler?.sicaklik || 0}°C, %${location.sensorler?.nem || 0}`}\n                                                            />\n                                                        );\n                                                    })}\n                                                </GoogleMap>\n                                            </div>\n                                        )}\n                                    </div>\n                                </div>\n                                {/* /google map */}\n                            </div>\n                        </div>\n\n                        {/* Cihaz Veri Geçmişi Tablosu */}\n                        <DeviceHistoryTable \n                            cihazID={shipmentDetail?.mgzKodu} \n                            onHistoryDataChange={handleHistoryData}\n                            dateRange={deviceDateRange}\n                        />\n\n                        {/* PDF Rapor Oluşturma */}\n                        {shipmentDetail?.mgzKodu && (\n                            <div className=\"row mb-4\">\n                                <div className=\"col-12\">\n                                    <ReportGenerator\n                                        cihazID={shipmentDetail.mgzKodu}\n                                        tableData={historyLocations}\n                                        onReportGenerated={(format, fileName) => {\n                                            console.log(`${format} raporu oluşturuldu: ${fileName}`);\n                                        }}\n                                    />\n                                </div>\n                            </div>\n                        )}\n\n                    </main>\n                    {/* /main sütun */}\n\n                    <Footer />\n                </div >\n            </div >\n\n            {/* Pulse animasyonu için CSS */}\n            < style >\n                {`\n                    @keyframes pulse {\n                        0% {\n                            transform: scale(0.8);\n                            opacity: 0.8;\n                        }\n                        70% {\n                            transform: scale(1.5);\n                            opacity: 0;\n                        }\n                        100% {\n                            transform: scale(1.8);\n                            opacity: 0;\n                        }\n                    }\n                `}\n            </style >\n\n            {/* Sevkiyat Tamamlama Onay Modalı */}\n            {\n                showCompleteModal && (\n                    <div className=\"modal fade show\" style={{ display: 'block', backgroundColor: 'rgba(0,0,0,0.5)' }} tabIndex=\"-1\">\n                        <div className=\"modal-dialog modal-dialog-centered\">\n                            <div className=\"modal-content\">\n                                <div className=\"modal-header\">\n                                    <h5 className=\"modal-title\">\n                                        <FontAwesomeIcon icon={faCheck} className=\"me-2 text-success\" />\n                                        Sevkiyat Tamamlama Onayı\n                                    </h5>\n                                    <button\n                                        type=\"button\"\n                                        className=\"btn-close\"\n                                        onClick={() => setShowCompleteModal(false)}\n                                        disabled={isCompleting}\n                                    ></button>\n                                </div>\n                                <div className=\"modal-body\">\n                                    <div className=\"alert alert-warning\">\n                                        <FontAwesomeIcon icon={faBell} className=\"me-2\" />\n                                        <strong>Dikkat!</strong> Bu işlemle birlikte sevkiyat tamamlanmış sayılacak ve geçmiş sevkiyatlara taşınacaktır. Devam etmek istiyor musunuz?\n                                    </div>\n                                    <div className=\"mb-3\">\n                                        <label htmlFor=\"completeNotes\" className=\"form-label\">Tamamlama Notları (İsteğe bağlı):</label>\n                                        <textarea\n                                            id=\"completeNotes\"\n                                            className=\"form-control\"\n                                            rows=\"3\"\n                                            value={completeNotes}\n                                            onChange={(e) => setCompleteNotes(e.target.value)}\n                                            placeholder=\"Sevkiyat tamamlama ile ilgili notlarınızı buraya yazabilirsiniz...\"\n                                            disabled={isCompleting}\n                                        ></textarea>\n                                    </div>\n                                </div>\n                                <div className=\"modal-footer\">\n                                    <button\n                                        type=\"button\"\n                                        className=\"btn btn-secondary\"\n                                        onClick={() => setShowCompleteModal(false)}\n                                        disabled={isCompleting}\n                                    >\n                                        İptal\n                                    </button>\n                                    <button\n                                        type=\"button\"\n                                        className=\"btn btn-success\"\n                                        onClick={confirmCompleteShipment}\n                                        disabled={isCompleting}\n                                    >\n                                        {isCompleting ? (\n                                            <>\n                                                <div className=\"spinner-border spinner-border-sm me-2\" role=\"status\">\n                                                    <span className=\"visually-hidden\">Loading...</span>\n                                                </div>\n                                                Tamamlanıyor...\n                                            </>\n                                        ) : (\n                                            <>\n                                                <FontAwesomeIcon icon={faCheck} className=\"me-1\" />\n                                                Sevkiyatı Tamamla\n                                            </>\n                                        )}\n                                    </button>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                )\n            }\n        </>\n    );\n};\n\nexport default ShipmentView; "], "names": ["Sidebar", "location", "useLocation", "isAdmin", "getUserRole", "_user$user", "_user$user2", "user", "JSON", "parse", "localStorage", "getItem", "role", "gorev", "_unused", "_jsx", "className", "children", "_jsxs", "tabIndex", "id", "type", "Link", "concat", "pathname", "to", "FontAwesomeIcon", "icon", "faDolly", "faFolderPlus", "faAnchor", "faFlag<PERSON><PERSON><PERSON><PERSON>", "faDesktop", "faBell", "faUser", "faStreetView", "faCreditCard", "faFileLines", "Footer", "Header", "navigate", "useNavigate", "userName", "setUserName", "useState", "loading", "setLoading", "useEffect", "async", "_storedUser$user", "_storedUser$user2", "_storedUser$user3", "_storedUser$user4", "storedUser", "userId", "musteri_ID", "name", "musteri_adi", "console", "warn", "userData", "kullaniciService", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setItem", "stringify", "apiError", "error", "fetchUserData", "src", "alt", "height", "onClick", "handleLogout", "removeItem", "faRightFromBracket", "faBars", "_ref", "cihazID", "tableData", "onReportGenerated", "isGenerating", "setIsGenerating", "setError", "length", "doc", "jsPDF", "setFont", "setLanguage", "fixTurkishChars", "text", "replace", "setFontSize", "pageWidth", "internal", "pageSize", "width", "title", "titleX", "getTextWidth", "currentDate", "Date", "toLocaleDateString", "recordCount", "subtitle", "subtitleX", "calculateBatteryPercentage", "voltage", "volt", "parseFloat", "isNaN", "percentage", "Math", "round", "max", "min", "tableHeaders", "map", "header", "tableRows", "row", "_row$sensorler", "_row$sensorler2", "_row$sensorler3", "_row$konum", "_row$konum2", "tarih", "zaman", "sensorler", "sicaklik", "nem", "pil", "konum", "enlem", "boy<PERSON>", "autoTable", "head", "body", "startY", "styles", "fontSize", "cellPadding", "headStyles", "fillColor", "textColor", "fontStyle", "alternateRowStyles", "margin", "top", "left", "right", "fileName", "toISOString", "split", "save", "message", "disabled", "_Fragment", "faSpinner", "faFilePdf", "date<PERSON><PERSON><PERSON>", "deviceData", "setDeviceData", "lastUpdate", "setLastUpdate", "fetchDeviceData", "response", "fetch", "ok", "Error", "status", "statusText", "data", "json", "success", "_data$data$sonSensorl", "_data$data$sonSensorl2", "_data$data$sonSensorl3", "_data$data$sonSensorl4", "_data$data$sonSensorl5", "processedData", "_objectSpread", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isik", "basinc", "toLocaleTimeString", "err", "getTemperatureColor", "temp", "temperature", "getHumidityColor", "humidity", "hum", "getBatteryColor", "batteryPercentage", "bat", "faThermometerHalf", "faSync", "size", "faDroplet", "faBatteryHalf", "faLightbulb", "faMapMarkerAlt", "toFixed", "aktif", "onHistoryDataChange", "historyData", "setHistoryData", "selectedLimit", "setSelectedLimit", "totalRecords", "setTotalRecords", "fetchDeviceHistory", "useCallback", "params", "URLSearchParams", "baslangic_tarihi", "append", "log", "bitis_tarihi", "queryString", "toString", "baslangic", "bitis", "expectedResult", "dateRangeEmpty", "Object", "keys", "_historyRecords$", "_historyRecords", "_data$data$pagination", "_data$data$pagination2", "historyRecords", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ilkKayit", "<PERSON><PERSON><PERSON><PERSON>", "pagination", "formatDate", "dateString", "toLocaleString", "day", "month", "year", "hour", "minute", "faHistory", "style", "value", "onChange", "e", "target", "parseInt", "scope", "faCalendarAlt", "record", "index", "_record$sensorler2", "_record$sensorler3", "_record$sensorler4", "_record$konum", "_record$konum2", "_record$sensorler", "libraries", "ShipmentView", "_window$google", "_window$google$maps", "_window$google$maps$C", "_window$google2", "_window$google2$maps", "_window$google2$maps$", "_window$google3", "_window$google3$maps", "_window$google3$maps$", "useParams", "userString", "mapCenter", "setMapCenter", "lat", "lng", "zoom", "setZoom", "mapRef", "useRef", "showInfoWindow", "setShowInfoWindow", "selectedInfoType", "setSelectedInfoType", "shipmentDetail", "setShipmentDetail", "isCompleting", "setIsCompleting", "showCompleteModal", "setShowCompleteModal", "completeNotes", "setCompleteNotes", "historyLocations", "setHistoryLocations", "showAllLocations", "setShowAllLocations", "isLoaded", "loadError", "useJsApiLoader", "googleMapsApiKey", "onMapLoad", "current", "addListener", "getZoom", "handleHistoryData", "locations", "deviceDateRange", "useMemo", "olusturma<PERSON>amani", "formatDateForAPI", "date", "formatted", "getTime", "original", "jsDate", "turkiye<PERSON><PERSON><PERSON>", "expectedDB", "startDate", "endDate", "isCompleted", "ta<PERSON><PERSON>lanma<PERSON>", "originalStart", "originalEnd", "sevkiyatTipi", "tari<PERSON><PERSON><PERSON><PERSON><PERSON>", "shipmentResponse", "sevkiyatService", "getSevkiyatById", "sensorResponse", "mgz24_kodu", "sensorError", "latestSensorData", "son<PERSON><PERSON><PERSON>", "olusturma_zamani", "tama<PERSON><PERSON><PERSON>_zamani", "sevkiyat_id", "_shipmentResponse$pal", "_shipmentResponse$net", "_shipmentResponse$bru", "_latestSensorData$son", "_latestSensorData$son2", "_latestSensorData$son3", "_latestSensorData$son4", "_split$", "_latestSensorData$son5", "_latestSensorData$son6", "_latestSensorData$son7", "_latestSensorData$son8", "_latestSensorData$son9", "_latestSensorData$son0", "_latestSensorData$son1", "fromName", "<PERSON><PERSON><PERSON>", "cikis_lokasyon", "gonderen_firma", "to<PERSON>ame", "nereye", "varis_lokasyon", "alici_firma", "carrierName", "<PERSON><PERSON><PERSON><PERSON>", "surucu_adi", "productName", "urun", "u<PERSON>_bilgisi", "tamamlandi_mi", "sevkiyatID", "sevkiyat_ID", "mgzKodu", "sevkiyat_adi", "plate", "plaka_no", "from", "carrier", "product", "orderNo", "pallet", "palet_sayisi", "net", "net_agirlik", "gross", "brut_agirlik", "added", "guncell<PERSON><PERSON>_zamani", "address", "sicaklik_araligi", "trim", "light", "kapi", "errorMessage", "errorType", "code", "request", "fetchData", "originalWarn", "_len", "arguments", "args", "Array", "_key", "includes", "apply", "includeTime", "undefined", "getDate", "padStart", "getMonth", "getFullYear", "hours", "getHours", "minutes", "getMinutes", "getGoogleMapsDirectionUrl", "faTruck", "window", "reload", "RealTimeDeviceData", "checked", "handleCompleteShipment", "boxShadow", "position", "zIndex", "faLocationDot", "GoogleMap", "mapContainerStyle", "borderRadius", "center", "onLoad", "options", "mapTypeId", "disableDefaultUI", "zoomControl", "mapTypeControl", "scaleControl", "streetViewControl", "rotateControl", "clickableIcons", "fullscreenControl", "<PERSON><PERSON><PERSON><PERSON>", "minZoom", "max<PERSON><PERSON>", "zoomControlOptions", "google", "maps", "ControlPosition", "TOP_RIGHT", "fullscreenControlOptions", "<PERSON><PERSON>", "animation", "Animation", "DROP", "url", "encodeURIComponent", "scaledSize", "Size", "anchor", "Point", "InfoWindow", "onCloseClick", "overflow", "faTemperatureThreeQuarters", "href", "rel", "getInfoContent", "_location$konum", "_location$konum2", "_location$sensorler", "_location$sensorler2", "DeviceHistoryTable", "ReportGenerator", "format", "display", "backgroundColor", "faCheck", "htmlFor", "rows", "placeholder", "result", "completeSevkiyat", "cihazKodu", "cihazBilgiService", "updateCihazStatus", "son_kullanim_tarihi", "notlar", "cihazError", "alert"], "sourceRoot": ""}