import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
    faDolly, faFolderPlus, faAnchor, faFlag<PERSON>he<PERSON>ed,
    faUser, faStreetView, faCreditCard, faFileLines,
    faBell, faDesktop
} from '@fortawesome/free-solid-svg-icons';

const Sidebar = () => {
    const location = useLocation();
    
    // Get user role from localStorage
    const getUserRole = () => {
        try {
            const user = JSON.parse(localStorage.getItem('user'));
            return user?.user?.role || user?.user?.gorev || 'user';
        } catch {
            return 'user';
        }
    };
    
    const userRole = getUserRole();
    const isAdmin = userRole === 'admin';

    return (
        <div className="sidebar col-md-4 col-lg-2 p-0 bg-light-subtle">
            <div className="offcanvas-md offcanvas-end" tabIndex="-1" id="yanMenu" aria-labelledby="yanMenu">
                <div className="offcanvas-header">
                    <h5 className="offcanvas-title" id="yanMenu">MGZ24 Gold</h5>
                    <button type="button" className="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#yanMenu" aria-label="Kapat"></button>
                </div>
                <div className="offcanvas-body d-md-flex flex-column p-0 pt-lg-3 overflow-y-auto">
                    <h6 className="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase">Sevkiyat İşlemleri</h6>
                    <ul className="nav nav-pills flex-column">
                        <li className="nav-item">
                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/' ? 'active' : ''}`} to="/">
                                <FontAwesomeIcon icon={faDolly} />Aktif Sevkiyatlar
                            </Link>
                        </li>
                        <li className="nav-item">
                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/add' ? 'active' : ''}`} to="/add">
                                <FontAwesomeIcon icon={faFolderPlus} />Yeni Sevkiyat Oluştur
                            </Link>
                        </li>
                        <li className="nav-item">
                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/inactive-devices' ? 'active' : ''}`} to="/inactive-devices">
                                <FontAwesomeIcon icon={faAnchor} />İnaktif Cihazlar
                            </Link>
                        </li>
                        <li className="nav-item">
                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/history' ? 'active' : ''}`} to="/history">
                                <FontAwesomeIcon icon={faFlagCheckered} />Geçmiş Sevkiyatlar
                            </Link>
                        </li>
                    </ul>

                    <hr className="my-3" />
                    <h6 className="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase">Sistem Yönetimi</h6>
                    <ul className="nav nav-pills flex-column">
                        {/* Cihaz Yönetimi - Sadece admin kullanıcılarına göster */}
                        {isAdmin && (
                            <li className="nav-item">
                                <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/devices' ? 'active' : ''}`} to="/devices">
                                    <FontAwesomeIcon icon={faDesktop} />Cihaz Yönetimi
                                </Link>
                            </li>
                        )}
                        <li className="nav-item">
                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/notifications' ? 'active' : ''}`} to="/notifications">
                                <FontAwesomeIcon icon={faBell} />Bildirimler
                            </Link>
                        </li>
                    </ul>

                    <hr className="my-3" />
                    <h6 className="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase">Kullanıcı Ayarları</h6>
                    <ul className="nav nav-pills flex-column">
                        <li className="nav-item">
                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/profile' ? 'active' : ''}`} to="/profile">
                                <FontAwesomeIcon icon={faUser} />Kullanıcı Profilim
                            </Link>
                        </li>
                        <li className="nav-item">
                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/viewers' ? 'active' : ''}`} to="/viewers">
                                <FontAwesomeIcon icon={faStreetView} />İzleyici İşlemleri
                            </Link>
                        </li>
                    </ul>

                    <hr className="my-3" />
                    <h6 className="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase">Ödeme ve Yapılandırma</h6>
                    <ul className="nav nav-pills flex-column">
                        <li className="nav-item">
                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/payment' ? 'active' : ''}`} to="/payment">
                                <FontAwesomeIcon icon={faCreditCard} />Ödeme Yap
                            </Link>
                        </li>
                        <li className="nav-item">
                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/invoices' ? 'active' : ''}`} to="/invoices">
                                <FontAwesomeIcon icon={faFileLines} />Faturalarım
                            </Link>
                        </li>
                    </ul>

                    <hr className="my-3" />
                    <h6 className="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-body-secondary text-uppercase">Kredi Durumu</h6>
                </div>
            </div>
        </div>
    );
};

export default Sidebar;