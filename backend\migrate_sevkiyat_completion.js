import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

const dbConfig = {
  host: process.env.DB_HOST || '************',
  user: process.env.DB_USER || 'mehmet',
  password: process.env.DB_PASSWORD || 'Inka.mS_335265933s.tech',
  database: process.env.DB_NAME || 'mgz24db',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  connectTimeout: 10000
};

async function runMigration() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('Veritabanına bağlandı');

    // Önce mevcut tablo yapısını kontrol et
    console.log('Mevcut sevkiyatlar tablosu yapısı:');
    const [columns] = await connection.execute('DESCRIBE sevkiyatlar');
    console.table(columns);

    // durum kolonu var mı kontrol et
    const durumExists = columns.some(col => col.Field === 'durum');
    
    if (!durumExists) {
      console.log('durum kolonu ekleniyor...');
      await connection.execute(`
        ALTER TABLE sevkiyatlar 
        ADD COLUMN durum ENUM('hazirlaniyor', 'yolda', 'tamamlandi', 'iptal_edildi') DEFAULT 'hazirlaniyor'
      `);
      console.log('✅ durum kolonu eklendi');
    } else {
      console.log('✅ durum kolonu zaten mevcut');
    }

    // tamamlanma_zamani kolonu var mı kontrol et
    const tamamlanmaExists = columns.some(col => col.Field === 'tamamlanma_zamani');
    
    if (!tamamlanmaExists) {
      console.log('tamamlanma_zamani kolonu ekleniyor...');
      await connection.execute(`
        ALTER TABLE sevkiyatlar 
        ADD COLUMN tamamlanma_zamani DATETIME NULL
      `);
      console.log('✅ tamamlanma_zamani kolonu eklendi');
    } else {
      console.log('✅ tamamlanma_zamani kolonu zaten mevcut');
    }

    // tamamlayan_kullanici_id kolonu var mı kontrol et
    const tamamlayanExists = columns.some(col => col.Field === 'tamamlayan_kullanici_id');
    
    if (!tamamlayanExists) {
      console.log('tamamlayan_kullanici_id kolonu ekleniyor...');
      await connection.execute(`
        ALTER TABLE sevkiyatlar 
        ADD COLUMN tamamlayan_kullanici_id INT NULL
      `);
      console.log('✅ tamamlayan_kullanici_id kolonu eklendi');
    } else {
      console.log('✅ tamamlayan_kullanici_id kolonu zaten mevcut');
    }

    // cihazBilgi tablosunu da kontrol et
    console.log('\nMevcut cihazBilgi tablosu yapısı:');
    const [cihazColumns] = await connection.execute('DESCRIBE cihazBilgi');
    console.table(cihazColumns);

    // aktif kolonu var mı kontrol et
    const aktifExists = cihazColumns.some(col => col.Field === 'aktif');
    
    if (!aktifExists) {
      console.log('aktif kolonu ekleniyor...');
      await connection.execute(`
        ALTER TABLE cihazBilgi 
        ADD COLUMN aktif BOOLEAN DEFAULT TRUE
      `);
      console.log('✅ aktif kolonu eklendi');
    } else {
      console.log('✅ aktif kolonu zaten mevcut');
    }

    // son_kullanim_tarihi kolonu var mı kontrol et
    const sonKullanimExists = cihazColumns.some(col => col.Field === 'son_kullanim_tarihi');
    
    if (!sonKullanimExists) {
      console.log('son_kullanim_tarihi kolonu ekleniyor...');
      await connection.execute(`
        ALTER TABLE cihazBilgi 
        ADD COLUMN son_kullanim_tarihi DATETIME NULL
      `);
      console.log('✅ son_kullanim_tarihi kolonu eklendi');
    } else {
      console.log('✅ son_kullanim_tarihi kolonu zaten mevcut');
    }

    console.log('\n🎉 Migration tamamlandı!');

  } catch (error) {
    console.error('Migration hatası:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('Veritabanı bağlantısı kapatıldı');
    }
  }
}

runMigration();