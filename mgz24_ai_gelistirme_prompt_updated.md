Proje Tanımı:
MGZ24 Takip Sistemi; s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, nem ve konum verisi toplayan IoT cihazlarının taşımacılık süreçlerinde izlenmesini sağlayan bir web uygulamasıdır. Bu sistem hem cihaz verilerini hem de sevkiyat yönetimini kapsayan, çoklu kullanıcı rolleri ve firma yapısı barındıran bir takip platformudur.

1. 🧩 Teknoloji Yığını (Stack)
Aşağıdaki teknolojileri öneriyorum ama uygun alternatiflerle değiştirebilirsin:

Frontend: React + TypeScript + TailwindCSS + Zustand/ContextAPI

Backend: Node.js + Express

Database: Mysql (tablolar oluşturuldu.)

Map: Google Maps API

Graph: Chart.js veya Recharts

Payment: param pos (Kredi kartı entegrasyonu için)

Deployment: ubuntu üzerinde apache kurulu

2. 📡 MGZ24 Cihaz Entegrasyonu
🔗 API Entegrasyonu:
MGZ24 cihaz verileri RESTful API ile alınacaktır.

Kullanılacak örnek endpoint’ler:

GET /api/v1/cihazlar/{CihazID}/son_data

GET /api/v1/cihazlar/{CihazID}/eski_data

🔄 Veri Senkronizasyonu:
Aktif sevkiyatlar için cihazdan gelen veriler periyodik olarak (örn. her 2 dakikada bir cron job ile) çekilecek.

Veriler doğrudan veritabanına kaydedilebilir.

🧪 Veri İşleme:
Ham veriler filtrelenip anlık, grafik ve tablo olarak kullanıcı arayüzüne uygun hale getirilecek.

3. 👥 Kullanıcı & Firma Yönetimi
👤 Kimlik Doğrulama:
JWT tabanlı oturum sistemi.
E-posta/parola girişi, parola 

🛡️ Rol Tabanlı Yetkilendirme:
Admin, Gönderici, Alıcı, İzleyici rolleri olacak.

Her rolün erişebileceği veri ve işlemler kısıtlanmış olacak.
Admin kullanıcısı diğer kullanıcılara izinler verebilecek.

🏢 Firma Yönetimi:
Firma adı, adres, iletişim bilgileri tutulacak.

Gönderici/alıcı/nakliye firmaları ilişkisel veritabanı modeli ile bağlanacak.

4. 🚚 Sevkiyat Yönetimi
📝 Yeni Sevkiyat Formu:
Giriş alanları:

Sevkiyat Adı, Boşta MGZ24 Cihaz ID’si

Kamyon Plakası, Çıkış/Varış adresi

Nakliye Firma + Şoför Bilgisi

Gönderici / Alıcı Firma seçimi

Ürün adı, ağırlık, palet sayısı

📊 Aktif Takip Ekranı:
Harita (anlık konum ve rota)

Grafik: Sıcaklık / nem (zaman eksenli)

Veri çizelgesi: Saat/sıcaklık/nem/konum

Son veri: anlık sıcaklık, nem, konum

✅ Tamamlama:
Alıcı firma “Sevkiyatı Tamamla” butonu ile işaretler.

Durum "Tamamlandı" olur ve tüm ilgili firmalara bilgi gönderilir.

📚 Arşiv:
Tamamlanan sevkiyatlar filtrelenebilir şekilde listelenir.

Tüm geçmiş verilere erişim sağlanır.

5. 📦 Cihaz Envanter Yönetimi
İnaktif cihazlar (örneğin 6 aydır kullanılmayanlar) listelenir.

Detay bilgiler: cihaz ID, son kullanım tarihi, hangi sevkiyatta kullanılmış.

Her cihazın seri numarası, kalibrasyon tarihi vb. statik bilgiler yönetilebilir.

6. 👁️ İzleyici Rolü
İzleyici kullanıcılar belirli sevkiyatlara atanabilir.

Atandıkları sevkiyatın tam veri erişimine sahip olur (sıcaklık, nem, konum vs).

7. 📢 Bildirim & Raporlama (Opsiyonel ama önemli)
Sıcaklık/nem eşiklerini aşınca veya konum dışı durumlarda:

E-posta, SMS veya push bildirimi gönderilir.

Sevkiyat süreleri, sıcaklık/nem ortalamaları, cihaz kullanım oranları gibi raporlar oluşturulur.

Dashboard’da geçmişe dönük analiz ve grafiksel görselleştirme sağlanır.

8. 💳 Ödeme Sistemi
🧾 Paketler:
5 gün = 3€

10 gün = 5€

20 gün = 6€

30 gün = 7€

90 gün = 18€

180 gün = 33€

360 gün = 60€

🛠️ Kur Fiyatı:
Güncel döviz kuru online API’den alınır (örn. https://www.turkiyefinans.com.tr/tr-tr/finans-portal/Sayfalar/finans-portali.aspx  türkiye Finans  Döviz Satış).

TL karşılığı hesaplanarak kredi kartı ile tahsil edilir.

📆 Cihaz Süre Takibi:
Kullanım hakkı satın alındığında cihaz süreye sahip olur.

Sevkiyat oluşturulduğu anda süre başlar.

Süresi dolan cihaz için kullanıcıya uyarı gösterilir (örn. cihaz ID yanında "Süre Doldu" etiketi).

********************************************************************

İnaktif cihazlar bölümü için genel yapıyı bozmadan aşağıdaki şekilde düzenler misin?
Bu sayfada harita olmasına gerek yok.
Kullanıcı bu panelde aşağıdaki bilgileri görebilmeli:

Bilgi	Açıklama
Cihaz ID	 Cihazın benzersiz kimliği
Son Sevkiyat ID / Adı	Hangi sevkiyatta kullanıldığı
Son Kullanım Tarihi	En son aktif olduğu tarih
Son Firma	Hangi firmada en son kullanıldığı
Son Konum	Harita üstünden ya da metin olarak
Kullanım Hakkı	Kredi alınmış mı, kullanım hakkı var mı
Uygunluk Durumu	Cihaz tekrar sevkiyata atanabilir mi?
Durum	Aktif değil, beklemede, arızalı, serviste gibi ek bilgiler


MGZ24 Takip Sistemi'nde “İnaktif Cihazlar” sayfasını geliştiriyorsun. Bu sayfa, sevkiyatını tamamlamış ve şu anda başka bir sevkiyatta kullanılmayan cihazları gösterir.

React + TypeScript + TailwindCSS tabanlı bir kullanıcı arayüzü geliştir. Sayfa aşağıdaki özellikleri içermelidir:

1. 📋 Tablo Alanı:
   - Kolonlar:
     - Cihaz ID (tıklanabilir, detay ekranına yönlendirir)
     - Son Sevkiyat ID ve Adı (linklenmiş)
     - Son Kullanım Tarihi (tarih formatında)
     - Son Firma Adı
     - Son Konum (metinsel ve harita ikonu ile)
     - Kullanım Hakkı:
       - Var: ✅
       - Yok: ❌ (üzerine gelince neden yok balonu çıkmalı)
     - Uygunluk Durumu:
       - Uygun, Arızalı, Serviste, İade Bekliyor gibi etiketlerle gösterilmeli
     - İşlem: “Yeni Sevkiyata Ata” butonu (sadece uygun cihazlar için)

2. 🔍 Filtreleme / Arama Özellikleri:
   - Firma adına göre filtreleme
   - Uygunluk durumuna göre filtreleme
   - Tarih aralığına göre arama


3. 🔐 Rol Bazlı Görüntüleme:
   - Yalnızca admin veya planlama yetkisi olan kullanıcılar “Ata” butonunu görür.
   - İzleyici kullanıcılar sadece listeyi görüntüleyebilir.

*************
Aşağıdaki düzenlemeleri ilgili menüden yapabilir miyiz?
MGZ24 Takip Sistemi’nde, “Geçmiş Sevkiyatlar” sayfası ve “Aktif Sevkiyatlar” sayfası arasında mantıklı bir geçiş sistemi kurmak istiyorum.

🔹 1. Aktif Sevkiyatlar'da:

- Her sevkiyatın detaylarına girildiğinde, ilgili cihaza ait bilgilerin bulunduğu satırın en sonunda bir “✅ Sevkiyat Tamamlandı” kutucuğu olmalı.
- Bu kutucuk işaretlendiğinde kullanıcıya şu şekilde bir onay mesajı gösterilmeli:
  > “Bu işlemle birlikte sevkiyat tamamlanmış sayılacak ve geçmiş sevkiyatlara taşınacaktır. Devam etmek istiyor musunuz?”
- Kullanıcı onaylarsa:
  - Sevkiyat aktif listesinden çıkarılır.
  - Tüm cihaz bilgileriyle birlikte “Geçmiş Sevkiyatlar” listesine eklenir.
  - Cihazın aktiflik durumu sistemde “inaktif” olarak güncellenir.

🔹 2. Geçmiş Sevkiyatlar Sayfası:

Sayfa, cihaz geçmişini tarihsel olarak takip etmek ve analiz etmek için kullanılmalıdır. Bu sayfa aşağıdaki özellikleri içermelidir:

📋 Liste Kolonları:
- Sevkiyat ID
- Sevkiyat Adı
- Cihaz ID (tıklanabilir → cihaz detayına gider)
- Gönderen Firma
- Alan Firma
- Sevkiyat Başlangıç Tarihi
- Sevkiyat Bitiş Tarihi (ya da tamamlanma tarihi)
- Net ve Brüt Bilgisi
- Ürün (isteğe bağlı)
- Durum (Tamamlandı, İade Edildi, Arıza Bildirildi gibi)

🔍 Filtreleme Özellikleri:
- Cihaz ID’ye göre filtreleme
- Gönderen firmaya göre filtreleme
- Alan firmaya göre filtreleme
- Tarih aralığına göre arama
- Ürün bazlı filtre (opsiyonel)

🔐 Yetki Kontrolü:
- Sadece sevkiyatı başlatan ya da tamamlayan firma, geçmiş sevkiyatı görebilir.
- Admin tüm geçmiş sevkiyatları görür.




-- Tüm tabloları listele
SHOW TABLES FROM mgz24db;

-- Belirli bir tablonun yapısını göster (örn. 'shipments' tablosu için)
DESCRIBE mgz24db.shipments;
-- VEYA
SHOW COLUMNS FROM mgz24db.shipments;

-- Tüm tabloların ve sütunlarının detaylarını listele (daha kapsamlı)
SELECT
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    IS_NULLABLE,
    COLUMN_KEY, -- PRI (Primary), MUL (Multiple/Foreign), UNI (Unique)
    COLUMN_DEFAULT,
    EXTRA
FROM
    INFORMATION_SCHEMA.COLUMNS
WHERE
    TABLE_SCHEMA = 'mgz24db'
ORDER BY
    TABLE_NAME, ORDINAL_POSITION;

-- Yabancı anahtar (FOREIGN KEY) ilişkilerini listele
SELECT
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM
    INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE
    TABLE_SCHEMA = 'mgz24db' AND REFERENCED_TABLE_NAME IS NOT NULL;

-- İndeksleri listele
SHOW INDEX FROM mgz24db.shipments; -- Belirli bir tablo için
-- VEYA
SELECT
    s.INDEX_SCHEMA,
    s.INDEX_NAME,
    s.TABLE_NAME,
    s.COLUMN_NAME,
    s.SEQ_IN_INDEX,
    s.COLLATION,
    s.CARDINALITY,
    s.SUB_PART,
    s.PACKED,
    s.NULLABLE,
    s.INDEX_TYPE,
    s.COMMENT
FROM
    INFORMATION_SCHEMA.STATISTICS s
WHERE
    s.TABLE_SCHEMA = 'mgz24db'
ORDER BY
    s.TABLE_NAME, s.INDEX_NAME, s.SEQ_IN_INDEX;
