{"version": 3, "file": "static/js/122.3f66346a.chunk.js", "mappings": "gKASA,MAwGA,EAxGgBA,KACZ,MAAMC,GAAWC,EAAAA,EAAAA,MAaXC,EAAuB,UAVTC,MAChB,IAAK,IAADC,EAAAC,EACA,MAAMC,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C,OAAW,OAAJJ,QAAI,IAAJA,GAAU,QAANF,EAAJE,EAAMA,YAAI,IAAAF,OAAN,EAAJA,EAAYO,QAAY,OAAJL,QAAI,IAAJA,GAAU,QAAND,EAAJC,EAAMA,YAAI,IAAAD,OAAN,EAAJA,EAAYO,QAAS,MACpD,CAAE,MAAAC,GACE,MAAO,MACX,GAGaV,GAGjB,OACIW,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC1DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6BAA6BG,SAAS,KAAKC,GAAG,UAAU,kBAAgB,UAASH,SAAA,EAC5FC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,EAC7BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,kBAAkBI,GAAG,UAASH,SAAC,gBAC7CF,EAAAA,EAAAA,KAAA,UAAQM,KAAK,SAASL,UAAU,YAAY,kBAAgB,YAAY,iBAAe,WAAW,aAAW,cAEjHE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mEAAkEC,SAAA,EAC7EF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,kCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,MAAtBtB,EAASuB,SAAmB,SAAW,IAAMC,GAAG,IAAGR,SAAA,EAC5GF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMC,EAAAA,MAAW,0BAG1Cb,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,SAAtBtB,EAASuB,SAAsB,SAAW,IAAMC,GAAG,OAAMR,SAAA,EAClHF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAME,EAAAA,MAAgB,mCAG/Cd,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,sBAAtBtB,EAASuB,SAAmC,SAAW,IAAMC,GAAG,oBAAmBR,SAAA,EAC5IF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMG,EAAAA,MAAY,8BAG3Cf,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMI,EAAAA,MAAmB,sCAKtDhB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wBACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,CAEpCd,IACGY,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMK,EAAAA,MAAa,0BAIhDjB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,mBAAtBtB,EAASuB,SAAgC,SAAW,IAAMC,GAAG,iBAAgBR,SAAA,EACtIF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMM,EAAAA,MAAU,uBAK7ClB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,uCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMO,EAAAA,MAAU,qCAGzCnB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMQ,EAAAA,MAAgB,6CAKnDpB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMS,EAAAA,MAAgB,qBAG/CrB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,cAAtBtB,EAASuB,SAA2B,SAAW,IAAMC,GAAG,YAAWR,SAAA,EAC5HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMU,EAAAA,MAAe,4BAKlDtB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wBAG1I,C,sDC3Gd,MAQA,EAReqB,KAEPvB,EAAAA,EAAAA,KAAA,UAAQC,UAAU,kBAAiBC,UAC/BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,cAAaC,SAAC,iD,qGCEvC,MA6GA,EA7GesB,KACX,MAAMC,GAAWC,EAAAA,EAAAA,OACVC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,YAClCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAGvCG,EAAAA,EAAAA,YAAU,KACgBC,WAClB,IAAK,IAADC,EAAAC,EAAAC,EAAAC,EAEA,MAAMC,EAAa7C,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C2C,GAAmB,OAAVD,QAAU,IAAVA,GAAgB,QAANJ,EAAVI,EAAY9C,YAAI,IAAA0C,OAAN,EAAVA,EAAkBM,cAAwB,OAAVF,QAAU,IAAVA,GAAgB,QAANH,EAAVG,EAAY9C,YAAI,IAAA2C,OAAN,EAAVA,EAAkB9B,IAC3DsB,GAAqB,OAAVW,QAAU,IAAVA,GAAgB,QAANF,EAAVE,EAAY9C,YAAI,IAAA4C,OAAN,EAAVA,EAAkBK,QAAkB,OAAVH,QAAU,IAAVA,GAAgB,QAAND,EAAVC,EAAY9C,YAAI,IAAA6C,OAAN,EAAVA,EAAkBK,aAE7D,IAAKH,EAID,OAHAI,QAAQC,KAAK,kCACbhB,EAAY,gBACZG,GAAW,GAKf,GAAIJ,EAGA,OAFAC,EAAYD,QACZI,GAAW,GAKf,IACI,MAAMc,QAAiBC,EAAAA,GAAiBC,aAAaR,GAGjDM,GAAYA,EAASH,cACrBd,EAAYiB,EAASH,aAGP,OAAVJ,QAAU,IAAVA,GAAAA,EAAY9C,OACZ8C,EAAW9C,KAAKiD,KAAOI,EAASH,YAChC/C,aAAaqD,QAAQ,OAAQvD,KAAKwD,UAAUX,KAGxD,CAAE,MAAOY,GACLP,QAAQC,KAAK,gGAEbhB,EAAYD,GAAY,sBAC5B,CACJ,CAAE,MAAOwB,GACLR,QAAQQ,MAAM,0DAAuCA,GAErDvB,EAAY,sBAChB,CAAC,QACGG,GAAW,EACf,GAGJqB,EAAe,GAChB,IAWH,OACIjD,EAAAA,EAAAA,MAAA,UAAQF,UAAU,0DAA0D,gBAAc,OAAMC,SAAA,EAC5FF,EAAAA,EAAAA,KAACO,EAAAA,GAAI,CAACN,UAAU,gEAAgES,GAAG,IAAGR,UAClFF,EAAAA,EAAAA,KAAA,OAAKqD,I,60RAAeC,IAAI,aAAaC,OAAO,UAEhDpD,EAAAA,EAAAA,MAAA,MAAIF,UAAU,yCAAwCC,SAAA,EAClDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8CAA6CC,UACvDC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,aAAYC,SAAA,EACxBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMO,EAAAA,IAAQlB,UAAU,SACxC6B,EAAU,mBAAkBH,QAGrC3B,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4BAA2BC,UACrCF,EAAAA,EAAAA,KAAA,UACIwD,QAtBCC,KAEjB9D,aAAa+D,WAAW,QAGxBjC,EAAS,SAAS,EAkBFxB,UAAU,gBACV,iBAAe,UACf,oBAAkB,OAClB,gBAAc,+BAAYC,UAE1BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM+C,EAAAA,WAG/B3D,EAAAA,EAAAA,KAAA,MAAIC,UAAU,iCAAgCC,UAC1CF,EAAAA,EAAAA,KAAA,UACIC,UAAU,gBACVK,KAAK,SACL,iBAAe,YACf,iBAAe,WACf,gBAAc,UACd,gBAAc,QACd,aAAW,kBAAcJ,UAEzBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMgD,EAAAA,eAI9B,C,oJCvGjB,MA+gBA,EA/gBgBC,KACZ,MAAMpC,GAAWC,EAAAA,EAAAA,OACVI,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCiC,EAAQC,IAAalC,EAAAA,EAAAA,WAAS,IAC9BsB,EAAOa,IAAYnC,EAAAA,EAAAA,UAAS,OAC5BoC,EAAgBC,IAAqBrC,EAAAA,EAAAA,UAAS,KAG9CsC,EAAaC,IAAkBvC,EAAAA,EAAAA,UAAS,CAC3CW,WAAY,GACZ6B,UAAW,GACXC,MAAO,GACP5B,YAAa,GACb6B,IAAK,GACLC,MAAO,GACPC,MAAO,GACP3E,MAAO,MAIJ4E,EAAUC,IAAe9C,EAAAA,EAAAA,UAAS,CACrC+C,SAAU,GACVN,MAAO,GACPO,UAAW,GACXC,MAAO,GACPC,QAAS,GACTC,QAAS,GACTC,iBAAkB,GAClBC,aAAc,GACdC,iBAAkB,MAItBnD,EAAAA,EAAAA,YAAU,KACgBC,WAClB,IACIF,GAAW,GACXiC,EAAS,MAGT,MAAM1B,EAAa7C,KAAKC,MAAMC,aAAaC,QAAQ,SAC7CwF,EAAoB,OAAV9C,QAAU,IAAVA,OAAU,EAAVA,EAAY9C,KAE5B,IAAK4F,IAAaA,EAAQ/E,KAAM+E,EAAQ5C,WAGpC,OAFAwB,EAAS,qFACTvC,EAAS,UAKb,MAAMc,EAAS6C,EAAQ/E,IAAM+E,EAAQ5C,WAGrC,IACI,MAAMK,QAAiBC,EAAAA,GAAiBC,aAAaR,GAGrD6B,EAAevB,GACf8B,EAAY,CACRC,SAAU/B,EAASwB,WAAa,GAChCC,MAAOzB,EAASyB,OAAS,GACzBO,UAAWhC,EAASH,aAAe,GACnCoC,MAAOjC,EAAS0B,KAAO,GACvBQ,QAASlC,EAAS2B,OAAS,GAC3BQ,QAASnC,EAAS4B,OAAS,GAC3BQ,iBAAkB,GAClBC,aAAc,GACdC,iBAAkB,KAGtBpD,GAAW,EACf,CAAE,MAAOmB,GAAW,IAADmC,EAAAC,EACf3C,QAAQQ,MAAM,+BAAsBD,GAGpC,MAAMZ,EAAa7C,KAAKC,MAAMC,aAAaC,QAAQ,SAC7CwF,GAAoB,OAAV9C,QAAU,IAAVA,OAAU,EAAVA,EAAY9C,OAAQ,CAAC,EACrC4E,EAAe,CACX5B,WAAY4C,EAAQ/E,IAAM+E,EAAQ5C,YAAc,IAChD6B,UAAWe,EAAQR,WAAyB,QAAjBS,EAAID,EAAQd,aAAK,IAAAe,OAAA,EAAbA,EAAeE,MAAM,KAAK,KAAM,YAC/DjB,MAAOc,EAAQd,OAAS,wBACxB5B,YAAa0C,EAAQ3C,MAAQ2C,EAAQ1C,aAAe,8BACpD6B,IAAKa,EAAQb,KAAO,GACpBC,MAAOY,EAAQZ,OAAS,GACxBC,MAAOW,EAAQX,OAAS,GACxB3E,MAAOsF,EAAQvF,MAAQuF,EAAQtF,OAAS,cAG5C6E,EAAY,CACRC,SAAUQ,EAAQR,WAAyB,QAAjBU,EAAIF,EAAQd,aAAK,IAAAgB,OAAA,EAAbA,EAAeC,MAAM,KAAK,KAAM,YAC9DjB,MAAOc,EAAQd,OAAS,wBACxBO,UAAWO,EAAQ3C,MAAQ2C,EAAQ1C,aAAe,8BAClDoC,MAAOM,EAAQb,KAAO,GACtBQ,QAASK,EAAQZ,OAAS,GAC1BQ,QAASI,EAAQX,OAAS,GAC1BQ,iBAAkB,GAClBC,aAAc,GACdC,iBAAkB,KAIlBjC,EAASsC,SAAWtC,EAASsC,QAAQC,SAAS,QAC9CzB,EAAS,0DACFd,EAASwC,UAAyC,MAA7BxC,EAASwC,SAASC,OAC9C3B,EAAS,0GACgB,iBAAlBd,EAAS0C,KAChB5B,EAAS,qEACmB,kBAArBd,EAASsC,QAChBxB,EAAS,2FAETA,EAAS,6GAGbjC,GAAW,EACf,CACJ,CAAE,MAAO8D,GACLlD,QAAQQ,MAAM,8BAA0B0C,GACxC7B,EAAS,uEACTjC,GAAW,EACf,GAGJqB,EAAe,GAChB,CAAC3B,IAGJ,MAAMqE,EAAgBC,IAClB,MAAM,KAAEtD,EAAI,MAAEuD,GAAUD,EAAEE,OAC1BtB,GAAYuB,IAASC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACdD,GAAS,IACZ,CAACzD,GAAOuD,KACT,EA8HP,OAAIlE,GAEI3B,EAAAA,EAAAA,MAAAiG,EAAAA,SAAA,CAAAlG,SAAA,EACIF,EAAAA,EAAAA,KAACwB,EAAAA,EAAM,KACPxB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAChBF,EAAAA,EAAAA,KAACf,EAAAA,EAAO,KACRe,EAAAA,EAAAA,KAAA,QAAMC,UAAU,iFAAgFC,UAC5FC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mDAAmDoG,MAAO,CAAE9C,OAAQ,QAASrD,SAAA,EACxFF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8BAA8BJ,KAAK,SAAQK,UACtDF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,kBAAiBC,SAAC,wBAEtCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,OAAMC,SAAC,iEAU/CC,EAAAA,EAAAA,MAAAiG,EAAAA,SAAA,CAAAlG,SAAA,EACIF,EAAAA,EAAAA,KAACwB,EAAAA,EAAM,KACPxB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAChBF,EAAAA,EAAAA,KAACf,EAAAA,EAAO,KACRkB,EAAAA,EAAAA,MAAA,QAAMF,UAAU,iFAAgFC,SAAA,EAC5FF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAAmCC,UAC9CC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,oBAAmBC,SAAA,EAC7BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMO,EAAAA,IAAQlB,UAAU,sBAAsB,oCAMtEkD,IACGhD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qBAAoBC,SAAA,EAC/BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMO,EAAAA,IAAQlB,UAAU,SACxCkD,KAKRc,IACG9D,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sBAAqBC,SAAA,EAChCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMO,EAAAA,IAAQlB,UAAU,SACxCgE,MAITjE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACrBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACtBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACjBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kCAAiCC,UAC5CF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,eAAcC,SAAC,yBAEjCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACtBC,EAAAA,EAAAA,MAAA,QAAMmG,SApLrBrE,UAQjB,GAPA8D,EAAEQ,iBAGFvC,EAAS,MACTE,EAAkB,IAGdQ,EAASQ,cAAgBR,EAASS,iBAAkB,CACpD,IAAKT,EAASO,iBAEV,YADAjB,EAAS,oFAIb,GAAIU,EAASQ,eAAiBR,EAASS,iBAEnC,YADAnB,EAAS,6EAIb,GAAIU,EAASQ,aAAasB,OAAS,EAE/B,YADAxC,EAAS,wDAGjB,CAEA,IACID,GAAU,GAGV,MAAM0C,EAAa,CACf7B,SAAUF,EAASE,SACnBN,MAAOI,EAASJ,MAChBO,UAAWH,EAASG,UACpBC,MAAOJ,EAASI,MAChBC,QAASL,EAASK,QAClBC,QAASN,EAASM,SAIlBN,EAASO,kBAAoBP,EAASQ,eACtCuB,EAAWxB,iBAAmBP,EAASO,iBACvCwB,EAAWvB,aAAeR,EAASQ,cAIvC,UACUpC,EAAAA,GAAiB4D,gBAAgBvC,EAAY3B,WAAYiE,GAG/D,MAAMnE,EAAa7C,KAAKC,MAAMC,aAAaC,QAAQ,SAC/C0C,IACAA,EAAWG,KAAOiC,EAASG,UAC3BvC,EAAWgC,MAAQI,EAASJ,MAC5B3E,aAAaqD,QAAQ,OAAQvD,KAAKwD,UAAUX,KAIhDqC,GAAYuB,IAASC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACdD,GAAS,IACZjB,iBAAkB,GAClBC,aAAc,GACdC,iBAAkB,OAGtBjB,EAAkB,2DAClBH,GAAU,GAGV4C,YAAW,KACPzC,EAAkB,GAAG,GACtB,IACP,CAAE,MAAOhB,GACLP,QAAQQ,MAAM,iCAA0BD,GAGxC,IAAI0D,EAAe,iDAEf1D,EAASwC,SACwB,MAA7BxC,EAASwC,SAASC,OAClBiB,EAAe,yCACqB,MAA7B1D,EAASwC,SAASC,OACzBiB,EAAe,iIACR1D,EAASwC,SAASmB,MAAQ3D,EAASwC,SAASmB,KAAKrB,UACxDoB,EAAe1D,EAASwC,SAASmB,KAAKrB,SAEjB,iBAAlBtC,EAAS0C,KAChBgB,EAAe,kEACa,kBAArB1D,EAASsC,UAChBoB,EAAe,oDAGnB5C,EAAS4C,GACT7C,GAAU,EACd,CACJ,CAAE,MAAO8B,GACLlD,QAAQQ,MAAM,oCAAgC0C,GAC9C7B,EAAS,yFACTD,GAAU,EACd,GAkF6D7D,SAAA,EACzBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACrBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACrBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kBAAiBC,SAAA,EAC5BC,EAAAA,EAAAA,MAAA,SAAO2G,QAAQ,WAAW7G,UAAU,aAAYC,SAAA,EAC5CF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMO,EAAAA,IAAQlB,UAAU,SAAS,mCAGtDD,EAAAA,EAAAA,KAAA,SACIM,KAAK,OACLL,UAAU,eACVI,GAAG,WACHoC,KAAK,WACLuD,MAAOtB,EAASE,SAChBmC,SAAUjB,EACVkB,UAAQ,QAIhB7G,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kBAAiBC,SAAA,EAC5BC,EAAAA,EAAAA,MAAA,SAAO2G,QAAQ,QAAQ7G,UAAU,aAAYC,SAAA,EACzCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMqG,EAAAA,IAAYhH,UAAU,SAAS,qBAG1DD,EAAAA,EAAAA,KAAA,SACIM,KAAK,QACLL,UAAU,eACVI,GAAG,QACHoC,KAAK,QACLuD,MAAOtB,EAASJ,MAChByC,SAAUjB,EACVkB,UAAQ,QAIhB7G,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kBAAiBC,SAAA,EAC5BC,EAAAA,EAAAA,MAAA,SAAO2G,QAAQ,YAAY7G,UAAU,aAAYC,SAAA,EAC7CF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMO,EAAAA,IAAQlB,UAAU,SAAS,eAGtDD,EAAAA,EAAAA,KAAA,SACIM,KAAK,OACLL,UAAU,eACVI,GAAG,YACHoC,KAAK,YACLuD,MAAOtB,EAASG,UAChBkC,SAAUjB,EACVkB,UAAQ,WAKpB7G,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACrBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kBAAiBC,SAAA,EAC5BC,EAAAA,EAAAA,MAAA,SAAO2G,QAAQ,QAAQ7G,UAAU,aAAYC,SAAA,EACzCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMsG,EAAAA,IAASjH,UAAU,SAAS,cAGvDD,EAAAA,EAAAA,KAAA,SACIM,KAAK,OACLL,UAAU,eACVI,GAAG,QACHoC,KAAK,QACLuD,MAAOtB,EAASI,MAChBiC,SAAUjB,QAIlB3F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kBAAiBC,SAAA,EAC5BC,EAAAA,EAAAA,MAAA,SAAO2G,QAAQ,UAAU7G,UAAU,aAAYC,SAAA,EAC3CF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMuG,EAAAA,IAAYlH,UAAU,SAAS,qBAG1DD,EAAAA,EAAAA,KAAA,SACIM,KAAK,OACLL,UAAU,eACVI,GAAG,UACHoC,KAAK,UACLuD,MAAOtB,EAASK,QAChBgC,SAAUjB,QAIlB3F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kBAAiBC,SAAA,EAC5BC,EAAAA,EAAAA,MAAA,SAAO2G,QAAQ,UAAU7G,UAAU,aAAYC,SAAA,EAC3CF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMuG,EAAAA,IAAYlH,UAAU,SAAS,YAG1DD,EAAAA,EAAAA,KAAA,SACIM,KAAK,OACLL,UAAU,eACVI,GAAG,UACHoC,KAAK,UACLuD,MAAOtB,EAASM,QAChB+B,SAAUjB,QAIlB3F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kBAAiBC,SAAA,EAC5BC,EAAAA,EAAAA,MAAA,SAAO2G,QAAQ,OAAO7G,UAAU,aAAYC,SAAA,EACxCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMwG,EAAAA,IAAWnH,UAAU,SAAS,kCAGzDD,EAAAA,EAAAA,KAAA,SACIM,KAAK,OACLL,UAAU,eACVI,GAAG,OACH2F,MAA6B,UAAtB7B,EAAYrE,MAAoB,cAAmC,YAAtBqE,EAAYrE,MAAsB,cAAU,sBAChGuH,UAAQ,cAMxBrH,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UAEdE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACrBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACtBF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,qCACJF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mBAAkBC,SAAC,kHAGpCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACrBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kBAAiBC,SAAA,EAC5BF,EAAAA,EAAAA,KAAA,SAAO8G,QAAQ,mBAAmB7G,UAAU,aAAYC,SAAC,uBACzDF,EAAAA,EAAAA,KAAA,SACIM,KAAK,WACLL,UAAU,eACVI,GAAG,mBACHoC,KAAK,mBACLuD,MAAOtB,EAASO,iBAChB8B,SAAUjB,UAKtB9F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACrBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kBAAiBC,SAAA,EAC5BF,EAAAA,EAAAA,KAAA,SAAO8G,QAAQ,eAAe7G,UAAU,aAAYC,SAAC,qBACrDF,EAAAA,EAAAA,KAAA,SACIM,KAAK,WACLL,UAAU,eACVI,GAAG,eACHoC,KAAK,eACLuD,MAAOtB,EAASQ,aAChB6B,SAAUjB,EACVwB,UAAU,YAKtBtH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACrBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kBAAiBC,SAAA,EAC5BF,EAAAA,EAAAA,KAAA,SAAO8G,QAAQ,mBAAmB7G,UAAU,aAAYC,SAAC,8BACzDF,EAAAA,EAAAA,KAAA,SACIM,KAAK,WACLL,UAAU,eACVI,GAAG,mBACHoC,KAAK,mBACLuD,MAAOtB,EAASS,iBAChB4B,SAAUjB,EACVwB,UAAU,eAM1BtH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,MAAKC,UAChBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACtBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCC,SAAA,EAC7CC,EAAAA,EAAAA,MAAA,UACIG,KAAK,SACLL,UAAU,oBACVuD,QA3PpC+D,KAEhB5C,EAAY,CACRC,SAAUT,EAAYE,WAAa,GACnCC,MAAOH,EAAYG,OAAS,GAC5BO,UAAWV,EAAYzB,aAAe,GACtCoC,MAAOX,EAAYI,KAAO,GAC1BQ,QAASZ,EAAYK,OAAS,GAC9BQ,QAASb,EAAYM,OAAS,GAC9BQ,iBAAkB,GAClBC,aAAc,GACdC,iBAAkB,KAItBnB,EAAS,MACTE,EAAkB,GAAG,EA4O+BmD,SAAUvD,EAAO5D,SAAA,EAEjBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM4G,EAAAA,IAAQvH,UAAU,SAAS,wBAGtDD,EAAAA,EAAAA,KAAA,UACIM,KAAK,SACLL,UAAU,kBACVoH,SAAUvD,EAAO5D,SAEhB4D,GACG3D,EAAAA,EAAAA,MAAAiG,EAAAA,SAAA,CAAAlG,SAAA,EACIF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,wCAAwCJ,KAAK,SAAS,cAAY,SAAc,sBAIpGM,EAAAA,EAAAA,MAAAiG,EAAAA,SAAA,CAAAlG,SAAA,EACIF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM6G,EAAAA,IAAQxH,UAAU,SAAS,0CAgB1GD,EAAAA,EAAAA,KAACuB,EAAAA,EAAM,MACR,C", "sources": ["components/Sidebar.js", "components/Footer.js", "components/Header.js", "pages/Profile.js"], "sourcesContent": ["import React from 'react';\r\nimport { Link, useLocation } from 'react-router-dom';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport {\r\n    faDolly, faFolderPlus, faAnchor, faFlag<PERSON>he<PERSON>ed,\r\n    faUser, faStreetView, faCreditCard, faFileLines,\r\n    faBell, faDesktop\r\n} from '@fortawesome/free-solid-svg-icons';\r\n\r\nconst Sidebar = () => {\r\n    const location = useLocation();\r\n    \r\n    // Get user role from localStorage\r\n    const getUserRole = () => {\r\n        try {\r\n            const user = JSON.parse(localStorage.getItem('user'));\r\n            return user?.user?.role || user?.user?.gorev || 'user';\r\n        } catch {\r\n            return 'user';\r\n        }\r\n    };\r\n    \r\n    const userRole = getUserRole();\r\n    const isAdmin = userRole === 'admin';\r\n\r\n    return (\r\n        <div className=\"sidebar col-md-4 col-lg-2 p-0 bg-light-subtle\">\r\n            <div className=\"offcanvas-md offcanvas-end\" tabIndex=\"-1\" id=\"yanMenu\" aria-labelledby=\"yanMenu\">\r\n                <div className=\"offcanvas-header\">\r\n                    <h5 className=\"offcanvas-title\" id=\"yanMenu\">MGZ24 Gold</h5>\r\n                    <button type=\"button\" className=\"btn-close\" data-bs-dismiss=\"offcanvas\" data-bs-target=\"#yanMenu\" aria-label=\"Kapat\"></button>\r\n                </div>\r\n                <div className=\"offcanvas-body d-md-flex flex-column p-0 pt-lg-3 overflow-y-auto\">\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Sevkiyat İşlemleri</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/' ? 'active' : ''}`} to=\"/\">\r\n                                <FontAwesomeIcon icon={faDolly} />Aktif Sevkiyatlar\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/add' ? 'active' : ''}`} to=\"/add\">\r\n                                <FontAwesomeIcon icon={faFolderPlus} />Yeni Sevkiyat Oluştur\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/inactive-devices' ? 'active' : ''}`} to=\"/inactive-devices\">\r\n                                <FontAwesomeIcon icon={faAnchor} />İnaktif Cihazlar\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/history' ? 'active' : ''}`} to=\"/history\">\r\n                                <FontAwesomeIcon icon={faFlagCheckered} />Geçmiş Sevkiyatlar\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Sistem Yönetimi</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        {/* Cihaz Yönetimi - Sadece admin kullanıcılarına göster */}\r\n                        {isAdmin && (\r\n                            <li className=\"nav-item\">\r\n                                <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/devices' ? 'active' : ''}`} to=\"/devices\">\r\n                                    <FontAwesomeIcon icon={faDesktop} />Cihaz Yönetimi\r\n                                </Link>\r\n                            </li>\r\n                        )}\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/notifications' ? 'active' : ''}`} to=\"/notifications\">\r\n                                <FontAwesomeIcon icon={faBell} />Bildirimler\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Kullanıcı Ayarları</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/profile' ? 'active' : ''}`} to=\"/profile\">\r\n                                <FontAwesomeIcon icon={faUser} />Kullanıcı Profilim\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/viewers' ? 'active' : ''}`} to=\"/viewers\">\r\n                                <FontAwesomeIcon icon={faStreetView} />İzleyici İşlemleri\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Ödeme ve Yapılandırma</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/payment' ? 'active' : ''}`} to=\"/payment\">\r\n                                <FontAwesomeIcon icon={faCreditCard} />Ödeme Yap\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/invoices' ? 'active' : ''}`} to=\"/invoices\">\r\n                                <FontAwesomeIcon icon={faFileLines} />Faturalarım\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-body-secondary text-uppercase\">Kredi Durumu</h6>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Sidebar;", "import React from 'react';\r\n\r\nconst Footer = () => {\r\n    return (\r\n        <footer className=\"py-5 border-top\">\r\n            <p className=\"text-center\">&copy;2025 Inkatech Ölçüm Sistemleri</p>\r\n        </footer>\r\n    );\r\n};\r\n\r\nexport default Footer; ", "import React, { useState, useEffect } from 'react';\r\nimport { Link, useNavigate } from 'react-router-dom';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { faRightFromBracket, faBars, faUser } from '@fortawesome/free-solid-svg-icons';\r\nimport logoDark from '../assets/img/logo.png';\r\nimport { kullaniciService } from '../api/dbService';\r\n\r\nconst Header = () => {\r\n    const navigate = useNavigate();\r\n    const [userName, setUserName] = useState('Misafir');\r\n    const [loading, setLoading] = useState(true);\r\n\r\n    // Kullanıcı bilgilerini veritabanından al\r\n    useEffect(() => {\r\n        const fetchUserData = async () => {\r\n            try {\r\n                // Local storage'dan kullanıcı bilgisini al\r\n                const storedUser = JSON.parse(localStorage.getItem('user'));\r\n                const userId = storedUser?.user?.musteri_ID || storedUser?.user?.id;\r\n                const userName = storedUser?.user?.name || storedUser?.user?.musteri_adi;\r\n\r\n                if (!userId) {\r\n                    console.warn('Oturum bilgisi bulunamadı');\r\n                    setUserName('Misafir');\r\n                    setLoading(false);\r\n                    return;\r\n                }\r\n\r\n                // Eğer localStorage'da isim varsa direkt kullan\r\n                if (userName) {\r\n                    setUserName(userName);\r\n                    setLoading(false);\r\n                    return;\r\n                }\r\n\r\n                // API'den kullanıcı bilgilerini getir (backend çalışıyorsa)\r\n                try {\r\n                    const userData = await kullaniciService.getKullanici(userId);\r\n\r\n                    // musteri_adi alanını kullan\r\n                    if (userData && userData.musteri_adi) {\r\n                        setUserName(userData.musteri_adi);\r\n\r\n                        // localStorage'ı güncelle\r\n                        if (storedUser?.user) {\r\n                            storedUser.user.name = userData.musteri_adi;\r\n                            localStorage.setItem('user', JSON.stringify(storedUser));\r\n                        }\r\n                    }\r\n                } catch (apiError) {\r\n                    console.warn('API\\'den kullanıcı bilgisi alınamadı, localStorage kullanılıyor');\r\n                    // API hatası durumunda localStorage'daki adı kullan\r\n                    setUserName(userName || 'Kullanıcı');\r\n                }\r\n            } catch (error) {\r\n                console.error('Kullanıcı bilgileri alınırken hata:', error);\r\n                // Hata durumunda varsayılan isim kullan\r\n                setUserName('Kullanıcı');\r\n            } finally {\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        fetchUserData();\r\n    }, []);\r\n\r\n    // Çıkış işlemi\r\n    const handleLogout = () => {\r\n        // Local storage'dan kullanıcı bilgilerini temizle\r\n        localStorage.removeItem('user');\r\n\r\n        // Login sayfasına yönlendir\r\n        navigate('/login');\r\n    };\r\n\r\n    return (\r\n        <header className=\"navbar sticky-top bg-darkblue flex-md-nowrap p-0 shadow\" data-bs-theme=\"dark\">\r\n            <Link className=\"navbar-brand col-md-4 col-lg-2 me-0 px-3 py-2 fs-6 text-white\" to=\"/\">\r\n                <img src={logoDark} alt=\"MGZ24 Logo\" height=\"40\" />\r\n            </Link>\r\n            <ul className=\"navbar-nav flex-row align-items-center\">\r\n                <li className=\"nav-item text-nowrap d-none d-md-block me-3\">\r\n                    <span className=\"text-white\">\r\n                        <FontAwesomeIcon icon={faUser} className=\"me-2\" />\r\n                        {loading ? 'Yükleniyor...' : userName}\r\n                    </span>\r\n                </li>\r\n                <li className=\"nav-item text-nowrap px-1\">\r\n                    <button\r\n                        onClick={handleLogout}\r\n                        className=\"nav-link px-3\"\r\n                        data-bs-toggle=\"tooltip\"\r\n                        data-bs-placement=\"left\"\r\n                        data-bs-title=\"Çıkış yap!\"\r\n                    >\r\n                        <FontAwesomeIcon icon={faRightFromBracket} />\r\n                    </button>\r\n                </li>\r\n                <li className=\"nav-item text-nowrap d-md-none\">\r\n                    <button\r\n                        className=\"nav-link px-3\"\r\n                        type=\"button\"\r\n                        data-bs-toggle=\"offcanvas\"\r\n                        data-bs-target=\"#yanMenu\"\r\n                        aria-controls=\"yanMenu\"\r\n                        aria-expanded=\"false\"\r\n                        aria-label=\"menu aç/kapa\"\r\n                    >\r\n                        <FontAwesomeIcon icon={faBars} />\r\n                    </button>\r\n                </li>\r\n            </ul>\r\n        </header>\r\n    );\r\n};\r\n\r\nexport default Header; ", "import React, { useState, useEffect } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { faUser, faSave, faUndo, faEnvelope, faPhone, faBuilding, faUserTag } from '@fortawesome/free-solid-svg-icons';\r\nimport Header from '../components/Header';\r\nimport Sidebar from '../components/Sidebar';\r\nimport Footer from '../components/Footer';\r\nimport { kullaniciService } from '../api/dbService';\r\n\r\nconst Profile = () => {\r\n    const navigate = useNavigate();\r\n    const [loading, setLoading] = useState(true);\r\n    const [saving, setSaving] = useState(false);\r\n    const [error, setError] = useState(null);\r\n    const [successMessage, setSuccessMessage] = useState('');\r\n\r\n    // Kullanıcı bilgileri için state\r\n    const [userProfile, setUserProfile] = useState({\r\n        musteri_ID: '',\r\n        kullanici: '',\r\n        email: '',\r\n        musteri_adi: '',\r\n        tel: '',\r\n        firma: '',\r\n        adres: '',\r\n        gorev: ''\r\n    });\r\n\r\n    // Form değerlerini saklamak için state\r\n    const [formData, setFormData] = useState({\r\n        username: '',\r\n        email: '',\r\n        full_name: '',\r\n        phone: '',\r\n        company: '',\r\n        address: '',\r\n        current_password: '',\r\n        new_password: '',\r\n        confirm_password: ''\r\n    });\r\n\r\n    // Kullanıcı bilgilerini yükle\r\n    useEffect(() => {\r\n        const fetchUserData = async () => {\r\n            try {\r\n                setLoading(true);\r\n                setError(null);\r\n\r\n                // Local storage'dan kullanıcı bilgilerini al\r\n                const storedUser = JSON.parse(localStorage.getItem('user'));\r\n                const userObj = storedUser?.user;\r\n\r\n                if (!userObj || !(userObj.id || userObj.musteri_ID)) {\r\n                    setError('Oturum bilgileriniz bulunamadı. Lütfen tekrar giriş yapın.');\r\n                    navigate('/login');\r\n                    return;\r\n                }\r\n\r\n                // Kullanıcı ID'sini al (id veya musteri_ID)\r\n                const userId = userObj.id || userObj.musteri_ID;\r\n\r\n                // API'den kullanıcı bilgilerini al\r\n                try {\r\n                    const userData = await kullaniciService.getKullanici(userId);\r\n\r\n                    // State'leri güncelle\r\n                    setUserProfile(userData);\r\n                    setFormData({\r\n                        username: userData.kullanici || '',\r\n                        email: userData.email || '',\r\n                        full_name: userData.musteri_adi || '',\r\n                        phone: userData.tel || '',\r\n                        company: userData.firma || '',\r\n                        address: userData.adres || '',\r\n                        current_password: '',\r\n                        new_password: '',\r\n                        confirm_password: ''\r\n                    });\r\n\r\n                    setLoading(false);\r\n                } catch (apiError) {\r\n                    console.error('API erişim hatası:', apiError);\r\n\r\n                    // API hatası durumunda local storage'daki bilgileri kullan\r\n                    const storedUser = JSON.parse(localStorage.getItem('user'));\r\n                    const userObj = storedUser?.user || {};\r\n                    setUserProfile({\r\n                        musteri_ID: userObj.id || userObj.musteri_ID || '1',\r\n                        kullanici: userObj.username || userObj.email?.split('@')[0] || 'kullanici',\r\n                        email: userObj.email || '<EMAIL>',\r\n                        musteri_adi: userObj.name || userObj.musteri_adi || 'Misafir Kullanıcı',\r\n                        tel: userObj.tel || '',\r\n                        firma: userObj.firma || '',\r\n                        adres: userObj.adres || '',\r\n                        gorev: userObj.role || userObj.gorev || 'kullanici'\r\n                    });\r\n\r\n                    setFormData({\r\n                        username: userObj.username || userObj.email?.split('@')[0] || 'kullanici',\r\n                        email: userObj.email || '<EMAIL>',\r\n                        full_name: userObj.name || userObj.musteri_adi || 'Misafir Kullanıcı',\r\n                        phone: userObj.tel || '',\r\n                        company: userObj.firma || '',\r\n                        address: userObj.adres || '',\r\n                        current_password: '',\r\n                        new_password: '',\r\n                        confirm_password: ''\r\n                    });\r\n\r\n                    // Demo verisi mi yoksa gerçek bir hata mı görelim\r\n                    if (apiError.message && apiError.message.includes('demo')) {\r\n                        setError('Sunucuya erişilemiyor. Demo veri gösteriliyor.');\r\n                    } else if (apiError.response && apiError.response.status === 404) {\r\n                        setError('Kullanıcı bilgisi veritabanında bulunamadı. Yerel bilgiler kullanılıyor.');\r\n                    } else if (apiError.code === 'ECONNABORTED') {\r\n                        setError('Sunucu yanıt vermedi. Yerel bilgiler kullanılıyor.');\r\n                    } else if (apiError.message === 'Network Error') {\r\n                        setError('Ağ hatası. Sunucuya erişilemiyor. Yerel bilgiler kullanılıyor.');\r\n                    } else {\r\n                        setError('Kullanıcı bilgileri yüklenirken bir hata oluştu. Yerel bilgiler kullanılıyor.');\r\n                    }\r\n\r\n                    setLoading(false);\r\n                }\r\n            } catch (err) {\r\n                console.error('Genel bir hata oluştu:', err);\r\n                setError('Beklenmeyen bir hata oluştu. Lütfen sayfayı yenileyin.');\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        fetchUserData();\r\n    }, [navigate]);\r\n\r\n    // Form değişikliklerini izle\r\n    const handleChange = (e) => {\r\n        const { name, value } = e.target;\r\n        setFormData(prevState => ({\r\n            ...prevState,\r\n            [name]: value\r\n        }));\r\n    };\r\n\r\n    // Formu gönder\r\n    const handleSubmit = async (e) => {\r\n        e.preventDefault();\r\n\r\n        // Hata ve başarı mesajlarını temizle\r\n        setError(null);\r\n        setSuccessMessage('');\r\n\r\n        // Şifre kontrolü\r\n        if (formData.new_password || formData.confirm_password) {\r\n            if (!formData.current_password) {\r\n                setError('Şifrenizi değiştirmek için mevcut şifrenizi girmelisiniz.');\r\n                return;\r\n            }\r\n\r\n            if (formData.new_password !== formData.confirm_password) {\r\n                setError('Yeni şifre ve şifre tekrarı aynı olmalıdır.');\r\n                return;\r\n            }\r\n\r\n            if (formData.new_password.length < 6) {\r\n                setError('Yeni şifre en az 6 karakter olmalıdır.');\r\n                return;\r\n            }\r\n        }\r\n\r\n        try {\r\n            setSaving(true);\r\n\r\n            // API'ye gönderilecek veriyi hazırla\r\n            const updateData = {\r\n                username: formData.username,\r\n                email: formData.email,\r\n                full_name: formData.full_name,\r\n                phone: formData.phone,\r\n                company: formData.company,\r\n                address: formData.address\r\n            };\r\n\r\n            // Şifre değiştirme isteği varsa\r\n            if (formData.current_password && formData.new_password) {\r\n                updateData.current_password = formData.current_password;\r\n                updateData.new_password = formData.new_password;\r\n            }\r\n\r\n            // API'ye güncelleme isteği gönder\r\n            try {\r\n                await kullaniciService.updateKullanici(userProfile.musteri_ID, updateData);\r\n\r\n                // LocalStorage'daki kullanıcı bilgilerini güncelle\r\n                const storedUser = JSON.parse(localStorage.getItem('user'));\r\n                if (storedUser) {\r\n                    storedUser.name = formData.full_name;\r\n                    storedUser.email = formData.email;\r\n                    localStorage.setItem('user', JSON.stringify(storedUser));\r\n                }\r\n\r\n                // Şifre alanlarını temizle\r\n                setFormData(prevState => ({\r\n                    ...prevState,\r\n                    current_password: '',\r\n                    new_password: '',\r\n                    confirm_password: ''\r\n                }));\r\n\r\n                setSuccessMessage('Profil bilgileriniz başarıyla güncellendi.');\r\n                setSaving(false);\r\n\r\n                // 3 saniye sonra başarı mesajını temizle\r\n                setTimeout(() => {\r\n                    setSuccessMessage('');\r\n                }, 3000);\r\n            } catch (apiError) {\r\n                console.error('API güncelleme hatası:', apiError);\r\n\r\n                // API yanıt hatalarını kontrol et\r\n                let errorMessage = 'Profil güncellenirken bir hata oluştu.';\r\n\r\n                if (apiError.response) {\r\n                    if (apiError.response.status === 401) {\r\n                        errorMessage = 'Mevcut şifreniz yanlış.';\r\n                    } else if (apiError.response.status === 409) {\r\n                        errorMessage = 'Bu e-posta adresi veya kullanıcı adı başka bir kullanıcı tarafından kullanılıyor.';\r\n                    } else if (apiError.response.data && apiError.response.data.message) {\r\n                        errorMessage = apiError.response.data.message;\r\n                    }\r\n                } else if (apiError.code === 'ECONNABORTED') {\r\n                    errorMessage = 'Sunucu yanıt vermedi. Lütfen daha sonra tekrar deneyin.';\r\n                } else if (apiError.message === 'Network Error') {\r\n                    errorMessage = 'Ağ hatası. Sunucuya erişilemiyor.';\r\n                }\r\n\r\n                setError(errorMessage);\r\n                setSaving(false);\r\n            }\r\n        } catch (err) {\r\n            console.error('Beklenmeyen bir hata oluştu:', err);\r\n            setError('Beklenmeyen bir hata oluştu. Lütfen sayfayı yenileyin ve tekrar deneyin.');\r\n            setSaving(false);\r\n        }\r\n    };\r\n\r\n    // Formu sıfırla\r\n    const handleReset = () => {\r\n        // Kullanıcı profilindeki bilgilerle formu sıfırla\r\n        setFormData({\r\n            username: userProfile.kullanici || '',\r\n            email: userProfile.email || '',\r\n            full_name: userProfile.musteri_adi || '',\r\n            phone: userProfile.tel || '',\r\n            company: userProfile.firma || '',\r\n            address: userProfile.adres || '',\r\n            current_password: '',\r\n            new_password: '',\r\n            confirm_password: ''\r\n        });\r\n\r\n        // Hata ve başarı mesajlarını temizle\r\n        setError(null);\r\n        setSuccessMessage('');\r\n    };\r\n\r\n    // Yükleniyor durumu\r\n    if (loading) {\r\n        return (\r\n            <>\r\n                <Header />\r\n                <div className=\"container-fluid\">\r\n                    <div className=\"row\">\r\n                        <Sidebar />\r\n                        <main className=\"col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white\">\r\n                            <div className=\"d-flex justify-content-center align-items-center\" style={{ height: '80vh' }}>\r\n                                <div className=\"spinner-border text-primary\" role=\"status\">\r\n                                    <span className=\"visually-hidden\">Yükleniyor...</span>\r\n                                </div>\r\n                                <span className=\"ms-2\">Kullanıcı bilgileri yükleniyor...</span>\r\n                            </div>\r\n                        </main>\r\n                    </div>\r\n                </div>\r\n            </>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <>\r\n            <Header />\r\n            <div className=\"container-fluid\">\r\n                <div className=\"row\">\r\n                    <Sidebar />\r\n                    <main className=\"col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white\">\r\n                        <div className=\"pt-3 pb-1 mt-3 mb-3 border-bottom\">\r\n                            <h1 className=\"h4 text-dark mb-0\">\r\n                                <FontAwesomeIcon icon={faUser} className=\"me-2 text-primary\" />\r\n                                Kullanıcı Profilim\r\n                            </h1>\r\n                        </div>\r\n\r\n                        {/* Hata mesajı */}\r\n                        {error && (\r\n                            <div className=\"alert alert-danger\">\r\n                                <FontAwesomeIcon icon={faUser} className=\"me-2\" />\r\n                                {error}\r\n                            </div>\r\n                        )}\r\n\r\n                        {/* Başarı mesajı */}\r\n                        {successMessage && (\r\n                            <div className=\"alert alert-success\">\r\n                                <FontAwesomeIcon icon={faUser} className=\"me-2\" />\r\n                                {successMessage}\r\n                            </div>\r\n                        )}\r\n\r\n                        <div className=\"row mb-4\">\r\n                            <div className=\"col-md-12\">\r\n                                <div className=\"card\">\r\n                                    <div className=\"card-header bg-dark-subtle py-2\">\r\n                                        <h5 className=\"mb-0 fw-bold\">Profil Bilgilerim</h5>\r\n                                    </div>\r\n                                    <div className=\"card-body\">\r\n                                        <form onSubmit={handleSubmit}>\r\n                                            <div className=\"row mb-3\">\r\n                                                <div className=\"col-md-6\">\r\n                                                    <div className=\"form-group mb-3\">\r\n                                                        <label htmlFor=\"username\" className=\"form-label\">\r\n                                                            <FontAwesomeIcon icon={faUser} className=\"me-2\" />\r\n                                                            Kullanıcı Adı\r\n                                                        </label>\r\n                                                        <input\r\n                                                            type=\"text\"\r\n                                                            className=\"form-control\"\r\n                                                            id=\"username\"\r\n                                                            name=\"username\"\r\n                                                            value={formData.username}\r\n                                                            onChange={handleChange}\r\n                                                            required\r\n                                                        />\r\n                                                    </div>\r\n\r\n                                                    <div className=\"form-group mb-3\">\r\n                                                        <label htmlFor=\"email\" className=\"form-label\">\r\n                                                            <FontAwesomeIcon icon={faEnvelope} className=\"me-2\" />\r\n                                                            E-posta Adresi\r\n                                                        </label>\r\n                                                        <input\r\n                                                            type=\"email\"\r\n                                                            className=\"form-control\"\r\n                                                            id=\"email\"\r\n                                                            name=\"email\"\r\n                                                            value={formData.email}\r\n                                                            onChange={handleChange}\r\n                                                            required\r\n                                                        />\r\n                                                    </div>\r\n\r\n                                                    <div className=\"form-group mb-3\">\r\n                                                        <label htmlFor=\"full_name\" className=\"form-label\">\r\n                                                            <FontAwesomeIcon icon={faUser} className=\"me-2\" />\r\n                                                            Ad Soyad\r\n                                                        </label>\r\n                                                        <input\r\n                                                            type=\"text\"\r\n                                                            className=\"form-control\"\r\n                                                            id=\"full_name\"\r\n                                                            name=\"full_name\"\r\n                                                            value={formData.full_name}\r\n                                                            onChange={handleChange}\r\n                                                            required\r\n                                                        />\r\n                                                    </div>\r\n                                                </div>\r\n\r\n                                                <div className=\"col-md-6\">\r\n                                                    <div className=\"form-group mb-3\">\r\n                                                        <label htmlFor=\"phone\" className=\"form-label\">\r\n                                                            <FontAwesomeIcon icon={faPhone} className=\"me-2\" />\r\n                                                            Telefon\r\n                                                        </label>\r\n                                                        <input\r\n                                                            type=\"text\"\r\n                                                            className=\"form-control\"\r\n                                                            id=\"phone\"\r\n                                                            name=\"phone\"\r\n                                                            value={formData.phone}\r\n                                                            onChange={handleChange}\r\n                                                        />\r\n                                                    </div>\r\n\r\n                                                    <div className=\"form-group mb-3\">\r\n                                                        <label htmlFor=\"company\" className=\"form-label\">\r\n                                                            <FontAwesomeIcon icon={faBuilding} className=\"me-2\" />\r\n                                                            Firma Adı\r\n                                                        </label>\r\n                                                        <input\r\n                                                            type=\"text\"\r\n                                                            className=\"form-control\"\r\n                                                            id=\"company\"\r\n                                                            name=\"company\"\r\n                                                            value={formData.company}\r\n                                                            onChange={handleChange}\r\n                                                        />\r\n                                                    </div>\r\n\r\n                                                    <div className=\"form-group mb-3\">\r\n                                                        <label htmlFor=\"address\" className=\"form-label\">\r\n                                                            <FontAwesomeIcon icon={faBuilding} className=\"me-2\" />\r\n                                                            Adres\r\n                                                        </label>\r\n                                                        <input\r\n                                                            type=\"text\"\r\n                                                            className=\"form-control\"\r\n                                                            id=\"address\"\r\n                                                            name=\"address\"\r\n                                                            value={formData.address}\r\n                                                            onChange={handleChange}\r\n                                                        />\r\n                                                    </div>\r\n\r\n                                                    <div className=\"form-group mb-3\">\r\n                                                        <label htmlFor=\"role\" className=\"form-label\">\r\n                                                            <FontAwesomeIcon icon={faUserTag} className=\"me-2\" />\r\n                                                            Kullanıcı Rolü\r\n                                                        </label>\r\n                                                        <input\r\n                                                            type=\"text\"\r\n                                                            className=\"form-control\"\r\n                                                            id=\"role\"\r\n                                                            value={userProfile.gorev === 'admin' ? 'Yönetici' : userProfile.gorev === 'manager' ? 'Müdür' : 'Kullanıcı'}\r\n                                                            disabled\r\n                                                        />\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n\r\n                                            <hr className=\"my-4\" />\r\n\r\n                                            <div className=\"row mb-3\">\r\n                                                <div className=\"col-md-12\">\r\n                                                    <h5>Şifre Değiştirme</h5>\r\n                                                    <p className=\"text-muted small\">Şifrenizi değiştirmek istemiyorsanız bu alanları boş bırakabilirsiniz.</p>\r\n                                                </div>\r\n\r\n                                                <div className=\"col-md-4\">\r\n                                                    <div className=\"form-group mb-3\">\r\n                                                        <label htmlFor=\"current_password\" className=\"form-label\">Mevcut Şifre</label>\r\n                                                        <input\r\n                                                            type=\"password\"\r\n                                                            className=\"form-control\"\r\n                                                            id=\"current_password\"\r\n                                                            name=\"current_password\"\r\n                                                            value={formData.current_password}\r\n                                                            onChange={handleChange}\r\n                                                        />\r\n                                                    </div>\r\n                                                </div>\r\n\r\n                                                <div className=\"col-md-4\">\r\n                                                    <div className=\"form-group mb-3\">\r\n                                                        <label htmlFor=\"new_password\" className=\"form-label\">Yeni Şifre</label>\r\n                                                        <input\r\n                                                            type=\"password\"\r\n                                                            className=\"form-control\"\r\n                                                            id=\"new_password\"\r\n                                                            name=\"new_password\"\r\n                                                            value={formData.new_password}\r\n                                                            onChange={handleChange}\r\n                                                            minLength=\"6\"\r\n                                                        />\r\n                                                    </div>\r\n                                                </div>\r\n\r\n                                                <div className=\"col-md-4\">\r\n                                                    <div className=\"form-group mb-3\">\r\n                                                        <label htmlFor=\"confirm_password\" className=\"form-label\">Yeni Şifre (Tekrar)</label>\r\n                                                        <input\r\n                                                            type=\"password\"\r\n                                                            className=\"form-control\"\r\n                                                            id=\"confirm_password\"\r\n                                                            name=\"confirm_password\"\r\n                                                            value={formData.confirm_password}\r\n                                                            onChange={handleChange}\r\n                                                            minLength=\"6\"\r\n                                                        />\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n\r\n                                            <div className=\"row\">\r\n                                                <div className=\"col-md-12\">\r\n                                                    <div className=\"d-flex justify-content-end gap-2\">\r\n                                                        <button\r\n                                                            type=\"button\"\r\n                                                            className=\"btn btn-secondary\"\r\n                                                            onClick={handleReset}\r\n                                                            disabled={saving}\r\n                                                        >\r\n                                                            <FontAwesomeIcon icon={faUndo} className=\"me-2\" />\r\n                                                            Sıfırla\r\n                                                        </button>\r\n                                                        <button\r\n                                                            type=\"submit\"\r\n                                                            className=\"btn btn-primary\"\r\n                                                            disabled={saving}\r\n                                                        >\r\n                                                            {saving ? (\r\n                                                                <>\r\n                                                                    <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\r\n                                                                    Kaydediliyor...\r\n                                                                </>\r\n                                                            ) : (\r\n                                                                <>\r\n                                                                    <FontAwesomeIcon icon={faSave} className=\"me-2\" />\r\n                                                                    Kaydet\r\n                                                                </>\r\n                                                            )}\r\n                                                        </button>\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </form>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </main>\r\n                </div>\r\n            </div>\r\n            <Footer />\r\n        </>\r\n    );\r\n};\r\n\r\nexport default Profile; "], "names": ["Sidebar", "location", "useLocation", "isAdmin", "getUserRole", "_user$user", "_user$user2", "user", "JSON", "parse", "localStorage", "getItem", "role", "gorev", "_unused", "_jsx", "className", "children", "_jsxs", "tabIndex", "id", "type", "Link", "concat", "pathname", "to", "FontAwesomeIcon", "icon", "faDolly", "faFolderPlus", "faAnchor", "faFlag<PERSON><PERSON><PERSON><PERSON>", "faDesktop", "faBell", "faUser", "faStreetView", "faCreditCard", "faFileLines", "Footer", "Header", "navigate", "useNavigate", "userName", "setUserName", "useState", "loading", "setLoading", "useEffect", "async", "_storedUser$user", "_storedUser$user2", "_storedUser$user3", "_storedUser$user4", "storedUser", "userId", "musteri_ID", "name", "musteri_adi", "console", "warn", "userData", "kullaniciService", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setItem", "stringify", "apiError", "error", "fetchUserData", "src", "alt", "height", "onClick", "handleLogout", "removeItem", "faRightFromBracket", "faBars", "Profile", "saving", "setSaving", "setError", "successMessage", "setSuccessMessage", "userProfile", "setUserProfile", "<PERSON><PERSON><PERSON><PERSON>", "email", "tel", "firma", "adres", "formData", "setFormData", "username", "full_name", "phone", "company", "address", "current_password", "new_password", "confirm_password", "userObj", "_userObj$email", "_userObj$email2", "split", "message", "includes", "response", "status", "code", "err", "handleChange", "e", "value", "target", "prevState", "_objectSpread", "_Fragment", "style", "onSubmit", "preventDefault", "length", "updateData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setTimeout", "errorMessage", "data", "htmlFor", "onChange", "required", "faEnvelope", "faPhone", "faBuilding", "faUserTag", "disabled", "<PERSON><PERSON><PERSON><PERSON>", "handleReset", "faUndo", "faSave"], "sourceRoot": ""}