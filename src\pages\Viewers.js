import React, { useState, useEffect } from 'react';
import axios from 'axios';
import Header from '../components/Header';
import Sidebar from '../components/Sidebar';
import Footer from '../components/Footer';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';

const Viewers = () => {
    const [viewers, setViewers] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [showAddForm, setShowAddForm] = useState(false);
    const [newViewer, setNewViewer] = useState({
        name: '',
        email: '',
        phone: '',
        permissions: 'view'
    });

    // Sayfanın yüklenmesi
    useEffect(() => {
        const fetchViewers = async () => {
            try {
                setLoading(true);
                setError(null);
                
                // Gerçek API çağrısı - viewers endpoint'i çağır
                const response = await axios.get('/api/kullanicilar', {
                    params: { role: 'viewer' }
                });
                
                // API yanıtını viewers formatına çevir
                const viewersData = response.data.map(user => ({
                    id: user.musteri_ID,
                    name: user.musteri_adi,
                    email: user.email,
                    phone: user.tel,
                    permissions: user.gorev,
                    status: 'active', // Backend'den gelen duruma göre ayarlanacak
                    createdAt: new Date(user.olusturma_tarihi).toLocaleDateString('tr-TR'),
                    lastAccess: new Date(user.guncelleme_tarihi).toLocaleString('tr-TR')
                }));
                
                setViewers(viewersData);
                setLoading(false);
            } catch (error) {
                console.error('İzleyici verileri alınırken hata:', error);
                setError('İzleyici verileri yüklenirken hata oluştu: ' + error.message);
                setLoading(false);
            }
        };

        fetchViewers();
    }, []);

    // Yeni izleyici ekleme
    const handleAddViewer = (e) => {
        e.preventDefault();
        
        // Validasyon
        if (!newViewer.name || !newViewer.email) {
            setError('Ad ve email alanları zorunludur.');
            return;
        }

        // Yeni izleyici ekle
        const newViewerData = {
            id: viewers.length + 1,
            ...newViewer,
            status: 'active',
            createdAt: new Date().toISOString().split('T')[0],
            lastAccess: '-'
        };

        setViewers([...viewers, newViewerData]);
        setNewViewer({ name: '', email: '', phone: '', permissions: 'view' });
        setShowAddForm(false);
        setError(null);
    };

    // İzleyici silme
    const handleDeleteViewer = (id) => {
        if (window.confirm('Bu izleyiciyi silmek istediğinizden emin misiniz?')) {
            setViewers(viewers.filter(viewer => viewer.id !== id));
        }
    };

    // Durum değiştirme
    const handleToggleStatus = (id) => {
        setViewers(viewers.map(viewer => 
            viewer.id === id 
                ? { ...viewer, status: viewer.status === 'active' ? 'inactive' : 'active' }
                : viewer
        ));
    };

    return (
        <>
            <Header />
            <div className="container-fluid">
                <div className="row">
                    <Sidebar />

                    {/* main sütun */}
                    <main className="col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white">
                        <div className="pt-3 pb-1 mt-3 mb-3 border-bottom d-flex justify-content-between align-items-center">
                            <h1 className="h4 text-dark">İzleyici İşlemleri</h1>
                            <button 
                                className="btn btn-primary btn-sm"
                                onClick={() => setShowAddForm(!showAddForm)}
                            >
                                {showAddForm ? 'İptal' : 'Yeni İzleyici Ekle'}
                            </button>
                        </div>

                        {loading ? (
                            <LoadingSpinner 
                                size="lg" 
                                variant="primary" 
                                message="İzleyici verileri yükleniyor..." 
                                centered={true}
                            />
                        ) : error ? (
                            <ErrorMessage 
                                message={error} 
                                variant="danger"
                                title="Veri Yükleme Hatası"
                                dismissible={true}
                                onDismiss={() => setError('')}
                            >
                                <button className="btn btn-primary btn-sm mt-2" onClick={() => window.location.reload()}>
                                    Yeniden Dene
                                </button>
                            </ErrorMessage>
                        ) : (
                            <div className="row">
                                <div className="col-12">
                                    {/* Yeni İzleyici Ekleme Formu */}
                                    {showAddForm && (
                                        <div className="card border-primary mb-4">
                                            <div className="card-header bg-primary text-white">
                                                <h5 className="mb-0">Yeni İzleyici Ekle</h5>
                                            </div>
                                            <div className="card-body">
                                                <form onSubmit={handleAddViewer}>
                                                    <div className="row">
                                                        <div className="col-md-6 mb-3">
                                                            <label htmlFor="name" className="form-label">Ad Soyad *</label>
                                                            <input
                                                                type="text"
                                                                className="form-control"
                                                                id="name"
                                                                value={newViewer.name}
                                                                onChange={(e) => setNewViewer({...newViewer, name: e.target.value})}
                                                                required
                                                            />
                                                        </div>
                                                        <div className="col-md-6 mb-3">
                                                            <label htmlFor="email" className="form-label">Email *</label>
                                                            <input
                                                                type="email"
                                                                className="form-control"
                                                                id="email"
                                                                value={newViewer.email}
                                                                onChange={(e) => setNewViewer({...newViewer, email: e.target.value})}
                                                                required
                                                            />
                                                        </div>
                                                        <div className="col-md-6 mb-3">
                                                            <label htmlFor="phone" className="form-label">Telefon</label>
                                                            <input
                                                                type="tel"
                                                                className="form-control"
                                                                id="phone"
                                                                value={newViewer.phone}
                                                                onChange={(e) => setNewViewer({...newViewer, phone: e.target.value})}
                                                            />
                                                        </div>
                                                        <div className="col-md-6 mb-3">
                                                            <label htmlFor="permissions" className="form-label">Yetki Seviyesi</label>
                                                            <select
                                                                className="form-select"
                                                                id="permissions"
                                                                value={newViewer.permissions}
                                                                onChange={(e) => setNewViewer({...newViewer, permissions: e.target.value})}
                                                            >
                                                                <option value="view">Sadece Görüntüleme</option>
                                                                <option value="edit">Düzenleme</option>
                                                                <option value="admin">Yönetici</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div className="d-flex gap-2">
                                                        <button type="submit" className="btn btn-primary">
                                                            İzleyici Ekle
                                                        </button>
                                                        <button 
                                                            type="button" 
                                                            className="btn btn-secondary"
                                                            onClick={() => setShowAddForm(false)}
                                                        >
                                                            İptal
                                                        </button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    )}

                                    {/* İzleyici Listesi */}
                                    <div className="card border-tertiary-subtle">
                                        <div className="card-header bg-light">
                                            <h5 className="mb-0">
                                                <i className="fas fa-users me-2"></i>
                                                İzleyici Listesi
                                                <span className="badge bg-primary ms-2">{viewers.length}</span>
                                            </h5>
                                        </div>
                                        <div className="card-body p-0">
                                            {viewers.length === 0 ? (
                                                <div className="p-4 text-center">
                                                    <ErrorMessage 
                                                        message="Henüz izleyici bulunmuyor. Yeni izleyici eklemek için yukarıdaki butonu kullanabilirsiniz."
                                                        variant="info"
                                                        showIcon={true}
                                                    />
                                                </div>
                                            ) : (
                                                <div className="table-responsive">
                                                    <table className="table table-hover table-striped mb-0">
                                                        <thead className="table-dark">
                                                            <tr>
                                                                <th>Ad Soyad</th>
                                                                <th>Email</th>
                                                                <th>Telefon</th>
                                                                <th>Yetki</th>
                                                                <th>Durum</th>
                                                                <th>Eklenme Tarihi</th>
                                                                <th>Son Erişim</th>
                                                                <th>İşlemler</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            {viewers.map((viewer) => (
                                                                <tr key={viewer.id}>
                                                                    <td>{viewer.name}</td>
                                                                    <td>{viewer.email}</td>
                                                                    <td>{viewer.phone || '-'}</td>
                                                                    <td>
                                                                        <span className={`badge ${
                                                                            viewer.permissions === 'admin' ? 'bg-danger' :
                                                                            viewer.permissions === 'edit' ? 'bg-warning' :
                                                                            'bg-info'
                                                                        }`}>
                                                                            {viewer.permissions === 'admin' ? 'Yönetici' :
                                                                             viewer.permissions === 'edit' ? 'Düzenleme' :
                                                                             'Görüntüleme'}
                                                                        </span>
                                                                    </td>
                                                                    <td>
                                                                        <span className={`badge ${
                                                                            viewer.status === 'active' ? 'bg-success' : 'bg-secondary'
                                                                        }`}>
                                                                            {viewer.status === 'active' ? 'Aktif' : 'Pasif'}
                                                                        </span>
                                                                    </td>
                                                                    <td>{viewer.createdAt}</td>
                                                                    <td>{viewer.lastAccess}</td>
                                                                    <td>
                                                                        <div className="btn-group" role="group">
                                                                            <button
                                                                                className={`btn btn-sm ${
                                                                                    viewer.status === 'active' ? 'btn-warning' : 'btn-success'
                                                                                }`}
                                                                                onClick={() => handleToggleStatus(viewer.id)}
                                                                                title={viewer.status === 'active' ? 'Pasifleştir' : 'Aktifleştir'}
                                                                            >
                                                                                {viewer.status === 'active' ? 'Pasif' : 'Aktif'}
                                                                            </button>
                                                                            <button
                                                                                className="btn btn-sm btn-danger"
                                                                                onClick={() => handleDeleteViewer(viewer.id)}
                                                                                title="Sil"
                                                                            >
                                                                                Sil
                                                                            </button>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                            ))}
                                                        </tbody>
                                                    </table>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                    </main>
                    
                    <Footer />
                </div>
            </div>
        </>
    );
};

export default Viewers;