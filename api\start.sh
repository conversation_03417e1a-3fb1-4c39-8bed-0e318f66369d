#!/bin/bash

# Define the full paths to your Node.js scripts
SCRIPT_JS="/var/www/html/backend/server.js"

# Check if server.js is already running using its full path
if pgrep -f "node ${SCRIPT_JS}" > /dev/null; then
    echo "Tüm kritik Node.js servisleri kontrol ediliyor..."

    # Check each service and start if not running
    if ! pgrep -f "node ${SCRIPT_JS}" > /dev/null; then
        echo "server.js çalışmıyor. Başlatılıyor..."
        node "${SCRIPT_JS}" &
    fi

   

    echo "Servis kontrolü tamamlandı."

else
    echo "Hiçbir Node.js servisi çalışmıyor gibi görünüyor. Tümü başlatılıyor..."
    node "${SCRIPT_JS}" &

    echo "Servis başlatıldı."
fi