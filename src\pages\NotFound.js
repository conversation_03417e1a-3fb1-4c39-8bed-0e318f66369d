import React from 'react';
import { Link } from 'react-router-dom';
import Header from '../components/Header';
import Sidebar from '../components/Sidebar';
import Footer from '../components/Footer';

const NotFound = () => {
    return (
        <>
            <Header />
            <div className="container-fluid">
                <div className="row">
                    <Sidebar />

                    {/* main sütun */}
                    <main className="col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white">
                        <div className="pt-3 pb-1 mt-3 mb-3 border-bottom">
                            <h1 className="h4 text-dark">Sayfa Bulunamadı</h1>
                        </div>

                        <div className="row justify-content-center">
                            <div className="col-md-8 col-lg-6">
                                <div className="card border-warning">
                                    <div className="card-body text-center py-5">
                                        <div className="mb-4">
                                            <i className="fas fa-exclamation-triangle text-warning" style={{ fontSize: '4rem' }}></i>
                                        </div>
                                        
                                        <h2 className="h1 text-warning mb-3">404</h2>
                                        <h3 className="h4 text-dark mb-4">Sayfa Bulunamadı</h3>
                                        
                                        <p className="text-muted mb-4">
                                            Aradığınız sayfa bulunamadı. Sayfa silinmiş, taşınmış veya geçici olarak 
                                            kullanım dışı olabilir.
                                        </p>

                                        <div className="d-flex flex-column flex-sm-row justify-content-center gap-3">
                                            <Link to="/" className="btn btn-primary">
                                                <i className="fas fa-home me-2"></i>
                                                Ana Sayfaya Dön
                                            </Link>
                                            <button 
                                                className="btn btn-outline-secondary"
                                                onClick={() => window.history.back()}
                                            >
                                                <i className="fas fa-arrow-left me-2"></i>
                                                Geri Dön
                                            </button>
                                        </div>

                                        <hr className="my-4" />

                                        <div className="row text-start">
                                            <div className="col-md-6 mb-3">
                                                <h6 className="text-primary">
                                                    <i className="fas fa-shipping-fast me-2"></i>
                                                    Sevkiyat İşlemleri
                                                </h6>
                                                <ul className="list-unstyled small text-muted">
                                                    <li><Link to="/" className="text-decoration-none">Aktif Sevkiyatlar</Link></li>
                                                    <li><Link to="/add" className="text-decoration-none">Yeni Sevkiyat Oluştur</Link></li>
                                                    <li><Link to="/history" className="text-decoration-none">Geçmiş Sevkiyatlar</Link></li>
                                                </ul>
                                            </div>
                                            <div className="col-md-6 mb-3">
                                                <h6 className="text-primary">
                                                    <i className="fas fa-user-cog me-2"></i>
                                                    Kullanıcı İşlemleri
                                                </h6>
                                                <ul className="list-unstyled small text-muted">
                                                    <li><Link to="/profile" className="text-decoration-none">Kullanıcı Profilim</Link></li>
                                                    <li><Link to="/viewers" className="text-decoration-none">İzleyici İşlemleri</Link></li>
                                                    <li><Link to="/payment" className="text-decoration-none">Ödeme Yap</Link></li>
                                                </ul>
                                            </div>
                                        </div>

                                        <div className="mt-4 p-3 bg-light rounded">
                                            <small className="text-muted">
                                                <i className="fas fa-info-circle me-1"></i>
                                                Sorun devam ederse, lütfen sistem yöneticinize başvurun.
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </main>
                    
                    <Footer />
                </div>
            </div>
        </>
    );
};

export default NotFound;