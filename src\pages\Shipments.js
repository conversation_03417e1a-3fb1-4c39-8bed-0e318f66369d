import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faEye, faEdit, faTrash, faPlus, faSearch, faFilter, faDownload,
  faShippingFast, faMapMarkerAlt, faCalendarAlt, faUser, faBox
} from '@fortawesome/free-solid-svg-icons';
import Header from '../components/Header';
import Sidebar from '../components/Sidebar';
import Footer from '../components/Footer';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';
import { sevkiyatService } from '../api/dbService';

const Shipments = () => {
  const [shipments, setShipments] = useState([]);
  const [filteredShipments, setFilteredShipments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('all');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedShipment, setSelectedShipment] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    fetchShipments();
  }, []);

  useEffect(() => {
    filterShipments();
  }, [shipments, searchTerm, statusFilter, dateFilter]);

  const fetchShipments = async () => {
    try {
      setLoading(true);
      setError(null);

      const storedUser = JSON.parse(localStorage.getItem('user'));
      const userId = storedUser?.user?.musteri_ID || storedUser?.user?.id || 1;

      const data = await sevkiyatService.getSevkiyatlarByMusteriId(userId);
      
      const processedData = data.map(item => ({
        id: item.id,
        sevkiyatID: item.sevkiyat_ID || '-',
        mgzKodu: item.mgz24_kodu || '-',
        name: item.sevkiyat_adi || 'İsimsiz Sevkiyat',
        plate: item.plaka_no || '-',
        from: item.cikis_lokasyon || `Lokasyon ${item.cikis_lokasyon_id || ''}`,
        to: item.varis_lokasyon || `Lokasyon ${item.varis_lokasyon_id || ''}`,
        carrier: item.nakliyeci || `Nakliyeci ${item.nakliyeci_id || ''}`,
        product: item.urun || `Ürün ${item.urun_id || ''}`,
        status: item.durum || 'Aktif',
        created: formatDate(item.olusturma_zamani),
        completed: item.tamamlanma_zamani ? formatDate(item.tamamlanma_zamani) : null,
        pallet: item.palet_sayisi?.toString() || '-',
        netWeight: item.net_agirlik?.toString() || '-',
        grossWeight: item.brut_agirlik?.toString() || '-',
        tempRange: item.sicaklik_araligi || '-'
      }));

      setShipments(processedData);
      setLoading(false);
    } catch (error) {
      console.error('Sevkiyat verileri alınırken hata:', error);
      setError('Sevkiyat verileri yüklenirken hata oluştu: ' + error.message);
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR') + ' ' + date.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' });
  };

  const filterShipments = () => {
    let filtered = [...shipments];

    // Arama filtresi
    if (searchTerm) {
      filtered = filtered.filter(shipment =>
        shipment.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        shipment.sevkiyatID.toLowerCase().includes(searchTerm.toLowerCase()) ||
        shipment.mgzKodu.toLowerCase().includes(searchTerm.toLowerCase()) ||
        shipment.plate.toLowerCase().includes(searchTerm.toLowerCase()) ||
        shipment.from.toLowerCase().includes(searchTerm.toLowerCase()) ||
        shipment.to.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Durum filtresi
    if (statusFilter !== 'all') {
      filtered = filtered.filter(shipment => shipment.status === statusFilter);
    }

    // Tarih filtresi
    if (dateFilter !== 'all') {
      const now = new Date();
      const filterDate = new Date();
      
      switch (dateFilter) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0);
          filtered = filtered.filter(shipment => new Date(shipment.created) >= filterDate);
          break;
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          filtered = filtered.filter(shipment => new Date(shipment.created) >= filterDate);
          break;
        case 'month':
          filterDate.setMonth(now.getMonth() - 1);
          filtered = filtered.filter(shipment => new Date(shipment.created) >= filterDate);
          break;
        default:
          break;
      }
    }

    setFilteredShipments(filtered);
  };

  const handleDelete = (shipment) => {
    setSelectedShipment(shipment);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (!selectedShipment) return;

    try {
      setDeleteLoading(true);
      await sevkiyatService.deleteSevkiyat(selectedShipment.id);
      setShowDeleteModal(false);
      setSelectedShipment(null);
      fetchShipments(); // Listeyi yenile
    } catch (error) {
      console.error('Sevkiyat silinirken hata:', error);
      setError('Sevkiyat silinirken hata oluştu: ' + error.message);
    } finally {
      setDeleteLoading(false);
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'Aktif':
        return <span className="badge bg-success">{status}</span>;
      case 'Tamamlandı':
        return <span className="badge bg-primary">{status}</span>;
      case 'İptal':
        return <span className="badge bg-danger">{status}</span>;
      default:
        return <span className="badge bg-secondary">{status}</span>;
    }
  };

  return (
    <>
      <Header />
      <div className="container-fluid">
        <div className="row">
          <Sidebar />
          
          <main className="col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white">
            <div className="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
              <h1 className="h2">
                <FontAwesomeIcon icon={faShippingFast} className="me-2" />
                Sevkiyat Yönetimi
              </h1>
              <Link to="/add" className="btn btn-primary">
                <FontAwesomeIcon icon={faPlus} className="me-2" />
                Yeni Sevkiyat
              </Link>
            </div>

            {loading ? (
              <LoadingSpinner size="lg" variant="primary" message="Sevkiyatlar yükleniyor..." centered={true} />
            ) : error ? (
              <ErrorMessage 
                message={error} 
                variant="danger"
                title="Veri Yükleme Hatası"
                dismissible={true}
                onDismiss={() => setError('')}
              >
                <button className="btn btn-primary btn-sm mt-2" onClick={fetchShipments}>
                  Yeniden Dene
                </button>
              </ErrorMessage>
            ) : (
              <>
                {/* Filtreler */}
                <div className="card mb-4">
                  <div className="card-body">
                    <div className="row g-3">
                      <div className="col-md-4">
                        <div className="input-group">
                          <span className="input-group-text">
                            <FontAwesomeIcon icon={faSearch} />
                          </span>
                          <input
                            type="text"
                            className="form-control"
                            placeholder="Sevkiyat ara..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                          />
                        </div>
                      </div>
                      <div className="col-md-3">
                        <select
                          className="form-select"
                          value={statusFilter}
                          onChange={(e) => setStatusFilter(e.target.value)}
                        >
                          <option value="all">Tüm Durumlar</option>
                          <option value="Aktif">Aktif</option>
                          <option value="Tamamlandı">Tamamlandı</option>
                          <option value="İptal">İptal</option>
                        </select>
                      </div>
                      <div className="col-md-3">
                        <select
                          className="form-select"
                          value={dateFilter}
                          onChange={(e) => setDateFilter(e.target.value)}
                        >
                          <option value="all">Tüm Tarihler</option>
                          <option value="today">Bugün</option>
                          <option value="week">Son 7 Gün</option>
                          <option value="month">Son 30 Gün</option>
                        </select>
                      </div>
                      <div className="col-md-2">
                        <button className="btn btn-outline-secondary w-100">
                          <FontAwesomeIcon icon={faDownload} className="me-2" />
                          Dışa Aktar
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Sevkiyat Listesi */}
                <div className="card">
                  <div className="card-header">
                    <h5 className="mb-0">
                      Sevkiyat Listesi 
                      <span className="badge bg-primary ms-2">{filteredShipments.length}</span>
                    </h5>
                  </div>
                  <div className="card-body p-0">
                    {filteredShipments.length === 0 ? (
                      <div className="text-center py-5">
                        <FontAwesomeIcon icon={faShippingFast} className="fa-3x text-muted mb-3" />
                        <h5 className="text-muted">Sevkiyat Bulunamadı</h5>
                        <p className="text-muted">Arama kriterlerinize uygun sevkiyat bulunamadı.</p>
                      </div>
                    ) : (
                      <div className="table-responsive">
                        <table className="table table-hover mb-0">
                          <thead className="table-light">
                            <tr>
                              <th>Sevkiyat ID</th>
                              <th>Sevkiyat Adı</th>
                              <th>Plaka</th>
                              <th>Güzergah</th>
                              <th>Nakliyeci</th>
                              <th>Durum</th>
                              <th>Oluşturma</th>
                              <th>İşlemler</th>
                            </tr>
                          </thead>
                          <tbody>
                            {filteredShipments.map((shipment) => (
                              <tr key={shipment.id}>
                                <td>
                                  <strong>{shipment.sevkiyatID}</strong>
                                  {shipment.mgzKodu && (
                                    <div className="small text-muted">{shipment.mgzKodu}</div>
                                  )}
                                </td>
                                <td>
                                  <div className="fw-bold">{shipment.name}</div>
                                  <div className="small text-muted">{shipment.product}</div>
                                </td>
                                <td>
                                  <span className="badge bg-dark">{shipment.plate}</span>
                                </td>
                                <td>
                                  <div className="small">
                                    <FontAwesomeIcon icon={faMapMarkerAlt} className="text-success me-1" />
                                    {shipment.from}
                                  </div>
                                  <div className="small">
                                    <FontAwesomeIcon icon={faMapMarkerAlt} className="text-danger me-1" />
                                    {shipment.to}
                                  </div>
                                </td>
                                <td>
                                  <FontAwesomeIcon icon={faUser} className="me-1" />
                                  {shipment.carrier}
                                </td>
                                <td>{getStatusBadge(shipment.status)}</td>
                                <td>
                                  <FontAwesomeIcon icon={faCalendarAlt} className="me-1" />
                                  <div className="small">{shipment.created}</div>
                                </td>
                                <td>
                                  <div className="btn-group" role="group">
                                    <Link
                                      to={`/view/${shipment.id}`}
                                      className="btn btn-sm btn-outline-primary"
                                      title="Detay"
                                    >
                                      <FontAwesomeIcon icon={faEye} />
                                    </Link>
                                    <Link
                                      to={`/edit/${shipment.id}`}
                                      className="btn btn-sm btn-outline-warning"
                                      title="Düzenle"
                                    >
                                      <FontAwesomeIcon icon={faEdit} />
                                    </Link>
                                    <button
                                      className="btn btn-sm btn-outline-danger"
                                      onClick={() => handleDelete(shipment)}
                                      title="Sil"
                                    >
                                      <FontAwesomeIcon icon={faTrash} />
                                    </button>
                                  </div>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}
          </main>
          
          <Footer />
        </div>
      </div>

      {/* Silme Onay Modalı */}
      {showDeleteModal && (
        <div className="modal show d-block" tabIndex="-1" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Sevkiyat Sil</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => setShowDeleteModal(false)}
                ></button>
              </div>
              <div className="modal-body">
                <p>
                  <strong>{selectedShipment?.name}</strong> sevkiyatını silmek istediğinizden emin misiniz?
                </p>
                <div className="alert alert-warning">
                  <strong>Uyarı:</strong> Bu işlem geri alınamaz. Sevkiyat ve bağlı tüm veriler silinecektir.
                </div>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => setShowDeleteModal(false)}
                  disabled={deleteLoading}
                >
                  İptal
                </button>
                <button
                  type="button"
                  className="btn btn-danger"
                  onClick={confirmDelete}
                  disabled={deleteLoading}
                >
                  {deleteLoading ? (
                    <>
                      <span className="spinner-border spinner-border-sm me-2"></span>
                      Siliniyor...
                    </>
                  ) : (
                    <>
                      <FontAwesomeIcon icon={faTrash} className="me-2" />
                      Sil
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Shipments;