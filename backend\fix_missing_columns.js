import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

const dbConfig = {
  host: process.env.DB_HOST || '************',
  user: process.env.DB_USER || 'mehmet',
  password: process.env.DB_PASSWORD || 'Inka.mS_335265933s.tech',
  database: process.env.DB_NAME || 'mgz24db',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  connectTimeout: 10000
};

async function fixMissingColumns() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('Veritabanına bağlandı');

    // cihazBilgi tablosundaki eksik kolonları ekle
    console.log('cihazBilgi tablosu kontrolü...');
    
    try {
      await connection.execute(`
        ALTER TABLE cihazBilgi 
        ADD COLUMN IF NOT EXISTS notlar TEXT NULL COMMENT 'Cihaz notları'
      `);
      console.log('✅ cihazBilgi.notlar kolonu eklendi');
    } catch (error) {
      console.log('ℹ️ cihazBilgi.notlar kolonu zaten mevcut');
    }

    try {
      await connection.execute(`
        ALTER TABLE cihazBilgi 
        ADD COLUMN IF NOT EXISTS aktif BOOLEAN DEFAULT TRUE COMMENT 'Cihaz aktiflik durumu'
      `);
      console.log('✅ cihazBilgi.aktif kolonu eklendi');
    } catch (error) {
      console.log('ℹ️ cihazBilgi.aktif kolonu zaten mevcut');
    }

    try {
      await connection.execute(`
        ALTER TABLE cihazBilgi 
        ADD COLUMN IF NOT EXISTS son_kullanim_tarihi DATETIME NULL COMMENT 'Son kullanım tarihi'
      `);
      console.log('✅ cihazBilgi.son_kullanim_tarihi kolonu eklendi');
    } catch (error) {
      console.log('ℹ️ cihazBilgi.son_kullanim_tarihi kolonu zaten mevcut');
    }

    // sevkiyatlar tablosundaki eksik kolonları ekle
    console.log('\nsevkiyatlar tablosu kontrolü...');
    
    try {
      await connection.execute(`
        ALTER TABLE sevkiyatlar 
        ADD COLUMN IF NOT EXISTS notlar TEXT NULL COMMENT 'Sevkiyat notları'
      `);
      console.log('✅ sevkiyatlar.notlar kolonu eklendi');
    } catch (error) {
      console.log('ℹ️ sevkiyatlar.notlar kolonu zaten mevcut');
    }

    try {
      await connection.execute(`
        ALTER TABLE sevkiyatlar 
        ADD COLUMN IF NOT EXISTS tamamlandi_mi TINYINT DEFAULT 0 COMMENT 'Sevkiyat tamamlandı mı?'
      `);
      console.log('✅ sevkiyatlar.tamamlandi_mi kolonu eklendi');
    } catch (error) {
      console.log('ℹ️ sevkiyatlar.tamamlandi_mi kolonu zaten mevcut');
    }

    try {
      await connection.execute(`
        ALTER TABLE sevkiyatlar 
        ADD COLUMN IF NOT EXISTS tamamlanma_zamani TIMESTAMP NULL COMMENT 'Sevkiyat tamamlanma zamanı'
      `);
      console.log('✅ sevkiyatlar.tamamlanma_zamani kolonu eklendi');
    } catch (error) {
      console.log('ℹ️ sevkiyatlar.tamamlanma_zamani kolonu zaten mevcut');
    }

    try {
      await connection.execute(`
        ALTER TABLE sevkiyatlar 
        ADD COLUMN IF NOT EXISTS tamamlayan_kullanici_id INT NULL COMMENT 'Sevkiyatı tamamlayan kullanıcı'
      `);
      console.log('✅ sevkiyatlar.tamamlayan_kullanici_id kolonu eklendi');
    } catch (error) {
      console.log('ℹ️ sevkiyatlar.tamamlayan_kullanici_id kolonu zaten mevcut');
    }

    // kullanicilar tablosundaki eksik kolonu kontrol et
    console.log('\nkullanicilar tablosu kontrolü...');
    
    try {
      await connection.execute(`
        ALTER TABLE kullanicilar 
        MODIFY COLUMN gorev ENUM('admin','manager','user','viewer') DEFAULT 'user'
      `);
      console.log('✅ kullanicilar.gorev enum güncellendi');
    } catch (error) {
      console.log('ℹ️ kullanicilar.gorev enum zaten güncel');
    }

    // Mevcut tablo yapılarını kontrol et
    console.log('\n=== Mevcut Tablo Yapıları ===');
    
    const [cihazColumns] = await connection.execute('DESCRIBE cihazBilgi');
    console.log('\ncihazBilgi tablosu kolonları:');
    cihazColumns.forEach(col => {
      console.log(`- ${col.Field} (${col.Type})`);
    });

    const [sevkiyatColumns] = await connection.execute('DESCRIBE sevkiyatlar');
    console.log('\nsevkiyatlar tablosu kolonları:');
    sevkiyatColumns.forEach(col => {
      console.log(`- ${col.Field} (${col.Type})`);
    });

    console.log('\n🎉 Eksik kolonlar eklendi!');

  } catch (error) {
    console.error('Hata:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('Veritabanı bağlantısı kapatıldı');
    }
  }
}

fixMissingColumns();