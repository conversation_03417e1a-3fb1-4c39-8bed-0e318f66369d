-- ===========================================
-- MGZ24 External API Entegrasyonu için Database Schema Güncellemeleri
-- ===========================================

-- Mevcut cihazBilgi tablosuna yeni kolonlar ekle
ALTER TABLE cihazBilgi 
ADD COLUMN IF NOT EXISTS ip_adres VARCHAR(15) NULL COMMENT 'Cihazın IP adresi',
ADD COLUMN IF NOT EXISTS operator VARCHAR(50) NULL COMMENT 'GSM operatörü',
ADD COLUMN IF NOT EXISTS ulke VARCHAR(50) NULL COMMENT 'Cihazın bulunduğu ülke',
ADD COLUMN IF NOT EXISTS basinc DECIMAL(8,2) NULL COMMENT 'Barometrik basınç',
ADD COLUMN IF NOT EXISTS error_count INT DEFAULT 0 COMMENT 'Hata sayacı',
ADD COLUMN IF NOT EXISTS last_error_time TIMESTAMP NULL COMMENT 'Son hata zamanı',
ADD COLUMN IF NOT EXISTS data_source ENUM('internal', 'external', 'sync') DEFAULT 'internal' COMMENT 'Veri kaynağı',
ADD COLUMN IF NOT EXISTS sync_status ENUM('pending', 'synced', 'failed') DEFAULT 'pending' COMMENT 'Senkronizasyon durumu',
ADD COLUMN IF NOT EXISTS last_sync_time TIMESTAMP NULL COMMENT 'Son senkronizasyon zamanı';

-- Indexes ekle
CREATE INDEX IF NOT EXISTS idx_cihazBilgi_ip_adres ON cihazBilgi(ip_adres);
CREATE INDEX IF NOT EXISTS idx_cihazBilgi_operator ON cihazBilgi(operator);
CREATE INDEX IF NOT EXISTS idx_cihazBilgi_sync_status ON cihazBilgi(sync_status);
CREATE INDEX IF NOT EXISTS idx_cihazBilgi_last_sync_time ON cihazBilgi(last_sync_time);

-- Cihaz atama geçmişi tablosu
CREATE TABLE IF NOT EXISTS device_assignments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    cihaz_id VARCHAR(50) NOT NULL,
    sevkiyat_id INT NOT NULL,
    assigned_by INT NOT NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    unassigned_by INT NULL,
    unassigned_at TIMESTAMP NULL,
    status ENUM('assigned', 'unassigned', 'transferred') DEFAULT 'assigned',
    reason VARCHAR(255) NULL COMMENT 'Atama/kaldırma sebebi',
    notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_device_assignments_cihaz_id (cihaz_id),
    INDEX idx_device_assignments_sevkiyat_id (sevkiyat_id),
    INDEX idx_device_assignments_status (status),
    INDEX idx_device_assignments_assigned_at (assigned_at),
    
    FOREIGN KEY (sevkiyat_id) REFERENCES sevkiyatlar(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES kullanicilar(id) ON DELETE SET NULL,
    FOREIGN KEY (unassigned_by) REFERENCES kullanicilar(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Sensör verisi geçmişi tablosu
CREATE TABLE IF NOT EXISTS sensor_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    cihaz_kodu VARCHAR(50) NOT NULL,
    sicaklik DECIMAL(5,2) NULL,
    nem DECIMAL(5,2) NULL,
    basinc DECIMAL(8,2) NULL,
    isik DECIMAL(8,2) NULL,
    pil_seviyesi INT NULL,
    enlem DECIMAL(10,8) NULL,
    boylam DECIMAL(11,8) NULL,
    okuma_zamani TIMESTAMP NOT NULL,
    durum ENUM('online', 'warning', 'offline') DEFAULT 'online',
    data_source ENUM('internal', 'external', 'sync') DEFAULT 'external',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_sensor_history_cihaz_kodu (cihaz_kodu),
    INDEX idx_sensor_history_okuma_zamani (okuma_zamani),
    INDEX idx_sensor_history_durum (durum),
    INDEX idx_sensor_history_data_source (data_source),
    
    -- Composite indexes for better performance
    INDEX idx_sensor_history_cihaz_okuma (cihaz_kodu, okuma_zamani),
    INDEX idx_sensor_history_cihaz_durum (cihaz_kodu, durum)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- External API log tablosu
CREATE TABLE IF NOT EXISTS external_api_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    cihaz_id VARCHAR(50) NULL,
    endpoint VARCHAR(255) NOT NULL,
    method ENUM('GET', 'POST', 'PUT', 'DELETE') DEFAULT 'GET',
    request_data JSON NULL,
    response_data JSON NULL,
    response_status INT NULL,
    execution_time_ms INT NULL,
    success BOOLEAN DEFAULT FALSE,
    error_message TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_external_api_logs_cihaz_id (cihaz_id),
    INDEX idx_external_api_logs_endpoint (endpoint),
    INDEX idx_external_api_logs_success (success),
    INDEX idx_external_api_logs_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Sistem bildirimleri tablosu
CREATE TABLE IF NOT EXISTS system_notifications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    notification_type ENUM('alert', 'warning', 'info', 'error') NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    cihaz_id VARCHAR(50) NULL,
    sevkiyat_id INT NULL,
    musteri_id INT NULL,
    severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    is_read BOOLEAN DEFAULT FALSE,
    is_resolved BOOLEAN DEFAULT FALSE,
    resolved_by INT NULL,
    resolved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    
    INDEX idx_system_notifications_type (notification_type),
    INDEX idx_system_notifications_cihaz_id (cihaz_id),
    INDEX idx_system_notifications_sevkiyat_id (sevkiyat_id),
    INDEX idx_system_notifications_musteri_id (musteri_id),
    INDEX idx_system_notifications_severity (severity),
    INDEX idx_system_notifications_is_read (is_read),
    INDEX idx_system_notifications_created_at (created_at),
    
    FOREIGN KEY (sevkiyat_id) REFERENCES sevkiyatlar(id) ON DELETE CASCADE,
    FOREIGN KEY (musteri_id) REFERENCES kullanicilar(id) ON DELETE CASCADE,
    FOREIGN KEY (resolved_by) REFERENCES kullanicilar(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- External API ayarları tablosu
CREATE TABLE IF NOT EXISTS external_api_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_external_api_settings_key (setting_key),
    INDEX idx_external_api_settings_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Varsayılan external API ayarları
INSERT INTO external_api_settings (setting_key, setting_value, setting_type, description) VALUES
('external_api_url', 'http://localhost:3001', 'string', 'External API base URL'),
('external_api_timeout', '10000', 'number', 'API timeout in milliseconds'),
('polling_interval_default', '30000', 'number', 'Default polling interval in milliseconds'),
('polling_interval_fast', '10000', 'number', 'Fast polling interval for critical devices'),
('polling_interval_slow', '60000', 'number', 'Slow polling interval for inactive devices'),
('max_concurrent_requests', '10', 'number', 'Maximum concurrent API requests'),
('retry_attempts', '3', 'number', 'Number of retry attempts for failed requests'),
('batch_sync_size', '100', 'number', 'Batch size for synchronization operations')
ON DUPLICATE KEY UPDATE 
    setting_value = VALUES(setting_value),
    updated_at = CURRENT_TIMESTAMP;

-- Sistem performans metrikleri tablosu
CREATE TABLE IF NOT EXISTS system_performance_metrics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,4) NOT NULL,
    metric_unit VARCHAR(20) NULL,
    cihaz_id VARCHAR(50) NULL,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_system_performance_metrics_name (metric_name),
    INDEX idx_system_performance_metrics_cihaz_id (cihaz_id),
    INDEX idx_system_performance_metrics_recorded_at (recorded_at),
    INDEX idx_system_performance_metrics_name_recorded (metric_name, recorded_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Temizlik görevi için stored procedure
DELIMITER $$
CREATE PROCEDURE IF NOT EXISTS CleanupOldData()
BEGIN
    -- 30 günden eski sensor_history kayıtlarını sil
    DELETE FROM sensor_history WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- 7 günden eski external_api_logs kayıtlarını sil
    DELETE FROM external_api_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 7 DAY);
    
    -- 30 günden eski ve çözülmüş bildirimleri sil
    DELETE FROM system_notifications 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY) 
    AND is_resolved = TRUE;
    
    -- 90 günden eski performans metriklerini sil
    DELETE FROM system_performance_metrics 
    WHERE recorded_at < DATE_SUB(NOW(), INTERVAL 90 DAY);
    
    -- İstatistik bilgilerini güncelle
    SELECT 
        'Cleanup completed' as status,
        NOW() as completed_at,
        ROW_COUNT() as rows_affected;
END$$
DELIMITER ;

-- Mevcut tablolara unique constraints ekle
ALTER TABLE cihazBilgi ADD UNIQUE INDEX IF NOT EXISTS idx_cihazBilgi_cihaz_kodu (cihaz_kodu);

-- Sevkiyatlar tablosuna external API alanları ekle
ALTER TABLE sevkiyatlar 
ADD COLUMN IF NOT EXISTS external_tracking_id VARCHAR(100) NULL COMMENT 'External API tracking ID',
ADD COLUMN IF NOT EXISTS last_external_sync TIMESTAMP NULL COMMENT 'Son external API senkronizasyon zamanı',
ADD COLUMN IF NOT EXISTS external_sync_status ENUM('pending', 'synced', 'failed') DEFAULT 'pending';

-- Kullanıcılar tablosuna API yetkileri ekle
ALTER TABLE kullanicilar 
ADD COLUMN IF NOT EXISTS api_permissions JSON NULL COMMENT 'API izinleri JSON formatında',
ADD COLUMN IF NOT EXISTS last_api_access TIMESTAMP NULL COMMENT 'Son API erişim zamanı';

-- Trigger: Cihaz durumu değiştiğinde bildirim oluştur
DELIMITER $$
CREATE TRIGGER IF NOT EXISTS tr_cihazBilgi_status_change
AFTER UPDATE ON cihazBilgi
FOR EACH ROW
BEGIN
    -- Batarya seviyesi kritik seviyeye düştüğünde bildirim oluştur
    IF NEW.pil_seviyesi < 20 AND OLD.pil_seviyesi >= 20 THEN
        INSERT INTO system_notifications (
            notification_type, title, message, cihaz_id, severity, created_at
        ) VALUES (
            'alert', 
            'Düşük Batarya Uyarısı', 
            CONCAT('Cihaz ', NEW.cihaz_kodu, ' batarya seviyesi %', NEW.pil_seviyesi, ' seviyesine düştü.'),
            NEW.cihaz_kodu,
            CASE 
                WHEN NEW.pil_seviyesi < 10 THEN 'critical'
                WHEN NEW.pil_seviyesi < 15 THEN 'high'
                ELSE 'medium'
            END,
            NOW()
        );
    END IF;
    
    -- Cihaz çevrimdışı olduğunda bildirim oluştur
    IF NEW.durum = 'pasif' AND OLD.durum = 'aktif' THEN
        INSERT INTO system_notifications (
            notification_type, title, message, cihaz_id, severity, created_at
        ) VALUES (
            'warning', 
            'Cihaz Bağlantısı Kesildi', 
            CONCAT('Cihaz ', NEW.cihaz_kodu, ' ile bağlantı kesildi.'),
            NEW.cihaz_kodu,
            'high',
            NOW()
        );
    END IF;
END$$
DELIMITER ;

-- View: Cihaz durumu özeti
CREATE OR REPLACE VIEW view_device_status_summary AS
SELECT 
    c.cihaz_kodu,
    c.sevkiyat_id,
    c.sicaklik,
    c.nem,
    c.pil_seviyesi,
    c.durum,
    c.son_kontrol,
    c.operator,
    c.ulke,
    s.sevkiyat_adi,
    s.plaka_no,
    s.durum as sevkiyat_durum,
    ci.MusteriID,
    ci.GoldCihaz,
    ci.KalanSure,
    CASE 
        WHEN TIMESTAMPDIFF(MINUTE, c.son_kontrol, NOW()) <= 10 THEN 'online'
        WHEN TIMESTAMPDIFF(MINUTE, c.son_kontrol, NOW()) <= 60 THEN 'warning'
        ELSE 'offline'
    END as connection_status,
    CASE 
        WHEN c.pil_seviyesi > 60 THEN 'good'
        WHEN c.pil_seviyesi > 30 THEN 'medium'
        ELSE 'low'
    END as battery_status,
    TIMESTAMPDIFF(MINUTE, c.son_kontrol, NOW()) as minutes_since_update
FROM cihazBilgi c
LEFT JOIN sevkiyatlar s ON c.sevkiyat_id = s.id
LEFT JOIN cihazID ci ON c.cihaz_kodu = ci.CihazID
WHERE c.aktif = 1;

-- Function: Cihaz sağlık skoru hesaplama
DELIMITER $$
CREATE FUNCTION IF NOT EXISTS CalculateDeviceHealthScore(cihaz_kodu VARCHAR(50)) 
RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE health_score INT DEFAULT 100;
    DECLARE battery_level INT DEFAULT 100;
    DECLARE minutes_offline INT DEFAULT 0;
    DECLARE error_count INT DEFAULT 0;
    
    -- Cihaz bilgilerini al
    SELECT 
        COALESCE(pil_seviyesi, 100),
        TIMESTAMPDIFF(MINUTE, son_kontrol, NOW()),
        COALESCE(error_count, 0)
    INTO battery_level, minutes_offline, error_count
    FROM cihazBilgi 
    WHERE cihaz_kodu = cihaz_kodu
    LIMIT 1;
    
    -- Batarya seviyesine göre skor düşür
    IF battery_level < 20 THEN
        SET health_score = health_score - 30;
    ELSEIF battery_level < 50 THEN
        SET health_score = health_score - 15;
    END IF;
    
    -- Çevrimdışı süresine göre skor düşür
    IF minutes_offline > 60 THEN
        SET health_score = health_score - 40;
    ELSEIF minutes_offline > 30 THEN
        SET health_score = health_score - 20;
    ELSEIF minutes_offline > 10 THEN
        SET health_score = health_score - 10;
    END IF;
    
    -- Hata sayısına göre skor düşür
    IF error_count > 10 THEN
        SET health_score = health_score - 20;
    ELSEIF error_count > 5 THEN
        SET health_score = health_score - 10;
    END IF;
    
    -- Minimum skor 0 olmalı
    IF health_score < 0 THEN
        SET health_score = 0;
    END IF;
    
    RETURN health_score;
END$$
DELIMITER ;

-- Indices'i optimize et
OPTIMIZE TABLE cihazBilgi;
OPTIMIZE TABLE sevkiyatlar;
OPTIMIZE TABLE device_assignments;
OPTIMIZE TABLE sensor_history;

-- Tamamlanma mesajı
SELECT 
    'MGZ24 External API Database Schema Update Completed' as message,
    NOW() as completed_at,
    VERSION() as mysql_version;