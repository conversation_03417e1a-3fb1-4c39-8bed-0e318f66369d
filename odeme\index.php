<?php
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
if (!isset($_SESSION['user_level']) || ($_SESSION['user_level'] != 1)) {
    header("Location: /index.php");
    exit();
}
global $mysqli;
$title = "Sim Status";
include $_SERVER['DOCUMENT_ROOT'] . '/templates/template.php';
include $_SERVER['DOCUMENT_ROOT'] . '/inc/db.php';
?>


<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ödeme Sistemi</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <link rel="stylesheet" href="/css/common.css">
    <link rel="stylesheet" href="odemecss.css">
</head>
<body>

<div class="container">
    <h2 class="text-center">Ödeme Bilgileri</h2>
    <form action="odeme_islem.php" method="POST">
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="kk_sahibi">Kart Sahibi Adı Soyadı:</label>
                    <input type="text" class="form-control" id="kk_sahibi" name="kk_sahibi" value="Mehmet Aşık" required>
                </div>
                <div class="form-group">
                    <label for="kk_no">Kart Numarası:</label>
                    <input type="text" class="form-control" id="kk_no" name="kk_no" value="5313894015054635 " required>
                </div>
                <div class="form-group">
                    <label for="kk_sk_ay">Son Kullanma Ayı:</label>
                    <input type="text" class="form-control" id="kk_sk_ay" name="kk_sk_ay" value="02" required>
                </div>
                <div class="form-group">
                    <label for="kk_sk_yil">Son Kullanma Yılı(4 hane):</label>
                    <input type="text" class="form-control" id="kk_sk_yil" name="kk_sk_yil" value="2030" required>
                </div>
                <div class="form-group">
                    <label for="kk_cvc">CVC:</label>
                    <input type="text" class="form-control" id="kk_cvc" name="kk_cvc" value="195" required>
                </div>
            </div>
            <div class="col-md-6">
<!--                <div class="form-group">-->
<!--                    <label for="kk_sahibi_gsm">Kart Sahibi GSM:</label>-->
<!--                    <input type="text" class="form-control" id="kk_sahibi_gsm" name="kk_sahibi_gsm" required>-->
<!--                </div>-->
                <div class="form-group">
                    <label for="siparis_id">Sipariş ID:</label>
                    <input type="text" class="form-control" id="siparis_id" name="siparis_id" value="TestsiparisId200" required>
                </div>
<!--                <div class="form-group">-->
<!--                    <label for="siparis_aciklama">Sipariş Açıklaması:</label>-->
<!--                    <input type="text" class="form-control" id="siparis_aciklama" name="siparis_aciklama" value="a" required>-->
<!--                </div>-->
                <div class="form-group">
                    <label for="taksit">Taksit Sayısı:</label>
                    <input type="text" class="form-control" id="taksit" name="taksit" value="1" required>
                </div>
                <div class="form-group">
                    <label for="islem_tutar">İşlem Tutarı:</label>
                    <input type="text" class="form-control" id="islem_tutar" name="islem_tutar" value="1,00" required>
                </div>
                <div class="form-group">
                    <label for="toplam_tutar">Toplam Tutar:</label>
                    <input type="text" class="form-control" id="toplam_tutar" name="toplam_tutar" value="1,00" required>
                </div>
            </div>
        </div>
        <button type="submit" class="btn btn-primary btn-block mt-3">Şimdi Öde</button>
    </form>
</div>

</body>
</html>
