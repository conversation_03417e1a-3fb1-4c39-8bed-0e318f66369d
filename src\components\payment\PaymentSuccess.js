import React, { useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faCheckCircle, 
  faDownload, 
  faEnvelope,
  faPrint,
  faHome,
  faClipboard
} from '@fortawesome/free-solid-svg-icons';

/**
 * Ödeme Başarılı Sayfası Bileşeni
 * Context7 referansı: /stripe-samples/accept-a-payment - Payment success handling
 */
const PaymentSuccess = ({ 
  paymentIntent, 
  packageInfo, 
  onDownloadReceipt,
  onSendEmail,
  onPrintReceipt,
  onReturnHome 
}) => {
  
  useEffect(() => {
    // Başarılı ödeme için analitik tracking (opsiyonel)
    if (window.gtag && paymentIntent) {
      window.gtag('event', 'purchase', {
        transaction_id: paymentIntent.id,
        value: paymentIntent.amount / 100,
        currency: paymentIntent.currency.toUpperCase(),
        items: [{
          item_id: packageInfo?.id || 'package',
          item_name: packageInfo?.name || 'MGZ24 Paketi',
          category: 'subscription',
          quantity: 1,
          price: paymentIntent.amount / 100
        }]
      });
    }
  }, [paymentIntent, packageInfo]);

  // Tarih formatla
  const formatDate = (timestamp) => {
    if (!timestamp) return '';
    const date = new Date(timestamp * 1000);
    return date.toLocaleString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Tutar formatla
  const formatAmount = (amount, currency) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: currency.toUpperCase()
    }).format(amount / 100);
  };

  // Ödeme ID'sini kopyala
  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
      alert('Ödeme ID kopyalandı!');
    });
  };

  if (!paymentIntent) {
    return (
      <div className="alert alert-warning">
        Ödeme bilgisi bulunamadı.
      </div>
    );
  }

  return (
    <div className="payment-success">
      <div className="container">
        <div className="row justify-content-center">
          <div className="col-lg-8">
            
            {/* Başarı mesajı */}
            <div className="card border-success shadow-lg">
              <div className="card-header bg-success text-white text-center py-4">
                <FontAwesomeIcon icon={faCheckCircle} size="3x" className="mb-3" />
                <h2 className="mb-0">Ödeme Başarılı!</h2>
                <p className="mb-0">İşleminiz başarıyla tamamlandı</p>
              </div>

              <div className="card-body p-4">
                
                {/* Ödeme detayları */}
                <div className="row mb-4">
                  <div className="col-12">
                    <h5 className="text-success mb-3">
                      <FontAwesomeIcon icon={faCheckCircle} className="me-2" />
                      İşlem Detayları
                    </h5>
                    
                    <div className="table-responsive">
                      <table className="table table-borderless">
                        <tbody>
                          <tr>
                            <td><strong>İşlem No:</strong></td>
                            <td>
                              <code>{paymentIntent.id}</code>
                              <button 
                                className="btn btn-sm btn-outline-secondary ms-2"
                                onClick={() => copyToClipboard(paymentIntent.id)}
                                title="Kopyala"
                              >
                                <FontAwesomeIcon icon={faClipboard} />
                              </button>
                            </td>
                          </tr>
                          <tr>
                            <td><strong>Tutar:</strong></td>
                            <td className="text-success">
                              <strong>{formatAmount(paymentIntent.amount, paymentIntent.currency)}</strong>
                            </td>
                          </tr>
                          <tr>
                            <td><strong>İşlem Tarihi:</strong></td>
                            <td>{formatDate(paymentIntent.created)}</td>
                          </tr>
                          <tr>
                            <td><strong>Durum:</strong></td>
                            <td>
                              <span className="badge bg-success">
                                <FontAwesomeIcon icon={faCheckCircle} className="me-1" />
                                Başarılı
                              </span>
                            </td>
                          </tr>
                          {packageInfo && (
                            <tr>
                              <td><strong>Paket:</strong></td>
                              <td>
                                <strong>{packageInfo.name}</strong>
                                <br />
                                <small className="text-muted">{packageInfo.description}</small>
                              </td>
                            </tr>
                          )}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>

                {/* Hizmet aktivasyon bilgisi */}
                {packageInfo && (
                  <div className="alert alert-info">
                    <h6 className="alert-heading">
                      <FontAwesomeIcon icon={faCheckCircle} className="me-2" />
                      Hizmet Aktivasyonu
                    </h6>
                    <p className="mb-0">
                      <strong>{packageInfo.name}</strong> paketiniz aktifleştirilmiştir. 
                      {packageInfo.duration && (
                        <> {packageInfo.duration} süreyle kullanabilirsiniz.</>
                      )}
                    </p>
                    {packageInfo.features && (
                      <ul className="mt-2 mb-0">
                        {packageInfo.features.map((feature, index) => (
                          <li key={index}>{feature}</li>
                        ))}
                      </ul>
                    )}
                  </div>
                )}

                {/* Aksiyonlar */}
                <div className="row g-2 mt-4">
                  <div className="col-sm-6 col-lg-3">
                    <button 
                      className="btn btn-outline-primary w-100"
                      onClick={onDownloadReceipt}
                    >
                      <FontAwesomeIcon icon={faDownload} className="me-2" />
                      Makbuz İndir
                    </button>
                  </div>
                  
                  <div className="col-sm-6 col-lg-3">
                    <button 
                      className="btn btn-outline-info w-100"
                      onClick={onSendEmail}
                    >
                      <FontAwesomeIcon icon={faEnvelope} className="me-2" />
                      E-posta Gönder
                    </button>
                  </div>
                  
                  <div className="col-sm-6 col-lg-3">
                    <button 
                      className="btn btn-outline-secondary w-100"
                      onClick={onPrintReceipt}
                    >
                      <FontAwesomeIcon icon={faPrint} className="me-2" />
                      Yazdır
                    </button>
                  </div>
                  
                  <div className="col-sm-6 col-lg-3">
                    <button 
                      className="btn btn-success w-100"
                      onClick={onReturnHome}
                    >
                      <FontAwesomeIcon icon={faHome} className="me-2" />
                      Ana Sayfa
                    </button>
                  </div>
                </div>

                {/* Destek bilgisi */}
                <div className="alert alert-light mt-4">
                  <h6 className="alert-heading">Destek</h6>
                  <p className="mb-0">
                    Herhangi bir sorunuz için: 
                    <strong> <EMAIL></strong> adresinden 
                    veya <strong>0850 123 4567</strong> numarasından bize ulaşabilirsiniz.
                  </p>
                  <small className="text-muted">
                    Destek talepleri için lütfen işlem numaranızı ({paymentIntent.id}) belirtin.
                  </small>
                </div>

              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentSuccess;