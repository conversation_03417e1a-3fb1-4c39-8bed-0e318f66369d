{"version": 3, "file": "static/js/156.f7b4a7a7.chunk.js", "mappings": "gKASA,MAwGA,EAxGgBA,KACZ,MAAMC,GAAWC,EAAAA,EAAAA,MAaXC,EAAuB,UAVTC,MAChB,IAAK,IAADC,EAAAC,EACA,MAAMC,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C,OAAW,OAAJJ,QAAI,IAAJA,GAAU,QAANF,EAAJE,EAAMA,YAAI,IAAAF,OAAN,EAAJA,EAAYO,QAAY,OAAJL,QAAI,IAAJA,GAAU,QAAND,EAAJC,EAAMA,YAAI,IAAAD,OAAN,EAAJA,EAAYO,QAAS,MACpD,CAAE,MAAAC,GACE,MAAO,MACX,GAGaV,GAGjB,OACIW,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC1DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6BAA6BG,SAAS,KAAKC,GAAG,UAAU,kBAAgB,UAASH,SAAA,EAC5FC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,EAC7BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,kBAAkBI,GAAG,UAASH,SAAC,gBAC7CF,EAAAA,EAAAA,KAAA,UAAQM,KAAK,SAASL,UAAU,YAAY,kBAAgB,YAAY,iBAAe,WAAW,aAAW,cAEjHE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mEAAkEC,SAAA,EAC7EF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,kCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,MAAtBtB,EAASuB,SAAmB,SAAW,IAAMC,GAAG,IAAGR,SAAA,EAC5GF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMC,EAAAA,MAAW,0BAG1Cb,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,SAAtBtB,EAASuB,SAAsB,SAAW,IAAMC,GAAG,OAAMR,SAAA,EAClHF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAME,EAAAA,MAAgB,mCAG/Cd,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,sBAAtBtB,EAASuB,SAAmC,SAAW,IAAMC,GAAG,oBAAmBR,SAAA,EAC5IF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMG,EAAAA,MAAY,8BAG3Cf,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMI,EAAAA,MAAmB,sCAKtDhB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wBACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,CAEpCd,IACGY,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMK,EAAAA,MAAa,0BAIhDjB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,mBAAtBtB,EAASuB,SAAgC,SAAW,IAAMC,GAAG,iBAAgBR,SAAA,EACtIF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMM,EAAAA,MAAU,uBAK7ClB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,uCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMO,EAAAA,MAAU,qCAGzCnB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMQ,EAAAA,MAAgB,6CAKnDpB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMS,EAAAA,MAAgB,qBAG/CrB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,cAAtBtB,EAASuB,SAA2B,SAAW,IAAMC,GAAG,YAAWR,SAAA,EAC5HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMU,EAAAA,MAAe,4BAKlDtB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wBAG1I,C,sDC3Gd,MAQA,EAReqB,KAEPvB,EAAAA,EAAAA,KAAA,UAAQC,UAAU,kBAAiBC,UAC/BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,cAAaC,SAAC,iD,qGCEvC,MA6GA,EA7GesB,KACX,MAAMC,GAAWC,EAAAA,EAAAA,OACVC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,YAClCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAGvCG,EAAAA,EAAAA,YAAU,KACgBC,WAClB,IAAK,IAADC,EAAAC,EAAAC,EAAAC,EAEA,MAAMC,EAAa7C,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C2C,GAAmB,OAAVD,QAAU,IAAVA,GAAgB,QAANJ,EAAVI,EAAY9C,YAAI,IAAA0C,OAAN,EAAVA,EAAkBM,cAAwB,OAAVF,QAAU,IAAVA,GAAgB,QAANH,EAAVG,EAAY9C,YAAI,IAAA2C,OAAN,EAAVA,EAAkB9B,IAC3DsB,GAAqB,OAAVW,QAAU,IAAVA,GAAgB,QAANF,EAAVE,EAAY9C,YAAI,IAAA4C,OAAN,EAAVA,EAAkBK,QAAkB,OAAVH,QAAU,IAAVA,GAAgB,QAAND,EAAVC,EAAY9C,YAAI,IAAA6C,OAAN,EAAVA,EAAkBK,aAE7D,IAAKH,EAID,OAHAI,QAAQC,KAAK,kCACbhB,EAAY,gBACZG,GAAW,GAKf,GAAIJ,EAGA,OAFAC,EAAYD,QACZI,GAAW,GAKf,IACI,MAAMc,QAAiBC,EAAAA,GAAiBC,aAAaR,GAGjDM,GAAYA,EAASH,cACrBd,EAAYiB,EAASH,aAGP,OAAVJ,QAAU,IAAVA,GAAAA,EAAY9C,OACZ8C,EAAW9C,KAAKiD,KAAOI,EAASH,YAChC/C,aAAaqD,QAAQ,OAAQvD,KAAKwD,UAAUX,KAGxD,CAAE,MAAOY,GACLP,QAAQC,KAAK,gGAEbhB,EAAYD,GAAY,sBAC5B,CACJ,CAAE,MAAOwB,GACLR,QAAQQ,MAAM,0DAAuCA,GAErDvB,EAAY,sBAChB,CAAC,QACGG,GAAW,EACf,GAGJqB,EAAe,GAChB,IAWH,OACIjD,EAAAA,EAAAA,MAAA,UAAQF,UAAU,0DAA0D,gBAAc,OAAMC,SAAA,EAC5FF,EAAAA,EAAAA,KAACO,EAAAA,GAAI,CAACN,UAAU,gEAAgES,GAAG,IAAGR,UAClFF,EAAAA,EAAAA,KAAA,OAAKqD,I,60RAAeC,IAAI,aAAaC,OAAO,UAEhDpD,EAAAA,EAAAA,MAAA,MAAIF,UAAU,yCAAwCC,SAAA,EAClDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8CAA6CC,UACvDC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,aAAYC,SAAA,EACxBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMO,EAAAA,IAAQlB,UAAU,SACxC6B,EAAU,mBAAkBH,QAGrC3B,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4BAA2BC,UACrCF,EAAAA,EAAAA,KAAA,UACIwD,QAtBCC,KAEjB9D,aAAa+D,WAAW,QAGxBjC,EAAS,SAAS,EAkBFxB,UAAU,gBACV,iBAAe,UACf,oBAAkB,OAClB,gBAAc,+BAAYC,UAE1BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM+C,EAAAA,WAG/B3D,EAAAA,EAAAA,KAAA,MAAIC,UAAU,iCAAgCC,UAC1CF,EAAAA,EAAAA,KAAA,UACIC,UAAU,gBACVK,KAAK,SACL,iBAAe,YACf,iBAAe,WACf,gBAAc,UACd,gBAAc,QACd,aAAW,kBAAcJ,UAEzBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMgD,EAAAA,eAI9B,C,8JClGjB,MA4YA,EA5YkBC,KAChB,MAAOC,EAAWC,IAAgBlC,EAAAA,EAAAA,UAAS,KACpCmC,EAAmBC,IAAwBpC,EAAAA,EAAAA,UAAS,KACpDC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCsB,EAAOe,IAAYrC,EAAAA,EAAAA,UAAS,OAC5BsC,EAAYC,IAAiBvC,EAAAA,EAAAA,UAAS,KACtCwC,EAAcC,IAAmBzC,EAAAA,EAAAA,UAAS,QAC1C0C,EAAYC,IAAiB3C,EAAAA,EAAAA,UAAS,QACtC4C,EAAiBC,IAAsB7C,EAAAA,EAAAA,WAAS,IAChD8C,EAAkBC,IAAuB/C,EAAAA,EAAAA,UAAS,OAClDgD,EAAeC,IAAoBjD,EAAAA,EAAAA,WAAS,IAClCH,EAAAA,EAAAA,OAEjBM,EAAAA,EAAAA,YAAU,KACR+C,GAAgB,GACf,KAEH/C,EAAAA,EAAAA,YAAU,KACRgD,GAAiB,GAChB,CAAClB,EAAWK,EAAYE,EAAcE,IAEzC,MAAMQ,EAAiB9C,UACrB,IAAK,IAADC,EAAAC,EACFJ,GAAW,GACXmC,EAAS,MAET,MAAM5B,EAAa7C,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C2C,GAAmB,OAAVD,QAAU,IAAVA,GAAgB,QAANJ,EAAVI,EAAY9C,YAAI,IAAA0C,OAAN,EAAVA,EAAkBM,cAAwB,OAAVF,QAAU,IAAVA,GAAgB,QAANH,EAAVG,EAAY9C,YAAI,IAAA2C,OAAN,EAAVA,EAAkB9B,KAAM,EAIjE4E,SAFaC,EAAAA,GAAgBC,0BAA0B5C,IAElC6C,KAAIC,IAAI,IAAAC,EAAAC,EAAAC,EAAA,MAAK,CACtCnF,GAAIgF,EAAKhF,GACToF,WAAYJ,EAAKK,aAAe,IAChCC,QAASN,EAAKO,YAAc,IAC5BnD,KAAM4C,EAAKQ,cAAgB,wBAC3BC,MAAOT,EAAKU,UAAY,IACxBC,KAAMX,EAAKY,gBAAc,YAAAzF,OAAgB6E,EAAKa,mBAAqB,IACnExF,GAAI2E,EAAKc,gBAAc,YAAA3F,OAAgB6E,EAAKe,mBAAqB,IACjEC,QAAShB,EAAKiB,WAAS,aAAA9F,OAAiB6E,EAAKkB,cAAgB,IAC7DC,QAASnB,EAAKoB,MAAI,cAAAjG,OAAY6E,EAAKqB,SAAW,IAC9CC,OAAQtB,EAAKuB,OAAS,QACtBC,QAASC,EAAWzB,EAAK0B,kBACzBC,UAAW3B,EAAK4B,kBAAoBH,EAAWzB,EAAK4B,mBAAqB,KACzEC,QAAyB,QAAjB5B,EAAAD,EAAK8B,oBAAY,IAAA7B,OAAA,EAAjBA,EAAmB8B,aAAc,IACzCC,WAA2B,QAAhB9B,EAAAF,EAAKiC,mBAAW,IAAA/B,OAAA,EAAhBA,EAAkB6B,aAAc,IAC3CG,aAA8B,QAAjB/B,EAAAH,EAAKmC,oBAAY,IAAAhC,OAAA,EAAjBA,EAAmB4B,aAAc,IAC9CK,UAAWpC,EAAKqC,kBAAoB,IACrC,IAED3D,EAAakB,GACblD,GAAW,EACb,CAAE,MAAOoB,GACPR,QAAQQ,MAAM,8CAAqCA,GACnDe,EAAS,sDAAgDf,EAAMwE,SAC/D5F,GAAW,EACb,GAGI+E,EAAcc,IAClB,IAAKA,EAAY,MAAO,GACxB,MAAMC,EAAO,IAAIC,KAAKF,GACtB,OAAOC,EAAKE,mBAAmB,SAAW,IAAMF,EAAKG,mBAAmB,QAAS,CAAEC,KAAM,UAAWC,OAAQ,WAAY,EAGpHlD,EAAkBA,KACtB,IAAImD,EAAW,IAAIrE,GAoBnB,GAjBIK,IACFgE,EAAWA,EAASC,QAAOC,GACzBA,EAAS5F,KAAK6F,cAAcC,SAASpE,EAAWmE,gBAChDD,EAAS5C,WAAW6C,cAAcC,SAASpE,EAAWmE,gBACtDD,EAAS1C,QAAQ2C,cAAcC,SAASpE,EAAWmE,gBACnDD,EAASvC,MAAMwC,cAAcC,SAASpE,EAAWmE,gBACjDD,EAASrC,KAAKsC,cAAcC,SAASpE,EAAWmE,gBAChDD,EAAS3H,GAAG4H,cAAcC,SAASpE,EAAWmE,kBAK7B,QAAjBjE,IACF8D,EAAWA,EAASC,QAAOC,GAAYA,EAAS1B,SAAWtC,KAI1C,QAAfE,EAAsB,CACxB,MAAMiE,EAAM,IAAIV,KACVW,EAAa,IAAIX,KAEvB,OAAQvD,GACN,IAAK,QACHkE,EAAWC,SAAS,EAAG,EAAG,EAAG,GAC7BP,EAAWA,EAASC,QAAOC,GAAY,IAAIP,KAAKO,EAASxB,UAAY4B,IACrE,MACF,IAAK,OACHA,EAAWE,QAAQH,EAAII,UAAY,GACnCT,EAAWA,EAASC,QAAOC,GAAY,IAAIP,KAAKO,EAASxB,UAAY4B,IACrE,MACF,IAAK,QACHA,EAAWI,SAASL,EAAIM,WAAa,GACrCX,EAAWA,EAASC,QAAOC,GAAY,IAAIP,KAAKO,EAASxB,UAAY4B,IAK3E,CAEAxE,EAAqBkE,EAAS,EAyB1BY,EAAkBpC,IACtB,OAAQA,GACN,IAAK,QACH,OAAO3G,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mBAAkBC,SAAEyG,IAC7C,IAAK,kBACH,OAAO3G,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mBAAkBC,SAAEyG,IAC7C,IAAK,aACH,OAAO3G,EAAAA,EAAAA,KAAA,QAAMC,UAAU,kBAAiBC,SAAEyG,IAC5C,QACE,OAAO3G,EAAAA,EAAAA,KAAA,QAAMC,UAAU,qBAAoBC,SAAEyG,IACjD,EAGF,OACExG,EAAAA,EAAAA,MAAA6I,EAAAA,SAAA,CAAA9I,SAAA,EACEF,EAAAA,EAAAA,KAACwB,EAAAA,EAAM,KACPxB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC9BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAClBF,EAAAA,EAAAA,KAACf,EAAAA,EAAO,KAERkB,EAAAA,EAAAA,MAAA,QAAMF,UAAU,iFAAgFC,SAAA,EAC9FC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0GAAyGC,SAAA,EACtHC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,KAAIC,SAAA,EAChBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMqI,EAAAA,IAAgBhJ,UAAU,SAAS,2BAG5DE,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACG,GAAG,OAAOT,UAAU,kBAAiBC,SAAA,EACzCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMsI,EAAAA,IAAQjJ,UAAU,SAAS,sBAKrD6B,GACC9B,EAAAA,EAAAA,KAACmJ,EAAAA,EAAc,CAACC,KAAK,KAAKC,QAAQ,UAAU1B,QAAQ,+BAA4B2B,UAAU,IACxFnG,GACFnD,EAAAA,EAAAA,KAACuJ,EAAAA,EAAY,CACX5B,QAASxE,EACTkG,QAAQ,SACRG,MAAM,8BACNC,aAAa,EACbC,UAAWA,IAAMxF,EAAS,IAAIhE,UAE9BF,EAAAA,EAAAA,KAAA,UAAQC,UAAU,8BAA8BuD,QAASuB,EAAe7E,SAAC,oBAK3EC,EAAAA,EAAAA,MAAA6I,EAAAA,SAAA,CAAA9I,SAAA,EAEEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACxBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,UAASC,SAAA,EACtBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACvBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mBAAkBC,UAChCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM+I,EAAAA,SAEzB3J,EAAAA,EAAAA,KAAA,SACEM,KAAK,OACLL,UAAU,eACV2J,YAAY,kBACZC,MAAO1F,EACP2F,SAAWC,GAAM3F,EAAc2F,EAAEC,OAAOH,eAI9C7J,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACvBC,EAAAA,EAAAA,MAAA,UACEF,UAAU,cACV4J,MAAOxF,EACPyF,SAAWC,GAAMzF,EAAgByF,EAAEC,OAAOH,OAAO3J,SAAA,EAEjDF,EAAAA,EAAAA,KAAA,UAAQ6J,MAAM,MAAK3J,SAAC,qBACpBF,EAAAA,EAAAA,KAAA,UAAQ6J,MAAM,QAAO3J,SAAC,WACtBF,EAAAA,EAAAA,KAAA,UAAQ6J,MAAM,kBAAY3J,SAAC,qBAC3BF,EAAAA,EAAAA,KAAA,UAAQ6J,MAAM,aAAO3J,SAAC,qBAG1BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACvBC,EAAAA,EAAAA,MAAA,UACEF,UAAU,cACV4J,MAAOtF,EACPuF,SAAWC,GAAMvF,EAAcuF,EAAEC,OAAOH,OAAO3J,SAAA,EAE/CF,EAAAA,EAAAA,KAAA,UAAQ6J,MAAM,MAAK3J,SAAC,qBACpBF,EAAAA,EAAAA,KAAA,UAAQ6J,MAAM,QAAO3J,SAAC,cACtBF,EAAAA,EAAAA,KAAA,UAAQ6J,MAAM,OAAM3J,SAAC,kBACrBF,EAAAA,EAAAA,KAAA,UAAQ6J,MAAM,QAAO3J,SAAC,wBAG1BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACvBC,EAAAA,EAAAA,MAAA,UAAQF,UAAU,kCAAiCC,SAAA,EACjDF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMqJ,EAAAA,IAAYhK,UAAU,SAAS,oCAShEE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,cAAaC,UAC1BC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,OAAMC,SAAA,CAAC,oBAEnBF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,wBAAuBC,SAAE8D,EAAkBkG,eAG/DlK,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,SACE,IAA7B8D,EAAkBkG,QACjB/J,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,EAC/BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMqI,EAAAA,IAAgBhJ,UAAU,2BACjDD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,aAAYC,SAAC,8BAC3BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,aAAYC,SAAC,6DAG5BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,UAC/BC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,yBAAwBC,SAAA,EACvCF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,cAAaC,UAC5BC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,iBACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,uBACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,WACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,iBACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,eACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,WACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,oBACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,6BAGRF,EAAAA,EAAAA,KAAA,SAAAE,SACG8D,EAAkBoB,KAAKiD,IACtBlI,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,UAAAE,SAASmI,EAAS5C,aACjB4C,EAAS1C,UACR3F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,SAAEmI,EAAS1C,cAGhDxF,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,UAASC,SAAEmI,EAAS5F,QACnCzC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,SAAEmI,EAAS7B,cAE9CxG,EAAAA,EAAAA,KAAA,MAAAE,UACEF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gBAAeC,SAAEmI,EAASvC,WAE5C3F,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,QAAOC,SAAA,EACpBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMuJ,EAAAA,IAAgBlK,UAAU,sBAChDoI,EAASrC,SAEZ7F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,QAAOC,SAAA,EACpBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMuJ,EAAAA,IAAgBlK,UAAU,qBAChDoI,EAAS3H,UAGdP,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACEF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMO,EAAAA,IAAQlB,UAAU,SACxCoI,EAAShC,YAEZrG,EAAAA,EAAAA,KAAA,MAAAE,SAAK6I,EAAeV,EAAS1B,WAC7BxG,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACEF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMwJ,EAAAA,IAAenK,UAAU,UAChDD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,QAAOC,SAAEmI,EAASxB,cAEnC7G,EAAAA,EAAAA,KAAA,MAAAE,UACEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAYJ,KAAK,QAAOK,SAAA,EACrCF,EAAAA,EAAAA,KAACO,EAAAA,GAAI,CACHG,GAAE,SAAAF,OAAW6H,EAAShI,IACtBJ,UAAU,iCACVuJ,MAAM,QAAOtJ,UAEbF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMyJ,EAAAA,SAEzBrK,EAAAA,EAAAA,KAACO,EAAAA,GAAI,CACHG,GAAE,SAAAF,OAAW6H,EAAShI,IACtBJ,UAAU,iCACVuJ,MAAM,aAAStJ,UAEfF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM0J,EAAAA,SAEzBtK,EAAAA,EAAAA,KAAA,UACEC,UAAU,gCACVuD,QAASA,IA7MvB6E,KACpBzD,EAAoByD,GACpB3D,GAAmB,EAAK,EA2MyB6F,CAAalC,GAC5BmB,MAAM,MAAKtJ,UAEXF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM4J,EAAAA,eAtDtBnC,EAAShI,yBAsEtCL,EAAAA,EAAAA,KAACuB,EAAAA,EAAM,SAKVkD,IACCzE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qBAAqBG,SAAS,KAAKqK,MAAO,CAAEC,gBAAiB,mBAAoBxK,UAC9FF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,eAAcC,UAC3BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,eAAcC,SAAA,EAC3BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,cAAaC,SAAC,kBAC5BF,EAAAA,EAAAA,KAAA,UACEM,KAAK,SACLL,UAAU,YACVuD,QAASA,IAAMkB,GAAmB,SAGtCvE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYC,SAAA,EACzBC,EAAAA,EAAAA,MAAA,KAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,UAAAE,SAAyB,OAAhByE,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAkBlC,OAAc,sEAE3CtC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sBAAqBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,gBAAe,qGAG3BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,eAAcC,SAAA,EAC3BF,EAAAA,EAAAA,KAAA,UACEM,KAAK,SACLL,UAAU,oBACVuD,QAASA,IAAMkB,GAAmB,GAClCiG,SAAU9F,EAAc3E,SACzB,gBAGDF,EAAAA,EAAAA,KAAA,UACEM,KAAK,SACLL,UAAU,iBACVuD,QAhQMvB,UACpB,GAAK0C,EAEL,IACEG,GAAiB,SACXI,EAAAA,GAAgB0F,eAAejG,EAAiBtE,IACtDqE,GAAmB,GACnBE,EAAoB,MACpBG,GACF,CAAE,MAAO5B,GACPR,QAAQQ,MAAM,4BAA6BA,GAC3Ce,EAAS,yCAAsCf,EAAMwE,QACvD,CAAC,QACC7C,GAAiB,EACnB,GAmPc6F,SAAU9F,EAAc3E,SAEvB2E,GACC1E,EAAAA,EAAAA,MAAA6I,EAAAA,SAAA,CAAA9I,SAAA,EACEF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,0CAA+C,mBAIjEE,EAAAA,EAAAA,MAAA6I,EAAAA,SAAA,CAAA9I,SAAA,EACEF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM4J,EAAAA,IAASvK,UAAU,SAAS,uBAUlE,C", "sources": ["components/Sidebar.js", "components/Footer.js", "components/Header.js", "pages/Shipments.js"], "sourcesContent": ["import React from 'react';\r\nimport { Link, useLocation } from 'react-router-dom';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport {\r\n    faDolly, faFolderPlus, faAnchor, faFlag<PERSON>he<PERSON>ed,\r\n    faUser, faStreetView, faCreditCard, faFileLines,\r\n    faBell, faDesktop\r\n} from '@fortawesome/free-solid-svg-icons';\r\n\r\nconst Sidebar = () => {\r\n    const location = useLocation();\r\n    \r\n    // Get user role from localStorage\r\n    const getUserRole = () => {\r\n        try {\r\n            const user = JSON.parse(localStorage.getItem('user'));\r\n            return user?.user?.role || user?.user?.gorev || 'user';\r\n        } catch {\r\n            return 'user';\r\n        }\r\n    };\r\n    \r\n    const userRole = getUserRole();\r\n    const isAdmin = userRole === 'admin';\r\n\r\n    return (\r\n        <div className=\"sidebar col-md-4 col-lg-2 p-0 bg-light-subtle\">\r\n            <div className=\"offcanvas-md offcanvas-end\" tabIndex=\"-1\" id=\"yanMenu\" aria-labelledby=\"yanMenu\">\r\n                <div className=\"offcanvas-header\">\r\n                    <h5 className=\"offcanvas-title\" id=\"yanMenu\">MGZ24 Gold</h5>\r\n                    <button type=\"button\" className=\"btn-close\" data-bs-dismiss=\"offcanvas\" data-bs-target=\"#yanMenu\" aria-label=\"Kapat\"></button>\r\n                </div>\r\n                <div className=\"offcanvas-body d-md-flex flex-column p-0 pt-lg-3 overflow-y-auto\">\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Sevkiyat İşlemleri</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/' ? 'active' : ''}`} to=\"/\">\r\n                                <FontAwesomeIcon icon={faDolly} />Aktif Sevkiyatlar\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/add' ? 'active' : ''}`} to=\"/add\">\r\n                                <FontAwesomeIcon icon={faFolderPlus} />Yeni Sevkiyat Oluştur\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/inactive-devices' ? 'active' : ''}`} to=\"/inactive-devices\">\r\n                                <FontAwesomeIcon icon={faAnchor} />İnaktif Cihazlar\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/history' ? 'active' : ''}`} to=\"/history\">\r\n                                <FontAwesomeIcon icon={faFlagCheckered} />Geçmiş Sevkiyatlar\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Sistem Yönetimi</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        {/* Cihaz Yönetimi - Sadece admin kullanıcılarına göster */}\r\n                        {isAdmin && (\r\n                            <li className=\"nav-item\">\r\n                                <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/devices' ? 'active' : ''}`} to=\"/devices\">\r\n                                    <FontAwesomeIcon icon={faDesktop} />Cihaz Yönetimi\r\n                                </Link>\r\n                            </li>\r\n                        )}\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/notifications' ? 'active' : ''}`} to=\"/notifications\">\r\n                                <FontAwesomeIcon icon={faBell} />Bildirimler\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Kullanıcı Ayarları</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/profile' ? 'active' : ''}`} to=\"/profile\">\r\n                                <FontAwesomeIcon icon={faUser} />Kullanıcı Profilim\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/viewers' ? 'active' : ''}`} to=\"/viewers\">\r\n                                <FontAwesomeIcon icon={faStreetView} />İzleyici İşlemleri\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Ödeme ve Yapılandırma</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/payment' ? 'active' : ''}`} to=\"/payment\">\r\n                                <FontAwesomeIcon icon={faCreditCard} />Ödeme Yap\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/invoices' ? 'active' : ''}`} to=\"/invoices\">\r\n                                <FontAwesomeIcon icon={faFileLines} />Faturalarım\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-body-secondary text-uppercase\">Kredi Durumu</h6>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Sidebar;", "import React from 'react';\r\n\r\nconst Footer = () => {\r\n    return (\r\n        <footer className=\"py-5 border-top\">\r\n            <p className=\"text-center\">&copy;2025 Inkatech Ölçüm Sistemleri</p>\r\n        </footer>\r\n    );\r\n};\r\n\r\nexport default Footer; ", "import React, { useState, useEffect } from 'react';\r\nimport { Link, useNavigate } from 'react-router-dom';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { faRightFromBracket, faBars, faUser } from '@fortawesome/free-solid-svg-icons';\r\nimport logoDark from '../assets/img/logo.png';\r\nimport { kullaniciService } from '../api/dbService';\r\n\r\nconst Header = () => {\r\n    const navigate = useNavigate();\r\n    const [userName, setUserName] = useState('Misafir');\r\n    const [loading, setLoading] = useState(true);\r\n\r\n    // Kullanıcı bilgilerini veritabanından al\r\n    useEffect(() => {\r\n        const fetchUserData = async () => {\r\n            try {\r\n                // Local storage'dan kullanıcı bilgisini al\r\n                const storedUser = JSON.parse(localStorage.getItem('user'));\r\n                const userId = storedUser?.user?.musteri_ID || storedUser?.user?.id;\r\n                const userName = storedUser?.user?.name || storedUser?.user?.musteri_adi;\r\n\r\n                if (!userId) {\r\n                    console.warn('Oturum bilgisi bulunamadı');\r\n                    setUserName('Misafir');\r\n                    setLoading(false);\r\n                    return;\r\n                }\r\n\r\n                // Eğer localStorage'da isim varsa direkt kullan\r\n                if (userName) {\r\n                    setUserName(userName);\r\n                    setLoading(false);\r\n                    return;\r\n                }\r\n\r\n                // API'den kullanıcı bilgilerini getir (backend çalışıyorsa)\r\n                try {\r\n                    const userData = await kullaniciService.getKullanici(userId);\r\n\r\n                    // musteri_adi alanını kullan\r\n                    if (userData && userData.musteri_adi) {\r\n                        setUserName(userData.musteri_adi);\r\n\r\n                        // localStorage'ı güncelle\r\n                        if (storedUser?.user) {\r\n                            storedUser.user.name = userData.musteri_adi;\r\n                            localStorage.setItem('user', JSON.stringify(storedUser));\r\n                        }\r\n                    }\r\n                } catch (apiError) {\r\n                    console.warn('API\\'den kullanıcı bilgisi alınamadı, localStorage kullanılıyor');\r\n                    // API hatası durumunda localStorage'daki adı kullan\r\n                    setUserName(userName || 'Kullanıcı');\r\n                }\r\n            } catch (error) {\r\n                console.error('Kullanıcı bilgileri alınırken hata:', error);\r\n                // Hata durumunda varsayılan isim kullan\r\n                setUserName('Kullanıcı');\r\n            } finally {\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        fetchUserData();\r\n    }, []);\r\n\r\n    // Çıkış işlemi\r\n    const handleLogout = () => {\r\n        // Local storage'dan kullanıcı bilgilerini temizle\r\n        localStorage.removeItem('user');\r\n\r\n        // Login sayfasına yönlendir\r\n        navigate('/login');\r\n    };\r\n\r\n    return (\r\n        <header className=\"navbar sticky-top bg-darkblue flex-md-nowrap p-0 shadow\" data-bs-theme=\"dark\">\r\n            <Link className=\"navbar-brand col-md-4 col-lg-2 me-0 px-3 py-2 fs-6 text-white\" to=\"/\">\r\n                <img src={logoDark} alt=\"MGZ24 Logo\" height=\"40\" />\r\n            </Link>\r\n            <ul className=\"navbar-nav flex-row align-items-center\">\r\n                <li className=\"nav-item text-nowrap d-none d-md-block me-3\">\r\n                    <span className=\"text-white\">\r\n                        <FontAwesomeIcon icon={faUser} className=\"me-2\" />\r\n                        {loading ? 'Yükleniyor...' : userName}\r\n                    </span>\r\n                </li>\r\n                <li className=\"nav-item text-nowrap px-1\">\r\n                    <button\r\n                        onClick={handleLogout}\r\n                        className=\"nav-link px-3\"\r\n                        data-bs-toggle=\"tooltip\"\r\n                        data-bs-placement=\"left\"\r\n                        data-bs-title=\"Çıkış yap!\"\r\n                    >\r\n                        <FontAwesomeIcon icon={faRightFromBracket} />\r\n                    </button>\r\n                </li>\r\n                <li className=\"nav-item text-nowrap d-md-none\">\r\n                    <button\r\n                        className=\"nav-link px-3\"\r\n                        type=\"button\"\r\n                        data-bs-toggle=\"offcanvas\"\r\n                        data-bs-target=\"#yanMenu\"\r\n                        aria-controls=\"yanMenu\"\r\n                        aria-expanded=\"false\"\r\n                        aria-label=\"menu aç/kapa\"\r\n                    >\r\n                        <FontAwesomeIcon icon={faBars} />\r\n                    </button>\r\n                </li>\r\n            </ul>\r\n        </header>\r\n    );\r\n};\r\n\r\nexport default Header; ", "import React, { useState, useEffect } from 'react';\r\nimport { Link, useNavigate } from 'react-router-dom';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { \r\n  faEye, faEdit, faTrash, faPlus, faSearch, faFilter, faDownload,\r\n  faShippingFast, faMapMarkerAlt, faCalendarAlt, faUser, faBox\r\n} from '@fortawesome/free-solid-svg-icons';\r\nimport Header from '../components/Header';\r\nimport Sidebar from '../components/Sidebar';\r\nimport Footer from '../components/Footer';\r\nimport LoadingSpinner from '../components/LoadingSpinner';\r\nimport ErrorMessage from '../components/ErrorMessage';\r\nimport { sevkiyatService } from '../api/dbService';\r\n\r\nconst Shipments = () => {\r\n  const [shipments, setShipments] = useState([]);\r\n  const [filteredShipments, setFilteredShipments] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [statusFilter, setStatusFilter] = useState('all');\r\n  const [dateFilter, setDateFilter] = useState('all');\r\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\r\n  const [selectedShipment, setSelectedShipment] = useState(null);\r\n  const [deleteLoading, setDeleteLoading] = useState(false);\r\n  const navigate = useNavigate();\r\n\r\n  useEffect(() => {\r\n    fetchShipments();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    filterShipments();\r\n  }, [shipments, searchTerm, statusFilter, dateFilter]);\r\n\r\n  const fetchShipments = async () => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      const storedUser = JSON.parse(localStorage.getItem('user'));\r\n      const userId = storedUser?.user?.musteri_ID || storedUser?.user?.id || 1;\r\n\r\n      const data = await sevkiyatService.getSevkiyatlarByMusteriId(userId);\r\n      \r\n      const processedData = data.map(item => ({\r\n        id: item.id,\r\n        sevkiyatID: item.sevkiyat_ID || '-',\r\n        mgzKodu: item.mgz24_kodu || '-',\r\n        name: item.sevkiyat_adi || 'İsimsiz Sevkiyat',\r\n        plate: item.plaka_no || '-',\r\n        from: item.cikis_lokasyon || `Lokasyon ${item.cikis_lokasyon_id || ''}`,\r\n        to: item.varis_lokasyon || `Lokasyon ${item.varis_lokasyon_id || ''}`,\r\n        carrier: item.nakliyeci || `Nakliyeci ${item.nakliyeci_id || ''}`,\r\n        product: item.urun || `Ürün ${item.urun_id || ''}`,\r\n        status: item.durum || 'Aktif',\r\n        created: formatDate(item.olusturma_zamani),\r\n        completed: item.tamamlanma_zamani ? formatDate(item.tamamlanma_zamani) : null,\r\n        pallet: item.palet_sayisi?.toString() || '-',\r\n        netWeight: item.net_agirlik?.toString() || '-',\r\n        grossWeight: item.brut_agirlik?.toString() || '-',\r\n        tempRange: item.sicaklik_araligi || '-'\r\n      }));\r\n\r\n      setShipments(processedData);\r\n      setLoading(false);\r\n    } catch (error) {\r\n      console.error('Sevkiyat verileri alınırken hata:', error);\r\n      setError('Sevkiyat verileri yüklenirken hata oluştu: ' + error.message);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const formatDate = (dateString) => {\r\n    if (!dateString) return '';\r\n    const date = new Date(dateString);\r\n    return date.toLocaleDateString('tr-TR') + ' ' + date.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' });\r\n  };\r\n\r\n  const filterShipments = () => {\r\n    let filtered = [...shipments];\r\n\r\n    // Arama filtresi\r\n    if (searchTerm) {\r\n      filtered = filtered.filter(shipment =>\r\n        shipment.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        shipment.sevkiyatID.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        shipment.mgzKodu.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        shipment.plate.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        shipment.from.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        shipment.to.toLowerCase().includes(searchTerm.toLowerCase())\r\n      );\r\n    }\r\n\r\n    // Durum filtresi\r\n    if (statusFilter !== 'all') {\r\n      filtered = filtered.filter(shipment => shipment.status === statusFilter);\r\n    }\r\n\r\n    // Tarih filtresi\r\n    if (dateFilter !== 'all') {\r\n      const now = new Date();\r\n      const filterDate = new Date();\r\n      \r\n      switch (dateFilter) {\r\n        case 'today':\r\n          filterDate.setHours(0, 0, 0, 0);\r\n          filtered = filtered.filter(shipment => new Date(shipment.created) >= filterDate);\r\n          break;\r\n        case 'week':\r\n          filterDate.setDate(now.getDate() - 7);\r\n          filtered = filtered.filter(shipment => new Date(shipment.created) >= filterDate);\r\n          break;\r\n        case 'month':\r\n          filterDate.setMonth(now.getMonth() - 1);\r\n          filtered = filtered.filter(shipment => new Date(shipment.created) >= filterDate);\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    }\r\n\r\n    setFilteredShipments(filtered);\r\n  };\r\n\r\n  const handleDelete = (shipment) => {\r\n    setSelectedShipment(shipment);\r\n    setShowDeleteModal(true);\r\n  };\r\n\r\n  const confirmDelete = async () => {\r\n    if (!selectedShipment) return;\r\n\r\n    try {\r\n      setDeleteLoading(true);\r\n      await sevkiyatService.deleteSevkiyat(selectedShipment.id);\r\n      setShowDeleteModal(false);\r\n      setSelectedShipment(null);\r\n      fetchShipments(); // Listeyi yenile\r\n    } catch (error) {\r\n      console.error('Sevkiyat silinirken hata:', error);\r\n      setError('Sevkiyat silinirken hata oluştu: ' + error.message);\r\n    } finally {\r\n      setDeleteLoading(false);\r\n    }\r\n  };\r\n\r\n  const getStatusBadge = (status) => {\r\n    switch (status) {\r\n      case 'Aktif':\r\n        return <span className=\"badge bg-success\">{status}</span>;\r\n      case 'Tamamlandı':\r\n        return <span className=\"badge bg-primary\">{status}</span>;\r\n      case 'İptal':\r\n        return <span className=\"badge bg-danger\">{status}</span>;\r\n      default:\r\n        return <span className=\"badge bg-secondary\">{status}</span>;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Header />\r\n      <div className=\"container-fluid\">\r\n        <div className=\"row\">\r\n          <Sidebar />\r\n          \r\n          <main className=\"col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white\">\r\n            <div className=\"d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom\">\r\n              <h1 className=\"h2\">\r\n                <FontAwesomeIcon icon={faShippingFast} className=\"me-2\" />\r\n                Sevkiyat Yönetimi\r\n              </h1>\r\n              <Link to=\"/add\" className=\"btn btn-primary\">\r\n                <FontAwesomeIcon icon={faPlus} className=\"me-2\" />\r\n                Yeni Sevkiyat\r\n              </Link>\r\n            </div>\r\n\r\n            {loading ? (\r\n              <LoadingSpinner size=\"lg\" variant=\"primary\" message=\"Sevkiyatlar yükleniyor...\" centered={true} />\r\n            ) : error ? (\r\n              <ErrorMessage \r\n                message={error} \r\n                variant=\"danger\"\r\n                title=\"Veri Yükleme Hatası\"\r\n                dismissible={true}\r\n                onDismiss={() => setError('')}\r\n              >\r\n                <button className=\"btn btn-primary btn-sm mt-2\" onClick={fetchShipments}>\r\n                  Yeniden Dene\r\n                </button>\r\n              </ErrorMessage>\r\n            ) : (\r\n              <>\r\n                {/* Filtreler */}\r\n                <div className=\"card mb-4\">\r\n                  <div className=\"card-body\">\r\n                    <div className=\"row g-3\">\r\n                      <div className=\"col-md-4\">\r\n                        <div className=\"input-group\">\r\n                          <span className=\"input-group-text\">\r\n                            <FontAwesomeIcon icon={faSearch} />\r\n                          </span>\r\n                          <input\r\n                            type=\"text\"\r\n                            className=\"form-control\"\r\n                            placeholder=\"Sevkiyat ara...\"\r\n                            value={searchTerm}\r\n                            onChange={(e) => setSearchTerm(e.target.value)}\r\n                          />\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"col-md-3\">\r\n                        <select\r\n                          className=\"form-select\"\r\n                          value={statusFilter}\r\n                          onChange={(e) => setStatusFilter(e.target.value)}\r\n                        >\r\n                          <option value=\"all\">Tüm Durumlar</option>\r\n                          <option value=\"Aktif\">Aktif</option>\r\n                          <option value=\"Tamamlandı\">Tamamlandı</option>\r\n                          <option value=\"İptal\">İptal</option>\r\n                        </select>\r\n                      </div>\r\n                      <div className=\"col-md-3\">\r\n                        <select\r\n                          className=\"form-select\"\r\n                          value={dateFilter}\r\n                          onChange={(e) => setDateFilter(e.target.value)}\r\n                        >\r\n                          <option value=\"all\">Tüm Tarihler</option>\r\n                          <option value=\"today\">Bugün</option>\r\n                          <option value=\"week\">Son 7 Gün</option>\r\n                          <option value=\"month\">Son 30 Gün</option>\r\n                        </select>\r\n                      </div>\r\n                      <div className=\"col-md-2\">\r\n                        <button className=\"btn btn-outline-secondary w-100\">\r\n                          <FontAwesomeIcon icon={faDownload} className=\"me-2\" />\r\n                          Dışa Aktar\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Sevkiyat Listesi */}\r\n                <div className=\"card\">\r\n                  <div className=\"card-header\">\r\n                    <h5 className=\"mb-0\">\r\n                      Sevkiyat Listesi \r\n                      <span className=\"badge bg-primary ms-2\">{filteredShipments.length}</span>\r\n                    </h5>\r\n                  </div>\r\n                  <div className=\"card-body p-0\">\r\n                    {filteredShipments.length === 0 ? (\r\n                      <div className=\"text-center py-5\">\r\n                        <FontAwesomeIcon icon={faShippingFast} className=\"fa-3x text-muted mb-3\" />\r\n                        <h5 className=\"text-muted\">Sevkiyat Bulunamadı</h5>\r\n                        <p className=\"text-muted\">Arama kriterlerinize uygun sevkiyat bulunamadı.</p>\r\n                      </div>\r\n                    ) : (\r\n                      <div className=\"table-responsive\">\r\n                        <table className=\"table table-hover mb-0\">\r\n                          <thead className=\"table-light\">\r\n                            <tr>\r\n                              <th>Sevkiyat ID</th>\r\n                              <th>Sevkiyat Adı</th>\r\n                              <th>Plaka</th>\r\n                              <th>Güzergah</th>\r\n                              <th>Nakliyeci</th>\r\n                              <th>Durum</th>\r\n                              <th>Oluşturma</th>\r\n                              <th>İşlemler</th>\r\n                            </tr>\r\n                          </thead>\r\n                          <tbody>\r\n                            {filteredShipments.map((shipment) => (\r\n                              <tr key={shipment.id}>\r\n                                <td>\r\n                                  <strong>{shipment.sevkiyatID}</strong>\r\n                                  {shipment.mgzKodu && (\r\n                                    <div className=\"small text-muted\">{shipment.mgzKodu}</div>\r\n                                  )}\r\n                                </td>\r\n                                <td>\r\n                                  <div className=\"fw-bold\">{shipment.name}</div>\r\n                                  <div className=\"small text-muted\">{shipment.product}</div>\r\n                                </td>\r\n                                <td>\r\n                                  <span className=\"badge bg-dark\">{shipment.plate}</span>\r\n                                </td>\r\n                                <td>\r\n                                  <div className=\"small\">\r\n                                    <FontAwesomeIcon icon={faMapMarkerAlt} className=\"text-success me-1\" />\r\n                                    {shipment.from}\r\n                                  </div>\r\n                                  <div className=\"small\">\r\n                                    <FontAwesomeIcon icon={faMapMarkerAlt} className=\"text-danger me-1\" />\r\n                                    {shipment.to}\r\n                                  </div>\r\n                                </td>\r\n                                <td>\r\n                                  <FontAwesomeIcon icon={faUser} className=\"me-1\" />\r\n                                  {shipment.carrier}\r\n                                </td>\r\n                                <td>{getStatusBadge(shipment.status)}</td>\r\n                                <td>\r\n                                  <FontAwesomeIcon icon={faCalendarAlt} className=\"me-1\" />\r\n                                  <div className=\"small\">{shipment.created}</div>\r\n                                </td>\r\n                                <td>\r\n                                  <div className=\"btn-group\" role=\"group\">\r\n                                    <Link\r\n                                      to={`/view/${shipment.id}`}\r\n                                      className=\"btn btn-sm btn-outline-primary\"\r\n                                      title=\"Detay\"\r\n                                    >\r\n                                      <FontAwesomeIcon icon={faEye} />\r\n                                    </Link>\r\n                                    <Link\r\n                                      to={`/edit/${shipment.id}`}\r\n                                      className=\"btn btn-sm btn-outline-warning\"\r\n                                      title=\"Düzenle\"\r\n                                    >\r\n                                      <FontAwesomeIcon icon={faEdit} />\r\n                                    </Link>\r\n                                    <button\r\n                                      className=\"btn btn-sm btn-outline-danger\"\r\n                                      onClick={() => handleDelete(shipment)}\r\n                                      title=\"Sil\"\r\n                                    >\r\n                                      <FontAwesomeIcon icon={faTrash} />\r\n                                    </button>\r\n                                  </div>\r\n                                </td>\r\n                              </tr>\r\n                            ))}\r\n                          </tbody>\r\n                        </table>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </>\r\n            )}\r\n          </main>\r\n          \r\n          <Footer />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Silme Onay Modalı */}\r\n      {showDeleteModal && (\r\n        <div className=\"modal show d-block\" tabIndex=\"-1\" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>\r\n          <div className=\"modal-dialog\">\r\n            <div className=\"modal-content\">\r\n              <div className=\"modal-header\">\r\n                <h5 className=\"modal-title\">Sevkiyat Sil</h5>\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"btn-close\"\r\n                  onClick={() => setShowDeleteModal(false)}\r\n                ></button>\r\n              </div>\r\n              <div className=\"modal-body\">\r\n                <p>\r\n                  <strong>{selectedShipment?.name}</strong> sevkiyatını silmek istediğinizden emin misiniz?\r\n                </p>\r\n                <div className=\"alert alert-warning\">\r\n                  <strong>Uyarı:</strong> Bu işlem geri alınamaz. Sevkiyat ve bağlı tüm veriler silinecektir.\r\n                </div>\r\n              </div>\r\n              <div className=\"modal-footer\">\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"btn btn-secondary\"\r\n                  onClick={() => setShowDeleteModal(false)}\r\n                  disabled={deleteLoading}\r\n                >\r\n                  İptal\r\n                </button>\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"btn btn-danger\"\r\n                  onClick={confirmDelete}\r\n                  disabled={deleteLoading}\r\n                >\r\n                  {deleteLoading ? (\r\n                    <>\r\n                      <span className=\"spinner-border spinner-border-sm me-2\"></span>\r\n                      Siliniyor...\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <FontAwesomeIcon icon={faTrash} className=\"me-2\" />\r\n                      Sil\r\n                    </>\r\n                  )}\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Shipments;"], "names": ["Sidebar", "location", "useLocation", "isAdmin", "getUserRole", "_user$user", "_user$user2", "user", "JSON", "parse", "localStorage", "getItem", "role", "gorev", "_unused", "_jsx", "className", "children", "_jsxs", "tabIndex", "id", "type", "Link", "concat", "pathname", "to", "FontAwesomeIcon", "icon", "faDolly", "faFolderPlus", "faAnchor", "faFlag<PERSON><PERSON><PERSON><PERSON>", "faDesktop", "faBell", "faUser", "faStreetView", "faCreditCard", "faFileLines", "Footer", "Header", "navigate", "useNavigate", "userName", "setUserName", "useState", "loading", "setLoading", "useEffect", "async", "_storedUser$user", "_storedUser$user2", "_storedUser$user3", "_storedUser$user4", "storedUser", "userId", "musteri_ID", "name", "musteri_adi", "console", "warn", "userData", "kullaniciService", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setItem", "stringify", "apiError", "error", "fetchUserData", "src", "alt", "height", "onClick", "handleLogout", "removeItem", "faRightFromBracket", "faBars", "Shipments", "shipments", "setShipments", "filteredShipments", "setFilteredShipments", "setError", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "dateFilter", "setDateFilter", "showDeleteModal", "setShowDeleteModal", "selectedShipment", "setSelectedShipment", "deleteLoading", "setDeleteLoading", "fetchShipments", "filterShipments", "processedData", "sevkiyatService", "getSevkiyatlarByMusteriId", "map", "item", "_item$palet_sayisi", "_item$net_agirlik", "_item$brut_agirlik", "sevkiyatID", "sevkiyat_ID", "mgzKodu", "mgz24_kodu", "sevkiyat_adi", "plate", "plaka_no", "from", "cikis_lokasyon", "cikis_lokasyon_id", "varis_lokasyon", "varis_lokasyon_id", "carrier", "<PERSON><PERSON><PERSON><PERSON>", "nakliyeci_id", "product", "urun", "urun_id", "status", "durum", "created", "formatDate", "olusturma_zamani", "completed", "tama<PERSON><PERSON><PERSON>_zamani", "pallet", "palet_sayisi", "toString", "netWeight", "net_agirlik", "grossWeight", "brut_agirlik", "tempRange", "sicaklik_araligi", "message", "dateString", "date", "Date", "toLocaleDateString", "toLocaleTimeString", "hour", "minute", "filtered", "filter", "shipment", "toLowerCase", "includes", "now", "filterDate", "setHours", "setDate", "getDate", "setMonth", "getMonth", "getStatusBadge", "_Fragment", "faShippingFast", "faPlus", "LoadingSpinner", "size", "variant", "centered", "ErrorMessage", "title", "dismissible", "on<PERSON><PERSON><PERSON>", "faSearch", "placeholder", "value", "onChange", "e", "target", "faDownload", "length", "faMapMarkerAlt", "faCalendarAlt", "faEye", "faEdit", "handleDelete", "faTrash", "style", "backgroundColor", "disabled", "deleteSevkiyat"], "sourceRoot": ""}