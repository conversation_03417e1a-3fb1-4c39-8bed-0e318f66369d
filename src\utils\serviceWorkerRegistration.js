// Service Worker Registration Utility

// Service worker'ı register et
export const register = () => {
  if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
      navigator.serviceWorker.register('/sw.js')
        .then((registration) => {
          console.log('Service Worker başarıyla kaydedildi:', registration.scope);
          
          // Update check
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing;
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed') {
                if (navigator.serviceWorker.controller) {
                  // Yeni güncelleme mevcut
                  console.log('Yeni içerik mevcut, sayfa yenilenecek...');
                  if (window.confirm('Yeni bir güncelleme mevcut. Sayfayı yenilemek ister misiniz?')) {
                    window.location.reload();
                  }
                } else {
                  // İlk kez cache'leniyor
                  console.log('İçerik cache\'lendi, çevrimdı<PERSON><PERSON> kullanım mevcut.');
                }
              }
            });
          });
        })
        .catch((error) => {
          // Service Worker dosyası bulunamadığında sessiz hata
          if (error.message.includes('404') || error.message.includes('not found')) {
            console.warn('Service Worker dosyası bulunamadı, atlayılıyor...');
          } else {
            console.error('Service Worker kayıt hatası:', error);
          }
        });
    });
  } else {
    console.log('Service Worker bu tarayıcıda desteklenmiyor.');
  }
};

// Service worker'ı unregister et
export const unregister = () => {
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.ready
      .then((registration) => {
        registration.unregister();
        console.log('Service Worker kaydı silindi');
      })
      .catch((error) => {
        console.error('Service Worker kayıt silme hatası:', error.message);
      });
  }
};

// Service worker update check
export const checkForUpdates = () => {
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.ready
      .then((registration) => {
        registration.update();
      });
  }
};