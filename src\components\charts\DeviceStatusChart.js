import React from 'react';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
  Cell
} from 'recharts';

/**
 * Cihaz durumlarını gösteren bar grafik bileşeni
 * Context7 referansı: /recharts/recharts - Bar<PERSON>hart with customized bars
 * @param {Object} props - Component props
 * @param {Array} props.data - Cihaz durum verileri
 * @param {string} props.title - Grafik başlığı
 * @param {number} props.height - Grafik yüksekliği
 */
const DeviceStatusChart = ({ 
  data = [], 
  title = 'Cihaz Durumları',
  height = 300 
}) => {
  // Durum renklerini belirle
  const getStatusColor = (status) => {
    const colors = {
      'aktif': '#4caf50',
      'pasif': '#f44336', 
      'bakimda': '#ff9800',
      'baglanti_sorunu': '#9c27b0',
      'pil_dusuk': '#ff5722'
    };
    return colors[status] || '#757575';
  };

  // Custom tooltip
  const CustomTooltip = ({ payload, label, active }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white border border-gray-300 rounded p-3 shadow-lg">
          <p className="font-semibold">{label}</p>
          <p style={{ color: payload[0].color }}>
            {`Cihaz Sayısı: ${payload[0].value}`}
          </p>
          {data.percentage && (
            <p className="text-sm text-gray-600">
              {`Oran: %${data.percentage.toFixed(1)}`}
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  // Custom bar label
  const renderCustomBarLabel = ({ x, y, width, height, value }) => {
    return (
      <text 
        x={x + width / 2} 
        y={y - 5} 
        fill="#666" 
        textAnchor="middle" 
        fontSize="12"
      >
        {value}
      </text>
    );
  };

  return (
    <div className="w-100">
      <h5 className="text-center mb-3">{title}</h5>
      <ResponsiveContainer width="100%" height={height}>
        <BarChart
          data={data}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 5,
          }}
          accessibilityLayer
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
          <XAxis 
            dataKey="status" 
            stroke="#666"
            fontSize={12}
          />
          <YAxis 
            stroke="#666"
            fontSize={12}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          
          <Bar 
            dataKey="count" 
            name="Cihaz Sayısı"
            radius={[4, 4, 0, 0]}
            label={renderCustomBarLabel}
          >
            {data.map((entry, index) => (
              <Cell 
                key={`cell-${index}`} 
                fill={getStatusColor(entry.status)} 
              />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default DeviceStatusChart;