/**
 * API ve veritabanı yapılandırma dosyası
 */

// Veritabanı yapılandırması
const dbConfig = {
    host: process.env.DB_HOST || '************',
    user: process.env.DB_USER || 'mehmet',
    password: process.env.DB_PASSWORD || 'Inka.mS_335265933s.tech',
    database: process.env.DB_NAME || 'mgz24db',
    // Bağlantı hatalarına karşı yapılandırma
    connectionLimit: 10,
    waitForConnections: true,
    queueLimit: 0,
    // Bağlantı zaman aşımı ayarları (ms cinsinden)
    connectTimeout: 10000
};

// API Yapılandırması
const apiConfig = {
    baseURL: process.env.REACT_APP_API_URL || 'http://localhost:3000/api',
    timeout: 15000, // 15 saniye zaman aşımı
    retryCount: 1, // Başarısız istekleri kaç kez yeniden deneyecek (console spam'ini azalt)
    retryDelay: 2000, // Yeniden denemeler arasında beklenecek süre (ms)
    // HTTP durumları için özel yanıt süreleri
    timeoutByStatus: {
        404: 5000, // 404 için daha kısa zaman aşımı (5 saniye)
        500: 20000, // 500 için daha uzun zaman aşımı (20 saniye)
    },
    // İstek zaman aşımlarından sonra gösterilecek demo veri kullanımını etkinleştir
    enableDemoData: false, // Geçici demo mod: Backend bağlantı sorunları için
    // Servis kullanılamadığında hata mesajlarını kişiselleştir
    errorMessages: {
        404: 'İstenen kayıt veritabanında bulunamadı.',
        500: 'Sunucu şu anda kullanılamıyor. Lütfen daha sonra tekrar deneyin.',
        network: 'Sunucuya bağlanılamadı. Lütfen internet bağlantınızı kontrol edin.',
        timeout: 'Sunucu yanıt vermiyor. Lütfen daha sonra tekrar deneyin.',
    }
};

export default dbConfig;
export { apiConfig }; 