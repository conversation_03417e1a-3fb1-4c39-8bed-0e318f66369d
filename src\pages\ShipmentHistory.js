import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Header from '../components/Header';
import Sidebar from '../components/Sidebar';
import Footer from '../components/Footer';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
    faCheck,
    faHistory,
    faExclamationTriangle,
    faFilter,
    faUndo,
    faSearch
} from '@fortawesome/free-solid-svg-icons';
import { sevkiyatService } from '../api/dbService';

const ShipmentHistory = () => {
    // State tanımlamaları
    const [gecmisler, setGecmisler] = useState([]);
    const [filteredGecmisler, setFilteredGecmisler] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    // const [userRole, setUserRole] = useState('viewer'); // Gerekirse eklenecek

    // Filtreleme için state
    const [filters, setFilters] = useState({
        deviceId: '',
        senderCompany: '',
        receiverCompany: '',
        startDate: '',
        endDate: '',
        product: ''
    });

    // Kullanıcı bilgisini localStorage'dan al
    const userString = localStorage.getItem('user');
    const userData = userString ? JSON.parse(userString) : null;
    const user = userData ? userData.user : null;
    const musteriID = user ? user.musteri_ID : null;

    // Geçmiş sevkiyatları yükle
    useEffect(() => {
        const fetchGecmisler = async () => {
            try {
                setLoading(true);
                setError(null);

                // Tamamlanmış sevkiyatları API'den çek (tamamlandi_mi = 1)
                const data = await sevkiyatService.getGecmisSevkiyatlarByMusteriId(musteriID);
                setGecmisler(data);
                setFilteredGecmisler(data);
                setLoading(false);
            } catch (error) {
                console.error('Sevkiyat geçmişi alınırken hata:', error);
                setError('Sevkiyat geçmişi yüklenirken bir hata oluştu: ' + error.message);
                setLoading(false);
            }
        };

        fetchGecmisler();

        // Kullanıcı rolü kontrolü (gerekirse eklenecek)
        // const checkUserRole = () => {
        //     const storedUser = JSON.parse(localStorage.getItem('user'));
        //     if (storedUser && storedUser.role) {
        //         setUserRole(storedUser.role);
        //     } else {
        //         setUserRole('viewer');
        //     }
        // };
        // checkUserRole();
    }, [musteriID]);

    // Filtre değişikliklerini işle
    const handleFilterChange = (e) => {
        const { name, value } = e.target;
        setFilters(prevFilters => ({
            ...prevFilters,
            [name]: value
        }));
    };

    // Filtreleri uygula
    useEffect(() => {
        let filtered = [...gecmisler];

        // Cihaz ID filtresi
        if (filters.deviceId) {
            filtered = filtered.filter(gecmis =>
                gecmis.mgz24_kodu?.toLowerCase().includes(filters.deviceId.toLowerCase())
            );
        }

        // Gönderen firma filtresi
        if (filters.senderCompany) {
            filtered = filtered.filter(gecmis =>
                gecmis.gonderen_firma_adi?.toLowerCase().includes(filters.senderCompany.toLowerCase())
            );
        }

        // Alan firma filtresi
        if (filters.receiverCompany) {
            filtered = filtered.filter(gecmis =>
                gecmis.alici_firma_adi?.toLowerCase().includes(filters.receiverCompany.toLowerCase())
            );
        }

        // Tarih aralığı filtresi
        if (filters.startDate && filters.endDate) {
            filtered = filtered.filter(gecmis => {
                const gecmisStartDate = new Date(gecmis.baslangic_zamani);
                const gecmisEndDate = new Date(gecmis.bitis_zamani);
                const filterStartDate = new Date(filters.startDate);
                const filterEndDate = new Date(filters.endDate);

                // Sevkiyat tarihleri ile filtreleme tarihleri arasında çakışma varsa göster
                return (gecmisStartDate <= filterEndDate && gecmisEndDate >= filterStartDate);
            });
        } else if (filters.startDate) {
            filtered = filtered.filter(gecmis => {
                const gecmisEndDate = new Date(gecmis.bitis_zamani);
                const filterStartDate = new Date(filters.startDate);
                return gecmisEndDate >= filterStartDate;
            });
        } else if (filters.endDate) {
            filtered = filtered.filter(gecmis => {
                const gecmisStartDate = new Date(gecmis.baslangic_zamani);
                const filterEndDate = new Date(filters.endDate);
                return gecmisStartDate <= filterEndDate;
            });
        }

        setFilteredGecmisler(filtered);
    }, [filters, gecmisler]);

    // Filtreleri sıfırla
    const resetFilters = () => {
        setFilters({
            deviceId: '',
            senderCompany: '',
            receiverCompany: '',
            startDate: '',
            endDate: '',
            product: ''
        });
    };

    // Durum etiketi gösterimi
    const renderStatus = (durum) => {
        switch (durum) {
            case 'tamamlandi':
                return <span className="badge bg-success">Tamamlandı</span>;
            case 'iptal_edildi':
                return <span className="badge bg-danger">İptal Edildi</span>;
            case 'iade_edildi':
                return <span className="badge bg-info">İade Edildi</span>;
            case 'ariza_bildirildi':
                return <span className="badge bg-warning">Arıza Bildirildi</span>;
            default:
                return <span className="badge bg-secondary">Bilinmiyor</span>;
        }
    };

    // Tarih formatını düzenleme
    const formatDate = (dateString) => {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleString('tr-TR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    return (
        <>
            <Header />
            <div className="container-fluid">
                <div className="row">
                    <Sidebar />

                    <main className="col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white">
                        <div className="pt-3 pb-1 mt-3 mb-3 border-bottom">
                            <h1 className="h4 text-dark">
                                <FontAwesomeIcon icon={faHistory} className="me-2" />
                                Geçmiş Sevkiyatlar
                            </h1>
                        </div>

                        {/* Filtreler */}
                        <div className="card bg-light mb-4">
                            <div className="card-header">
                                <h5 className="card-title mb-0">
                                    <FontAwesomeIcon icon={faFilter} className="me-2" />
                                    Filtreler
                                </h5>
                            </div>
                            <div className="card-body">
                                <div className="row g-3">
                                    <div className="col-md-2">
                                        <label htmlFor="deviceId" className="form-label">Cihaz Kodu</label>
                                        <input
                                            type="text"
                                            className="form-control"
                                            id="deviceId"
                                            name="deviceId"
                                            value={filters.deviceId}
                                            onChange={handleFilterChange}
                                            placeholder="Cihaz kodu"
                                        />
                                    </div>

                                    <div className="col-md-2">
                                        <label htmlFor="senderCompany" className="form-label">Gönderen Firma</label>
                                        <input
                                            type="text"
                                            className="form-control"
                                            id="senderCompany"
                                            name="senderCompany"
                                            value={filters.senderCompany}
                                            onChange={handleFilterChange}
                                            placeholder="Gönderen firma"
                                        />
                                    </div>

                                    <div className="col-md-2">
                                        <label htmlFor="receiverCompany" className="form-label">Alan Firma</label>
                                        <input
                                            type="text"
                                            className="form-control"
                                            id="receiverCompany"
                                            name="receiverCompany"
                                            value={filters.receiverCompany}
                                            onChange={handleFilterChange}
                                            placeholder="Alan firma"
                                        />
                                    </div>

                                    <div className="col-md-2">
                                        <label htmlFor="startDate" className="form-label">Başlangıç Tarihi</label>
                                        <input
                                            type="date"
                                            className="form-control"
                                            id="startDate"
                                            name="startDate"
                                            value={filters.startDate}
                                            onChange={handleFilterChange}
                                        />
                                    </div>

                                    <div className="col-md-2">
                                        <label htmlFor="endDate" className="form-label">Bitiş Tarihi</label>
                                        <input
                                            type="date"
                                            className="form-control"
                                            id="endDate"
                                            name="endDate"
                                            value={filters.endDate}
                                            onChange={handleFilterChange}
                                        />
                                    </div>
                                </div>

                                <div className="d-flex justify-content-end mt-3">
                                    <button
                                        className="btn btn-outline-secondary me-2"
                                        onClick={resetFilters}
                                    >
                                        <FontAwesomeIcon icon={faUndo} className="me-1" />
                                        Filtreleri Sıfırla
                                    </button>

                                    <button className="btn btn-primary">
                                        <FontAwesomeIcon icon={faSearch} className="me-1" />
                                        Ara
                                    </button>
                                </div>
                            </div>
                        </div>

                        {/* Ana İçerik */}
                        {loading ? (
                            <div className="d-flex justify-content-center my-5">
                                <div className="spinner-border text-primary" role="status">
                                    <span className="visually-hidden">Yükleniyor...</span>
                                </div>
                                <span className="ms-2">Sevkiyat geçmişi yükleniyor...</span>
                            </div>
                        ) : error ? (
                            <div className="alert alert-danger">
                                <h5 className="alert-heading">Hata</h5>
                                <p>{error}</p>
                                <hr />
                                <button className="btn btn-primary" onClick={() => window.location.reload()}>
                                    Yeniden Dene
                                </button>
                            </div>
                        ) : (
                            <>
                                {filteredGecmisler.length === 0 ? (
                                    <div className="alert alert-warning">
                                        <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
                                        Arama kriterlerinize uygun geçmiş sevkiyat bulunamadı.
                                    </div>
                                ) : (
                                    <>
                                        <div className="alert alert-info mb-3">
                                            <FontAwesomeIcon icon={faCheck} className="me-2" />
                                            <strong>{filteredGecmisler.length}</strong> geçmiş sevkiyat listeleniyor.
                                        </div>

                                        <div className="table-responsive">
                                            <table className="table table-striped table-hover border">
                                                <thead className="table-light">
                                                    <tr>
                                                        <th>Sevkiyat ID</th>
                                                        <th>Sevkiyat Adı</th>
                                                        <th>Plaka No</th>
                                                        <th>Nereden</th>
                                                        <th>Nereye</th>
                                                        <th>Nakliyeci</th>
                                                        <th>Ürün</th>
                                                        <th>Sipariş No</th>
                                                        <th>Palet</th>
                                                        <th>Net</th>
                                                        <th>Brüt</th>
                                                        <th>Eklenme</th>
                                                        <th>İşlemler</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {filteredGecmisler.map((gecmis) => (
                                                        <tr key={gecmis.id}>
                                                            <td>
                                                                <strong>{gecmis.sevkiyat_ID || `GS-${String(gecmis.id).padStart(6, '0')}`}</strong>
                                                                {gecmis.mgz24_kodu && (
                                                                    <div className="small text-muted">{gecmis.mgz24_kodu}</div>
                                                                )}
                                                            </td>
                                                            <td>
                                                                <div className="fw-bold">{gecmis.sevkiyat_adi || 'Bilinmiyor'}</div>
                                                                <div className="small text-muted">{gecmis.urun || 'Ürün bilgisi yok'}</div>
                                                            </td>
                                                            <td>
                                                                <span className="badge bg-dark">{gecmis.plaka_no || '-'}</span>
                                                            </td>
                                                            <td>{gecmis.cikis_lokasyon || gecmis.gonderen_firma_adi || 'Bilinmiyor'}</td>
                                                            <td>{gecmis.varis_lokasyon || gecmis.alici_firma_adi || 'Bilinmiyor'}</td>
                                                            <td>{gecmis.nakliyeci || 'Bilinmiyor'}</td>
                                                            <td>{gecmis.urun || 'Bilinmiyor'}</td>
                                                            <td>{gecmis.mgz24_kodu || '-'}</td>
                                                            <td>{gecmis.palet_sayisi || '-'}</td>
                                                            <td>{gecmis.net_agirlik || '-'}</td>
                                                            <td>{gecmis.brut_agirlik || '-'}</td>
                                                            <td>{formatDate(gecmis.baslangic_zamani) || formatDate(gecmis.olusturma_zamani)}</td>
                                                            <td>
                                                                <Link to={`/view/${gecmis.id}`} className="btn btn-sm btn-outline-primary">
                                                                    Detaylar
                                                                </Link>
                                                            </td>
                                                        </tr>
                                                    ))}
                                                </tbody>
                                            </table>
                                        </div>
                                    </>
                                )}
                            </>
                        )}
                    </main>
                </div>
            </div>
            <Footer />
        </>
    );
};

export default ShipmentHistory;