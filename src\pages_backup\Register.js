import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import logoDark from '../assets/img/logo-dark.png';
import backgroundImg from '../assets/img/on-the-road.jpg';

const Register = () => {
    const navigate = useNavigate();
    const [formData, setFormData] = useState({
        email: '',
        password: '',
        passwordConfirm: '',
        company: '',
        authorizedPerson: '',
        phoneNumber: ''
    });

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData({
            ...formData,
            [name]: value
        });
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        if (formData.password !== formData.passwordConfirm) {
            alert('Parolalar eşleşmiyor!');
            return;
        }
        console.log('Kayıt Bilgileri:', formData);
        // Başarılı kayıt durumunda giriş sayfasına yön<PERSON>dir
        navigate('/login');
    };

    // Stil tanımlamaları
    const styles = {
        // Arkaplan stili
        loginContainer: {
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '100vh',
            backgroundImage: `url(${backgroundImg})`,
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'center center',
            backgroundSize: 'cover',
            position: 'relative'
        },
        // Arkaplan kaplama stili
        overlay: {
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 1
        },
        // Kart stili
        card: {
            width: '100%',
            maxWidth: '450px',
            padding: '30px 25px',
            backgroundColor: '#fff',
            borderRadius: '8px',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.2)',
            border: '1px solid #e0e0e0',
            position: 'relative',
            zIndex: 2
        },
        // Logo stili
        logo: {
            maxWidth: '250px',
            marginBottom: '1rem'
        },
        // Form kontrol stili
        formControl: {
            height: '48px',
            borderRadius: '4px',
            border: '1px solid #ced4da',
            padding: '10px 15px',
            fontSize: '16px',
            width: '100%'
        },
        // Giriş butonu stili
        btnInfo: {
            backgroundColor: '#00c3f7',
            borderColor: '#00c3f7',
            color: '#fff',
            fontWeight: 500,
            borderRadius: '4px',
            height: '48px',
            fontSize: '16px',
            width: '100%',
            border: 'none',
            cursor: 'pointer'
        },
        // Form grubu stili
        formGroup: {
            marginBottom: '1rem'
        }
    };

    return (
        <div style={styles.loginContainer}>
            <div style={styles.overlay}></div>
            <div style={styles.card}>
                <div style={{ textAlign: 'center', marginBottom: '1.5rem' }}>
                    <img src={logoDark} alt="MGZ24 Logo" style={styles.logo} />
                </div>

                <form onSubmit={handleSubmit}>
                    <div style={styles.formGroup}>
                        <input
                            type="email"
                            style={styles.formControl}
                            placeholder="Email adresiniz"
                            name="email"
                            value={formData.email}
                            onChange={handleChange}
                            required
                        />
                    </div>

                    <div style={styles.formGroup}>
                        <input
                            type="password"
                            style={styles.formControl}
                            placeholder="Parola"
                            name="password"
                            value={formData.password}
                            onChange={handleChange}
                            required
                        />
                    </div>

                    <div style={styles.formGroup}>
                        <input
                            type="password"
                            style={styles.formControl}
                            placeholder="Parolanızı tekrar yazın"
                            name="passwordConfirm"
                            value={formData.passwordConfirm}
                            onChange={handleChange}
                            required
                        />
                    </div>

                    <div style={styles.formGroup}>
                        <input
                            type="text"
                            style={styles.formControl}
                            placeholder="Firma"
                            name="company"
                            value={formData.company}
                            onChange={handleChange}
                            required
                        />
                    </div>

                    <div style={styles.formGroup}>
                        <input
                            type="text"
                            style={styles.formControl}
                            placeholder="Yetkili ad ve soyad"
                            name="authorizedPerson"
                            value={formData.authorizedPerson}
                            onChange={handleChange}
                            required
                        />
                    </div>

                    <div style={styles.formGroup}>
                        <input
                            type="text"
                            style={styles.formControl}
                            placeholder="Yetkili GSM no"
                            name="phoneNumber"
                            value={formData.phoneNumber}
                            onChange={handleChange}
                            required
                        />
                    </div>

                    <button style={styles.btnInfo} type="submit">Kaydı tamamla</button>

                    <div style={{ textAlign: 'center', margin: '1.5rem 0' }}>
                        <span>Hesabınız varsa buradan </span>
                        <Link to="/login" style={{ color: '#007f97', textDecoration: 'none' }}>
                            <strong>giriş</strong>
                        </Link>
                        <span> yapın</span>
                    </div>

                    <hr />

                    <div style={{ textAlign: 'center', color: '#6c757d' }}>
                        &copy; 2025 Inkatech Ölçüm Sistemleri
                    </div>
                </form>
            </div>
        </div>
    );
};

export default Register; 