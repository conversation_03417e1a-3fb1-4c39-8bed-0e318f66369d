// Authentication middleware for MGZ24 API
import jwt from 'jsonwebtoken';

const authMiddleware = (req, res, next) => {
    try {
        // Skip authentication for development - just pass through
        // In production, this should validate JWT tokens
        
        // Mock user for testing
        req.user = {
            id: 1,
            musteri_ID: 1,
            role: 'user'
        };
        
        next();
    } catch (error) {
        console.error('Auth middleware error:', error);
        return res.status(401).json({
            success: false,
            message: 'Authentication failed'
        });
    }
};

export default authMiddleware;