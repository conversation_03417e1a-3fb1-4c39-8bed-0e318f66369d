/**
 * JSONP ile API çağrısı
 * @param {string} url - API URL'i
 * @returns {Promise<Object>} API sonucu
 */
const jsonpRequest = (url) => {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    const callbackName = 'jsonpCallback_' + Math.random().toString(36).substr(2, 9);

    window[callbackName] = (data) => {
      resolve(data);
      document.head.removeChild(script);
      delete window[callbackName];
    };

    script.src = `${url}${url.includes('?') ? '&' : '?'}callback=${callbackName}`;
    script.onerror = () => {
      reject(new Error('JSONP request failed'));
      document.head.removeChild(script);
      delete window[callbackName];
    };

    document.head.appendChild(script);
  });
};

/**
 * XMLHttpRequest ile API çağrısı
 * @param {string} url - API URL'i
 * @returns {Promise<Object>} API sonucu
 */
const xhrRequest = (url) => {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    xhr.open('GET', url, true);
    xhr.setRequestHeader('Content-Type', 'application/json');
    xhr.setRequestHeader('Accept', 'application/json');

    xhr.onload = function () {
      if (xhr.status === 200) {
        try {
          const response = JSON.parse(xhr.responseText);
          resolve(response);
        } catch (e) {
          reject(new Error('JSON parse error'));
        }
      } else {
        reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
      }
    };

    xhr.onerror = function () {
      reject(new Error('Network error'));
    };

    xhr.send();
  });
};

/**
 * Gerçek zamanlı cihaz verisi çeker (ffl21.fun API)
 * @param {string} cihazID - Cihaz ID'si
 * @returns {Promise<Object>} Cihaz verisi
 */
export const fetchRealTimeDeviceData = async (cihazID) => {
  try {
    console.log('API çağrısı başlatılıyor:', cihazID);

    // CORS sorunu için basit çözüm - XMLHttpRequest kullan
    const data = await new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      xhr.open('GET', `https://ffl21.fun:3001/api/cihaz/${cihazID}`, true);
      xhr.setRequestHeader('Content-Type', 'application/json');
      xhr.setRequestHeader('Accept', 'application/json');

      xhr.onload = function () {
        if (xhr.status === 200) {
          try {
            const response = JSON.parse(xhr.responseText);
            resolve(response);
          } catch (e) {
            reject(new Error('JSON parse error'));
          }
        } else {
          reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
        }
      };

      xhr.onerror = function () {
        reject(new Error('Network error'));
      };

      xhr.send();
    });

    console.log('API verisi:', data);

    if (data.success) {
      return {
        success: true,
        data: {
          sicaklik: data.data.sonSensorler.sicaklik,
          nem: data.data.sonSensorler.nem,
          isik: data.data.sonSensorler.isik,
          pil_seviyesi: data.data.sonSensorler.pil,
          enlem: data.data.sonKonum.enlem,
          boylam: data.data.sonKonum.boylam,
          durum: data.data.cihazDurum.sonGuncellenme ? 'aktif' : 'pasif',
          son_guncelleme: data.data.cihazDurum.sonGuncellenme
        },
        source: 'external_api',
        timestamp: new Date().toISOString()
      };
    } else {
      return {
        success: false,
        error: 'API_ERROR',
        message: data.message || 'API hatası'
      };
    }
  } catch (error) {
    console.error('Gerçek zamanlı veri çekme hatası:', error);

    return {
      success: false,
      error: 'NETWORK_ERROR',
      message: 'Ağ bağlantı hatası: ' + error.message
    };
  }
};

/**
 * Cihaz detay bilgilerini çeker (ffl21.fun API)
 * @param {string} cihazID - Cihaz ID'si
 * @returns {Promise<Object>} Cihaz detay bilgileri
 */
export const fetchDeviceDetails = async (cihazID) => {
  try {
    const data = await xhrRequest(`https://ffl21.fun:3001/api/cihaz/${cihazID}`);

    return {
      success: true,
      data: data.data,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Cihaz detay çekme hatası:', error);

    return {
      success: false,
      error: 'NETWORK_ERROR',
      message: 'Ağ bağlantı hatası: ' + error.message
    };
  }
};

/**
 * Cihaz geçmişi verilerini çeker (son 100 data)
 * @param {string} cihazID - Cihaz ID'si
 * @param {number} limit - Veri sayısı (varsayılan 100)
 * @returns {Promise<Object>} Cihaz geçmişi
 */
export const fetchDeviceHistory = async (cihazID, limit = 100) => {
  try {
    const data = await xhrRequest(`https://ffl21.fun:3001/api/cihaz/${cihazID}/history?limit=${limit}`);

    if (data.success) {
      return {
        success: true,
        data: data.data.gecmisVeriler,
        pagination: data.data.pagination,
        timestamp: new Date().toISOString()
      };
    } else {
      return {
        success: false,
        error: 'API_ERROR',
        message: data.message || 'API hatası'
      };
    }
  } catch (error) {
    console.error('Cihaz geçmişi çekme hatası:', error);

    return {
      success: false,
      error: 'NETWORK_ERROR',
      message: 'Cihaz geçmişi alınamadı: ' + error.message
    };
  }
};

/**
 * Cihaz konum bilgilerini çeker
 * @param {string} cihazID - Cihaz ID'si
 * @param {number} limit - Konum sayısı (varsayılan 10)
 * @returns {Promise<Object>} Konum bilgileri
 */
export const fetchDeviceLocations = async (cihazID, limit = 10) => {
  try {
    const data = await xhrRequest(`https://ffl21.fun:3001/api/konum/${cihazID}?limit=${limit}`);

    if (data.success) {
      return {
        success: true,
        data: data.data.konumlar,
        timestamp: new Date().toISOString()
      };
    } else {
      return {
        success: false,
        error: 'API_ERROR',
        message: data.message || 'API hatası'
      };
    }
  } catch (error) {
    console.error('Konum bilgisi çekme hatası:', error);

    return {
      success: false,
      error: 'NETWORK_ERROR',
      message: 'Konum bilgileri alınamadı: ' + error.message
    };
  }
};

/**
 * Cihaz listesini çeker
 * @param {Object} filters - Filtreler (musteriId, aktif, krediVar)
 * @returns {Promise<Object>} Cihaz listesi
 */
export const fetchDeviceList = async (filters = {}) => {
  try {
    const params = new URLSearchParams(filters).toString();
    const response = await fetch(`https://ffl21.fun:3001/api/cihazlar?${params}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();

    return {
      success: true,
      data: data.data,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Cihaz listesi çekme hatası:', error);

    return {
      success: false,
      error: 'NETWORK_ERROR',
      message: 'Cihaz listesi alınamadı: ' + error.message
    };
  }
};

/**
 * Cihaz kredi satın alma
 * @param {string} cihazID - Cihaz ID'si
 * @param {number} krediGun - Kredi günü
 * @param {Object} odemeDetaylari - Ödeme detayları
 * @returns {Promise<Object>} Satın alma sonucu
 */
export const buyDeviceCredit = async (cihazID, krediGun, odemeDetaylari) => {
  try {
    const response = await fetch(`https://ffl21.fun:3001/api/cihaz-id/buy-credit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify({
        cihazId: cihazID,
        krediGun,
        odemeDetaylari
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();

    return {
      success: true,
      data: data,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Kredi satın alma hatası:', error);

    return {
      success: false,
      error: 'PAYMENT_ERROR',
      message: error.message || 'Kredi satın alma başarısız'
    };
  }
};

/**
 * Cihaz atama (admin)
 * @param {string} cihazID - Cihaz ID'si
 * @param {number} musteriID - Müşteri ID'si
 * @returns {Promise<Object>} Atama sonucu
 */
export const assignDevice = async (cihazID, musteriID) => {
  try {
    const response = await fetch(`https://ffl21.fun:3001/api/cihaz-id/assign`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify({
        cihazId: cihazID,
        musteriId: musteriID
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();

    return {
      success: true,
      data: data,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Cihaz atama hatası:', error);

    return {
      success: false,
      error: 'ASSIGNMENT_ERROR',
      message: error.message || 'Cihaz atama başarısız'
    };
  }
};

/**
 * Polling için gerçek zamanlı veri çekme
 * @param {string} cihazID - Cihaz ID'si
 * @param {Function} callback - Veri geldiğinde çalışacak callback
 * @param {number} interval - Polling interval (ms)
 * @returns {Function} Polling'i durduran function
 */
export const startRealTimePolling = (cihazID, callback, interval = 5000) => {
  let isPolling = true;

  const poll = async () => {
    if (!isPolling) return;

    try {
      const result = await fetchRealTimeDeviceData(cihazID);
      if (result.success) {
        callback(result.data);
      } else {
        console.warn('Polling hatası:', result.message);
      }
    } catch (error) {
      console.error('Polling hatası:', error);
    }

    if (isPolling) {
      setTimeout(poll, interval);
    }
  };

  // İlk veriyi hemen çek
  poll();

  // Polling'i durduran function döndür
  return () => {
    isPolling = false;
  };
};

export default {
  fetchRealTimeDeviceData,
  fetchDeviceDetails,
  fetchDeviceList,
  fetchDeviceHistory,
  buyDeviceCredit,
  assignDevice,
  startRealTimePolling
};