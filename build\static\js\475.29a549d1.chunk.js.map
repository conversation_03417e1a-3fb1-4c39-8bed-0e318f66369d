{"version": 3, "file": "static/js/475.29a549d1.chunk.js", "mappings": "gKASA,MAwGA,EAxGgBA,KACZ,MAAMC,GAAWC,EAAAA,EAAAA,MAaXC,EAAuB,UAVTC,MAChB,IAAK,IAADC,EAAAC,EACA,MAAMC,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C,OAAW,OAAJJ,QAAI,IAAJA,GAAU,QAANF,EAAJE,EAAMA,YAAI,IAAAF,OAAN,EAAJA,EAAYO,QAAY,OAAJL,QAAI,IAAJA,GAAU,QAAND,EAAJC,EAAMA,YAAI,IAAAD,OAAN,EAAJA,EAAYO,QAAS,MACpD,CAAE,MAAAC,GACE,MAAO,MACX,GAGaV,GAGjB,OACIW,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC1DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6BAA6BG,SAAS,KAAKC,GAAG,UAAU,kBAAgB,UAASH,SAAA,EAC5FC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,EAC7BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,kBAAkBI,GAAG,UAASH,SAAC,gBAC7CF,EAAAA,EAAAA,KAAA,UAAQM,KAAK,SAASL,UAAU,YAAY,kBAAgB,YAAY,iBAAe,WAAW,aAAW,cAEjHE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mEAAkEC,SAAA,EAC7EF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,kCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,MAAtBtB,EAASuB,SAAmB,SAAW,IAAMC,GAAG,IAAGR,SAAA,EAC5GF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMC,EAAAA,MAAW,0BAG1Cb,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,SAAtBtB,EAASuB,SAAsB,SAAW,IAAMC,GAAG,OAAMR,SAAA,EAClHF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAME,EAAAA,MAAgB,mCAG/Cd,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,sBAAtBtB,EAASuB,SAAmC,SAAW,IAAMC,GAAG,oBAAmBR,SAAA,EAC5IF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMG,EAAAA,MAAY,8BAG3Cf,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMI,EAAAA,MAAmB,sCAKtDhB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wBACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,CAEpCd,IACGY,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMK,EAAAA,MAAa,0BAIhDjB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,mBAAtBtB,EAASuB,SAAgC,SAAW,IAAMC,GAAG,iBAAgBR,SAAA,EACtIF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMM,EAAAA,MAAU,uBAK7ClB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,uCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMO,EAAAA,MAAU,qCAGzCnB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMQ,EAAAA,MAAgB,6CAKnDpB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMS,EAAAA,MAAgB,qBAG/CrB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,cAAtBtB,EAASuB,SAA2B,SAAW,IAAMC,GAAG,YAAWR,SAAA,EAC5HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMU,EAAAA,MAAe,4BAKlDtB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wBAG1I,C,sDC3Gd,MAQA,EAReqB,KAEPvB,EAAAA,EAAAA,KAAA,UAAQC,UAAU,kBAAiBC,UAC/BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,cAAaC,SAAC,iD,qGCEvC,MA6GA,EA7GesB,KACX,MAAMC,GAAWC,EAAAA,EAAAA,OACVC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,YAClCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAGvCG,EAAAA,EAAAA,YAAU,KACgBC,WAClB,IAAK,IAADC,EAAAC,EAAAC,EAAAC,EAEA,MAAMC,EAAa7C,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C2C,GAAmB,OAAVD,QAAU,IAAVA,GAAgB,QAANJ,EAAVI,EAAY9C,YAAI,IAAA0C,OAAN,EAAVA,EAAkBM,cAAwB,OAAVF,QAAU,IAAVA,GAAgB,QAANH,EAAVG,EAAY9C,YAAI,IAAA2C,OAAN,EAAVA,EAAkB9B,IAC3DsB,GAAqB,OAAVW,QAAU,IAAVA,GAAgB,QAANF,EAAVE,EAAY9C,YAAI,IAAA4C,OAAN,EAAVA,EAAkBK,QAAkB,OAAVH,QAAU,IAAVA,GAAgB,QAAND,EAAVC,EAAY9C,YAAI,IAAA6C,OAAN,EAAVA,EAAkBK,aAE7D,IAAKH,EAID,OAHAI,QAAQC,KAAK,kCACbhB,EAAY,gBACZG,GAAW,GAKf,GAAIJ,EAGA,OAFAC,EAAYD,QACZI,GAAW,GAKf,IACI,MAAMc,QAAiBC,EAAAA,GAAiBC,aAAaR,GAGjDM,GAAYA,EAASH,cACrBd,EAAYiB,EAASH,aAGP,OAAVJ,QAAU,IAAVA,GAAAA,EAAY9C,OACZ8C,EAAW9C,KAAKiD,KAAOI,EAASH,YAChC/C,aAAaqD,QAAQ,OAAQvD,KAAKwD,UAAUX,KAGxD,CAAE,MAAOY,GACLP,QAAQC,KAAK,gGAEbhB,EAAYD,GAAY,sBAC5B,CACJ,CAAE,MAAOwB,GACLR,QAAQQ,MAAM,0DAAuCA,GAErDvB,EAAY,sBAChB,CAAC,QACGG,GAAW,EACf,GAGJqB,EAAe,GAChB,IAWH,OACIjD,EAAAA,EAAAA,MAAA,UAAQF,UAAU,0DAA0D,gBAAc,OAAMC,SAAA,EAC5FF,EAAAA,EAAAA,KAACO,EAAAA,GAAI,CAACN,UAAU,gEAAgES,GAAG,IAAGR,UAClFF,EAAAA,EAAAA,KAAA,OAAKqD,I,60RAAeC,IAAI,aAAaC,OAAO,UAEhDpD,EAAAA,EAAAA,MAAA,MAAIF,UAAU,yCAAwCC,SAAA,EAClDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8CAA6CC,UACvDC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,aAAYC,SAAA,EACxBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMO,EAAAA,IAAQlB,UAAU,SACxC6B,EAAU,mBAAkBH,QAGrC3B,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4BAA2BC,UACrCF,EAAAA,EAAAA,KAAA,UACIwD,QAtBCC,KAEjB9D,aAAa+D,WAAW,QAGxBjC,EAAS,SAAS,EAkBFxB,UAAU,gBACV,iBAAe,UACf,oBAAkB,OAClB,gBAAc,+BAAYC,UAE1BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM+C,EAAAA,WAG/B3D,EAAAA,EAAAA,KAAA,MAAIC,UAAU,iCAAgCC,UAC1CF,EAAAA,EAAAA,KAAA,UACIC,UAAU,gBACVK,KAAK,SACL,iBAAe,YACf,iBAAe,WACf,gBAAc,UACd,gBAAc,QACd,aAAW,kBAAcJ,UAEzBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMgD,EAAAA,eAI9B,C,gICxGjB,MAoYA,EApYiBC,KACb,MAAOC,EAAUC,IAAelC,EAAAA,EAAAA,UAAS,KAClCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCsB,EAAOa,IAAYnC,EAAAA,EAAAA,UAAS,OAC5BoC,EAAiBC,IAAsBrC,EAAAA,EAAAA,UAAS,OAChDsC,EAAWC,IAAgBvC,EAAAA,EAAAA,WAAS,IACpCwC,EAAcC,IAAmBzC,EAAAA,EAAAA,UAAS,QAC1C0C,EAAaC,IAAkB3C,EAAAA,EAAAA,UAAS,QAG/CG,EAAAA,EAAAA,YAAU,KACgBC,WAClB,IACIF,GAAW,GACXiC,EAAS,MAGT,MAAMxE,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SACvC2C,GAAa,OAAJ/C,QAAI,IAAJA,OAAI,EAAJA,EAAMgD,aAAc,EAK7BiC,SAHiBC,EAAAA,EAAMC,IAAI,yBAADnE,OAA0B+B,KAG5BqC,KAAKC,KAAIC,IAAO,CAC1CzE,GAAIyE,EAAQzE,GACZ0E,UAAU,OAADvE,OAASsE,EAAQE,KAAKC,MAAM,KAAK,GAAE,KAAAzE,OAAIsE,EAAQzE,GAAG6E,WAAWC,SAAS,EAAG,MAClFH,KAAMF,EAAQE,KACdI,QAAS,IAAIC,KAAK,IAAIA,KAAKP,EAAQE,MAAMM,UAAY,QAA0BC,cAAcN,MAAM,KAAK,GACxGO,OAAQV,EAAQW,UAChBC,IAAyB,IAApBZ,EAAQW,UACbE,MAAOb,EAAQc,UACfC,OAA2B,cAAnBf,EAAQe,OAAyB,OAAS,UAClDC,YAAahB,EAAQiB,YACrBC,YAAa,eACbC,YAAgC,cAAnBnB,EAAQe,OAAyBf,EAAQE,KAAO,KAC7DkB,cAAe,uBAGnBnC,EAAYU,GACZ1C,GAAW,EACf,CAAE,MAAOoB,GACLR,QAAQQ,MAAM,4CAAmCA,GACjDa,EAAS,oDAA8Cb,EAAMgD,SAC7DpE,GAAW,EACf,GAGJqE,EAAe,GAChB,IAGH,MAMMC,EAAkBR,IACpB,OAAQA,GACJ,IAAK,OACD,MAAO,UACX,IAAK,UACD,MAAO,UACX,IAAK,UACD,MAAO,SACX,QACI,MAAO,YACf,EAIES,EAAiBT,IACnB,OAAQA,GACJ,IAAK,OACD,MAAO,YACX,IAAK,UACD,MAAO,WACX,IAAK,UACD,MAAO,kBACX,QACI,MAAO,aACf,EAIEU,EAAsBA,IACjBzC,EAAS0C,QAAOC,IACnB,MAAMC,EAA+B,QAAjBrC,GAA0BoC,EAAQZ,SAAWxB,EAC3DsC,EAA6B,QAAhBpC,GAAyBkC,EAAQzB,KAAK4B,UAAU,EAAG,KAAOrC,EAC7E,OAAOmC,GAAeC,CAAU,IAelCE,EAVkBC,MACpB,MAAMC,EAAWR,IACjB,MAAO,CACHZ,MAAOoB,EAASC,QAAO,CAACC,EAAKR,IAAYQ,EAAMR,EAAQd,OAAO,GAC9DuB,KAAMH,EAASP,QAAOW,GAAsB,SAAfA,EAAItB,SAAmBmB,QAAO,CAACC,EAAKR,IAAYQ,EAAMR,EAAQd,OAAO,GAClGyB,QAASL,EAASP,QAAOW,GAAsB,YAAfA,EAAItB,SAAsBmB,QAAO,CAACC,EAAKR,IAAYQ,EAAMR,EAAQd,OAAO,GACxG0B,QAASN,EAASP,QAAOW,GAAsB,YAAfA,EAAItB,SAAsBmB,QAAO,CAACC,EAAKR,IAAYQ,EAAMR,EAAQd,OAAO,GAC3G,EAGUmB,GAEf,OACI3G,EAAAA,EAAAA,MAAAmH,EAAAA,SAAA,CAAApH,SAAA,EACIF,EAAAA,EAAAA,KAACwB,EAAAA,EAAM,KACPxB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAChBF,EAAAA,EAAAA,KAACf,EAAAA,EAAO,KAGRkB,EAAAA,EAAAA,MAAA,QAAMF,UAAU,iFAAgFC,SAAA,EAC5FF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAAmCC,UAC9CF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,eAAcC,SAAC,uBAGhC4B,GACG9B,EAAAA,EAAAA,KAACuH,EAAAA,EAAc,CACXC,KAAK,KACLC,QAAQ,UACRtB,QAAQ,mCACRuB,UAAU,IAEdvE,GACAnD,EAAAA,EAAAA,KAAC2H,EAAAA,EAAY,CACTxB,QAAShD,EACTsE,QAAQ,SACRG,MAAM,8BACNC,aAAa,EACbC,UAAWA,IAAM9D,EAAS,IAAI9D,UAE9BF,EAAAA,EAAAA,KAAA,UAAQC,UAAU,8BAA8BuD,QAASA,IAAMuE,OAAO7I,SAAS8I,SAAS9H,SAAC,oBAK7FF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,MAAKC,UAChBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,SAAQC,SAAA,EAEnBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACrBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yBAAwBC,UACnCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sBAAqBC,UAChCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,0BAAyBC,SAAC,kBACxCC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,eAAcC,SAAA,CAAE2G,EAAOlB,MAAMsC,QAAQ,GAAG,qBAIlEjI,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yBAAwBC,UACnCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sBAAqBC,UAChCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,0BAAyBC,SAAC,eACxCC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,eAAcC,SAAA,CAAE2G,EAAOK,KAAKe,QAAQ,GAAG,qBAIjEjI,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yBAAwBC,UACnCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sBAAqBC,UAChCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,0BAAyBC,SAAC,cACxCC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,eAAcC,SAAA,CAAE2G,EAAOO,QAAQa,QAAQ,GAAG,qBAIpEjI,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yBAAwBC,UACnCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qBAAoBC,UAC/BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yBAAwBC,SAAC,qBACvCC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,cAAaC,SAAA,CAAE2G,EAAOQ,QAAQY,QAAQ,GAAG,wBAOvE9H,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACtBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,cAAaC,UACxBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,OAAMC,SAAC,iBAEzBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACtBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAChBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACrBF,EAAAA,EAAAA,KAAA,SAAOkI,QAAQ,eAAejI,UAAU,aAAYC,SAAC,WACrDC,EAAAA,EAAAA,MAAA,UACIE,GAAG,eACHJ,UAAU,cACVkI,MAAO9D,EACP+D,SAAWC,GAAM/D,EAAgB+D,EAAEC,OAAOH,OAAOjI,SAAA,EAEjDF,EAAAA,EAAAA,KAAA,UAAQmI,MAAM,MAAKjI,SAAC,gBACpBF,EAAAA,EAAAA,KAAA,UAAQmI,MAAM,OAAMjI,SAAC,eACrBF,EAAAA,EAAAA,KAAA,UAAQmI,MAAM,UAASjI,SAAC,cACxBF,EAAAA,EAAAA,KAAA,UAAQmI,MAAM,UAASjI,SAAC,2BAGhCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACrBF,EAAAA,EAAAA,KAAA,SAAOkI,QAAQ,cAAcjI,UAAU,aAAYC,SAAC,QACpDC,EAAAA,EAAAA,MAAA,UACIE,GAAG,cACHJ,UAAU,cACVkI,MAAO5D,EACP6D,SAAWC,GAAM7D,EAAe6D,EAAEC,OAAOH,OAAOjI,SAAA,EAEhDF,EAAAA,EAAAA,KAAA,UAAQmI,MAAM,MAAKjI,SAAC,gBACpBF,EAAAA,EAAAA,KAAA,UAAQmI,MAAM,UAASjI,SAAC,eACxBF,EAAAA,EAAAA,KAAA,UAAQmI,MAAM,UAASjI,SAAC,qBACxBF,EAAAA,EAAAA,KAAA,UAAQmI,MAAM,UAASjI,SAAC,eACxBF,EAAAA,EAAAA,KAAA,UAAQmI,MAAM,UAASjI,SAAC,4BAGhCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kCAAiCC,UAC5CF,EAAAA,EAAAA,KAAA,UACIC,UAAU,oBACVuD,QAASA,KACLc,EAAgB,OAChBE,EAAe,MAAM,EACvBtE,SACL,kCASjBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EACxCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uBAAsBC,UACjCC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,OAAMC,SAAA,EAChBF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6BAA+B,kBAE5CD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,wBAAuBC,SAAEqG,IAAsBgC,eAGvEvI,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,SACQ,IAAjCqG,IAAsBgC,QACnBvI,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC5BF,EAAAA,EAAAA,KAAC2H,EAAAA,EAAY,CACTxB,QAAQ,sDACRsB,QAAQ,OACRe,UAAU,OAIlBxI,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,UAC7BC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,uCAAsCC,SAAA,EACnDF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,aAAYC,UACzBC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACIF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,eACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,WACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,iBACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,WACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,SACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,YACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,WACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,uBACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,6BAGZF,EAAAA,EAAAA,KAAA,SAAAE,SACKqG,IAAsB1B,KAAK4B,IACxBtG,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACIF,EAAAA,EAAAA,KAAA,MAAAE,UACIF,EAAAA,EAAAA,KAAA,UAAAE,SAASuG,EAAQ1B,eAErB/E,EAAAA,EAAAA,KAAA,MAAAE,SAAKuG,EAAQzB,QACbhF,EAAAA,EAAAA,KAAA,MAAAE,SAAKuG,EAAQrB,WACbjF,EAAAA,EAAAA,MAAA,MAAAD,SAAA,CAAKuG,EAAQjB,OAAOyC,QAAQ,GAAG,cAC/B9H,EAAAA,EAAAA,MAAA,MAAAD,SAAA,CAAKuG,EAAQf,IAAIuC,QAAQ,GAAG,cAC5BjI,EAAAA,EAAAA,KAAA,MAAAE,UAAIC,EAAAA,EAAAA,MAAA,UAAAD,SAAA,CAASuG,EAAQd,MAAMsC,QAAQ,GAAG,gBACtCjI,EAAAA,EAAAA,KAAA,MAAAE,UACIF,EAAAA,EAAAA,KAAA,QAAMC,UAAS,YAAAO,OAAc6F,EAAeI,EAAQZ,SAAU3F,SACzDoG,EAAcG,EAAQZ,aAG/B7F,EAAAA,EAAAA,KAAA,MAAAE,SAAKuG,EAAQT,eACbhG,EAAAA,EAAAA,KAAA,MAAAE,UACIC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAYJ,KAAK,QAAOK,SAAA,EACnCF,EAAAA,EAAAA,KAAA,UACIC,UAAU,iCACVuD,QAASA,IAxO1DiD,KACvBvC,EAAmBuC,GACnBrC,GAAa,EAAK,EAsOqEqE,CAAkBhC,GACjCmB,MAAM,2BAAkB1H,UAExBF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kBAEjBD,EAAAA,EAAAA,KAAA,UACIC,UAAU,iCACV2H,MAAM,iBAAW1H,UAEjBF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6BA5BpBwG,EAAQpG,uBA6CxD8D,GAAaF,IACVjE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0BAA0BG,SAAS,KAAKsI,MAAO,CAAEC,gBAAiB,mBAAoBzI,UACjGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wBAAuBC,UAClCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeC,SAAA,EAC1BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,eAAcC,SAAA,EACzBC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,cAAaC,SAAA,CAAC,wBAAiB+D,EAAgBc,cAC7D/E,EAAAA,EAAAA,KAAA,UACIM,KAAK,SACLL,UAAU,YACVuD,QAASA,IAAMY,GAAa,SAGpCjE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYC,SAAA,EACvBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAChBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACrBF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,sBACJC,EAAAA,EAAAA,MAAA,KAAAD,SAAA,EAAGF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,eAAmB,IAAE+D,EAAgBc,cAChD5E,EAAAA,EAAAA,MAAA,KAAAD,SAAA,EAAGF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,WAAe,IAAE+D,EAAgBe,SAC5C7E,EAAAA,EAAAA,MAAA,KAAAD,SAAA,EAAGF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,iBAAqB,IAAE+D,EAAgBmB,YAClDjF,EAAAA,EAAAA,MAAA,KAAAD,SAAA,EAAGF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,YACPF,EAAAA,EAAAA,KAAA,QAAMC,UAAS,YAAAO,OAAc6F,EAAepC,EAAgB4B,QAAO,SAAQ3F,SACtEoG,EAAcrC,EAAgB4B,iBAI3C1F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACrBF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,uBACwB,SAA3B+D,EAAgB4B,QACb1F,EAAAA,EAAAA,MAAAmH,EAAAA,SAAA,CAAApH,SAAA,EACIC,EAAAA,EAAAA,MAAA,KAAAD,SAAA,EAAGF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,qBAAsB,IAAE+D,EAAgBgC,gBACnD9F,EAAAA,EAAAA,MAAA,KAAAD,SAAA,EAAGF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,yBAAuB,IAAE+D,EAAgBiC,qBAGxDlG,EAAAA,EAAAA,KAAA,KAAGC,UAAU,aAAYC,SAAC,uDAItCF,EAAAA,EAAAA,KAAA,UACAA,EAAAA,EAAAA,KAAA,MAAAE,SAAI,2BACJC,EAAAA,EAAAA,MAAA,KAAAD,SAAA,EAAGF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,uBAAqB,IAAE+D,EAAgB+B,gBAClD7F,EAAAA,EAAAA,MAAA,KAAAD,SAAA,EAAGF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,sBAAkB,IAAE+D,EAAgB6B,gBAC/C9F,EAAAA,EAAAA,KAAA,UACAG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAChBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACrBF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,4BAERC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAC9BC,EAAAA,EAAAA,MAAA,KAAAD,SAAA,EAAGF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,WAAe,IAAE+D,EAAgBuB,OAAOyC,QAAQ,GAAG,cAC9D9H,EAAAA,EAAAA,MAAA,KAAAD,SAAA,EAAGF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,eAAmB,IAAE+D,EAAgByB,IAAIuC,QAAQ,GAAG,cAC/DjI,EAAAA,EAAAA,KAAA,UACAG,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EAAIF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,YAAgB,IAAE+D,EAAgB0B,MAAMsC,QAAQ,GAAG,uBAI3E9H,EAAAA,EAAAA,MAAA,OAAKF,UAAU,eAAcC,SAAA,EACzBC,EAAAA,EAAAA,MAAA,UAAQG,KAAK,SAASL,UAAU,kBAAiBC,SAAA,EAC7CF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yBAA2B,qBAE5CD,EAAAA,EAAAA,KAAA,UACIM,KAAK,SACLL,UAAU,oBACVuD,QAASA,IAAMY,GAAa,GAAOlE,SACtC,wBAUzBF,EAAAA,EAAAA,KAACuB,EAAAA,EAAM,WAGhB,C", "sources": ["components/Sidebar.js", "components/Footer.js", "components/Header.js", "pages/Invoices.js"], "sourcesContent": ["import React from 'react';\r\nimport { Link, useLocation } from 'react-router-dom';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport {\r\n    faDolly, faFolderPlus, faAnchor, faFlag<PERSON>he<PERSON>ed,\r\n    faUser, faStreetView, faCreditCard, faFileLines,\r\n    faBell, faDesktop\r\n} from '@fortawesome/free-solid-svg-icons';\r\n\r\nconst Sidebar = () => {\r\n    const location = useLocation();\r\n    \r\n    // Get user role from localStorage\r\n    const getUserRole = () => {\r\n        try {\r\n            const user = JSON.parse(localStorage.getItem('user'));\r\n            return user?.user?.role || user?.user?.gorev || 'user';\r\n        } catch {\r\n            return 'user';\r\n        }\r\n    };\r\n    \r\n    const userRole = getUserRole();\r\n    const isAdmin = userRole === 'admin';\r\n\r\n    return (\r\n        <div className=\"sidebar col-md-4 col-lg-2 p-0 bg-light-subtle\">\r\n            <div className=\"offcanvas-md offcanvas-end\" tabIndex=\"-1\" id=\"yanMenu\" aria-labelledby=\"yanMenu\">\r\n                <div className=\"offcanvas-header\">\r\n                    <h5 className=\"offcanvas-title\" id=\"yanMenu\">MGZ24 Gold</h5>\r\n                    <button type=\"button\" className=\"btn-close\" data-bs-dismiss=\"offcanvas\" data-bs-target=\"#yanMenu\" aria-label=\"Kapat\"></button>\r\n                </div>\r\n                <div className=\"offcanvas-body d-md-flex flex-column p-0 pt-lg-3 overflow-y-auto\">\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Sevkiyat İşlemleri</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/' ? 'active' : ''}`} to=\"/\">\r\n                                <FontAwesomeIcon icon={faDolly} />Aktif Sevkiyatlar\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/add' ? 'active' : ''}`} to=\"/add\">\r\n                                <FontAwesomeIcon icon={faFolderPlus} />Yeni Sevkiyat Oluştur\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/inactive-devices' ? 'active' : ''}`} to=\"/inactive-devices\">\r\n                                <FontAwesomeIcon icon={faAnchor} />İnaktif Cihazlar\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/history' ? 'active' : ''}`} to=\"/history\">\r\n                                <FontAwesomeIcon icon={faFlagCheckered} />Geçmiş Sevkiyatlar\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Sistem Yönetimi</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        {/* Cihaz Yönetimi - Sadece admin kullanıcılarına göster */}\r\n                        {isAdmin && (\r\n                            <li className=\"nav-item\">\r\n                                <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/devices' ? 'active' : ''}`} to=\"/devices\">\r\n                                    <FontAwesomeIcon icon={faDesktop} />Cihaz Yönetimi\r\n                                </Link>\r\n                            </li>\r\n                        )}\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/notifications' ? 'active' : ''}`} to=\"/notifications\">\r\n                                <FontAwesomeIcon icon={faBell} />Bildirimler\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Kullanıcı Ayarları</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/profile' ? 'active' : ''}`} to=\"/profile\">\r\n                                <FontAwesomeIcon icon={faUser} />Kullanıcı Profilim\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/viewers' ? 'active' : ''}`} to=\"/viewers\">\r\n                                <FontAwesomeIcon icon={faStreetView} />İzleyici İşlemleri\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Ödeme ve Yapılandırma</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/payment' ? 'active' : ''}`} to=\"/payment\">\r\n                                <FontAwesomeIcon icon={faCreditCard} />Ödeme Yap\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/invoices' ? 'active' : ''}`} to=\"/invoices\">\r\n                                <FontAwesomeIcon icon={faFileLines} />Faturalarım\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-body-secondary text-uppercase\">Kredi Durumu</h6>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Sidebar;", "import React from 'react';\r\n\r\nconst Footer = () => {\r\n    return (\r\n        <footer className=\"py-5 border-top\">\r\n            <p className=\"text-center\">&copy;2025 Inkatech Ölçüm Sistemleri</p>\r\n        </footer>\r\n    );\r\n};\r\n\r\nexport default Footer; ", "import React, { useState, useEffect } from 'react';\r\nimport { Link, useNavigate } from 'react-router-dom';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { faRightFromBracket, faBars, faUser } from '@fortawesome/free-solid-svg-icons';\r\nimport logoDark from '../assets/img/logo.png';\r\nimport { kullaniciService } from '../api/dbService';\r\n\r\nconst Header = () => {\r\n    const navigate = useNavigate();\r\n    const [userName, setUserName] = useState('Misafir');\r\n    const [loading, setLoading] = useState(true);\r\n\r\n    // Kullanıcı bilgilerini veritabanından al\r\n    useEffect(() => {\r\n        const fetchUserData = async () => {\r\n            try {\r\n                // Local storage'dan kullanıcı bilgisini al\r\n                const storedUser = JSON.parse(localStorage.getItem('user'));\r\n                const userId = storedUser?.user?.musteri_ID || storedUser?.user?.id;\r\n                const userName = storedUser?.user?.name || storedUser?.user?.musteri_adi;\r\n\r\n                if (!userId) {\r\n                    console.warn('Oturum bilgisi bulunamadı');\r\n                    setUserName('Misafir');\r\n                    setLoading(false);\r\n                    return;\r\n                }\r\n\r\n                // Eğer localStorage'da isim varsa direkt kullan\r\n                if (userName) {\r\n                    setUserName(userName);\r\n                    setLoading(false);\r\n                    return;\r\n                }\r\n\r\n                // API'den kullanıcı bilgilerini getir (backend çalışıyorsa)\r\n                try {\r\n                    const userData = await kullaniciService.getKullanici(userId);\r\n\r\n                    // musteri_adi alanını kullan\r\n                    if (userData && userData.musteri_adi) {\r\n                        setUserName(userData.musteri_adi);\r\n\r\n                        // localStorage'ı güncelle\r\n                        if (storedUser?.user) {\r\n                            storedUser.user.name = userData.musteri_adi;\r\n                            localStorage.setItem('user', JSON.stringify(storedUser));\r\n                        }\r\n                    }\r\n                } catch (apiError) {\r\n                    console.warn('API\\'den kullanıcı bilgisi alınamadı, localStorage kullanılıyor');\r\n                    // API hatası durumunda localStorage'daki adı kullan\r\n                    setUserName(userName || 'Kullanıcı');\r\n                }\r\n            } catch (error) {\r\n                console.error('Kullanıcı bilgileri alınırken hata:', error);\r\n                // Hata durumunda varsayılan isim kullan\r\n                setUserName('Kullanıcı');\r\n            } finally {\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        fetchUserData();\r\n    }, []);\r\n\r\n    // Çıkış işlemi\r\n    const handleLogout = () => {\r\n        // Local storage'dan kullanıcı bilgilerini temizle\r\n        localStorage.removeItem('user');\r\n\r\n        // Login sayfasına yönlendir\r\n        navigate('/login');\r\n    };\r\n\r\n    return (\r\n        <header className=\"navbar sticky-top bg-darkblue flex-md-nowrap p-0 shadow\" data-bs-theme=\"dark\">\r\n            <Link className=\"navbar-brand col-md-4 col-lg-2 me-0 px-3 py-2 fs-6 text-white\" to=\"/\">\r\n                <img src={logoDark} alt=\"MGZ24 Logo\" height=\"40\" />\r\n            </Link>\r\n            <ul className=\"navbar-nav flex-row align-items-center\">\r\n                <li className=\"nav-item text-nowrap d-none d-md-block me-3\">\r\n                    <span className=\"text-white\">\r\n                        <FontAwesomeIcon icon={faUser} className=\"me-2\" />\r\n                        {loading ? 'Yükleniyor...' : userName}\r\n                    </span>\r\n                </li>\r\n                <li className=\"nav-item text-nowrap px-1\">\r\n                    <button\r\n                        onClick={handleLogout}\r\n                        className=\"nav-link px-3\"\r\n                        data-bs-toggle=\"tooltip\"\r\n                        data-bs-placement=\"left\"\r\n                        data-bs-title=\"Çıkış yap!\"\r\n                    >\r\n                        <FontAwesomeIcon icon={faRightFromBracket} />\r\n                    </button>\r\n                </li>\r\n                <li className=\"nav-item text-nowrap d-md-none\">\r\n                    <button\r\n                        className=\"nav-link px-3\"\r\n                        type=\"button\"\r\n                        data-bs-toggle=\"offcanvas\"\r\n                        data-bs-target=\"#yanMenu\"\r\n                        aria-controls=\"yanMenu\"\r\n                        aria-expanded=\"false\"\r\n                        aria-label=\"menu aç/kapa\"\r\n                    >\r\n                        <FontAwesomeIcon icon={faBars} />\r\n                    </button>\r\n                </li>\r\n            </ul>\r\n        </header>\r\n    );\r\n};\r\n\r\nexport default Header; ", "import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport Header from '../components/Header';\nimport Sidebar from '../components/Sidebar';\nimport Footer from '../components/Footer';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorMessage from '../components/ErrorMessage';\n\nconst Invoices = () => {\n    const [invoices, setInvoices] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState(null);\n    const [selectedInvoice, setSelectedInvoice] = useState(null);\n    const [showModal, setShowModal] = useState(false);\n    const [filterStatus, setFilterStatus] = useState('all');\n    const [filterMonth, setFilterMonth] = useState('all');\n\n    // Sayfanın yüklenmesi\n    useEffect(() => {\n        const fetchInvoices = async () => {\n            try {\n                setLoading(true);\n                setError(null);\n                \n                // Gerçek API çağrısı - user'ın fatura geçmişi\n                const user = JSON.parse(localStorage.getItem('user'));\n                const userId = user?.musteri_ID || 1;\n                \n                const response = await axios.get(`/api/payments/history/${userId}`);\n                \n                // API yanıtını invoice formatına çevir\n                const invoicesData = response.data.map(payment => ({\n                    id: payment.id,\n                    invoiceNo: `FAT-${payment.date.split('-')[0]}-${payment.id.toString().padStart(3, '0')}`,\n                    date: payment.date,\n                    dueDate: new Date(new Date(payment.date).getTime() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n                    amount: payment.amountEUR,\n                    tax: payment.amountEUR * 0.18,\n                    total: payment.amountTRY,\n                    status: payment.status === 'completed' ? 'paid' : 'pending',\n                    description: payment.packageName,\n                    serviceType: 'MGZ24 Hizmet',\n                    paymentDate: payment.status === 'completed' ? payment.date : null,\n                    paymentMethod: 'Kredi Kartı'\n                }));\n                \n                setInvoices(invoicesData);\n                setLoading(false);\n            } catch (error) {\n                console.error('Fatura verileri alınırken hata:', error);\n                setError('Fatura verileri yüklenirken hata oluştu: ' + error.message);\n                setLoading(false);\n            }\n        };\n\n        fetchInvoices();\n    }, []);\n\n    // Fatura detayını göster\n    const handleShowInvoice = (invoice) => {\n        setSelectedInvoice(invoice);\n        setShowModal(true);\n    };\n\n    // Fatura durum rengini al\n    const getStatusColor = (status) => {\n        switch (status) {\n            case 'paid':\n                return 'success';\n            case 'pending':\n                return 'warning';\n            case 'overdue':\n                return 'danger';\n            default:\n                return 'secondary';\n        }\n    };\n\n    // Fatura durum metnini al\n    const getStatusText = (status) => {\n        switch (status) {\n            case 'paid':\n                return 'Ödendi';\n            case 'pending':\n                return 'Bekliyor';\n            case 'overdue':\n                return 'Vadesi Geçti';\n            default:\n                return 'Bilinmiyor';\n        }\n    };\n\n    // Filtrelenmiş faturaları al\n    const getFilteredInvoices = () => {\n        return invoices.filter(invoice => {\n            const statusMatch = filterStatus === 'all' || invoice.status === filterStatus;\n            const monthMatch = filterMonth === 'all' || invoice.date.substring(0, 7) === filterMonth;\n            return statusMatch && monthMatch;\n        });\n    };\n\n    // Toplamları hesapla\n    const calculateTotals = () => {\n        const filtered = getFilteredInvoices();\n        return {\n            total: filtered.reduce((sum, invoice) => sum + invoice.total, 0),\n            paid: filtered.filter(inv => inv.status === 'paid').reduce((sum, invoice) => sum + invoice.total, 0),\n            pending: filtered.filter(inv => inv.status === 'pending').reduce((sum, invoice) => sum + invoice.total, 0),\n            overdue: filtered.filter(inv => inv.status === 'overdue').reduce((sum, invoice) => sum + invoice.total, 0)\n        };\n    };\n\n    const totals = calculateTotals();\n\n    return (\n        <>\n            <Header />\n            <div className=\"container-fluid\">\n                <div className=\"row\">\n                    <Sidebar />\n\n                    {/* main sütun */}\n                    <main className=\"col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white\">\n                        <div className=\"pt-3 pb-1 mt-3 mb-3 border-bottom\">\n                            <h1 className=\"h4 text-dark\">Faturalarım</h1>\n                        </div>\n\n                        {loading ? (\n                            <LoadingSpinner \n                                size=\"lg\" \n                                variant=\"primary\" \n                                message=\"Fatura verileri yükleniyor...\" \n                                centered={true}\n                            />\n                        ) : error ? (\n                            <ErrorMessage \n                                message={error} \n                                variant=\"danger\"\n                                title=\"Veri Yükleme Hatası\"\n                                dismissible={true}\n                                onDismiss={() => setError('')}\n                            >\n                                <button className=\"btn btn-primary btn-sm mt-2\" onClick={() => window.location.reload()}>\n                                    Yeniden Dene\n                                </button>\n                            </ErrorMessage>\n                        ) : (\n                            <div className=\"row\">\n                                <div className=\"col-12\">\n                                    {/* Özet Kartları */}\n                                    <div className=\"row mb-4\">\n                                        <div className=\"col-md-3 col-sm-6 mb-3\">\n                                            <div className=\"card border-primary\">\n                                                <div className=\"card-body text-center\">\n                                                    <h5 className=\"card-title text-primary\">Toplam Tutar</h5>\n                                                    <h4 className=\"text-primary\">{totals.total.toFixed(2)} ₺</h4>\n                                                </div>\n                                            </div>\n                                        </div>\n                                        <div className=\"col-md-3 col-sm-6 mb-3\">\n                                            <div className=\"card border-success\">\n                                                <div className=\"card-body text-center\">\n                                                    <h5 className=\"card-title text-success\">Ödenen</h5>\n                                                    <h4 className=\"text-success\">{totals.paid.toFixed(2)} ₺</h4>\n                                                </div>\n                                            </div>\n                                        </div>\n                                        <div className=\"col-md-3 col-sm-6 mb-3\">\n                                            <div className=\"card border-warning\">\n                                                <div className=\"card-body text-center\">\n                                                    <h5 className=\"card-title text-warning\">Bekleyen</h5>\n                                                    <h4 className=\"text-warning\">{totals.pending.toFixed(2)} ₺</h4>\n                                                </div>\n                                            </div>\n                                        </div>\n                                        <div className=\"col-md-3 col-sm-6 mb-3\">\n                                            <div className=\"card border-danger\">\n                                                <div className=\"card-body text-center\">\n                                                    <h5 className=\"card-title text-danger\">Vadesi Geçen</h5>\n                                                    <h4 className=\"text-danger\">{totals.overdue.toFixed(2)} ₺</h4>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </div>\n\n                                    {/* Filtreler */}\n                                    <div className=\"card mb-4\">\n                                        <div className=\"card-header\">\n                                            <h5 className=\"mb-0\">Filtreler</h5>\n                                        </div>\n                                        <div className=\"card-body\">\n                                            <div className=\"row\">\n                                                <div className=\"col-md-4\">\n                                                    <label htmlFor=\"statusFilter\" className=\"form-label\">Durum</label>\n                                                    <select\n                                                        id=\"statusFilter\"\n                                                        className=\"form-select\"\n                                                        value={filterStatus}\n                                                        onChange={(e) => setFilterStatus(e.target.value)}\n                                                    >\n                                                        <option value=\"all\">Tümü</option>\n                                                        <option value=\"paid\">Ödendi</option>\n                                                        <option value=\"pending\">Bekliyor</option>\n                                                        <option value=\"overdue\">Vadesi Geçti</option>\n                                                    </select>\n                                                </div>\n                                                <div className=\"col-md-4\">\n                                                    <label htmlFor=\"monthFilter\" className=\"form-label\">Ay</label>\n                                                    <select\n                                                        id=\"monthFilter\"\n                                                        className=\"form-select\"\n                                                        value={filterMonth}\n                                                        onChange={(e) => setFilterMonth(e.target.value)}\n                                                    >\n                                                        <option value=\"all\">Tümü</option>\n                                                        <option value=\"2024-03\">Mart 2024</option>\n                                                        <option value=\"2024-02\">Şubat 2024</option>\n                                                        <option value=\"2024-01\">Ocak 2024</option>\n                                                        <option value=\"2023-12\">Aralık 2023</option>\n                                                    </select>\n                                                </div>\n                                                <div className=\"col-md-4 d-flex align-items-end\">\n                                                    <button \n                                                        className=\"btn btn-secondary\"\n                                                        onClick={() => {\n                                                            setFilterStatus('all');\n                                                            setFilterMonth('all');\n                                                        }}\n                                                    >\n                                                        Filtreleri Temizle\n                                                    </button>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </div>\n\n                                    {/* Fatura Listesi */}\n                                    <div className=\"card border-tertiary-subtle\">\n                                        <div className=\"card-header bg-light\">\n                                            <h5 className=\"mb-0\">\n                                                <i className=\"fas fa-file-invoice me-2\"></i>\n                                                Fatura Listesi\n                                                <span className=\"badge bg-primary ms-2\">{getFilteredInvoices().length}</span>\n                                            </h5>\n                                        </div>\n                                        <div className=\"card-body p-0\">\n                                            {getFilteredInvoices().length === 0 ? (\n                                                <div className=\"p-4 text-center\">\n                                                    <ErrorMessage \n                                                        message=\"Seçilen kriterlere uygun fatura bulunamadı.\"\n                                                        variant=\"info\"\n                                                        showIcon={true}\n                                                    />\n                                                </div>\n                                            ) : (\n                                                <div className=\"table-responsive\">\n                                                    <table className=\"table table-hover table-striped mb-0\">\n                                                        <thead className=\"table-dark\">\n                                                            <tr>\n                                                                <th>Fatura No</th>\n                                                                <th>Tarih</th>\n                                                                <th>Vade Tarihi</th>\n                                                                <th>Tutar</th>\n                                                                <th>KDV</th>\n                                                                <th>Toplam</th>\n                                                                <th>Durum</th>\n                                                                <th>Hizmet Türü</th>\n                                                                <th>İşlemler</th>\n                                                            </tr>\n                                                        </thead>\n                                                        <tbody>\n                                                            {getFilteredInvoices().map((invoice) => (\n                                                                <tr key={invoice.id}>\n                                                                    <td>\n                                                                        <strong>{invoice.invoiceNo}</strong>\n                                                                    </td>\n                                                                    <td>{invoice.date}</td>\n                                                                    <td>{invoice.dueDate}</td>\n                                                                    <td>{invoice.amount.toFixed(2)} ₺</td>\n                                                                    <td>{invoice.tax.toFixed(2)} ₺</td>\n                                                                    <td><strong>{invoice.total.toFixed(2)} ₺</strong></td>\n                                                                    <td>\n                                                                        <span className={`badge bg-${getStatusColor(invoice.status)}`}>\n                                                                            {getStatusText(invoice.status)}\n                                                                        </span>\n                                                                    </td>\n                                                                    <td>{invoice.serviceType}</td>\n                                                                    <td>\n                                                                        <div className=\"btn-group\" role=\"group\">\n                                                                            <button\n                                                                                className=\"btn btn-sm btn-outline-primary\"\n                                                                                onClick={() => handleShowInvoice(invoice)}\n                                                                                title=\"Detayları Göster\"\n                                                                            >\n                                                                                <i className=\"fas fa-eye\"></i>\n                                                                            </button>\n                                                                            <button\n                                                                                className=\"btn btn-sm btn-outline-success\"\n                                                                                title=\"PDF İndir\"\n                                                                            >\n                                                                                <i className=\"fas fa-download\"></i>\n                                                                            </button>\n                                                                        </div>\n                                                                    </td>\n                                                                </tr>\n                                                            ))}\n                                                        </tbody>\n                                                    </table>\n                                                </div>\n                                            )}\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        )}\n\n                        {/* Fatura Detay Modal */}\n                        {showModal && selectedInvoice && (\n                            <div className=\"modal fade show d-block\" tabIndex=\"-1\" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>\n                                <div className=\"modal-dialog modal-lg\">\n                                    <div className=\"modal-content\">\n                                        <div className=\"modal-header\">\n                                            <h5 className=\"modal-title\">Fatura Detayı - {selectedInvoice.invoiceNo}</h5>\n                                            <button \n                                                type=\"button\" \n                                                className=\"btn-close\" \n                                                onClick={() => setShowModal(false)}\n                                            ></button>\n                                        </div>\n                                        <div className=\"modal-body\">\n                                            <div className=\"row\">\n                                                <div className=\"col-md-6\">\n                                                    <h6>Fatura Bilgileri</h6>\n                                                    <p><strong>Fatura No:</strong> {selectedInvoice.invoiceNo}</p>\n                                                    <p><strong>Tarih:</strong> {selectedInvoice.date}</p>\n                                                    <p><strong>Vade Tarihi:</strong> {selectedInvoice.dueDate}</p>\n                                                    <p><strong>Durum:</strong> \n                                                        <span className={`badge bg-${getStatusColor(selectedInvoice.status)} ms-2`}>\n                                                            {getStatusText(selectedInvoice.status)}\n                                                        </span>\n                                                    </p>\n                                                </div>\n                                                <div className=\"col-md-6\">\n                                                    <h6>Ödeme Bilgileri</h6>\n                                                    {selectedInvoice.status === 'paid' ? (\n                                                        <>\n                                                            <p><strong>Ödeme Tarihi:</strong> {selectedInvoice.paymentDate}</p>\n                                                            <p><strong>Ödeme Yöntemi:</strong> {selectedInvoice.paymentMethod}</p>\n                                                        </>\n                                                    ) : (\n                                                        <p className=\"text-muted\">Henüz ödeme yapılmamış</p>\n                                                    )}\n                                                </div>\n                                            </div>\n                                            <hr />\n                                            <h6>Hizmet Detayları</h6>\n                                            <p><strong>Hizmet Türü:</strong> {selectedInvoice.serviceType}</p>\n                                            <p><strong>Açıklama:</strong> {selectedInvoice.description}</p>\n                                            <hr />\n                                            <div className=\"row\">\n                                                <div className=\"col-md-8\">\n                                                    <h6>Tutar Detayları</h6>\n                                                </div>\n                                                <div className=\"col-md-4 text-end\">\n                                                    <p><strong>Tutar:</strong> {selectedInvoice.amount.toFixed(2)} ₺</p>\n                                                    <p><strong>KDV (%18):</strong> {selectedInvoice.tax.toFixed(2)} ₺</p>\n                                                    <hr />\n                                                    <h5><strong>Toplam:</strong> {selectedInvoice.total.toFixed(2)} ₺</h5>\n                                                </div>\n                                            </div>\n                                        </div>\n                                        <div className=\"modal-footer\">\n                                            <button type=\"button\" className=\"btn btn-success\">\n                                                <i className=\"fas fa-download me-2\"></i>PDF İndir\n                                            </button>\n                                            <button \n                                                type=\"button\" \n                                                className=\"btn btn-secondary\" \n                                                onClick={() => setShowModal(false)}\n                                            >\n                                                Kapat\n                                            </button>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        )}\n                    </main>\n                    \n                    <Footer />\n                </div>\n            </div>\n        </>\n    );\n};\n\nexport default Invoices;"], "names": ["Sidebar", "location", "useLocation", "isAdmin", "getUserRole", "_user$user", "_user$user2", "user", "JSON", "parse", "localStorage", "getItem", "role", "gorev", "_unused", "_jsx", "className", "children", "_jsxs", "tabIndex", "id", "type", "Link", "concat", "pathname", "to", "FontAwesomeIcon", "icon", "faDolly", "faFolderPlus", "faAnchor", "faFlag<PERSON><PERSON><PERSON><PERSON>", "faDesktop", "faBell", "faUser", "faStreetView", "faCreditCard", "faFileLines", "Footer", "Header", "navigate", "useNavigate", "userName", "setUserName", "useState", "loading", "setLoading", "useEffect", "async", "_storedUser$user", "_storedUser$user2", "_storedUser$user3", "_storedUser$user4", "storedUser", "userId", "musteri_ID", "name", "musteri_adi", "console", "warn", "userData", "kullaniciService", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setItem", "stringify", "apiError", "error", "fetchUserData", "src", "alt", "height", "onClick", "handleLogout", "removeItem", "faRightFromBracket", "faBars", "Invoices", "invoices", "setInvoices", "setError", "selectedInvoice", "setSelectedInvoice", "showModal", "setShowModal", "filterStatus", "setFilterStatus", "filterMonth", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "invoicesData", "axios", "get", "data", "map", "payment", "invoiceNo", "date", "split", "toString", "padStart", "dueDate", "Date", "getTime", "toISOString", "amount", "amountEUR", "tax", "total", "amountTRY", "status", "description", "packageName", "serviceType", "paymentDate", "paymentMethod", "message", "fetchInvoices", "getStatusColor", "getStatusText", "getFilteredInvoices", "filter", "invoice", "statusMatch", "monthMatch", "substring", "totals", "calculateTotals", "filtered", "reduce", "sum", "paid", "inv", "pending", "overdue", "_Fragment", "LoadingSpinner", "size", "variant", "centered", "ErrorMessage", "title", "dismissible", "on<PERSON><PERSON><PERSON>", "window", "reload", "toFixed", "htmlFor", "value", "onChange", "e", "target", "length", "showIcon", "handleShowInvoice", "style", "backgroundColor"], "sourceRoot": ""}