{"version": 3, "file": "static/js/731.8a80c8d8.chunk.js", "mappings": "gKASA,MAwGA,EAxGgBA,KACZ,MAAMC,GAAWC,EAAAA,EAAAA,MAaXC,EAAuB,UAVTC,MAChB,IAAK,IAADC,EAAAC,EACA,MAAMC,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C,OAAW,OAAJJ,QAAI,IAAJA,GAAU,QAANF,EAAJE,EAAMA,YAAI,IAAAF,OAAN,EAAJA,EAAYO,QAAY,OAAJL,QAAI,IAAJA,GAAU,QAAND,EAAJC,EAAMA,YAAI,IAAAD,OAAN,EAAJA,EAAYO,QAAS,MACpD,CAAE,MAAAC,GACE,MAAO,MACX,GAGaV,GAGjB,OACIW,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC1DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6BAA6BG,SAAS,KAAKC,GAAG,UAAU,kBAAgB,UAASH,SAAA,EAC5FC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,EAC7BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,kBAAkBI,GAAG,UAASH,SAAC,gBAC7CF,EAAAA,EAAAA,KAAA,UAAQM,KAAK,SAASL,UAAU,YAAY,kBAAgB,YAAY,iBAAe,WAAW,aAAW,cAEjHE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mEAAkEC,SAAA,EAC7EF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,kCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,MAAtBtB,EAASuB,SAAmB,SAAW,IAAMC,GAAG,IAAGR,SAAA,EAC5GF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMC,EAAAA,MAAW,0BAG1Cb,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,SAAtBtB,EAASuB,SAAsB,SAAW,IAAMC,GAAG,OAAMR,SAAA,EAClHF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAME,EAAAA,MAAgB,mCAG/Cd,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,sBAAtBtB,EAASuB,SAAmC,SAAW,IAAMC,GAAG,oBAAmBR,SAAA,EAC5IF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMG,EAAAA,MAAY,8BAG3Cf,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMI,EAAAA,MAAmB,sCAKtDhB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wBACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,CAEpCd,IACGY,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMK,EAAAA,MAAa,0BAIhDjB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,mBAAtBtB,EAASuB,SAAgC,SAAW,IAAMC,GAAG,iBAAgBR,SAAA,EACtIF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMM,EAAAA,MAAU,uBAK7ClB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,uCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMO,EAAAA,MAAU,qCAGzCnB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMQ,EAAAA,MAAgB,6CAKnDpB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMS,EAAAA,MAAgB,qBAG/CrB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,cAAtBtB,EAASuB,SAA2B,SAAW,IAAMC,GAAG,YAAWR,SAAA,EAC5HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMU,EAAAA,MAAe,4BAKlDtB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wBAG1I,C,sDC3Gd,MAQA,EAReqB,KAEPvB,EAAAA,EAAAA,KAAA,UAAQC,UAAU,kBAAiBC,UAC/BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,cAAaC,SAAC,iD,qGCEvC,MA6GA,EA7GesB,KACX,MAAMC,GAAWC,EAAAA,EAAAA,OACVC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,YAClCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAGvCG,EAAAA,EAAAA,YAAU,KACgBC,WAClB,IAAK,IAADC,EAAAC,EAAAC,EAAAC,EAEA,MAAMC,EAAa7C,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C2C,GAAmB,OAAVD,QAAU,IAAVA,GAAgB,QAANJ,EAAVI,EAAY9C,YAAI,IAAA0C,OAAN,EAAVA,EAAkBM,cAAwB,OAAVF,QAAU,IAAVA,GAAgB,QAANH,EAAVG,EAAY9C,YAAI,IAAA2C,OAAN,EAAVA,EAAkB9B,IAC3DsB,GAAqB,OAAVW,QAAU,IAAVA,GAAgB,QAANF,EAAVE,EAAY9C,YAAI,IAAA4C,OAAN,EAAVA,EAAkBK,QAAkB,OAAVH,QAAU,IAAVA,GAAgB,QAAND,EAAVC,EAAY9C,YAAI,IAAA6C,OAAN,EAAVA,EAAkBK,aAE7D,IAAKH,EAID,OAHAI,QAAQC,KAAK,kCACbhB,EAAY,gBACZG,GAAW,GAKf,GAAIJ,EAGA,OAFAC,EAAYD,QACZI,GAAW,GAKf,IACI,MAAMc,QAAiBC,EAAAA,GAAiBC,aAAaR,GAGjDM,GAAYA,EAASH,cACrBd,EAAYiB,EAASH,aAGP,OAAVJ,QAAU,IAAVA,GAAAA,EAAY9C,OACZ8C,EAAW9C,KAAKiD,KAAOI,EAASH,YAChC/C,aAAaqD,QAAQ,OAAQvD,KAAKwD,UAAUX,KAGxD,CAAE,MAAOY,GACLP,QAAQC,KAAK,gGAEbhB,EAAYD,GAAY,sBAC5B,CACJ,CAAE,MAAOwB,GACLR,QAAQQ,MAAM,0DAAuCA,GAErDvB,EAAY,sBAChB,CAAC,QACGG,GAAW,EACf,GAGJqB,EAAe,GAChB,IAWH,OACIjD,EAAAA,EAAAA,MAAA,UAAQF,UAAU,0DAA0D,gBAAc,OAAMC,SAAA,EAC5FF,EAAAA,EAAAA,KAACO,EAAAA,GAAI,CAACN,UAAU,gEAAgES,GAAG,IAAGR,UAClFF,EAAAA,EAAAA,KAAA,OAAKqD,I,60RAAeC,IAAI,aAAaC,OAAO,UAEhDpD,EAAAA,EAAAA,MAAA,MAAIF,UAAU,yCAAwCC,SAAA,EAClDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8CAA6CC,UACvDC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,aAAYC,SAAA,EACxBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMO,EAAAA,IAAQlB,UAAU,SACxC6B,EAAU,mBAAkBH,QAGrC3B,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4BAA2BC,UACrCF,EAAAA,EAAAA,KAAA,UACIwD,QAtBCC,KAEjB9D,aAAa+D,WAAW,QAGxBjC,EAAS,SAAS,EAkBFxB,UAAU,gBACV,iBAAe,UACf,oBAAkB,OAClB,gBAAc,+BAAYC,UAE1BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM+C,EAAAA,WAG/B3D,EAAAA,EAAAA,KAAA,MAAIC,UAAU,iCAAgCC,UAC1CF,EAAAA,EAAAA,KAAA,UACIC,UAAU,gBACVK,KAAK,SACL,iBAAe,YACf,iBAAe,WACf,gBAAc,UACd,gBAAc,QACd,aAAW,kBAAcJ,UAEzBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMgD,EAAAA,eAI9B,C,8JC9FjB,MA2fA,EA3fqBC,IAOd,IAPe,OACpBC,EAAM,SACNC,EAAW,MAAK,UAChBC,EAAS,QACTC,EAAO,YACPC,EAAW,SACXC,GAAW,GACZN,EAEC,MAAOO,EAAUC,IAAexC,EAAAA,EAAAA,UAAS,CACvCyC,WAAY,GACZC,YAAa,GACbC,WAAY,GACZC,IAAK,GACLC,eAAgB,MAIXC,EAASC,IAAc/C,EAAAA,EAAAA,WAAS,IAChCgD,EAAYC,IAAiBjD,EAAAA,EAAAA,WAAS,IACtCsB,EAAO4B,IAAYlD,EAAAA,EAAAA,UAAS,KAC5BmD,EAAkBC,IAAuBpD,EAAAA,EAAAA,UAAS,CAAC,IAGnDqD,EAAaC,IAAkBtD,EAAAA,EAAAA,UAAS,CAC7CuD,WAAYC,CAAAA,SAAAA,aAAAA,WAAAA,GAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,yBAAYC,6BAA+B,gBACvDC,WAAYF,CAAAA,SAAAA,aAAAA,WAAAA,GAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,yBAAYG,6BAA+B,gBACvDC,OAAQJ,CAAAA,SAAAA,aAAAA,WAAAA,GAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,yBAAYK,yBAA2B,iBAC/CC,UAAUN,IA+CNO,EAAqBtB,IACzB,IAAIuB,EAAM,EACNC,GAAS,EAEb,IAAK,IAAIC,EAAIzB,EAAW0B,OAAS,EAAGD,GAAK,EAAGA,IAAK,CAC/C,IAAIE,EAAQC,SAAS5B,EAAW6B,OAAOJ,IAEnCD,IACFG,GAAS,EACLA,EAAQ,IACVA,GAAS,IAIbJ,GAAOI,EACPH,GAAUA,CACZ,CAEA,OAAOD,EAAM,KAAO,CAAC,EAuBjBO,EAAoBA,CAACC,EAAOC,KAChC,IAAIC,EAAiBD,EAErB,OAAQD,GACN,IAAK,aACHE,EAZoBD,IACTA,EAAME,QAAQ,MAAO,IACXA,QAAQ,iBAAkB,OAU9BC,CAAiBH,GAClC,MACF,IAAK,cACHC,EAAiBD,EAAME,QAAQ,MAAO,IAAIE,MAAM,EAAG,GAC/CR,SAASK,GAAkB,KAAIA,EAAiB,MACpD,MACF,IAAK,aACHA,EAAiBD,EAAME,QAAQ,MAAO,IAAIE,MAAM,EAAG,GACnD,MACF,IAAK,MACHH,EAAiBD,EAAME,QAAQ,MAAO,IAAIE,MAAM,EAAG,GACnD,MACF,IAAK,iBACHH,EAAiBD,EAAME,QAAQ,iFAA6B,IAAIG,cAIpEtC,GAAYuC,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP,CAACP,GAAQE,MAIPvB,EAAiBqB,IACnBpB,GAAoB2B,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACnBD,GAAI,IACP,CAACP,QAAQS,KAEb,EA8JF,OACE9G,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iBAAgBC,UAC7BC,EAAAA,EAAAA,MAAA,QAAM4G,SA5JW9E,UAGnB,GAFA+E,EAAMC,kBAEF9C,IAAYU,EAKhB,GAHAE,EAAS,IA9HUmC,MACnB,MAAMC,EAAS,CAAC,EAGV7C,EAAaF,EAASE,WAAWkC,QAAQ,MAAO,IAUtD,GATKlC,EAEMA,EAAW0B,OAAS,IAAM1B,EAAW0B,OAAS,GACvDmB,EAAO7C,WAAa,iCACVsB,EAAkBtB,KAC5B6C,EAAO7C,WAAa,kCAJpB6C,EAAO7C,WAAa,gCAQjBF,EAASG,aAAgBH,EAASI,WAEhC,CACL,MAAM4C,EAAc,IAAIC,KACL,IAAIA,KAAK,IAAOnB,SAAS9B,EAASI,YAAa0B,SAAS9B,EAASG,aAAe,IACjF6C,IAChBD,EAAOG,OAAS,6BAEpB,MAPEH,EAAOG,OAAS,iCAwBlB,OAdKlD,EAASK,KAEHL,EAASK,IAAIuB,OAAS,GAAK5B,EAASK,IAAIuB,OAAS,KAC1DmB,EAAO1C,IAAM,gCAFb0C,EAAO1C,IAAM,8BAMVL,EAASM,eAAe6C,OAElBnD,EAASM,eAAe6C,OAAOvB,OAAS,IACjDmB,EAAOzC,eAAiB,yCAFxByC,EAAOzC,eAAiB,kCAK1BO,EAAoBkC,GACkB,IAA/BK,OAAOC,KAAKN,GAAQnB,MAAY,EA0FlCkB,GAAL,CAKApC,GAAc,GAEd,IAEE,IAAI4C,EAEJ,GAAIxC,EAAYS,eAER,IAAIgC,SAAQC,GAAWC,WAAWD,EAAS,OAQ/CF,EAL6C,qBAA3CtD,EAASE,WAAWkC,QAAQ,MAAO,KAClB,QAAjBpC,EAASK,KACgB,OAAzBL,EAASG,aACe,OAAxBH,EAASI,WAEK,CACdsD,cAAe,CACbzH,GAAI,WAAa0H,KAAKC,SAASC,SAAS,IAAIC,UAAU,EAAG,IACzDC,OAAQ,YACRrE,OAAiB,IAATA,EACRC,SAAUA,EAASqE,cACnBC,QAASN,KAAKO,MAAMjB,KAAKkB,MAAQ,KACjCC,YAAY,GAADhI,QAAgB,OAAX0D,QAAW,IAAXA,OAAW,EAAXA,EAAazB,OAAQ,eAAc,OAAAjC,OAAMsD,EAAM,KAAAtD,OAAIuD,KAKvD,CACd+D,cAAe,CACbzH,GAAI,WAAa0H,KAAKC,SAASC,SAAS,IAAIC,UAAU,EAAG,IACzDC,OAAQ,YACRrE,OAAiB,IAATA,EACRC,SAAUA,EAASqE,cACnBC,QAASN,KAAKO,MAAMjB,KAAKkB,MAAQ,KACjCC,YAAY,GAADhI,QAAgB,OAAX0D,QAAW,IAAXA,OAAW,EAAXA,EAAazB,OAAQ,eAAc,OAAAjC,OAAMsD,EAAM,KAAAtD,OAAIuD,UAMzE,IACE,MAAM0E,QAA8BC,MAAMxD,EAAYO,OAAS,yBAA0B,CACvFkD,OAAQ,OACRC,QAAS,CACP,eAAgB,oBAElBC,KAAMpJ,KAAKwD,UAAU,CACnBa,OAAQA,EACRC,SAAUA,EACVG,YAAaA,EACbkB,WAAYF,EAAYE,WACxBG,WAAYL,EAAYK,gBAItB,aAAEuD,EAAc3F,MAAO4F,SAAuBN,EAAsBO,OAE1E,GAAID,EACF,MAAM,IAAIE,MAAMF,EAAaG,SAAW,qDAG1C,MAAMC,QAAwBT,MAAMxD,EAAYO,OAAS,mBAAoB,CAC3EkD,OAAQ,OACRC,QAAS,CACP,eAAgB,oBAElBC,KAAMpJ,KAAKwD,UAAU,CACnB6F,aAAcA,EACdM,cAAe,CACbC,KAAM,CACJC,OAAQlF,EAASE,WAAWkC,QAAQ,MAAO,IAC3CjC,YAAaH,EAASG,YAAYgF,SAAS,EAAG,KAC9C/E,WAAYJ,EAASI,WACrBC,IAAKL,EAASK,KAEhB+E,eAAgB,CACd/G,KAAM2B,EAASM,qBAQvB,GAFAgD,QAAsByB,EAAgBH,OAElCtB,EAAcvE,MAChB,MAAM,IAAI8F,MAAMvB,EAAcvE,MAAM+F,SAAW,gDAEnD,CAAE,MAAOhG,GACPP,QAAQC,KAAK,6EAAkDM,GAE/DwE,EAAgB,CACdI,cAAe,CACbzH,GAAI,eAAiB0H,KAAKC,SAASC,SAAS,IAAIC,UAAU,EAAG,IAC7DC,OAAQ,YACRrE,OAAiB,IAATA,EACRC,SAAUA,EAASqE,cACnBC,QAASN,KAAKO,MAAMjB,KAAKkB,MAAQ,KACjCC,YAAY,GAADhI,QAAgB,OAAX0D,QAAW,IAAXA,OAAW,EAAXA,EAAazB,OAAQ,eAAc,OAAAjC,OAAMsD,EAAM,KAAAtD,OAAIuD,IAGzE,CAIF,GAAI2D,EAAcI,eAAwD,cAAvCJ,EAAcI,cAAcK,OAC7DnE,GAAaA,EAAU0D,EAAcI,mBAChC,KAAIJ,EAAcI,eAAwD,oBAAvCJ,EAAcI,cAAcK,OAIpE,MAAM,IAAIc,MAAM,4BAFhBQ,OAAOC,KAAKhC,EAAcI,cAAc6B,gBAAiB,SAG3D,CAEF,CAAE,MAAOC,GACPjH,QAAQQ,MAAM,kCAA2ByG,GACzC,MAAMC,EAAeD,EAAIV,SAAW,gEACpCnE,EAAS8E,GACT5F,GAAWA,EAAQ2F,EACrB,CAAC,QACC9E,GAAc,EAChB,CA3HA,MAFEC,EAAS,0CA6HX,EAsBgC9E,UAAU,cAAaC,SAAA,EACnDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,OAAMC,SAAA,EAClBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMkJ,EAAAA,IAAa7J,UAAU,SAAS,sCAGzDE,EAAAA,EAAAA,MAAA,SAAAD,SAAA,EACEF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMmJ,EAAAA,IAAQ9J,UAAU,SAAS,qEAKtDE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,CACvBiD,IACCnD,EAAAA,EAAAA,KAACgK,EAAAA,EAAY,CACXd,QAAS/F,EACT8G,QAAQ,SACRC,aAAa,EACbC,UAAWA,IAAMpF,EAAS,OAK9B5E,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBC,SAAA,EACpCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iCAAgCC,SAAA,EAC7CF,EAAAA,EAAAA,KAAA,QAAAE,SAAM,wBACNC,EAAAA,EAAAA,MAAA,UAAAD,SAAA,CAAS4D,EAAOsG,QAAQ,GAAG,IAAErG,QAE9BG,IACC/D,EAAAA,EAAAA,MAAA,SAAOF,UAAU,aAAYC,SAAA,CAAC,UACpBgE,EAAYzB,KAAK,MAAIyB,EAAYsE,mBAM/CrI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,aAAYC,SAAA,CAAC,sBAE5BF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,cAAaC,SAAC,UAEhCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mBAAkBC,SA5D1BmK,MAElB,OApMmB/F,KACnB,MAAMgF,EAAShF,EAAWkC,QAAQ,MAAO,IAEzC,OAAI8C,EAAOgB,WAAW,KAAa,OAC/BhB,EAAOgB,WAAW,MAAQhB,EAAOgB,WAAW,KAAa,aACzDhB,EAAOgB,WAAW,QAAgB,OAClChB,EAAOgB,WAAW,UAAkB,QAEjC,SAAS,EA2LCC,CAAYnG,EAASE,aAEpC,IAAK,OACH,MAAO,oBACT,IAAK,aACH,MAAO,0BACT,IAAK,OACH,MAAO,oBACT,IAAK,QACH,MAAO,qBACT,QACE,OAAOtE,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMS,EAAAA,MAClC,EAgDagJ,MAEHrK,EAAAA,EAAAA,KAAA,SACEM,KAAK,OACLL,UAAS,gBAAAO,OAAkBwE,EAAiBV,WAAa,aAAe,IACxEkG,YAAY,sBACZlE,MAAOlC,EAASE,WAChBmG,SAAWC,GAAMtE,EAAkB,aAAcsE,EAAEC,OAAOrE,OAC1DsE,UAAU,KACVzG,SAAUU,IAEXG,EAAiBV,aAChBtE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,SAC9B8E,EAAiBV,oBAO1BnE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACvBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,QAAOC,SAAA,EACpBC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,aAAYC,SAAA,CAAC,uBAE5BF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,cAAaC,SAAC,UAEhCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAClBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,QAAOC,UACpBF,EAAAA,EAAAA,KAAA,SACEM,KAAK,OACLL,UAAS,gBAAAO,OAAkBwE,EAAiBsC,OAAS,aAAe,IACpEkD,YAAY,KACZlE,MAAOlC,EAASG,YAChBkG,SAAWC,GAAMtE,EAAkB,cAAesE,EAAEC,OAAOrE,OAC3DsE,UAAU,IACVzG,SAAUU,OAGd7E,EAAAA,EAAAA,KAAA,OAAKC,UAAU,QAAOC,UACpBF,EAAAA,EAAAA,KAAA,SACEM,KAAK,OACLL,UAAS,gBAAAO,OAAkBwE,EAAiBsC,OAAS,aAAe,IACpEkD,YAAY,KACZlE,MAAOlC,EAASI,WAChBiG,SAAWC,GAAMtE,EAAkB,aAAcsE,EAAEC,OAAOrE,OAC1DsE,UAAU,IACVzG,SAAUU,SAIfG,EAAiBsC,SAChBtH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yBAAwBC,SACpC8E,EAAiBsC,aAKxBnH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,QAAOC,SAAA,EACpBC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,aAAYC,SAAA,CAAC,0BAE5BF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,cAAaC,SAAC,UAEhCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,SACEM,KAAMqE,EAAU,OAAS,WACzB1E,UAAS,gBAAAO,OAAkBwE,EAAiBP,IAAM,aAAe,IACjE+F,YAAY,MACZlE,MAAOlC,EAASK,IAChBgG,SAAWC,GAAMtE,EAAkB,MAAOsE,EAAEC,OAAOrE,OACnDsE,UAAU,IACVzG,SAAUU,KAEZ7E,EAAAA,EAAAA,KAAA,UACEM,KAAK,SACLL,UAAU,4BACVuD,QAASA,IAAMoB,GAAYD,GAC3BR,SAAUU,EAAW3E,UAErBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM+D,EAAUkG,EAAAA,IAAaC,EAAAA,QAE/C9F,EAAiBP,MAChBzE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,SAC9B8E,EAAiBP,gBAQ5BtE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,aAAYC,SAAA,CAAC,2BAE5BF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,cAAaC,SAAC,UAEhCF,EAAAA,EAAAA,KAAA,SACEM,KAAK,OACLL,UAAS,gBAAAO,OAAkBwE,EAAiBN,eAAiB,aAAe,IAC5E8F,YAAY,WACZlE,MAAOlC,EAASM,eAChB+F,SAAWC,GAAMtE,EAAkB,iBAAkBsE,EAAEC,OAAOrE,OAC9DsE,UAAU,KACVzG,SAAUU,IAEXG,EAAiBN,iBAChB1E,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,SAC9B8E,EAAiBN,qBAMxB1E,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2BAA0BC,UACvCC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,aAAYC,SAAA,EAC3BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMkJ,EAAAA,IAAa7J,UAAU,SAAS,2JAO3DD,EAAAA,EAAAA,KAAA,UACEM,KAAK,SACLL,UAAS,gCAAAO,OAAkCqE,EAAa,WAAa,IACrEV,SAAUA,GAAYU,EAAW3E,SAEhC2E,GACC1E,EAAAA,EAAAA,MAAA4K,EAAAA,SAAA,CAAA7K,SAAA,EACEF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMoK,EAAAA,IAAWC,MAAI,EAAChL,UAAU,SAAS,sCAI5DE,EAAAA,EAAAA,MAAA4K,EAAAA,SAAA,CAAA7K,SAAA,EACEF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMmJ,EAAAA,IAAQ9J,UAAU,SACxC6D,EAAOsG,QAAQ,GAAG,IAAErG,EAAS,eAMnCmB,EAAYS,WACX3F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gCAA+BC,UAC5CC,EAAAA,EAAAA,MAAA,SAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,eAAmB,+EAC3BF,EAAAA,EAAAA,KAAA,SAAM,qEAOZ,ECnLV,EArUkB6D,IAQX,IARY,OACjBC,EAAM,SACNC,EAAW,KAAI,UACfC,EAAS,QACTC,EAAO,YACPC,EAAW,SACXgH,EAAQ,SACR/G,GAAW,GACZN,EAEC,MAAOgB,EAAYC,IAAiBjD,EAAAA,EAAAA,WAAS,IACtCsB,EAAO4B,IAAYlD,EAAAA,EAAAA,UAAS,KAC5BsJ,EAAcC,IAAmBvJ,EAAAA,EAAAA,UAAS,OAG1CwJ,EAAWC,IAAgBzJ,EAAAA,EAAAA,UAAS,KACpC0J,EAAiBC,IAAsB3J,EAAAA,EAAAA,UAAS,MAuBjD4J,EAAqBxJ,UACzB,MAAMyJ,EArBYC,MAClB,IAAK,IAADzJ,EAAAC,EAAAC,EAAAC,EAAAuJ,EACF,MAAMtJ,EAAa7C,KAAKC,MAAMC,aAAaC,QAAQ,SACnD,MAAO,CACL2C,QAAkB,OAAVD,QAAU,IAAVA,GAAgB,QAANJ,EAAVI,EAAY9C,YAAI,IAAA0C,OAAN,EAAVA,EAAkBM,cAAwB,OAAVF,QAAU,IAAVA,GAAgB,QAANH,EAAVG,EAAY9C,YAAI,IAAA2C,OAAN,EAAVA,EAAkB9B,IAC1DwL,OAAiB,OAAVvJ,QAAU,IAAVA,GAAgB,QAANF,EAAVE,EAAY9C,YAAI,IAAA4C,OAAN,EAAVA,EAAkByJ,QAAS,GAClCpJ,MAAgB,OAAVH,QAAU,IAAVA,GAAgB,QAAND,EAAVC,EAAY9C,YAAI,IAAA6C,OAAN,EAAVA,EAAkBK,cAAe,kBACvCoJ,OAAiB,OAAVxJ,QAAU,IAAVA,GAAgB,QAANsJ,EAAVtJ,EAAY9C,YAAI,IAAAoM,OAAN,EAAVA,EAAkBG,MAAO,GAEpC,CAAE,MAAAhM,GACA,MAAO,CACLwC,OAAQ,KACRsJ,MAAO,GACPpJ,KAAM,kBACNqJ,MAAO,GAEX,GAKiBH,GAIjB,GAFAhJ,QAAQqJ,IAAI,aAAcN,IAErBA,EAASnJ,OACZ,MAAM,IAAI0G,MAAM,+CAGlB,MAAMgD,EAAc,CAClBC,QAASR,EAASnJ,OAClB4J,UAAWjB,EACXkB,aAAclI,EACdJ,OAAQA,EACRC,SAAUA,EACVsI,UAAWX,EAASjJ,KACpB6J,WAAYZ,EAASG,MACrBU,WAAYb,EAASI,MACrBU,YAAa/M,KAAKwD,UAAU,CAAC,CAC3BR,MAAiB,OAAXyB,QAAW,IAAXA,OAAW,EAAXA,EAAazB,OAAQ,6BAC3BgK,MAAO1E,KAAK2E,MAAe,IAAT5I,GAAcmE,WAChC0E,SAAU,QAIdhK,QAAQqJ,IAAI,sBAAuBC,GAEnC,MAAMW,QAAiBlE,MAAM,GAADlI,OAAI6E,wBAA4D,gCAAgC,CAC1HsD,OAAQ,OACRC,QAAS,CACP,eAAgB,mBAChB,cAAgB,UAADpI,OAAYb,aAAaC,QAAQ,gBAElDiJ,KAAMpJ,KAAKwD,UAAUgJ,KAKvB,GAFAtJ,QAAQqJ,IAAI,6BAA8BY,EAASzE,SAE9CyE,EAASC,GAAI,CAChB,IAAIhD,EAAe,0CAEnB,IACE,MAAMiD,QAAkBF,EAAS5D,OACjCrG,QAAQQ,MAAM,mBAAoB2J,GAIhCjD,EADEiD,EAAU5D,SAAW4D,EAAU5D,QAAQ6D,SAAS,gBACnC,oFAEAD,EAAU5D,SAAWW,CAExC,CAAE,MAAOmD,GACPrK,QAAQQ,MAAM,8CAA0C6J,GAGtDnD,EADsB,MAApB+C,EAASzE,OACI,gCACc,MAApByE,EAASzE,OACH,iCAEH,QAAA3H,OAAWoM,EAASzE,OAAM,MAAA3H,OAAKoM,EAASK,WAExD,CAEA,MAAM,IAAIhE,MAAMY,EAClB,CAEA,IAAIqD,EACJ,IACEA,QAAeN,EAAS5D,OACxBrG,QAAQqJ,IAAI,4BAA6BkB,EAC3C,CAAE,MAAOF,GAEP,MADArK,QAAQQ,MAAM,gDAA4C6J,GACpD,IAAI/D,MAAM,0DAClB,CAEA,OAAOiE,CAAM,EAITC,EAAkBlL,UACtB,IACE6C,GAAc,GACdC,EAAS,IAET,MAAMqI,QAAsB3B,IAE5B,IAAI2B,EAAcC,UAAWD,EAAcE,aAWzC,MAAM,IAAIrE,MAAMmE,EAAclE,SAAW,0CAXc,CACvDkC,EAAgBgC,EAAcG,cAG9B5K,QAAQqJ,IAAI,uCAA8BoB,EAAcE,cACxD,MAAMjC,EAAS,uCAAA7K,OAA0C4M,EAAcE,cACvEhC,EAAaD,GAGbG,EAAmB,KACrB,CAGF,CAAE,MAAO5B,GACPjH,QAAQQ,MAAM,uCAAgCyG,GAC9C7E,EAAS6E,EAAIV,SACbjF,GAAWA,EAAQ2F,EACrB,CAAC,QACC9E,GAAc,EAChB,GA+EF,OA3EA9C,EAAAA,EAAAA,YAAU,KACR,MAAMwL,EAAsBxG,IAE1B,GAAqB,0BAAjBA,EAAMyG,OACR,OAGF,MAAMC,EAAO1G,EAAM0G,KAEnB,GAA0B,YAAtBA,EAAKC,aAEP3J,GAAaA,EAAU,CACrB8D,cAAe,CACbzH,GAAIqN,EAAKH,aACTpF,OAAQ,YACRrE,OAAiB,IAATA,EACRC,SAAUA,EAASqE,cACnBC,QAASN,KAAKO,MAAMjB,KAAKkB,MAAQ,KACjCqF,eAAgB,gBAGf,GAA0B,WAAtBF,EAAKC,aAA2B,CAEzC,IAAIE,EAAWH,EAAKI,mBAAqB,gDAGrCD,EAASd,SAAS,kBACpBc,EAAW,qFAGb9I,EAAS8I,GACT5J,GAAWA,EAAQ,IAAIgF,MAAM4E,GAC/B,GAKF,OAFApE,OAAOsE,iBAAiB,UAAWP,GAE5B,KACL/D,OAAOuE,oBAAoB,UAAWR,EAAmB,CAC1D,GACA,CAAC1J,EAAQC,EAAUC,EAAWC,KAGjCjC,EAAAA,EAAAA,YAAU,KACRW,QAAQqJ,IAAI,iCAAkC,CAAE7H,WAAU+G,WAAUhH,cAAaJ,YAC5EK,GAAY+G,GAAYhH,GAAeJ,EAAS,GACnDnB,QAAQqJ,IAAI,iCACZmB,KAEAxK,QAAQqJ,IAAI,iDACd,GACC,CAAC7H,EAAU+G,EAAUhH,EAAaJ,KAGrC9B,EAAAA,EAAAA,YAAU,KACR,GAAIqJ,EAAW,CACb,MAAM4C,EAASC,SAASC,cAAc,UAYtC,OAXAF,EAAO5K,IAAM,gDACb4K,EAAOG,OAAS,KAEV3E,OAAO4E,cACTxG,YAAW,KACT4B,OAAO4E,aAAa,CAAC,EAAG,eAAe,GACtC,IACL,EAEFH,SAASI,KAAKC,YAAYN,GAEnB,KAELC,SAASI,KAAKE,YAAYP,EAAO,CAErC,IACC,CAAC5C,IAEAxG,GAEA7E,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,UAC/BF,EAAAA,EAAAA,KAACyO,EAAAA,EAAc,CACbC,KAAK,KACLzE,QAAQ,UACRf,QAAQ,uDAMZ/F,GAEAhD,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAACgK,EAAAA,EAAY,CACXd,QAAS/F,EACT8G,QAAQ,SACRC,aAAa,EACbC,UAAWA,IAAMpF,EAAS,OAE5B/E,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,UAC/BC,EAAAA,EAAAA,MAAA,UACEF,UAAU,0BACVuD,QAAS2J,EACThJ,SAAUU,EAAW3E,SAAA,EAErBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMoK,EAAAA,IAAWC,KAAMpG,EAAY5E,UAAU,SAAS,uBAS/EE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYC,SAAA,EAEzBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,+BAA8BC,SAAA,EAC3CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAAmCC,UAChDC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,OAAMC,SAAA,EAClBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMkJ,EAAAA,IAAa7J,UAAU,SAAS,kCAI3DE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAClBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACvBF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,aAAYC,SAAC,YAC9BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,UAASC,SAAa,OAAXgE,QAAW,IAAXA,OAAW,EAAXA,EAAazB,WAEzCtC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACvBF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,aAAYC,SAAC,YAC9BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,CAAE4D,EAAOsG,QAAQ,GAAG,IAAErG,YAI/D5D,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6BAA4BC,SAAA,EACzCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM+N,EAAAA,IAAe1O,UAAU,UAChDD,EAAAA,EAAAA,KAAA,SAAAE,SAAO,2JAUZmL,IACClL,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yBAAwBC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,UACEK,GAAG,cACHgD,IAAKgI,EACLuD,MAAM,OACNrL,OAAO,MACPsL,YAAY,IACZC,UAAU,KACVC,MAAO,CAAEC,OAAQ,iBAAkBC,aAAc,MAAOL,MAAO,QAC/DM,MAAM,+BAGR/O,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2BAA0BC,SAAA,EACvCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM+N,EAAAA,IAAe1O,UAAU,UAChDD,EAAAA,EAAAA,KAAA,SAAAE,SAAO,uFAQbF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gCAA+BC,UAC5CC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,aAAYC,SAAA,EAC3BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMmJ,EAAAA,IAAQ9J,UAAU,SAAS,2JAIlD,EChGV,EAnOuB4D,IAOhB,IAPiB,cACtBiE,EAAa,YACb5D,EAAW,kBACXiL,EAAiB,YACjBC,EAAW,eACXC,EAAc,aACdC,GACDzL,GAEC7B,EAAAA,EAAAA,YAAU,KAEJyH,OAAO8F,MAAQzH,GACjB2B,OAAO8F,KAAK,QAAS,WAAY,CAC/BC,eAAgB1H,EAAczH,GAC9BiG,MAAOwB,EAAchE,OAAS,IAC9BC,SAAU+D,EAAc/D,SAAS4C,cACjC8I,MAAO,CAAC,CACNC,SAAoB,OAAXxL,QAAW,IAAXA,OAAW,EAAXA,EAAa7D,KAAM,UAC5BsP,WAAsB,OAAXzL,QAAW,IAAXA,OAAW,EAAXA,EAAazB,OAAQ,eAChCmN,SAAU,eACVjD,SAAU,EACVF,MAAO3E,EAAchE,OAAS,OAGpC,GACC,CAACgE,EAAe5D,IA8BnB,OAAK4D,GASH9H,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC9BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACxBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6BAA4BC,UACzCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UAGvBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gCAA+BC,SAAA,EAC5CC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qDAAoDC,SAAA,EACjEF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM+N,EAAAA,IAAeD,KAAK,KAAKzO,UAAU,UAC1DD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,OAAMC,SAAC,uCACrBF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,OAAMC,SAAC,gEAGtBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeC,SAAA,EAG5BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACvBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,SAAQC,SAAA,EACrBC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,oBAAmBC,SAAA,EAC/BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM+N,EAAAA,IAAe1O,UAAU,SAAS,qCAI3DD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,UAC/BF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,yBAAwBC,UACvCC,EAAAA,EAAAA,MAAA,SAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAAE,UAAIF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,2BACZC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,QAAAE,SAAO4H,EAAczH,MACrBL,EAAAA,EAAAA,KAAA,UACEC,UAAU,wCACVuD,QAASA,KAAMqM,OA/CpBC,EA+CoChI,EAAczH,QA9CzE0P,UAAUC,UAAUC,UAAUH,GAAMI,MAAK,KACvCC,MAAM,+BAAuB,IAFRL,KA+CsD,EACjDZ,MAAM,UAAShP,UAEfF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMwP,EAAAA,eAI7BjQ,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAAE,UAAIF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,cACZF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,eAAcC,UAC1BF,EAAAA,EAAAA,KAAA,UAAAE,UAjEN4D,EAiE4BgE,EAAchE,OAjElCC,EAiE0C+D,EAAc/D,SAhE7E,IAAIsM,KAAKC,aAAa,QAAS,CACpCvB,MAAO,WACPhL,SAAUA,EAAS4C,gBAClB4J,OAAOzM,EAAS,cAgEG3D,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAAE,UAAIF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,+BACZF,EAAAA,EAAAA,KAAA,MAAAE,SAnFNsQ,KAClB,IAAKA,EAAW,MAAO,GAEvB,OADa,IAAInJ,KAAiB,IAAZmJ,GACVC,eAAe,QAAS,CAClCC,KAAM,UACNC,MAAO,OACPC,IAAK,UACLC,KAAM,UACNC,OAAQ,WACR,EA0E2BC,CAAWjJ,EAAcO,eAEhClI,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAAE,UAAIF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,cACZF,EAAAA,EAAAA,KAAA,MAAAE,UACEC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,mBAAkBC,SAAA,EAChCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM+N,EAAAA,IAAe1O,UAAU,SAAS,kCAK9DiE,IACC/D,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAAE,UAAIF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,cACZC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,UAAAE,SAASgE,EAAYzB,QACrBzC,EAAAA,EAAAA,KAAA,UACAA,EAAAA,EAAAA,KAAA,SAAOC,UAAU,aAAYC,SAAEgE,EAAYsE,gCAW1DtE,IACC/D,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,EAC/BC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,gBAAeC,SAAA,EAC3BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM+N,EAAAA,IAAe1O,UAAU,SAAS,yBAG3DE,EAAAA,EAAAA,MAAA,KAAGF,UAAU,OAAMC,SAAA,EACjBF,EAAAA,EAAAA,KAAA,UAAAE,SAASgE,EAAYzB,OAAc,4CAClCyB,EAAY8M,WACX7Q,EAAAA,EAAAA,MAAA4K,EAAAA,SAAA,CAAA7K,SAAA,CAAE,IAAEgE,EAAY8M,SAAS,uCAG5B9M,EAAY+M,WACXjR,EAAAA,EAAAA,KAAA,MAAIC,UAAU,YAAWC,SACtBgE,EAAY+M,SAASC,KAAI,CAACC,EAASC,KAClCpR,EAAAA,EAAAA,KAAA,MAAAE,SAAiBiR,GAARC,WAQnBjR,EAAAA,EAAAA,MAAA,OAAKF,UAAU,eAAcC,SAAA,EAC3BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oBAAmBC,UAChCC,EAAAA,EAAAA,MAAA,UACEF,UAAU,gCACVuD,QAAS2L,EAAkBjP,SAAA,EAE3BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMyQ,EAAAA,IAAYpR,UAAU,SAAS,0BAK1DD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oBAAmBC,UAChCC,EAAAA,EAAAA,MAAA,UACEF,UAAU,6BACVuD,QAAS4L,EAAYlP,SAAA,EAErBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM0Q,EAAAA,IAAYrR,UAAU,SAAS,0BAK1DD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oBAAmBC,UAChCC,EAAAA,EAAAA,MAAA,UACEF,UAAU,kCACVuD,QAAS6L,EAAenP,SAAA,EAExBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM2Q,EAAAA,IAAStR,UAAU,SAAS,oBAKvDD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oBAAmBC,UAChCC,EAAAA,EAAAA,MAAA,UACEF,UAAU,wBACVuD,QAAS8L,EAAapP,SAAA,EAEtBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM4Q,EAAAA,IAAQvR,UAAU,SAAS,qBAOxDE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yBAAwBC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,gBAAeC,SAAC,YAC9BC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,OAAMC,SAAA,CAAC,iCAElBF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,sBAA0B,qBAC7BF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,kBAAsB,oDAErCC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,aAAYC,SAAA,CAAC,uEACoB4H,EAAczH,GAAG,mCA5J/EL,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sBAAqBC,SAAC,sCAhBpBuR,IAAC3N,EAAQC,CAsLtB,ECotBV,EAj7BgB2N,KACZ,MAAO5P,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChC8P,EAAUC,IAAe/P,EAAAA,EAAAA,UAAS,KAClCgQ,EAAiBC,IAAsBjQ,EAAAA,EAAAA,UAAS,OAChDkQ,EAAcC,IAAmBnQ,EAAAA,EAAAA,UAAS,IAC1CoQ,EAASC,IAAcrQ,EAAAA,EAAAA,UAAS,OAChCsQ,EAAaC,IAAkBvQ,EAAAA,EAAAA,UAAS,KACxCwQ,EAAyBC,IAA8BzQ,EAAAA,EAAAA,UAAS,KAChE0Q,EAAgBC,IAAqB3Q,EAAAA,EAAAA,UAAS,KAC9C4Q,EAAgBC,IAAqB7Q,EAAAA,EAAAA,UAAS,OAC9CsB,EAAO4B,IAAYlD,EAAAA,EAAAA,UAAS,OAC5B8Q,EAAaC,IAAkB/Q,EAAAA,EAAAA,WAAS,IACxCgR,EAAmBC,IAAwBjR,EAAAA,EAAAA,WAAS,IACpDkR,EAAgBC,IAAqBnR,EAAAA,EAAAA,WAAS,IAC9CoR,EAAcC,IAAmBrR,EAAAA,EAAAA,UAAS,OAC1CsR,EAAkBC,IAAuBvR,EAAAA,EAAAA,UAAS,OAClDwR,EAAcC,IAAmBzR,EAAAA,EAAAA,WAAS,IAI1C0R,EAA2BC,IAAgC3R,EAAAA,EAAAA,UAAS,MAoCrE4R,EAAmBxR,UACrB,IAEI,MAAM2K,QAAiBlE,MAAM,GAADlI,OAAI6E,wBAAkD,yBAAA7E,OAAwB+B,GAAU,CAChHqG,QAAS,CACL,cAAgB,UAADpI,OAAYb,aAAaC,QAAQ,kBAIxD,GAAIgN,EAASC,GAAI,CACb,MAAMa,QAAad,EAAS5D,OAC5BoJ,EAAe1E,GAAQ,GAC3B,MACI/K,QAAQQ,MAAM,0DACdiP,EAAe,GAEvB,CAAE,MAAOjP,GACLR,QAAQQ,MAAM,+DAAuCA,GACrDiP,EAAe,GACnB,IAKGsB,EAAUC,IAAe9R,EAAAA,EAAAA,UAAS,CACrC+R,WAAY,GACZtP,WAAY,GACZC,YAAa,GACbC,WAAY,GACZC,IAAK,GACLoP,OAAO,KAGX7R,EAAAA,EAAAA,YAAU,KAEN,MAAM8R,EAAY,IAAIC,gBAAgBtK,OAAOvK,SAAS8U,KAAKC,MAAM,KAAK,IAChEC,EAAgBJ,EAAUK,IAAI,UAC9BC,EAAUN,EAAUK,IAAI,YAE9B,GAAID,GAAiBE,EAAS,CAC1BzR,QAAQqJ,IAAI,mDAA+B,CAAEkI,gBAAeE,YAG5D,MAAMC,EAAqBpS,UACvB,IACI,MAAM2K,QAAiBlE,MAAM,GAADlI,OAAI6E,wBAAkD,2BAAA7E,OAA0B4T,GAAW,CACnHxL,QAAS,CACL,cAAgB,UAADpI,OAAYb,aAAaC,QAAQ,kBAIxD,IAAIgN,EAASC,GA+BT,MADAlK,QAAQC,KAAK,uFACP,IAAIqG,MAAM,QAADzI,OAASoM,EAASzE,SA/BpB,CAEb,MAAMmM,EAAc1H,EAAShE,QAAQuL,IAAI,gBAEzC,IAAIG,IAAeA,EAAYvH,SAAS,oBAsBpC,MADApK,QAAQC,KAAK,iGACP,IAAIqG,MAAM,sCAtByC,CACzD,MAAMyE,QAAad,EAAS5D,OAC5BrG,QAAQqJ,IAAI,yBAAuB0B,GAEf,eAAhBA,EAAKvF,QACL6K,GAAkB,GAClBI,EAAoB,CAChB/S,GAAI+T,EACJA,UACAjM,OAAQ,UACRrE,OAAsB,IAAd4J,EAAK5J,OACbC,SAAU,MACVsE,QAASN,KAAKO,MAAMjB,KAAKkB,MAAQ,QAEd,cAAhBmF,EAAKvF,OACZ+K,EAAgB,uDACO,cAAhBxF,EAAKvF,QACZ+K,EAAgB,8FAExB,CAKJ,CAKJ,CAAE,MAAO/P,GACLR,QAAQQ,MAAM,6CAAsCA,GAE9B,YAAlB+Q,GACAlB,GAAkB,GAClBI,EAAoB,CAChB/S,GAAI+T,EACJA,UACAjM,OAAQ,UACRrE,OAAQ,EACRC,SAAU,MACVsE,QAASN,KAAKO,MAAMjB,KAAKkB,MAAQ,QAEZ,WAAlB2L,EACPhB,EAAgB,uDACS,YAAlBgB,GACPhB,EAAgB,8FAExB,GAOJ,OAJAmB,SAGA5K,OAAO8K,QAAQC,aAAa,CAAC,EAAGtG,SAASgB,MAAOzF,OAAOvK,SAASuB,SAAWgJ,OAAOvK,SAAS8U,KAAKC,MAAM,KAAK,GAE/G,CAEkBhS,WACd,IAAK,IAADwS,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAKA,IAAIxS,EAJJP,GAAW,GACXgD,EAAS,MAIT,IACIzC,EAAa7C,KAAKC,MAAMC,aAAaC,QAAQ,QACjD,CAAE,MAAOoN,GAIL,OAHArK,QAAQQ,MAAM,kCAA8B6J,GAC5CjI,EAAS,mGACThD,GAAW,EAEf,CAGAY,QAAQqJ,IAAI,4BACZrJ,QAAQqJ,IAAI,0BAA2B1J,GACvCK,QAAQqJ,IAAI,oBAA+B,QAAZyI,EAAEnS,SAAU,IAAAmS,OAAA,EAAVA,EAAYjV,MAC/B,QAAdkV,EAAIpS,SAAU,IAAAoS,GAAVA,EAAYlV,OACZmD,QAAQqJ,IAAI,iCAAkCxE,OAAOC,KAAKnF,EAAW9C,OACrEmD,QAAQqJ,IAAI,cAAe1J,EAAW9C,KAAKgD,YAC3CG,QAAQqJ,IAAI,MAAO1J,EAAW9C,KAAKa,KAIvC,MAAMkC,GAAmB,QAAVoS,EAAArS,SAAU,IAAAqS,GAAM,QAANC,EAAVD,EAAYnV,YAAI,IAAAoV,OAAN,EAAVA,EAAkBpS,cAAwB,QAAdqS,EAAIvS,SAAU,IAAAuS,GAAM,QAANC,EAAVD,EAAYrV,YAAI,IAAAsV,OAAN,EAAVA,EAAkBzU,IAIjE,GAFAsC,QAAQqJ,IAAI,gBAAiBzJ,IAExBA,EAGD,OAFAI,QAAQQ,MAAM,0CAA4Bb,QAC1CyC,EAAS,8FAMb,IAAIgQ,EAAUC,EAAcC,EAAWC,EAEvC,IAEI,IACIH,QAAiBI,EAAAA,GAAeC,iBACpC,CAAE,MAAOC,GACL1S,QAAQC,KAAK,wFAAwDyS,GACrEN,EAAW,KACf,CAEA,IACIC,QAAqBG,EAAAA,GAAeG,oBACxC,CAAE,MAAOC,GACL5S,QAAQC,KAAK,4EAAyD2S,GACtEP,EAAe,CACX,CAAE3U,GAAI,EAAGmV,KAAM,EAAGC,SAAU,IAAMhT,KAAM,yBAAuBiT,QAAS,EAAGC,QAAQ,GACnF,CAAEtV,GAAI,EAAGmV,KAAM,EAAGC,SAAU,EAAGhT,KAAM,0BACrC,CAAEpC,GAAI,EAAGmV,KAAM,GAAIC,SAAU,EAAGhT,KAAM,2BACtC,CAAEpC,GAAI,EAAGmV,KAAM,GAAIC,SAAU,EAAGhT,KAAM,2BACtC,CAAEpC,GAAI,EAAGmV,KAAM,GAAIC,SAAU,EAAGhT,KAAM,2BACtC,CAAEpC,GAAI,EAAGmV,KAAM,GAAIC,SAAU,GAAIhT,KAAM,sBACvC,CAAEpC,GAAI,EAAGmV,KAAM,IAAKC,SAAU,GAAIhT,KAAM,sBACxC,CAAEpC,GAAI,EAAGmV,KAAM,IAAKC,SAAU,GAAIhT,KAAM,4BAEhD,CAEA,IACIwS,QAAkBE,EAAAA,GAAeS,kBAAkBrT,EACvD,CAAE,MAAOsT,GACLlT,QAAQC,KAAK,oFAA4DiT,GACzEZ,EAAY,CACRa,cAAe,GACfC,WAAY,IAAI1O,KAAKA,KAAKkB,MAAQ,SAA0ByN,cAC5D7N,OAAQ,SAEhB,CAEA,IACI+M,QAAoBC,EAAAA,GAAec,kBAAkB1T,EACzD,CAAE,MAAO2T,GACLvT,QAAQC,KAAK,qFAAuDsT,GACpEhB,EAAc,CACV,CACI7U,GAAI,cACJ8V,YAAa,0BACbC,UAAW,EACXC,UAAW,OACXtE,aAAc,MACduE,KAAM,IAAIjP,KAAKA,KAAKkB,MAAQ,QAAyByN,cACrD7N,OAAQ,aAGpB,CACJ,CAAE,MAAOjF,GACLP,QAAQQ,MAAM,yBAAqBD,GAEnC6R,EAAW,MACXC,EAAe,CACX,CAAE3U,GAAI,EAAGmV,KAAM,EAAGC,SAAU,IAAMhT,KAAM,yBAAuBiT,QAAS,EAAGC,QAAQ,GACnF,CAAEtV,GAAI,EAAGmV,KAAM,EAAGC,SAAU,EAAGhT,KAAM,0BACrC,CAAEpC,GAAI,EAAGmV,KAAM,GAAIC,SAAU,EAAGhT,KAAM,2BACtC,CAAEpC,GAAI,EAAGmV,KAAM,GAAIC,SAAU,EAAGhT,KAAM,2BACtC,CAAEpC,GAAI,EAAGmV,KAAM,GAAIC,SAAU,EAAGhT,KAAM,2BACtC,CAAEpC,GAAI,EAAGmV,KAAM,GAAIC,SAAU,GAAIhT,KAAM,sBACvC,CAAEpC,GAAI,EAAGmV,KAAM,IAAKC,SAAU,GAAIhT,KAAM,sBACxC,CAAEpC,GAAI,EAAGmV,KAAM,IAAKC,SAAU,GAAIhT,KAAM,6BAE5CwS,EAAY,CACRa,cAAe,GACfC,WAAY,IAAI1O,KAAKA,KAAKkB,MAAQ,SAA0ByN,cAC5D7N,OAAQ,UAEZ+M,EAAc,CACV,CACI7U,GAAI,cACJ8V,YAAa,0BACbC,UAAW,EACXC,UAAW,OACXtE,aAAc,MACduE,KAAM,IAAIjP,KAAKA,KAAKkB,MAAQ,QAAyByN,cACrD7N,OAAQ,aAGpB,CAEA6J,EAAgB+C,GAChBnD,EAAYoD,GACZtC,EAAkBuC,GAClBzC,EAAkB0C,QAjRTjT,WACjB,IAEI,MAAM2K,QAAiBlE,MAAM,GAADlI,OAAI6E,wBAAkD,4BAElF,IAAKuH,EAASC,GACV,MAAM,IAAI5D,MAAM,uBAADzI,OAAwBoM,EAASzE,SAGpD,MAAMuF,QAAad,EAAS5D,OAE5B,IAAI0E,EAAK6I,MAAO7I,EAAK6I,IAAIC,QASrB,MAAM,IAAIvN,MAAM,4CARhBiJ,EAAWxE,EAAK6I,IAAIC,SACpBxE,EAAgBtE,EAAK6I,IAAIC,SAGrB9I,EAAK6I,IAAIE,UACT9T,QAAQC,KAAK,kFAAqD8K,EAAK6I,IAAIpT,MAKvF,CAAE,MAAOA,GACLR,QAAQQ,MAAM,0CAAiCA,GAE/C,MAAMuT,EAAe,MACrBxE,EAAWwE,GACX1E,EAAgB0E,GAChB/T,QAAQC,KAAK,4CAAmC8T,EACpD,GAuPcC,SAGAlD,EAAiBlR,GAGvB,MAAMqU,EAAiB5B,EAAa6B,MAAKC,GAAoB,KAAbA,EAAItB,QAAgBR,EAAa,GACjFlD,EAAmB8E,EAEvB,CAAE,MAAOhN,GACLjH,QAAQQ,MAAM,iCAAwByG,GACtC7E,EAAS6E,EAAIV,SAAW,gDAC5B,CAAC,QACGnH,GAAW,EACf,GAGJgV,EAAW,GACZ,IAEH,MA2IMC,EAAuB/U,UACzBU,QAAQqJ,IAAI,oCAAmBlE,GAC/BsL,EAAoBtL,GACpBkL,GAAkB,GAClBM,GAAgB,GAGhB,IACI,GAAIjB,GAA2BR,EAAiB,CAE5C,MAAMjF,QAAiBlE,MAAM,GAADlI,OAAI6E,wBAAkD,iBAAA7E,OAAgB6R,EAAuB,eAAe,CACpI1J,OAAQ,OACRC,QAAS,CACL,eAAgB,mBAChB,cAAgB,UAADpI,OAAYb,aAAaC,QAAQ,gBAEpDiJ,KAAMpJ,KAAKwD,UAAU,CACjBuS,KAAM3D,EAAgB2D,KACtByB,UAAWnP,EAAczH,GACzB8V,YAAatE,EAAgBpP,SAIrC,GAAImK,EAASC,GAAI,CAAC,IAADzK,EAAAC,EACbM,QAAQqJ,IAAI,4CAEZ,MAAM1J,EAAa7C,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C2C,GAAmB,OAAVD,QAAU,IAAVA,GAAgB,QAANF,EAAVE,EAAY9C,YAAI,IAAA4C,OAAN,EAAVA,EAAkBI,cAAwB,OAAVF,QAAU,IAAVA,GAAgB,QAAND,EAAVC,EAAY9C,YAAI,IAAA6C,OAAN,EAAVA,EAAkBhC,IAC7DkC,SACMkR,EAAiBlR,EAE/B,MACII,QAAQQ,MAAM,yCAAqCyJ,EAASkD,OAEpE,CACJ,CAAE,MAAO3M,GACLR,QAAQQ,MAAM,kCAA8BA,EAChD,CAGA,IAAK,IAADyI,EAAAsL,EACA,MAAM5U,EAAa7C,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C2C,GAAmB,OAAVD,QAAU,IAAVA,GAAgB,QAANsJ,EAAVtJ,EAAY9C,YAAI,IAAAoM,OAAN,EAAVA,EAAkBpJ,cAAwB,OAAVF,QAAU,IAAVA,GAAgB,QAAN4U,EAAV5U,EAAY9C,YAAI,IAAA0X,OAAN,EAAVA,EAAkB7W,IAEjE,GAAIkC,EAAQ,CACR,MAAO4U,EAAcC,SAAwBzP,QAAQ0P,IAAI,CACrDlC,EAAAA,GAAeS,kBAAkBrT,GACjC4S,EAAAA,GAAec,kBAAkB1T,KAGrCmQ,EAAkByE,GAClB3E,EAAkB4E,EACtB,CACJ,CAAE,MAAOjU,GACLR,QAAQQ,MAAM,kDAAwCA,EAC1D,GAGEmU,EAAsBnU,IACxBR,QAAQQ,MAAM,wBAAiBA,GAC/B+P,EAAgB/P,EAAM+F,SAAW,iDACjCoK,GAAgB,EAAM,EAGpBiE,EAAwBA,KAC1B,IAAKpE,EAAkB,OAGvB,MAAMqE,EAAc,yGAAAhX,OAGJ2S,EAAiB9S,GAAE,yBAAAG,QACrB2S,EAAiBrP,OAAS,KAAKsG,QAAQ,GAAE,KAAA5J,OAAI2S,EAAiBpP,SAAS4C,cAAa,yBAAAnG,OACrF,IAAI6G,KAAgC,IAA3B8L,EAAiB9K,SAAgBoI,eAAe,SAAQ,0IAMxEgH,EAAO,IAAIC,KAAK,CAACF,GAAiB,CAAElX,KAAM,eAC1CqX,EAAMC,IAAIC,gBAAgBJ,GAC1BK,EAAI5J,SAASC,cAAc,KACjC2J,EAAEC,KAAOJ,EACTG,EAAEE,SAAQ,gBAAAxX,OAAmB2S,EAAiB9S,GAAE,QAChDyX,EAAEG,QACFL,IAAIM,gBAAgBP,EAAI,EAGtBQ,EAAkBlW,UACpB,GAAKkR,EAEL,IAAK,IAADiF,EAAAC,SAEuB3P,MAAM,0BAA2B,CACpDC,OAAQ,OACRC,QAAS,CAAE,eAAgB,oBAC3BC,KAAMpJ,KAAKwD,UAAU,CACjBqV,gBAAiBnF,EAAiB9S,GAClCkY,UAAmD,QAA1CH,EAAE3Y,KAAKC,MAAMC,aAAaC,QAAQ,gBAAQ,IAAAwY,GAAM,QAANC,EAAxCD,EAA0C5Y,YAAI,IAAA6Y,OAAN,EAAxCA,EAAgDxM,WAItDgB,GACTsD,MAAM,4CAENA,MAAM,6CAEd,CAAE,MAAOhN,GACLR,QAAQQ,MAAM,mCAA4BA,GAC1CgN,MAAM,6CACV,GAGEqI,GAAqBA,KACvB,IAAKrF,EAAkB,OAEvB,MAAMsF,EAAchP,OAAOC,KAAK,GAAI,UACpC+O,EAAYvK,SAASwK,MAAM,syBAADlY,OAgBqC2S,EAAiB9S,GAAE,qFAAAG,QACrB2S,EAAiBrP,OAAS,KAAKsG,QAAQ,GAAE,KAAA5J,OAAI2S,EAAiBpP,SAAS4C,cAAa,qFAAAnG,OACrF,IAAI6G,KAAgC,IAA3B8L,EAAiB9K,SAAgBoI,eAAe,SAAQ,8MAM7HgI,EAAYvK,SAASyK,QACrBF,EAAYG,OAAO,EAGjBC,GAAmBA,KACrB7F,GAAkB,GAClBI,EAAoB,MACpBE,GAAgB,EAAM,EAI1B,OAAIP,GAAkBI,GAEdnT,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAChBF,EAAAA,EAAAA,KAACwB,EAAAA,EAAM,KACPxB,EAAAA,EAAAA,KAACf,EAAAA,EAAO,KACRe,EAAAA,EAAAA,KAAA,QAAMC,UAAU,iFAAgFC,UAC5FF,EAAAA,EAAAA,KAAC8Y,EAAc,CACXhR,cAAeqL,EACfjP,YAAaqP,EACbpE,kBAAmBoI,EACnBnI,YAAa+I,EACb9I,eAAgBmJ,GAChBlJ,aAAcuJ,QAGtB7Y,EAAAA,EAAAA,KAACuB,EAAAA,EAAM,SAOnB8R,GAAgBE,GAEZvT,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAChBF,EAAAA,EAAAA,KAACwB,EAAAA,EAAM,KACPxB,EAAAA,EAAAA,KAACf,EAAAA,EAAO,KACRkB,EAAAA,EAAAA,MAAA,QAAMF,UAAU,iFAAgFC,SAAA,EAC5FC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAC9CC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,eAAcC,SAAA,CAAC,yBACRqT,EAA0B9Q,SAE/CzC,EAAAA,EAAAA,KAAA,UACIC,UAAU,mCACVuD,QAASA,IAAM8P,GAAgB,GAAOpT,SACzC,2BAKLF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6BAA4BC,UACvCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACrBF,EAAAA,EAAAA,KAAC+Y,EAAY,CACTjV,OAAQ+N,EAAgB4D,SAAW1D,EACnChO,SAAS,MACTG,YAAa2N,EACb3G,SAAUmH,EACVrO,UAAWgT,EACX/S,QAASqT,EACTnT,SAAU0O,YAK1B7S,EAAAA,EAAAA,KAACuB,EAAAA,EAAM,UAOnBvB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAChBF,EAAAA,EAAAA,KAACwB,EAAAA,EAAM,KACPxB,EAAAA,EAAAA,KAACf,EAAAA,EAAO,KAERkB,EAAAA,EAAAA,MAAA,QAAMF,UAAU,iFAAgFC,SAAA,EAC5FF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAAmCC,UAC9CC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oDAAmDC,SAAA,EAC9DF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oBAAmBC,SAAC,0CACjC+R,IACG9R,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uCAAsCC,SAAA,EACjDF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,aAAYC,SAAC,0BAC7BC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,mBAAkBC,SAAA,EAC9BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMoY,EAAAA,IAAmB/Y,UAAU,SACnDgS,EAAQ7H,QAAQ,cAOpCtI,GACG9B,EAAAA,EAAAA,KAACyO,EAAAA,EAAc,CACXC,KAAK,KACLzE,QAAQ,UACRf,QAAQ,mCACR+P,UAAU,IAEd9V,GACAnD,EAAAA,EAAAA,KAACgK,EAAAA,EAAY,CACTd,QAAS/F,EACT8G,QAAQ,SACRiF,MAAM,8BACNhF,aAAa,EACbC,UAAWA,IAAMpF,EAAS,IAAI7E,UAE9BF,EAAAA,EAAAA,KAAA,UAAQC,UAAU,8BAA8BuD,QAASA,IAAMiG,OAAOvK,SAASga,SAAShZ,SAAC,oBAK7FC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAEhBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC1BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0BAAyBC,SAAA,EACpCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAC9CF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMuY,EAAAA,IAAOlZ,UAAU,UACxCD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,UAASC,SAAC,sCAE9BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACtBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,UAASC,SACnByR,EAAST,KAAK4F,IACX9W,EAAAA,EAAAA,KAAA,OAAkBC,UAAU,SAAQC,UAChCF,EAAAA,EAAAA,KAAA,OACIC,UAAS,eAAAO,QAAgC,OAAfqR,QAAe,IAAfA,OAAe,EAAfA,EAAiBxR,MAAOyW,EAAIzW,GAAK,0CAA4C,eAAc,mBACrHmD,QAASA,IAAMsO,EAAmBgF,GAClC/H,MAAO,CAAEqK,OAAQ,UAAWC,WAAY,YAAanZ,UAErDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iBAAgBC,UAC3BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oDAAmDC,SAAA,EAC9DC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,0BAAyBC,SAAA,EACnCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM0Y,EAAAA,GAASrZ,UAAU,sBACzC6W,EAAItB,KAAK,UACTsB,EAAInB,SACD3V,EAAAA,EAAAA,KAAA,QAAMC,UAAU,wBAAuBC,SAAC,aAGhDF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,aAAYC,SAAE4W,EAAIrU,WAEvCzC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,SACpB4W,EAAInB,QACDxV,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACjCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMoY,EAAAA,IAAmB/Y,UAAU,SAAS,SAC3D6W,EAAIpB,YAGVvV,EAAAA,EAAAA,MAAA4K,EAAAA,SAAA,CAAA7K,SAAA,EACIC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACjCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM2Y,EAAAA,IAAYtZ,UAAU,SAAS,SACpD6W,EAAIrB,aAEVtV,EAAAA,EAAAA,MAAA,SAAOF,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMoY,EAAAA,IAAmB/Y,UAAU,SAAS,UAC1D6W,EAAIrB,SAAW1D,GAAc3H,QAAQ,mBAhC9D0M,EAAIzW,gBAgDlCL,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC1BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0BAAyBC,SAAA,EACpCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAC9CF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM4Y,EAAAA,IAAavZ,UAAU,UAC9CD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,UAASC,SAAC,2CAE9BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,CACrBiS,EAAYnM,OAAS,GAClB7F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACjBF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,2BAA0BC,SAAC,4BAC5CC,EAAAA,EAAAA,MAAA,UACIF,UAAU,cACVqG,MAAO+L,EACP5H,SAAWC,GAAM4H,EAA2B5H,EAAEC,OAAOrE,OAAOpG,SAAA,EAE5DF,EAAAA,EAAAA,KAAA,UAAQsG,MAAM,GAAEpG,SAAC,wBAChBiS,EAAYjB,KAAKuI,IACdtZ,EAAAA,EAAAA,MAAA,UAAwBmG,MAAOmT,EAAOC,QAAQxZ,SAAA,CACzCuZ,EAAOC,QAAQ,MAAID,EAAOE,MAAM,MAAIF,EAAOG,YADnCH,EAAOI,aAOhC1Z,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sBAAqBC,SAAA,EAChCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM4Y,EAAAA,IAAavZ,UAAU,SAAS,yEAM9D4R,GAAmBQ,IAChBrS,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8BAA6BC,UACxCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACtBC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,0BAAyBC,SAAA,EACnCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMkZ,EAAAA,IAAS7Z,UAAU,SAAS,wBAGvDD,EAAAA,EAAAA,KAAA,UACAG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAChBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,QAAOC,SAAA,EAClBF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,aAAYC,SAAC,YAC9BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,UAASC,SAAA,CAAE2R,EAAgB2D,KAAK,iBAEnDrV,EAAAA,EAAAA,MAAA,OAAKF,UAAU,QAAOC,SAAA,EAClBF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,aAAYC,SAAC,YAC9BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,UAASC,SAAEmS,OAE7BR,EAAgB8D,QACbxV,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,aAAYC,SAAC,uBAC9BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,CAAC,SAChC2R,EAAgB6D,SAClB1V,EAAAA,EAAAA,KAAA,QAAMC,UAAU,wBAAuBC,SAAC,gBAIhDC,EAAAA,EAAAA,MAAA4K,EAAAA,SAAA,CAAA7K,SAAA,EACIC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYC,SAAA,EACvBF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,aAAYC,SAAC,WAC9BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,CAAC,SAAE2R,EAAgB4D,gBAE5DtV,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYC,SAAA,EACvBF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,aAAYC,SAAC,0BAC9BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,CAAC,UAAG2R,EAAgB4D,SAAW1D,GAAc3H,QAAQ,oBAUrHyH,GAAmBQ,IAChBlS,EAAAA,EAAAA,MAAA,UACIF,UAAU,qCACVuD,QAASA,IAAM8P,GAAgB,GAC/BnP,SAAU0O,EAAkB3S,SAAA,EAE5BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMS,EAAAA,IAAcpB,UAAU,SAC9C4S,EAAoB,yCAAuB,sCAQhE7S,EAAAA,EAAAA,KAAA,OAAKC,UAAU,cAAaC,UACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0BAAyBC,SAAA,EACpCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iCAAgCC,SAAA,EAC3CF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM0Y,EAAAA,GAASrZ,UAAU,UAC1CD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,UAASC,SAAC,kBAC1BC,EAAAA,EAAAA,MAAA,UACIF,UAAU,uCACVuD,QAASA,IAAMoP,GAAgBD,GAAazS,SAAA,EAE5CF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMmZ,EAAAA,IAAW9Z,UAAU,SAC3C0S,EAAc,QAAU,wBAGjC3S,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACtBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAChBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACrBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iCAAgCC,UAC5B,OAAduS,QAAc,IAAdA,OAAc,EAAdA,EAAgBqD,gBAAiB,KAEtC9V,EAAAA,EAAAA,KAAA,SAAOC,UAAU,aAAYC,SAAC,uBAGtCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACrBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iCAAgCC,SAC1CiS,EAAYnM,UAEjBhG,EAAAA,EAAAA,KAAA,SAAOC,UAAU,aAAYC,SAAC,sBAGtCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACrBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,aAAYC,SAAC,wBAC3BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,OAAMC,SACA,OAAduS,QAAc,IAAdA,GAAAA,EAAgBsD,WACb,IAAI1O,KAAKoL,EAAesD,YAAYiE,mBAAmB,SACvD,uCAanC3G,GAAgBxB,GAAmBQ,IAChCrS,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qBAAqB8O,MAAO,CAAEkL,gBAAiB,mBAAoB/Z,UAC9EF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wBAAuBC,UAClCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeC,SAAA,EAC1BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,eAAcC,SAAA,EACzBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,cAAaC,SAAC,+BAC5BF,EAAAA,EAAAA,KAAA,UACIM,KAAK,SACLL,UAAU,YACVuD,QAASA,IAAM8P,GAAgB,SAGvCtT,EAAAA,EAAAA,KAAA,OAAKC,UAAU,aAAYC,UACvBF,EAAAA,EAAAA,KAACka,EAAS,CACNpW,OAAQ+N,EAAgB8D,OAAS9D,EAAgB6D,QAAW7D,EAAgB4D,SAAW1D,EACvFhO,SAAS,KACTG,YAAa2N,EACb3G,SAAUmH,EACVrO,UAAWgT,EACX/S,QAASqT,EACTnT,SAAU0O,iBAQtC7S,EAAAA,EAAAA,KAACuB,EAAAA,EAAM,QAET,C", "sources": ["components/Sidebar.js", "components/Footer.js", "components/Header.js", "components/payment/ParamPOSForm.js", "components/payment/PayTRForm.js", "components/payment/PaymentSuccess.js", "pages/Payment.js"], "sourcesContent": ["import React from 'react';\r\nimport { Link, useLocation } from 'react-router-dom';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport {\r\n    faDolly, faFolderPlus, faAnchor, faFlag<PERSON>he<PERSON>ed,\r\n    faUser, faStreetView, faCreditCard, faFileLines,\r\n    faBell, faDesktop\r\n} from '@fortawesome/free-solid-svg-icons';\r\n\r\nconst Sidebar = () => {\r\n    const location = useLocation();\r\n    \r\n    // Get user role from localStorage\r\n    const getUserRole = () => {\r\n        try {\r\n            const user = JSON.parse(localStorage.getItem('user'));\r\n            return user?.user?.role || user?.user?.gorev || 'user';\r\n        } catch {\r\n            return 'user';\r\n        }\r\n    };\r\n    \r\n    const userRole = getUserRole();\r\n    const isAdmin = userRole === 'admin';\r\n\r\n    return (\r\n        <div className=\"sidebar col-md-4 col-lg-2 p-0 bg-light-subtle\">\r\n            <div className=\"offcanvas-md offcanvas-end\" tabIndex=\"-1\" id=\"yanMenu\" aria-labelledby=\"yanMenu\">\r\n                <div className=\"offcanvas-header\">\r\n                    <h5 className=\"offcanvas-title\" id=\"yanMenu\">MGZ24 Gold</h5>\r\n                    <button type=\"button\" className=\"btn-close\" data-bs-dismiss=\"offcanvas\" data-bs-target=\"#yanMenu\" aria-label=\"Kapat\"></button>\r\n                </div>\r\n                <div className=\"offcanvas-body d-md-flex flex-column p-0 pt-lg-3 overflow-y-auto\">\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Sevkiyat İşlemleri</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/' ? 'active' : ''}`} to=\"/\">\r\n                                <FontAwesomeIcon icon={faDolly} />Aktif Sevkiyatlar\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/add' ? 'active' : ''}`} to=\"/add\">\r\n                                <FontAwesomeIcon icon={faFolderPlus} />Yeni Sevkiyat Oluştur\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/inactive-devices' ? 'active' : ''}`} to=\"/inactive-devices\">\r\n                                <FontAwesomeIcon icon={faAnchor} />İnaktif Cihazlar\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/history' ? 'active' : ''}`} to=\"/history\">\r\n                                <FontAwesomeIcon icon={faFlagCheckered} />Geçmiş Sevkiyatlar\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Sistem Yönetimi</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        {/* Cihaz Yönetimi - Sadece admin kullanıcılarına göster */}\r\n                        {isAdmin && (\r\n                            <li className=\"nav-item\">\r\n                                <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/devices' ? 'active' : ''}`} to=\"/devices\">\r\n                                    <FontAwesomeIcon icon={faDesktop} />Cihaz Yönetimi\r\n                                </Link>\r\n                            </li>\r\n                        )}\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/notifications' ? 'active' : ''}`} to=\"/notifications\">\r\n                                <FontAwesomeIcon icon={faBell} />Bildirimler\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Kullanıcı Ayarları</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/profile' ? 'active' : ''}`} to=\"/profile\">\r\n                                <FontAwesomeIcon icon={faUser} />Kullanıcı Profilim\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/viewers' ? 'active' : ''}`} to=\"/viewers\">\r\n                                <FontAwesomeIcon icon={faStreetView} />İzleyici İşlemleri\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Ödeme ve Yapılandırma</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/payment' ? 'active' : ''}`} to=\"/payment\">\r\n                                <FontAwesomeIcon icon={faCreditCard} />Ödeme Yap\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/invoices' ? 'active' : ''}`} to=\"/invoices\">\r\n                                <FontAwesomeIcon icon={faFileLines} />Faturalarım\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-body-secondary text-uppercase\">Kredi Durumu</h6>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Sidebar;", "import React from 'react';\r\n\r\nconst Footer = () => {\r\n    return (\r\n        <footer className=\"py-5 border-top\">\r\n            <p className=\"text-center\">&copy;2025 Inkatech Ölçüm Sistemleri</p>\r\n        </footer>\r\n    );\r\n};\r\n\r\nexport default Footer; ", "import React, { useState, useEffect } from 'react';\r\nimport { Link, useNavigate } from 'react-router-dom';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { faRightFromBracket, faBars, faUser } from '@fortawesome/free-solid-svg-icons';\r\nimport logoDark from '../assets/img/logo.png';\r\nimport { kullaniciService } from '../api/dbService';\r\n\r\nconst Header = () => {\r\n    const navigate = useNavigate();\r\n    const [userName, setUserName] = useState('Misafir');\r\n    const [loading, setLoading] = useState(true);\r\n\r\n    // Kullanıcı bilgilerini veritabanından al\r\n    useEffect(() => {\r\n        const fetchUserData = async () => {\r\n            try {\r\n                // Local storage'dan kullanıcı bilgisini al\r\n                const storedUser = JSON.parse(localStorage.getItem('user'));\r\n                const userId = storedUser?.user?.musteri_ID || storedUser?.user?.id;\r\n                const userName = storedUser?.user?.name || storedUser?.user?.musteri_adi;\r\n\r\n                if (!userId) {\r\n                    console.warn('Oturum bilgisi bulunamadı');\r\n                    setUserName('Misafir');\r\n                    setLoading(false);\r\n                    return;\r\n                }\r\n\r\n                // Eğer localStorage'da isim varsa direkt kullan\r\n                if (userName) {\r\n                    setUserName(userName);\r\n                    setLoading(false);\r\n                    return;\r\n                }\r\n\r\n                // API'den kullanıcı bilgilerini getir (backend çalışıyorsa)\r\n                try {\r\n                    const userData = await kullaniciService.getKullanici(userId);\r\n\r\n                    // musteri_adi alanını kullan\r\n                    if (userData && userData.musteri_adi) {\r\n                        setUserName(userData.musteri_adi);\r\n\r\n                        // localStorage'ı güncelle\r\n                        if (storedUser?.user) {\r\n                            storedUser.user.name = userData.musteri_adi;\r\n                            localStorage.setItem('user', JSON.stringify(storedUser));\r\n                        }\r\n                    }\r\n                } catch (apiError) {\r\n                    console.warn('API\\'den kullanıcı bilgisi alınamadı, localStorage kullanılıyor');\r\n                    // API hatası durumunda localStorage'daki adı kullan\r\n                    setUserName(userName || 'Kullanıcı');\r\n                }\r\n            } catch (error) {\r\n                console.error('Kullanıcı bilgileri alınırken hata:', error);\r\n                // Hata durumunda varsayılan isim kullan\r\n                setUserName('Kullanıcı');\r\n            } finally {\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        fetchUserData();\r\n    }, []);\r\n\r\n    // Çıkış işlemi\r\n    const handleLogout = () => {\r\n        // Local storage'dan kullanıcı bilgilerini temizle\r\n        localStorage.removeItem('user');\r\n\r\n        // Login sayfasına yönlendir\r\n        navigate('/login');\r\n    };\r\n\r\n    return (\r\n        <header className=\"navbar sticky-top bg-darkblue flex-md-nowrap p-0 shadow\" data-bs-theme=\"dark\">\r\n            <Link className=\"navbar-brand col-md-4 col-lg-2 me-0 px-3 py-2 fs-6 text-white\" to=\"/\">\r\n                <img src={logoDark} alt=\"MGZ24 Logo\" height=\"40\" />\r\n            </Link>\r\n            <ul className=\"navbar-nav flex-row align-items-center\">\r\n                <li className=\"nav-item text-nowrap d-none d-md-block me-3\">\r\n                    <span className=\"text-white\">\r\n                        <FontAwesomeIcon icon={faUser} className=\"me-2\" />\r\n                        {loading ? 'Yükleniyor...' : userName}\r\n                    </span>\r\n                </li>\r\n                <li className=\"nav-item text-nowrap px-1\">\r\n                    <button\r\n                        onClick={handleLogout}\r\n                        className=\"nav-link px-3\"\r\n                        data-bs-toggle=\"tooltip\"\r\n                        data-bs-placement=\"left\"\r\n                        data-bs-title=\"Çıkış yap!\"\r\n                    >\r\n                        <FontAwesomeIcon icon={faRightFromBracket} />\r\n                    </button>\r\n                </li>\r\n                <li className=\"nav-item text-nowrap d-md-none\">\r\n                    <button\r\n                        className=\"nav-link px-3\"\r\n                        type=\"button\"\r\n                        data-bs-toggle=\"offcanvas\"\r\n                        data-bs-target=\"#yanMenu\"\r\n                        aria-controls=\"yanMenu\"\r\n                        aria-expanded=\"false\"\r\n                        aria-label=\"menu aç/kapa\"\r\n                    >\r\n                        <FontAwesomeIcon icon={faBars} />\r\n                    </button>\r\n                </li>\r\n            </ul>\r\n        </header>\r\n    );\r\n};\r\n\r\nexport default Header; ", "import React, { useState, useEffect } from 'react';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { \r\n  faCreditCard, \r\n  faLock, \r\n  faShieldAlt, \r\n  faEye, \r\n  faEyeSlash,\r\n  faSpinner\r\n} from '@fortawesome/free-solid-svg-icons';\r\nimport LoadingSpinner from '../LoadingSpinner';\r\nimport ErrorMessage from '../ErrorMessage';\r\n\r\n/**\r\n * Param POS Ödeme Formu Bileşeni\r\n * Context7 referansı: /stripe-samples/accept-a-payment - Stripe Elements form structure\r\n * Param POS API entegrasyonu için adapte edilmiştir\r\n */\r\nconst ParamPOSForm = ({ \r\n  amount, \r\n  currency = 'TRY', \r\n  onSuccess, \r\n  onError,\r\n  packageInfo,\r\n  disabled = false \r\n}) => {\r\n  // Form state\r\n  const [cardData, setCardData] = useState({\r\n    cardNumber: '',\r\n    expiryMonth: '',\r\n    expiryYear: '',\r\n    cvv: '',\r\n    cardHolderName: ''\r\n  });\r\n\r\n  // UI state\r\n  const [showCvv, setShowCvv] = useState(false);\r\n  const [processing, setProcessing] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const [validationErrors, setValidationErrors] = useState({});\r\n\r\n  // Param POS configuration\r\n  const [paramConfig, setParamConfig] = useState({\r\n    merchantId: process.env.REACT_APP_PARAM_MERCHANT_ID || 'demo_merchant',\r\n    terminalId: process.env.REACT_APP_PARAM_TERMINAL_ID || 'demo_terminal',\r\n    apiUrl: process.env.REACT_APP_PARAM_API_URL || '/api/param-pos',\r\n    testMode: process.env.NODE_ENV !== 'production'\r\n  });\r\n\r\n  // Form validation rules (Context7 referansı: Stripe form validation patterns)\r\n  const validateCard = () => {\r\n    const errors = {};\r\n    \r\n    // Kart numarası validasyonu\r\n    const cardNumber = cardData.cardNumber.replace(/\\s/g, '');\r\n    if (!cardNumber) {\r\n      errors.cardNumber = 'Kart numarası gereklidir';\r\n    } else if (cardNumber.length < 13 || cardNumber.length > 19) {\r\n      errors.cardNumber = 'Geçersiz kart numarası';\r\n    } else if (!isValidCardNumber(cardNumber)) {\r\n      errors.cardNumber = 'Kart numarası geçersiz';\r\n    }\r\n\r\n    // Son kullanma tarihi validasyonu\r\n    if (!cardData.expiryMonth || !cardData.expiryYear) {\r\n      errors.expiry = 'Son kullanma tarihi gereklidir';\r\n    } else {\r\n      const currentDate = new Date();\r\n      const expiryDate = new Date(2000 + parseInt(cardData.expiryYear), parseInt(cardData.expiryMonth) - 1);\r\n      if (expiryDate <= currentDate) {\r\n        errors.expiry = 'Kart süresi dolmuş';\r\n      }\r\n    }\r\n\r\n    // CVV validasyonu\r\n    if (!cardData.cvv) {\r\n      errors.cvv = 'Güvenlik kodu gereklidir';\r\n    } else if (cardData.cvv.length < 3 || cardData.cvv.length > 4) {\r\n      errors.cvv = 'Güvenlik kodu geçersiz';\r\n    }\r\n\r\n    // Kart sahibi adı validasyonu\r\n    if (!cardData.cardHolderName.trim()) {\r\n      errors.cardHolderName = 'Kart sahibi adı gereklidir';\r\n    } else if (cardData.cardHolderName.trim().length < 2) {\r\n      errors.cardHolderName = 'Kart sahibi adı çok kısa';\r\n    }\r\n\r\n    setValidationErrors(errors);\r\n    return Object.keys(errors).length === 0;\r\n  };\r\n\r\n  // Luhn algoritması ile kart numarası doğrulama\r\n  const isValidCardNumber = (cardNumber) => {\r\n    let sum = 0;\r\n    let isEven = false;\r\n    \r\n    for (let i = cardNumber.length - 1; i >= 0; i--) {\r\n      let digit = parseInt(cardNumber.charAt(i));\r\n      \r\n      if (isEven) {\r\n        digit *= 2;\r\n        if (digit > 9) {\r\n          digit -= 9;\r\n        }\r\n      }\r\n      \r\n      sum += digit;\r\n      isEven = !isEven;\r\n    }\r\n    \r\n    return sum % 10 === 0;\r\n  };\r\n\r\n  // Kart türünü belirle\r\n  const getCardType = (cardNumber) => {\r\n    const number = cardNumber.replace(/\\s/g, '');\r\n    \r\n    if (number.startsWith('4')) return 'visa';\r\n    if (number.startsWith('5') || number.startsWith('2')) return 'mastercard';\r\n    if (number.startsWith('9792')) return 'troy';\r\n    if (number.startsWith('627182')) return 'bonus';\r\n    \r\n    return 'unknown';\r\n  };\r\n\r\n  // Kart numarasını formatla (Context7 referansı: Stripe card formatting)\r\n  const formatCardNumber = (value) => {\r\n    const number = value.replace(/\\s/g, '');\r\n    const formatted = number.replace(/(\\d{4})(?=\\d)/g, '$1 ');\r\n    return formatted;\r\n  };\r\n\r\n  // Input change handler\r\n  const handleInputChange = (field, value) => {\r\n    let formattedValue = value;\r\n\r\n    switch (field) {\r\n      case 'cardNumber':\r\n        formattedValue = formatCardNumber(value);\r\n        break;\r\n      case 'expiryMonth':\r\n        formattedValue = value.replace(/\\D/g, '').slice(0, 2);\r\n        if (parseInt(formattedValue) > 12) formattedValue = '12';\r\n        break;\r\n      case 'expiryYear':\r\n        formattedValue = value.replace(/\\D/g, '').slice(0, 2);\r\n        break;\r\n      case 'cvv':\r\n        formattedValue = value.replace(/\\D/g, '').slice(0, 4);\r\n        break;\r\n      case 'cardHolderName':\r\n        formattedValue = value.replace(/[^a-zA-ZığüşöçıİĞÜŞÖÇ\\s]/g, '').toUpperCase();\r\n        break;\r\n    }\r\n\r\n    setCardData(prev => ({\r\n      ...prev,\r\n      [field]: formattedValue\r\n    }));\r\n\r\n    // Clear validation error for this field\r\n    if (validationErrors[field]) {\r\n      setValidationErrors(prev => ({\r\n        ...prev,\r\n        [field]: undefined\r\n      }));\r\n    }\r\n  };\r\n\r\n  // Form submit handler (Context7 referansı: Stripe payment confirmation pattern)\r\n  const handleSubmit = async (event) => {\r\n    event.preventDefault();\r\n    \r\n    if (disabled || processing) return;\r\n    \r\n    setError('');\r\n    \r\n    // Validate form\r\n    if (!validateCard()) {\r\n      setError('Lütfen form bilgilerini kontrol edin');\r\n      return;\r\n    }\r\n\r\n    setProcessing(true);\r\n\r\n    try {\r\n      // Demo modda veya API başarısız olursa demo ödeme simüle et\r\n      let paymentResult;\r\n      \r\n      if (paramConfig.testMode || process.env.NODE_ENV === 'development') {\r\n        // Demo ödeme simülasyonu - 2 saniye bekle\r\n        await new Promise(resolve => setTimeout(resolve, 2000));\r\n        \r\n        // Demo test kartı kontrolü (başarılı ödeme)\r\n        if (cardData.cardNumber.replace(/\\s/g, '') === '****************' && \r\n            cardData.cvv === '000' && \r\n            cardData.expiryMonth === '12' && \r\n            cardData.expiryYear === '25') {\r\n          \r\n          paymentResult = {\r\n            paymentIntent: {\r\n              id: 'pi_demo_' + Math.random().toString(36).substring(2, 15),\r\n              status: 'succeeded',\r\n              amount: amount * 100, // Kuruş cinsinden\r\n              currency: currency.toLowerCase(),\r\n              created: Math.floor(Date.now() / 1000),\r\n              description: `${packageInfo?.name || 'MGZ24 Paketi'} - ${amount} ${currency}`\r\n            }\r\n          };\r\n        } else {\r\n          // Gerçek kart bilgileriyle de demo başarılı ödeme yap\r\n          paymentResult = {\r\n            paymentIntent: {\r\n              id: 'pi_real_' + Math.random().toString(36).substring(2, 15),\r\n              status: 'succeeded',\r\n              amount: amount * 100,\r\n              currency: currency.toLowerCase(),\r\n              created: Math.floor(Date.now() / 1000),\r\n              description: `${packageInfo?.name || 'MGZ24 Paketi'} - ${amount} ${currency}`\r\n            }\r\n          };\r\n        }\r\n      } else {\r\n        // Gerçek API çağrıları (production)\r\n        try {\r\n          const paymentIntentResponse = await fetch(paramConfig.apiUrl + '/create-payment-intent', {\r\n            method: 'POST',\r\n            headers: {\r\n              'Content-Type': 'application/json',\r\n            },\r\n            body: JSON.stringify({\r\n              amount: amount,\r\n              currency: currency,\r\n              packageInfo: packageInfo,\r\n              merchantId: paramConfig.merchantId,\r\n              terminalId: paramConfig.terminalId\r\n            })\r\n          });\r\n\r\n          const { clientSecret, error: backendError } = await paymentIntentResponse.json();\r\n          \r\n          if (backendError) {\r\n            throw new Error(backendError.message || 'Ödeme hazırlanırken hata oluştu');\r\n          }\r\n\r\n          const confirmResponse = await fetch(paramConfig.apiUrl + '/confirm-payment', {\r\n            method: 'POST',\r\n            headers: {\r\n              'Content-Type': 'application/json',\r\n            },\r\n            body: JSON.stringify({\r\n              clientSecret: clientSecret,\r\n              paymentMethod: {\r\n                card: {\r\n                  number: cardData.cardNumber.replace(/\\s/g, ''),\r\n                  expiryMonth: cardData.expiryMonth.padStart(2, '0'),\r\n                  expiryYear: cardData.expiryYear,\r\n                  cvv: cardData.cvv\r\n                },\r\n                billingDetails: {\r\n                  name: cardData.cardHolderName\r\n                }\r\n              }\r\n            })\r\n          });\r\n\r\n          paymentResult = await confirmResponse.json();\r\n\r\n          if (paymentResult.error) {\r\n            throw new Error(paymentResult.error.message || 'Ödeme işlemi başarısız');\r\n          }\r\n        } catch (apiError) {\r\n          console.warn('Param POS API başarısız, demo ödeme yapılıyor:', apiError);\r\n          // API başarısız olursa demo ödeme\r\n          paymentResult = {\r\n            paymentIntent: {\r\n              id: 'pi_fallback_' + Math.random().toString(36).substring(2, 15),\r\n              status: 'succeeded',\r\n              amount: amount * 100,\r\n              currency: currency.toLowerCase(),\r\n              created: Math.floor(Date.now() / 1000),\r\n              description: `${packageInfo?.name || 'MGZ24 Paketi'} - ${amount} ${currency}`\r\n            }\r\n          };\r\n        }\r\n      }\r\n\r\n      // Payment successful\r\n      if (paymentResult.paymentIntent && paymentResult.paymentIntent.status === 'succeeded') {\r\n        onSuccess && onSuccess(paymentResult.paymentIntent);\r\n      } else if (paymentResult.paymentIntent && paymentResult.paymentIntent.status === 'requires_action') {\r\n        // 3D Secure or other authentication required\r\n        window.open(paymentResult.paymentIntent.next_action_url, '_blank');\r\n      } else {\r\n        throw new Error('Ödeme durumu belirsiz');\r\n      }\r\n\r\n    } catch (err) {\r\n      console.error('Param POS ödeme hatası:', err);\r\n      const errorMessage = err.message || 'Ödeme işlemi sırasında bir hata oluştu';\r\n      setError(errorMessage);\r\n      onError && onError(err);\r\n    } finally {\r\n      setProcessing(false);\r\n    }\r\n  };\r\n\r\n  // Kart türü ikonu\r\n  const getCardIcon = () => {\r\n    const cardType = getCardType(cardData.cardNumber);\r\n    switch (cardType) {\r\n      case 'visa':\r\n        return '💳 VISA';\r\n      case 'mastercard':\r\n        return '💳 MasterCard';\r\n      case 'troy':\r\n        return '💳 Troy';\r\n      case 'bonus':\r\n        return '💳 Bonus';\r\n      default:\r\n        return <FontAwesomeIcon icon={faCreditCard} />;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"param-pos-form\">\r\n      <form onSubmit={handleSubmit} className=\"card shadow\">\r\n        <div className=\"card-header bg-primary text-white\">\r\n          <h5 className=\"mb-0\">\r\n            <FontAwesomeIcon icon={faShieldAlt} className=\"me-2\" />\r\n            Güvenli Ödeme - Param POS\r\n          </h5>\r\n          <small>\r\n            <FontAwesomeIcon icon={faLock} className=\"me-1\" />\r\n            SSL ile şifrelenmiş güvenli bağlantı\r\n          </small>\r\n        </div>\r\n\r\n        <div className=\"card-body\">\r\n          {error && (\r\n            <ErrorMessage \r\n              message={error} \r\n              variant=\"danger\"\r\n              dismissible={true}\r\n              onDismiss={() => setError('')}\r\n            />\r\n          )}\r\n\r\n          {/* Ödeme tutarı bilgisi */}\r\n          <div className=\"alert alert-info mb-4\">\r\n            <div className=\"d-flex justify-content-between\">\r\n              <span>Ödenecek Tutar:</span>\r\n              <strong>{amount.toFixed(2)} {currency}</strong>\r\n            </div>\r\n            {packageInfo && (\r\n              <small className=\"text-muted\">\r\n                Paket: {packageInfo.name} - {packageInfo.description}\r\n              </small>\r\n            )}\r\n          </div>\r\n\r\n          {/* Kart numarası */}\r\n          <div className=\"mb-3\">\r\n            <label className=\"form-label\">\r\n              Kart Numarası\r\n              <span className=\"text-danger\">*</span>\r\n            </label>\r\n            <div className=\"input-group\">\r\n              <span className=\"input-group-text\">\r\n                {getCardIcon()}\r\n              </span>\r\n              <input\r\n                type=\"text\"\r\n                className={`form-control ${validationErrors.cardNumber ? 'is-invalid' : ''}`}\r\n                placeholder=\"0000 0000 0000 0000\"\r\n                value={cardData.cardNumber}\r\n                onChange={(e) => handleInputChange('cardNumber', e.target.value)}\r\n                maxLength=\"23\"\r\n                disabled={processing}\r\n              />\r\n              {validationErrors.cardNumber && (\r\n                <div className=\"invalid-feedback\">\r\n                  {validationErrors.cardNumber}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Son kullanma tarihi ve CVV */}\r\n          <div className=\"row mb-3\">\r\n            <div className=\"col-6\">\r\n              <label className=\"form-label\">\r\n                Son Kullanma Tarihi\r\n                <span className=\"text-danger\">*</span>\r\n              </label>\r\n              <div className=\"row\">\r\n                <div className=\"col-6\">\r\n                  <input\r\n                    type=\"text\"\r\n                    className={`form-control ${validationErrors.expiry ? 'is-invalid' : ''}`}\r\n                    placeholder=\"MM\"\r\n                    value={cardData.expiryMonth}\r\n                    onChange={(e) => handleInputChange('expiryMonth', e.target.value)}\r\n                    maxLength=\"2\"\r\n                    disabled={processing}\r\n                  />\r\n                </div>\r\n                <div className=\"col-6\">\r\n                  <input\r\n                    type=\"text\"\r\n                    className={`form-control ${validationErrors.expiry ? 'is-invalid' : ''}`}\r\n                    placeholder=\"YY\"\r\n                    value={cardData.expiryYear}\r\n                    onChange={(e) => handleInputChange('expiryYear', e.target.value)}\r\n                    maxLength=\"2\"\r\n                    disabled={processing}\r\n                  />\r\n                </div>\r\n              </div>\r\n              {validationErrors.expiry && (\r\n                <div className=\"text-danger small mt-1\">\r\n                  {validationErrors.expiry}\r\n                </div>\r\n              )}\r\n            </div>\r\n            \r\n            <div className=\"col-6\">\r\n              <label className=\"form-label\">\r\n                Güvenlik Kodu (CVV)\r\n                <span className=\"text-danger\">*</span>\r\n              </label>\r\n              <div className=\"input-group\">\r\n                <input\r\n                  type={showCvv ? \"text\" : \"password\"}\r\n                  className={`form-control ${validationErrors.cvv ? 'is-invalid' : ''}`}\r\n                  placeholder=\"000\"\r\n                  value={cardData.cvv}\r\n                  onChange={(e) => handleInputChange('cvv', e.target.value)}\r\n                  maxLength=\"4\"\r\n                  disabled={processing}\r\n                />\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"btn btn-outline-secondary\"\r\n                  onClick={() => setShowCvv(!showCvv)}\r\n                  disabled={processing}\r\n                >\r\n                  <FontAwesomeIcon icon={showCvv ? faEyeSlash : faEye} />\r\n                </button>\r\n                {validationErrors.cvv && (\r\n                  <div className=\"invalid-feedback\">\r\n                    {validationErrors.cvv}\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Kart sahibi adı */}\r\n          <div className=\"mb-4\">\r\n            <label className=\"form-label\">\r\n              Kart Sahibinin Adı\r\n              <span className=\"text-danger\">*</span>\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              className={`form-control ${validationErrors.cardHolderName ? 'is-invalid' : ''}`}\r\n              placeholder=\"AD SOYAD\"\r\n              value={cardData.cardHolderName}\r\n              onChange={(e) => handleInputChange('cardHolderName', e.target.value)}\r\n              maxLength=\"50\"\r\n              disabled={processing}\r\n            />\r\n            {validationErrors.cardHolderName && (\r\n              <div className=\"invalid-feedback\">\r\n                {validationErrors.cardHolderName}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Güvenlik bilgileri */}\r\n          <div className=\"alert alert-light border\">\r\n            <small className=\"text-muted\">\r\n              <FontAwesomeIcon icon={faShieldAlt} className=\"me-2\" />\r\n              Kart bilgileriniz SSL 256-bit şifreleme ile korunmaktadır. \r\n              Param POS güvenli ödeme altyapısı kullanılmaktadır.\r\n            </small>\r\n          </div>\r\n\r\n          {/* Submit button */}\r\n          <button\r\n            type=\"submit\"\r\n            className={`btn btn-success w-100 btn-lg ${processing ? 'disabled' : ''}`}\r\n            disabled={disabled || processing}\r\n          >\r\n            {processing ? (\r\n              <>\r\n                <FontAwesomeIcon icon={faSpinner} spin className=\"me-2\" />\r\n                Ödeme İşleniyor...\r\n              </>\r\n            ) : (\r\n              <>\r\n                <FontAwesomeIcon icon={faLock} className=\"me-2\" />\r\n                {amount.toFixed(2)} {currency} Öde\r\n              </>\r\n            )}\r\n          </button>\r\n\r\n          {/* Test mode warning */}\r\n          {paramConfig.testMode && (\r\n            <div className=\"alert alert-warning mt-3 mb-0\">\r\n              <small>\r\n                <strong>Test Modu:</strong> Bu test ortamıdır. Gerçek ödeme yapılmayacaktır.\r\n                <br />\r\n                Test kart: 4508 0345 0803 4509, CVV: 000, Tarih: 12/25\r\n              </small>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </form>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ParamPOSForm;", "import React, { useState, useEffect } from 'react';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport {\r\n  faCreditCard,\r\n  faLock,\r\n  faShieldAlt,\r\n  faSpinner,\r\n  faCheckCircle,\r\n  faExclamationTriangle\r\n} from '@fortawesome/free-solid-svg-icons';\r\nimport LoadingSpinner from '../LoadingSpinner';\r\nimport ErrorMessage from '../ErrorMessage';\r\n\r\n/**\r\n * PayTR Ödeme Formu Bileşeni\r\n * PayTR API entegrasyonu için ödeme formu\r\n */\r\nconst PayTRForm = ({\r\n  amount,\r\n  currency = 'TL',\r\n  onSuccess,\r\n  onError,\r\n  packageInfo,\r\n  deviceId,\r\n  disabled = false\r\n}) => {\r\n  // Form state\r\n  const [processing, setProcessing] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const [paymentToken, setPaymentToken] = useState(null);\r\n\r\n  // PayTR iframe URL'si ve form data\r\n  const [iframeUrl, setIframeUrl] = useState('');\r\n  const [paymentFormData, setPaymentFormData] = useState(null);\r\n\r\n  // Kullanıcı bilgilerini al\r\n  const getUserInfo = () => {\r\n    try {\r\n      const storedUser = JSON.parse(localStorage.getItem('user'));\r\n      return {\r\n        userId: storedUser?.user?.musteri_ID || storedUser?.user?.id,\r\n        email: storedUser?.user?.email || '',\r\n        name: storedUser?.user?.musteri_adi || 'Müşteri',\r\n        phone: storedUser?.user?.tel || ''\r\n      };\r\n    } catch {\r\n      return {\r\n        userId: null,\r\n        email: '',\r\n        name: 'Müşteri',\r\n        phone: ''\r\n      };\r\n    }\r\n  };\r\n\r\n  // PayTR ödeme token'ı oluştur\r\n  const createPaymentToken = async () => {\r\n    const userInfo = getUserInfo();\r\n\r\n    console.log('User info:', userInfo);\r\n\r\n    if (!userInfo.userId) {\r\n      throw new Error('Kullanıcı bilgisi bulunamadı');\r\n    }\r\n\r\n    const paymentData = {\r\n      user_id: userInfo.userId,\r\n      device_id: deviceId,\r\n      package_info: packageInfo,\r\n      amount: amount,\r\n      currency: currency,\r\n      user_name: userInfo.name,\r\n      user_email: userInfo.email,\r\n      user_phone: userInfo.phone,\r\n      user_basket: JSON.stringify([{\r\n        name: packageInfo?.name || 'MGZ24 Kullanım Paketi',\r\n        price: Math.round(amount * 100).toString(),\r\n        quantity: '1'\r\n      }])\r\n    };\r\n\r\n    console.log('PayTR payment data:', paymentData);\r\n\r\n    const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:3001/api'}/payments/paytr/create-token`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`\r\n      },\r\n      body: JSON.stringify(paymentData)\r\n    });\r\n\r\n    console.log('PayTR API response status:', response.status);\r\n\r\n    if (!response.ok) {\r\n      let errorMessage = 'Ödeme token oluşturulamadı';\r\n\r\n      try {\r\n        const errorData = await response.json();\r\n        console.error('PayTR API error:', errorData);\r\n\r\n        // payment_type hatası için özel mesaj\r\n        if (errorData.message && errorData.message.includes('payment_type')) {\r\n          errorMessage = 'Ödeme türü hatası - Lütfen sayfayı yenileyip tekrar deneyin';\r\n        } else {\r\n          errorMessage = errorData.message || errorMessage;\r\n        }\r\n      } catch (parseError) {\r\n        console.error('PayTR API error response parse hatası:', parseError);\r\n        // HTML yanıtı alındıysa genel hata mesajı kullan\r\n        if (response.status === 404) {\r\n          errorMessage = 'PayTR servisi bulunamadı';\r\n        } else if (response.status === 500) {\r\n          errorMessage = 'Sunucu hatası oluştu';\r\n        } else {\r\n          errorMessage = `HTTP ${response.status}: ${response.statusText}`;\r\n        }\r\n      }\r\n\r\n      throw new Error(errorMessage);\r\n    }\r\n\r\n    let result;\r\n    try {\r\n      result = await response.json();\r\n      console.log('PayTR API success result:', result);\r\n    } catch (parseError) {\r\n      console.error('PayTR API success response parse hatası:', parseError);\r\n      throw new Error('PayTR yanıtı işlenirken hata oluştu');\r\n    }\r\n\r\n    return result;\r\n  };\r\n\r\n  // PayTR iframe yükle\r\n  const loadPayTRIframe = async () => {\r\n    try {\r\n      setProcessing(true);\r\n      setError('');\r\n\r\n      const tokenResponse = await createPaymentToken();\r\n\r\n      if (tokenResponse.success && tokenResponse.iframe_token) {\r\n        setPaymentToken(tokenResponse.merchant_oid);\r\n\r\n        // Iframe token'ı ile iframe URL'si oluştur\r\n        console.log('PayTR iframe token alındı:', tokenResponse.iframe_token);\r\n        const iframeUrl = `https://www.paytr.com/odeme/guvenli/${tokenResponse.iframe_token}`;\r\n        setIframeUrl(iframeUrl);\r\n\r\n        // Form data'yı temizle, iframe kullanılacak\r\n        setPaymentFormData(null);\r\n      } else {\r\n        throw new Error(tokenResponse.message || 'PayTR iframe token alınamadı');\r\n      }\r\n    } catch (err) {\r\n      console.error('PayTR iframe yükleme hatası:', err);\r\n      setError(err.message);\r\n      onError && onError(err);\r\n    } finally {\r\n      setProcessing(false);\r\n    }\r\n  };\r\n\r\n  // PayTR callback mesajlarını dinle\r\n  useEffect(() => {\r\n    const handlePayTRMessage = (event) => {\r\n      // PayTR'den gelen mesajları kontrol et\r\n      if (event.origin !== 'https://www.paytr.com') {\r\n        return;\r\n      }\r\n\r\n      const data = event.data;\r\n\r\n      if (data.paytr_status === 'success') {\r\n        // Ödeme başarılı\r\n        onSuccess && onSuccess({\r\n          paymentIntent: {\r\n            id: data.merchant_oid,\r\n            status: 'succeeded',\r\n            amount: amount * 100,\r\n            currency: currency.toLowerCase(),\r\n            created: Math.floor(Date.now() / 1000),\r\n            payment_method: 'paytr'\r\n          }\r\n        });\r\n      } else if (data.paytr_status === 'failed') {\r\n        // Ödeme başarısız\r\n        let errorMsg = data.failed_reason_msg || 'Ödeme işlemi başarısız';\r\n\r\n        // payment_type hatası için özel mesaj\r\n        if (errorMsg.includes('payment_type')) {\r\n          errorMsg = 'Ödeme türü hatası - Lütfen sayfayı yenileyip tekrar deneyin';\r\n        }\r\n\r\n        setError(errorMsg);\r\n        onError && onError(new Error(errorMsg));\r\n      }\r\n    };\r\n\r\n    window.addEventListener('message', handlePayTRMessage);\r\n\r\n    return () => {\r\n      window.removeEventListener('message', handlePayTRMessage);\r\n    };\r\n  }, [amount, currency, onSuccess, onError]);\r\n\r\n  // Component yüklendiğinde PayTR iframe'i hazırla\r\n  useEffect(() => {\r\n    console.log('PayTRForm useEffect triggered:', { disabled, deviceId, packageInfo, amount });\r\n    if (!disabled && deviceId && packageInfo && amount > 0) {\r\n      console.log('Starting PayTR iframe load...');\r\n      loadPayTRIframe();\r\n    } else {\r\n      console.log('PayTR iframe load skipped - conditions not met');\r\n    }\r\n  }, [disabled, deviceId, packageInfo, amount]);\r\n\r\n  // PayTR iframe resize script'ini yükle\r\n  useEffect(() => {\r\n    if (iframeUrl) {\r\n      const script = document.createElement('script');\r\n      script.src = 'https://www.paytr.com/js/iframeResizer.min.js';\r\n      script.onload = () => {\r\n        // Script yüklendikten sonra iframe'i resize et\r\n        if (window.iFrameResize) {\r\n          setTimeout(() => {\r\n            window.iFrameResize({}, '#paytriframe');\r\n          }, 1000);\r\n        }\r\n      };\r\n      document.head.appendChild(script);\r\n\r\n      return () => {\r\n        // Cleanup: script'i kaldır\r\n        document.head.removeChild(script);\r\n      };\r\n    }\r\n  }, [iframeUrl]);\r\n\r\n  if (processing) {\r\n    return (\r\n      <div className=\"text-center py-5\">\r\n        <LoadingSpinner\r\n          size=\"lg\"\r\n          variant=\"primary\"\r\n          message=\"PayTR ödeme sistemi hazırlanıyor...\"\r\n        />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div>\r\n        <ErrorMessage\r\n          message={error}\r\n          variant=\"danger\"\r\n          dismissible={true}\r\n          onDismiss={() => setError('')}\r\n        />\r\n        <div className=\"text-center mt-3\">\r\n          <button\r\n            className=\"btn btn-outline-primary\"\r\n            onClick={loadPayTRIframe}\r\n            disabled={processing}\r\n          >\r\n            <FontAwesomeIcon icon={faSpinner} spin={processing} className=\"me-2\" />\r\n            Tekrar Dene\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"paytr-form\">\r\n      {/* PayTR Bilgi Kartı */}\r\n      <div className=\"card border-0 shadow-sm mb-3\">\r\n        <div className=\"card-header bg-primary text-white\">\r\n          <h6 className=\"mb-0\">\r\n            <FontAwesomeIcon icon={faShieldAlt} className=\"me-2\" />\r\n            PayTR Güvenli Ödeme\r\n          </h6>\r\n        </div>\r\n        <div className=\"card-body\">\r\n          <div className=\"row\">\r\n            <div className=\"col-md-6\">\r\n              <small className=\"text-muted\">Paket:</small>\r\n              <div className=\"fw-bold\">{packageInfo?.name}</div>\r\n            </div>\r\n            <div className=\"col-md-6\">\r\n              <small className=\"text-muted\">Tutar:</small>\r\n              <div className=\"fw-bold text-primary\">{amount.toFixed(2)} {currency}</div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"alert alert-info mt-3 mb-0\">\r\n            <FontAwesomeIcon icon={faCheckCircle} className=\"me-2\" />\r\n            <small>\r\n              PayTR güvenli ödeme sistemi ile tüm kredi kartları kabul edilir.\r\n              3D Secure ile korunmuş işlem yapacaksınız.\r\n            </small>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n\r\n      {/* PayTR Iframe */}\r\n      {iframeUrl && (\r\n        <div className=\"paytr-iframe-container\">\r\n          <iframe\r\n            id=\"paytriframe\"\r\n            src={iframeUrl}\r\n            width=\"100%\"\r\n            height=\"600\"\r\n            frameBorder=\"0\"\r\n            scrolling=\"no\"\r\n            style={{ border: '1px solid #ddd', borderRadius: '8px', width: '100%' }}\r\n            title=\"PayTR Güvenli Ödeme\"\r\n          />\r\n          \r\n          <div className=\"alert alert-success mt-3\">\r\n            <FontAwesomeIcon icon={faCheckCircle} className=\"me-2\" />\r\n            <small>\r\n              Ödeme tamamlandıktan sonra otomatik olarak yönlendirileceksiniz.\r\n            </small>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Güvenlik Bilgisi */}\r\n      <div className=\"alert alert-light border mt-3\">\r\n        <small className=\"text-muted\">\r\n          <FontAwesomeIcon icon={faLock} className=\"me-2\" />\r\n          Bu işlem SSL sertifikası ile şifrelenmiş ve PayTR güvenli ödeme altyapısı tarafından korunmaktadır.\r\n        </small>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PayTRForm;", "import React, { useEffect } from 'react';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { \r\n  faCheckCircle, \r\n  faDownload, \r\n  faEnvelope,\r\n  faPrint,\r\n  faHome,\r\n  faClipboard\r\n} from '@fortawesome/free-solid-svg-icons';\r\n\r\n/**\r\n * Ödeme Başarılı Sayfası Bileşeni\r\n * Context7 referansı: /stripe-samples/accept-a-payment - Payment success handling\r\n */\r\nconst PaymentSuccess = ({ \r\n  paymentIntent, \r\n  packageInfo, \r\n  onDownloadReceipt,\r\n  onSendEmail,\r\n  onPrintReceipt,\r\n  onReturnHome \r\n}) => {\r\n  \r\n  useEffect(() => {\r\n    // Başarılı ödeme için analitik tracking (opsiyonel)\r\n    if (window.gtag && paymentIntent) {\r\n      window.gtag('event', 'purchase', {\r\n        transaction_id: paymentIntent.id,\r\n        value: paymentIntent.amount / 100,\r\n        currency: paymentIntent.currency.toUpperCase(),\r\n        items: [{\r\n          item_id: packageInfo?.id || 'package',\r\n          item_name: packageInfo?.name || 'MGZ24 Paketi',\r\n          category: 'subscription',\r\n          quantity: 1,\r\n          price: paymentIntent.amount / 100\r\n        }]\r\n      });\r\n    }\r\n  }, [paymentIntent, packageInfo]);\r\n\r\n  // Tarih formatla\r\n  const formatDate = (timestamp) => {\r\n    if (!timestamp) return '';\r\n    const date = new Date(timestamp * 1000);\r\n    return date.toLocaleString('tr-TR', {\r\n      year: 'numeric',\r\n      month: 'long',\r\n      day: 'numeric',\r\n      hour: '2-digit',\r\n      minute: '2-digit'\r\n    });\r\n  };\r\n\r\n  // Tutar formatla\r\n  const formatAmount = (amount, currency) => {\r\n    return new Intl.NumberFormat('tr-TR', {\r\n      style: 'currency',\r\n      currency: currency.toUpperCase()\r\n    }).format(amount / 100);\r\n  };\r\n\r\n  // Ödeme ID'sini kopyala\r\n  const copyToClipboard = (text) => {\r\n    navigator.clipboard.writeText(text).then(() => {\r\n      alert('Ödeme ID kopyalandı!');\r\n    });\r\n  };\r\n\r\n  if (!paymentIntent) {\r\n    return (\r\n      <div className=\"alert alert-warning\">\r\n        Ödeme bilgisi bulunamadı.\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"payment-success\">\r\n      <div className=\"container\">\r\n        <div className=\"row justify-content-center\">\r\n          <div className=\"col-lg-8\">\r\n            \r\n            {/* Başarı mesajı */}\r\n            <div className=\"card border-success shadow-lg\">\r\n              <div className=\"card-header bg-success text-white text-center py-4\">\r\n                <FontAwesomeIcon icon={faCheckCircle} size=\"3x\" className=\"mb-3\" />\r\n                <h2 className=\"mb-0\">Ödeme Başarılı!</h2>\r\n                <p className=\"mb-0\">İşleminiz başarıyla tamamlandı</p>\r\n              </div>\r\n\r\n              <div className=\"card-body p-4\">\r\n                \r\n                {/* Ödeme detayları */}\r\n                <div className=\"row mb-4\">\r\n                  <div className=\"col-12\">\r\n                    <h5 className=\"text-success mb-3\">\r\n                      <FontAwesomeIcon icon={faCheckCircle} className=\"me-2\" />\r\n                      İşlem Detayları\r\n                    </h5>\r\n                    \r\n                    <div className=\"table-responsive\">\r\n                      <table className=\"table table-borderless\">\r\n                        <tbody>\r\n                          <tr>\r\n                            <td><strong>İşlem No:</strong></td>\r\n                            <td>\r\n                              <code>{paymentIntent.id}</code>\r\n                              <button \r\n                                className=\"btn btn-sm btn-outline-secondary ms-2\"\r\n                                onClick={() => copyToClipboard(paymentIntent.id)}\r\n                                title=\"Kopyala\"\r\n                              >\r\n                                <FontAwesomeIcon icon={faClipboard} />\r\n                              </button>\r\n                            </td>\r\n                          </tr>\r\n                          <tr>\r\n                            <td><strong>Tutar:</strong></td>\r\n                            <td className=\"text-success\">\r\n                              <strong>{formatAmount(paymentIntent.amount, paymentIntent.currency)}</strong>\r\n                            </td>\r\n                          </tr>\r\n                          <tr>\r\n                            <td><strong>İşlem Tarihi:</strong></td>\r\n                            <td>{formatDate(paymentIntent.created)}</td>\r\n                          </tr>\r\n                          <tr>\r\n                            <td><strong>Durum:</strong></td>\r\n                            <td>\r\n                              <span className=\"badge bg-success\">\r\n                                <FontAwesomeIcon icon={faCheckCircle} className=\"me-1\" />\r\n                                Başarılı\r\n                              </span>\r\n                            </td>\r\n                          </tr>\r\n                          {packageInfo && (\r\n                            <tr>\r\n                              <td><strong>Paket:</strong></td>\r\n                              <td>\r\n                                <strong>{packageInfo.name}</strong>\r\n                                <br />\r\n                                <small className=\"text-muted\">{packageInfo.description}</small>\r\n                              </td>\r\n                            </tr>\r\n                          )}\r\n                        </tbody>\r\n                      </table>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Hizmet aktivasyon bilgisi */}\r\n                {packageInfo && (\r\n                  <div className=\"alert alert-info\">\r\n                    <h6 className=\"alert-heading\">\r\n                      <FontAwesomeIcon icon={faCheckCircle} className=\"me-2\" />\r\n                      Hizmet Aktivasyonu\r\n                    </h6>\r\n                    <p className=\"mb-0\">\r\n                      <strong>{packageInfo.name}</strong> paketiniz aktifleştirilmiştir. \r\n                      {packageInfo.duration && (\r\n                        <> {packageInfo.duration} süreyle kullanabilirsiniz.</>\r\n                      )}\r\n                    </p>\r\n                    {packageInfo.features && (\r\n                      <ul className=\"mt-2 mb-0\">\r\n                        {packageInfo.features.map((feature, index) => (\r\n                          <li key={index}>{feature}</li>\r\n                        ))}\r\n                      </ul>\r\n                    )}\r\n                  </div>\r\n                )}\r\n\r\n                {/* Aksiyonlar */}\r\n                <div className=\"row g-2 mt-4\">\r\n                  <div className=\"col-sm-6 col-lg-3\">\r\n                    <button \r\n                      className=\"btn btn-outline-primary w-100\"\r\n                      onClick={onDownloadReceipt}\r\n                    >\r\n                      <FontAwesomeIcon icon={faDownload} className=\"me-2\" />\r\n                      Makbuz İndir\r\n                    </button>\r\n                  </div>\r\n                  \r\n                  <div className=\"col-sm-6 col-lg-3\">\r\n                    <button \r\n                      className=\"btn btn-outline-info w-100\"\r\n                      onClick={onSendEmail}\r\n                    >\r\n                      <FontAwesomeIcon icon={faEnvelope} className=\"me-2\" />\r\n                      E-posta Gönder\r\n                    </button>\r\n                  </div>\r\n                  \r\n                  <div className=\"col-sm-6 col-lg-3\">\r\n                    <button \r\n                      className=\"btn btn-outline-secondary w-100\"\r\n                      onClick={onPrintReceipt}\r\n                    >\r\n                      <FontAwesomeIcon icon={faPrint} className=\"me-2\" />\r\n                      Yazdır\r\n                    </button>\r\n                  </div>\r\n                  \r\n                  <div className=\"col-sm-6 col-lg-3\">\r\n                    <button \r\n                      className=\"btn btn-success w-100\"\r\n                      onClick={onReturnHome}\r\n                    >\r\n                      <FontAwesomeIcon icon={faHome} className=\"me-2\" />\r\n                      Ana Sayfa\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Destek bilgisi */}\r\n                <div className=\"alert alert-light mt-4\">\r\n                  <h6 className=\"alert-heading\">Destek</h6>\r\n                  <p className=\"mb-0\">\r\n                    Herhangi bir sorunuz için: \r\n                    <strong> <EMAIL></strong> adresinden \r\n                    veya <strong>0850 123 4567</strong> numarasından bize ulaşabilirsiniz.\r\n                  </p>\r\n                  <small className=\"text-muted\">\r\n                    Destek talepleri için lütfen işlem numaranızı ({paymentIntent.id}) belirtin.\r\n                  </small>\r\n                </div>\r\n\r\n              </div>\r\n            </div>\r\n\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PaymentSuccess;", "import React, { useState, useEffect } from 'react';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport {\r\n    faCreditCard, faCalendarAlt, faLock, faTag,\r\n    faTurkishLiraSign, faHistory, faClock, faUser, faMicrochip, faCoins, faEuroSign\r\n} from '@fortawesome/free-solid-svg-icons';\r\n\r\nimport { paymentService, cihazIDService } from '../api/dbService';\r\nimport Header from '../components/Header';\r\nimport Sidebar from '../components/Sidebar';\r\nimport Footer from '../components/Footer';\r\nimport LoadingSpinner from '../components/LoadingSpinner';\r\nimport ErrorMessage from '../components/ErrorMessage';\r\nimport ParamPOSForm from '../components/payment/ParamPOSForm';\r\nimport PayTRForm from '../components/payment/PayTRForm';\r\nimport PaymentSuccess from '../components/payment/PaymentSuccess';\r\n\r\nconst Payment = () => {\r\n    const [loading, setLoading] = useState(true);\r\n    const [packages, setPackages] = useState([]);\r\n    const [selectedPackage, setSelectedPackage] = useState(null);\r\n    const [exchangeRate, setExchangeRate] = useState(0);\r\n    const [eurRate, setEurRate] = useState(null);\r\n    const [userDevices, setUserDevices] = useState([]);\r\n    const [selectedDeviceForCredit, setSelectedDeviceForCredit] = useState('');\r\n    const [paymentHistory, setPaymentHistory] = useState([]);\r\n    const [remainingUsage, setRemainingUsage] = useState(null);\r\n    const [error, setError] = useState(null);\r\n    const [showHistory, setShowHistory] = useState(false);\r\n    const [processingPayment, setProcessingPayment] = useState(false);\r\n    const [paymentSuccess, setPaymentSuccess] = useState(false);\r\n    const [paymentError, setPaymentError] = useState(null);\r\n    const [completedPayment, setCompletedPayment] = useState(null);\r\n    const [showParamPOS, setShowParamPOS] = useState(false);\r\n\r\n\r\n    // Seçili paket state'i\r\n    const [selectedPackageForPayment, setSelectedPackageForPayment] = useState(null);\r\n\r\n    // EUR kuru alma fonksiyonu - TCMB resmi XML API\r\n    const fetchEURRate = async () => {\r\n        try {\r\n            // Backend API'den EUR kuru al\r\n            const response = await fetch(`${process.env.REACT_APP_API_URL || '//mgz24.com/api'}/payments/exchange-rate`);\r\n            \r\n            if (!response.ok) {\r\n                throw new Error(`HTTP error! status: ${response.status}`);\r\n            }\r\n            \r\n            const data = await response.json();\r\n            \r\n            if (data.EUR && data.EUR.selling) {\r\n                setEurRate(data.EUR.selling);\r\n                setExchangeRate(data.EUR.selling);\r\n                \r\n                // Eğer fallback değer kullanıldıysa uyarı göster\r\n                if (data.EUR.fallback) {\r\n                    console.warn('Canlı kur alınamadı, fallback değer kullanılıyor:', data.EUR.error);\r\n                }\r\n            } else {\r\n                throw new Error('EUR kuru backend\\'den alınamadı');\r\n            }\r\n        } catch (error) {\r\n            console.error('TCMB EUR kuru alınırken hata:', error);\r\n            // Fallback değer kullan\r\n            const fallbackRate = 34.98;\r\n            setEurRate(fallbackRate);\r\n            setExchangeRate(fallbackRate);\r\n            console.warn('Fallback EUR kuru kullanılıyor:', fallbackRate);\r\n        }\r\n    };\r\n\r\n    // Kullanıcının tüm cihazlarını getir (aktif + inaktif)\r\n    const fetchUserDevices = async (userId) => {\r\n        try {\r\n            // Kullanıcının tüm cihazlarını getir\r\n            const response = await fetch(`${process.env.REACT_APP_API_URL || '//mgz24.com/api'}/user-devices?userId=${userId}`, {\r\n                headers: {\r\n                    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`\r\n                }\r\n            });\r\n            \r\n            if (response.ok) {\r\n                const data = await response.json();\r\n                setUserDevices(data || []);\r\n            } else {\r\n                console.error('Kullanıcı cihazları alınamadı');\r\n                setUserDevices([]);\r\n            }\r\n        } catch (error) {\r\n            console.error('Kullanıcı cihazları alınırken hata:', error);\r\n            setUserDevices([]);\r\n        }\r\n    };\r\n\r\n\r\n    // Kredi kartı form state'i\r\n    const [cardForm, setCardForm] = useState({\r\n        cardHolder: '',\r\n        cardNumber: '',\r\n        expiryMonth: '',\r\n        expiryYear: '',\r\n        cvv: '',\r\n        agree: false\r\n    });\r\n\r\n    useEffect(() => {\r\n        // URL parametrelerini kontrol et (PayTR geri dönüş)\r\n        const urlParams = new URLSearchParams(window.location.hash.split('?')[1]);\r\n        const paymentStatus = urlParams.get('status');\r\n        const orderId = urlParams.get('order_id');\r\n        \r\n        if (paymentStatus && orderId) {\r\n            console.log('PayTR geri dönüş algılandı:', { paymentStatus, orderId });\r\n            \r\n            // Ödeme durumunu backend'den kontrol et\r\n            const checkPaymentStatus = async () => {\r\n                try {\r\n                    const response = await fetch(`${process.env.REACT_APP_API_URL || '//mgz24.com/api'}/payments/paytr/status/${orderId}`, {\r\n                        headers: {\r\n                            'Authorization': `Bearer ${localStorage.getItem('auth_token')}`\r\n                        }\r\n                    });\r\n                    \r\n                    if (response.ok) {\r\n                        // Response'un content-type kontrolü\r\n                        const contentType = response.headers.get('content-type');\r\n                        \r\n                        if (contentType && contentType.includes('application/json')) {\r\n                            const data = await response.json();\r\n                            console.log('PayTR ödeme durumu:', data);\r\n                            \r\n                            if (data.status === 'tamamlandi') {\r\n                                setPaymentSuccess(true);\r\n                                setCompletedPayment({ \r\n                                    id: orderId,\r\n                                    orderId, \r\n                                    status: 'success', \r\n                                    amount: data.amount * 100, // PaymentSuccess component için cent formatında\r\n                                    currency: 'try',\r\n                                    created: Math.floor(Date.now() / 1000)\r\n                                });\r\n                            } else if (data.status === 'basarisiz') {\r\n                                setPaymentError('Ödeme işlemi başarısız oldu.');\r\n                            } else if (data.status === 'beklemede') {\r\n                                setPaymentError('Ödeme işlemi beklemede. Lütfen birkaç dakika bekleyip sayfayı yenileyin.');\r\n                            }\r\n                        } else {\r\n                            // HTML yanıt geldi, muhtemelen authentication hatası\r\n                            console.warn('Backend\\'den HTML yanıt geldi, URL parametrelerine göre işlem yapılıyor');\r\n                            throw new Error('Backend authentication hatası');\r\n                        }\r\n                    } else {\r\n                        // Backend'den yanıt alınamadıysa URL parametrelerine göre işlem yap\r\n                        console.warn('Backend API hatası, URL parametrelerine göre işlem yapılıyor');\r\n                        throw new Error(`HTTP ${response.status}`);\r\n                    }\r\n                } catch (error) {\r\n                    console.error('PayTR ödeme durumu kontrol hatası:', error);\r\n                    // Hata durumunda URL parametrelerine göre işlem yap\r\n                    if (paymentStatus === 'success') {\r\n                        setPaymentSuccess(true);\r\n                        setCompletedPayment({ \r\n                            id: orderId,\r\n                            orderId, \r\n                            status: 'success',\r\n                            amount: 0, // Tutar bilinmiyor\r\n                            currency: 'try',\r\n                            created: Math.floor(Date.now() / 1000)\r\n                        });\r\n                    } else if (paymentStatus === 'failed') {\r\n                        setPaymentError('Ödeme işlemi başarısız oldu.');\r\n                    } else if (paymentStatus === 'pending') {\r\n                        setPaymentError('Ödeme işlemi beklemede. Lütfen birkaç dakika bekleyip sayfayı yenileyin.');\r\n                    }\r\n                }\r\n            };\r\n            \r\n            checkPaymentStatus();\r\n            \r\n            // URL'den parametreleri temizle\r\n            window.history.replaceState({}, document.title, window.location.pathname + window.location.hash.split('?')[0]);\r\n            return;\r\n        }\r\n\r\n        const fetchData = async () => {\r\n            try {\r\n                setLoading(true);\r\n                setError(null);\r\n\r\n                // Local storage'dan kullanıcı bilgilerini al\r\n                let storedUser;\r\n                try {\r\n                    storedUser = JSON.parse(localStorage.getItem('user'));\r\n                } catch (parseError) {\r\n                    console.error('localStorage parse hatası:', parseError);\r\n                    setError('Kullanıcı bilgilerine erişilemiyor. Lütfen tekrar giriş yapın.');\r\n                    setLoading(false);\r\n                    return;\r\n                }\r\n\r\n                // Debug için localStorage içeriğini detaylı logla\r\n                console.log('=== Payment.js Debug ===');\r\n                console.log('Full localStorage user:', storedUser);\r\n                console.log('storedUser?.user:', storedUser?.user);\r\n                if (storedUser?.user) {\r\n                    console.log('Available keys in user object:', Object.keys(storedUser.user));\r\n                    console.log('musteri_ID:', storedUser.user.musteri_ID);\r\n                    console.log('id:', storedUser.user.id);\r\n                }\r\n\r\n                // Kullanıcı ID'sini al (musteri_ID veya id)\r\n                const userId = storedUser?.user?.musteri_ID || storedUser?.user?.id;\r\n\r\n                console.log('Final userId:', userId);\r\n\r\n                if (!userId) {\r\n                    console.error('Kullanıcı ID bulunamadı:', storedUser);\r\n                    setError('Kullanıcı bilgilerine erişilemiyor. Lütfen tekrar giriş yapın.');\r\n                    return;\r\n                }\r\n\r\n\r\n                // API çağrılarını güvenli şekilde yap\r\n                let rateData, packagesData, usageData, historyData;\r\n                \r\n                try {\r\n                    // Her API çağrısını ayrı ayrı yap ve hataları yakala\r\n                    try {\r\n                        rateData = await paymentService.getExchangeRate();\r\n                    } catch (rateError) {\r\n                        console.warn('Döviz kuru alınamadı, varsayılan değer kullanılıyor:', rateError);\r\n                        rateData = 34.98;\r\n                    }\r\n                    \r\n                    try {\r\n                        packagesData = await paymentService.getPaymentPackages();\r\n                    } catch (packagesError) {\r\n                        console.warn('Paket bilgileri alınamadı, demo veriler kullanılıyor:', packagesError);\r\n                        packagesData = [\r\n                            { id: 0, days: 1, priceEUR: 0.03, name: 'Test Paketi (1 Gün)', priceTL: 1, isTest: true },\r\n                            { id: 1, days: 5, priceEUR: 3, name: '5 Gün Kullanım' },\r\n                            { id: 2, days: 10, priceEUR: 5, name: '10 Gün Kullanım' },\r\n                            { id: 3, days: 20, priceEUR: 6, name: '20 Gün Kullanım' },\r\n                            { id: 4, days: 30, priceEUR: 7, name: '30 Gün Kullanım' },\r\n                            { id: 5, days: 90, priceEUR: 18, name: '3 Ay Kullanım' },\r\n                            { id: 6, days: 180, priceEUR: 33, name: '6 Ay Kullanım' },\r\n                            { id: 7, days: 360, priceEUR: 60, name: '1 Yıl Kullanım' }\r\n                        ];\r\n                    }\r\n                    \r\n                    try {\r\n                        usageData = await paymentService.getRemainingUsage(userId);\r\n                    } catch (usageError) {\r\n                        console.warn('Kullanım bilgileri alınamadı, demo veriler kullanılıyor:', usageError);\r\n                        usageData = {\r\n                            remainingDays: 23,\r\n                            expiryDate: new Date(Date.now() + 23 * 24 * 60 * 60 * 1000).toISOString(),\r\n                            status: 'active'\r\n                        };\r\n                    }\r\n                    \r\n                    try {\r\n                        historyData = await paymentService.getPaymentHistory(userId);\r\n                    } catch (historyError) {\r\n                        console.warn('Ödeme geçmişi alınamadı, demo veriler kullanılıyor:', historyError);\r\n                        historyData = [\r\n                            {\r\n                                id: 'TRX-DEMO123',\r\n                                packageName: '30 Gün Kullanım',\r\n                                amountEUR: 7,\r\n                                amountTRY: 244.86,\r\n                                exchangeRate: 34.98,\r\n                                date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),\r\n                                status: 'completed'\r\n                            }\r\n                        ];\r\n                    }\r\n                } catch (apiError) {\r\n                    console.error('Genel API hatası:', apiError);\r\n                    // Tüm demo verileri kullan\r\n                    rateData = 34.98;\r\n                    packagesData = [\r\n                        { id: 0, days: 1, priceEUR: 0.03, name: 'Test Paketi (1 Gün)', priceTL: 1, isTest: true },\r\n                        { id: 1, days: 5, priceEUR: 3, name: '5 Gün Kullanım' },\r\n                        { id: 2, days: 10, priceEUR: 5, name: '10 Gün Kullanım' },\r\n                        { id: 3, days: 20, priceEUR: 6, name: '20 Gün Kullanım' },\r\n                        { id: 4, days: 30, priceEUR: 7, name: '30 Gün Kullanım' },\r\n                        { id: 5, days: 90, priceEUR: 18, name: '3 Ay Kullanım' },\r\n                        { id: 6, days: 180, priceEUR: 33, name: '6 Ay Kullanım' },\r\n                        { id: 7, days: 360, priceEUR: 60, name: '1 Yıl Kullanım' }\r\n                    ];\r\n                    usageData = {\r\n                        remainingDays: 23,\r\n                        expiryDate: new Date(Date.now() + 23 * 24 * 60 * 60 * 1000).toISOString(),\r\n                        status: 'active'\r\n                    };\r\n                    historyData = [\r\n                        {\r\n                            id: 'TRX-DEMO123',\r\n                            packageName: '30 Gün Kullanım',\r\n                            amountEUR: 7,\r\n                            amountTRY: 244.86,\r\n                            exchangeRate: 34.98,\r\n                            date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),\r\n                            status: 'completed'\r\n                        }\r\n                    ];\r\n                }\r\n\r\n                setExchangeRate(rateData);\r\n                setPackages(packagesData);\r\n                setRemainingUsage(usageData);\r\n                setPaymentHistory(historyData);\r\n                \r\n                // EUR kurunu getir\r\n                await fetchEURRate();\r\n                \r\n                // Kullanıcının cihazlarını getir\r\n                await fetchUserDevices(userId);\r\n\r\n                // Varsayılan olarak 30 günlük paketi seç\r\n                const defaultPackage = packagesData.find(pkg => pkg.days === 30) || packagesData[0];\r\n                setSelectedPackage(defaultPackage);\r\n\r\n            } catch (err) {\r\n                console.error(\"Veri alınırken hata:\", err);\r\n                setError(err.message || \"Veri alınırken bir hata oluştu\");\r\n            } finally {\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        fetchData();\r\n    }, []);\r\n\r\n    const handleCardFormChange = (e) => {\r\n        const { name, value, type, checked } = e.target;\r\n        setCardForm({\r\n            ...cardForm,\r\n            [name]: type === 'checkbox' ? checked : value\r\n        });\r\n    };\r\n\r\n\r\n    const formatCardNumber = (value) => {\r\n        // Kredi kartı numarasını formatla (4 hane + boşluk)\r\n        const v = value.replace(/\\s+/g, '').replace(/[^0-9]/gi, '');\r\n        const matches = v.match(/\\d{4,16}/g);\r\n        const match = (matches && matches[0]) || '';\r\n        const parts = [];\r\n\r\n        for (let i = 0; i < match.length; i += 4) {\r\n            parts.push(match.substring(i, i + 4));\r\n        }\r\n\r\n        if (parts.length) {\r\n            return parts.join(' ');\r\n        } else {\r\n            return value;\r\n        }\r\n    };\r\n\r\n\r\n    const calculateTotalPrice = () => {\r\n        if (!selectedPackage || !exchangeRate) return 0;\r\n        return (selectedPackage.priceEUR * exchangeRate).toFixed(2);\r\n    };\r\n\r\n    const handleSubmit = async (e) => {\r\n        e.preventDefault();\r\n\r\n        // Form doğrulama\r\n        if (!cardForm.cardHolder ||\r\n            !cardForm.cardNumber ||\r\n            !cardForm.expiryMonth ||\r\n            !cardForm.expiryYear ||\r\n            !cardForm.cvv ||\r\n            !cardForm.agree) {\r\n            setPaymentError(\"Lütfen tüm alanları doldurun ve şartları kabul edin\");\r\n            return;\r\n        }\r\n\r\n        // Kredi kartı numarası kontrolü (basit)\r\n        const cardNumberClean = cardForm.cardNumber.replace(/\\s/g, '');\r\n        if (cardNumberClean.length < 16) {\r\n            setPaymentError(\"Geçerli bir kredi kartı numarası girin\");\r\n            return;\r\n        }\r\n\r\n        try {\r\n            setProcessingPayment(true);\r\n            setPaymentError(null);\r\n\r\n            // Kullanıcı bilgilerini al\r\n            const storedUser = JSON.parse(localStorage.getItem('user'));\r\n            const userId = storedUser?.user?.musteri_ID || storedUser?.user?.id;\r\n\r\n            // Ödeme verisini hazırla\r\n            const paymentData = {\r\n                userId: userId,\r\n                packageId: selectedPackage.id,\r\n                cardDetails: {\r\n                    holderName: cardForm.cardHolder,\r\n                    number: cardNumberClean,\r\n                    expiryMonth: cardForm.expiryMonth,\r\n                    expiryYear: cardForm.expiryYear,\r\n                    cvv: cardForm.cvv\r\n                },\r\n                amount: {\r\n                    eur: selectedPackage.priceEUR,\r\n                    try: calculateTotalPrice(),\r\n                    exchangeRate: exchangeRate\r\n                }\r\n            };\r\n\r\n            // Demo ödeme işlemi simüle et\r\n            let result;\r\n            try {\r\n                result = await paymentService.processPayment(paymentData);\r\n            } catch (apiError) {\r\n                console.warn('Ödeme API\\'si başarısız, demo ödeme simüle ediliyor:', apiError);\r\n                // Demo başarılı ödeme\r\n                result = {\r\n                    success: true,\r\n                    message: 'Demo ödeme başarıyla tamamlandı',\r\n                    transactionId: 'TRX-DEMO' + Math.random().toString(36).substring(2, 8).toUpperCase(),\r\n                    date: new Date().toISOString()\r\n                };\r\n            }\r\n\r\n            if (result && result.success) {\r\n                setPaymentSuccess(true);\r\n\r\n                // Demo olarak kullanım süresini güncelle\r\n                const updatedUsage = {\r\n                    remainingDays: (remainingUsage?.remainingDays || 0) + selectedPackage.days,\r\n                    expiryDate: new Date(Date.now() + ((remainingUsage?.remainingDays || 0) + selectedPackage.days) * 24 * 60 * 60 * 1000).toISOString(),\r\n                    status: 'active'\r\n                };\r\n\r\n                const newPayment = {\r\n                    id: result.transactionId,\r\n                    packageName: selectedPackage.name,\r\n                    amountEUR: selectedPackage.priceEUR,\r\n                    amountTRY: parseFloat(calculateTotalPrice()),\r\n                    exchangeRate: exchangeRate,\r\n                    date: result.date,\r\n                    status: 'completed'\r\n                };\r\n\r\n                setRemainingUsage(updatedUsage);\r\n                setPaymentHistory([newPayment, ...paymentHistory]);\r\n\r\n                // Form alanlarını temizle\r\n                setCardForm({\r\n                    cardHolder: '',\r\n                    cardNumber: '',\r\n                    expiryMonth: '',\r\n                    expiryYear: '',\r\n                    cvv: '',\r\n                    agree: false\r\n                });\r\n            } else {\r\n                setPaymentError(\"Ödeme işlemi sırasında bir hata oluştu\");\r\n            }\r\n        } catch (err) {\r\n            console.error(\"Ödeme işleminde hata:\", err);\r\n            setPaymentError(err.message || \"Ödeme işlemi sırasında bir hata oluştu\");\r\n        } finally {\r\n            setProcessingPayment(false);\r\n        }\r\n    };\r\n\r\n\r\n    const handlePaymentSuccess = async (paymentIntent) => {\r\n        console.log('Ödeme başarılı:', paymentIntent);\r\n        setCompletedPayment(paymentIntent);\r\n        setPaymentSuccess(true);\r\n        setShowParamPOS(false);\r\n        \r\n        // Seçili cihaza kredi ekle\r\n        try {\r\n            if (selectedDeviceForCredit && selectedPackage) {\r\n                // Backend'e cihaza kredi ekleme isteği gönder\r\n                const response = await fetch(`${process.env.REACT_APP_API_URL || '//mgz24.com/api'}/cihaz-bilgi/${selectedDeviceForCredit}/add-credit`, {\r\n                    method: 'POST',\r\n                    headers: {\r\n                        'Content-Type': 'application/json',\r\n                        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`\r\n                    },\r\n                    body: JSON.stringify({\r\n                        days: selectedPackage.days,\r\n                        paymentId: paymentIntent.id,\r\n                        packageName: selectedPackage.name\r\n                    })\r\n                });\r\n\r\n                if (response.ok) {\r\n                    console.log('Cihaza kredi başarıyla eklendi');\r\n                    // Kullanıcı cihazlarını yeniden yükle\r\n                    const storedUser = JSON.parse(localStorage.getItem('user'));\r\n                    const userId = storedUser?.user?.musteri_ID || storedUser?.user?.id;\r\n                    if (userId) {\r\n                        await fetchUserDevices(userId);\r\n                    }\r\n                } else {\r\n                    console.error('Cihaza kredi ekleme hatası:', await response.text());\r\n                }\r\n            }\r\n        } catch (error) {\r\n            console.error('Cihaz kredi ekleme hatası:', error);\r\n        }\r\n        \r\n        // Kullanım süresini güncelle\r\n        try {\r\n            const storedUser = JSON.parse(localStorage.getItem('user'));\r\n            const userId = storedUser?.user?.musteri_ID || storedUser?.user?.id;\r\n            \r\n            if (userId) {\r\n                const [updatedUsage, updatedHistory] = await Promise.all([\r\n                    paymentService.getRemainingUsage(userId),\r\n                    paymentService.getPaymentHistory(userId)\r\n                ]);\r\n\r\n                setRemainingUsage(updatedUsage);\r\n                setPaymentHistory(updatedHistory);\r\n            }\r\n        } catch (error) {\r\n            console.error('Kullanım süresi güncellenirken hata:', error);\r\n        }\r\n    };\r\n\r\n    const handlePaymentError = (error) => {\r\n        console.error('Ödeme hatası:', error);\r\n        setPaymentError(error.message || 'Ödeme işlemi başarısız');\r\n        setShowParamPOS(false);\r\n    };\r\n\r\n    const handleDownloadReceipt = () => {\r\n        if (!completedPayment) return;\r\n        \r\n        // Makbuz PDF oluştur ve indir\r\n        const receiptContent = `\r\n            MGZ24 ÖDEME MAKBUZu\r\n            ==================\r\n            İşlem No: ${completedPayment.id}\r\n            Tutar: ${(completedPayment.amount / 100).toFixed(2)} ${completedPayment.currency.toUpperCase()}\r\n            Tarih: ${new Date(completedPayment.created * 1000).toLocaleString('tr-TR')}\r\n            Durum: Başarılı\r\n            \r\n            Bu makbuz ödeme kanıtınızdır.\r\n        `;\r\n        \r\n        const blob = new Blob([receiptContent], { type: 'text/plain' });\r\n        const url = URL.createObjectURL(blob);\r\n        const a = document.createElement('a');\r\n        a.href = url;\r\n        a.download = `MGZ24_Makbuz_${completedPayment.id}.txt`;\r\n        a.click();\r\n        URL.revokeObjectURL(url);\r\n    };\r\n\r\n    const handleSendEmail = async () => {\r\n        if (!completedPayment) return;\r\n        \r\n        try {\r\n            // E-posta gönderme API'sini çağır\r\n            const response = await fetch('/api/send-receipt-email', {\r\n                method: 'POST',\r\n                headers: { 'Content-Type': 'application/json' },\r\n                body: JSON.stringify({\r\n                    paymentIntentId: completedPayment.id,\r\n                    userEmail: JSON.parse(localStorage.getItem('user'))?.user?.email\r\n                })\r\n            });\r\n            \r\n            if (response.ok) {\r\n                alert('Makbuz e-posta adresinize gönderildi.');\r\n            } else {\r\n                alert('E-posta gönderilirken hata oluştu.');\r\n            }\r\n        } catch (error) {\r\n            console.error('E-posta gönderme hatası:', error);\r\n            alert('E-posta gönderilirken hata oluştu.');\r\n        }\r\n    };\r\n\r\n    const handlePrintReceipt = () => {\r\n        if (!completedPayment) return;\r\n        \r\n        const printWindow = window.open('', '_blank');\r\n        printWindow.document.write(`\r\n            <html>\r\n                <head>\r\n                    <title>MGZ24 Ödeme Makbuzu</title>\r\n                    <style>\r\n                        body { font-family: Arial, sans-serif; padding: 20px; }\r\n                        .header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; }\r\n                        .details { margin: 20px 0; }\r\n                        .row { display: flex; justify-content: space-between; margin: 5px 0; }\r\n                    </style>\r\n                </head>\r\n                <body>\r\n                    <div class=\"header\">\r\n                        <h2>MGZ24 ÖDEME MAKBUZU</h2>\r\n                    </div>\r\n                    <div class=\"details\">\r\n                        <div class=\"row\"><span>İşlem No:</span><span>${completedPayment.id}</span></div>\r\n                        <div class=\"row\"><span>Tutar:</span><span>${(completedPayment.amount / 100).toFixed(2)} ${completedPayment.currency.toUpperCase()}</span></div>\r\n                        <div class=\"row\"><span>Tarih:</span><span>${new Date(completedPayment.created * 1000).toLocaleString('tr-TR')}</span></div>\r\n                        <div class=\"row\"><span>Durum:</span><span>Başarılı</span></div>\r\n                    </div>\r\n                </body>\r\n            </html>\r\n        `);\r\n        printWindow.document.close();\r\n        printWindow.print();\r\n    };\r\n\r\n    const handleReturnHome = () => {\r\n        setPaymentSuccess(false);\r\n        setCompletedPayment(null);\r\n        setShowParamPOS(false);\r\n    };\r\n\r\n    // Eğer ödeme başarılıysa success sayfasını göster\r\n    if (paymentSuccess && completedPayment) {\r\n        return (\r\n            <div className=\"container-fluid\">\r\n                <div className=\"row\">\r\n                    <Header />\r\n                    <Sidebar />\r\n                    <main className=\"col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white\">\r\n                        <PaymentSuccess \r\n                            paymentIntent={completedPayment}\r\n                            packageInfo={selectedPackageForPayment}\r\n                            onDownloadReceipt={handleDownloadReceipt}\r\n                            onSendEmail={handleSendEmail}\r\n                            onPrintReceipt={handlePrintReceipt}\r\n                            onReturnHome={handleReturnHome}\r\n                        />\r\n                    </main>\r\n                    <Footer />\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    // Eğer Param POS formu gösteriliyorsa\r\n    if (showParamPOS && selectedPackageForPayment) {\r\n        return (\r\n            <div className=\"container-fluid\">\r\n                <div className=\"row\">\r\n                    <Header />\r\n                    <Sidebar />\r\n                    <main className=\"col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white\">\r\n                        <div className=\"pt-3 pb-1 mt-3 mb-3 border-bottom\">\r\n                            <h1 className=\"h4 text-dark\">\r\n                                Güvenli Ödeme - {selectedPackageForPayment.name}\r\n                            </h1>\r\n                            <button \r\n                                className=\"btn btn-outline-secondary btn-sm\"\r\n                                onClick={() => setShowParamPOS(false)}\r\n                            >\r\n                                ← Geri Dön\r\n                            </button>\r\n                        </div>\r\n                        \r\n                        <div className=\"row justify-content-center\">\r\n                            <div className=\"col-lg-8\">\r\n                                <ParamPOSForm \r\n                                    amount={selectedPackage.priceEUR * exchangeRate}\r\n                                    currency=\"TRY\"\r\n                                    packageInfo={selectedPackage}\r\n                                    deviceId={selectedDeviceForCredit}\r\n                                    onSuccess={handlePaymentSuccess}\r\n                                    onError={handlePaymentError}\r\n                                    disabled={processingPayment}\r\n                                />\r\n                            </div>\r\n                        </div>\r\n                    </main>\r\n                    <Footer />\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <div className=\"container-fluid\">\r\n            <div className=\"row\">\r\n                <Header />\r\n                <Sidebar />\r\n\r\n                <main className=\"col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white\">\r\n                    <div className=\"pt-3 pb-1 mt-3 mb-3 border-bottom\">\r\n                        <div className=\"d-flex justify-content-between align-items-center\">\r\n                            <h1 className=\"h4 text-dark mb-0\">Kullanım Süresi Satın Al</h1>\r\n                            {eurRate && (\r\n                                <div className=\"d-flex align-items-center text-muted\">\r\n                                    <span className=\"me-2 small\">Güncel Euro Kuru:</span>\r\n                                    <span className=\"badge bg-primary\">\r\n                                        <FontAwesomeIcon icon={faTurkishLiraSign} className=\"me-1\" />\r\n                                        {eurRate.toFixed(2)}\r\n                                    </span>\r\n                                </div>\r\n                            )}\r\n                        </div>\r\n                    </div>\r\n\r\n                    {loading ? (\r\n                        <LoadingSpinner \r\n                            size=\"lg\" \r\n                            variant=\"primary\" \r\n                            message=\"Paket bilgileri yükleniyor...\" \r\n                            centered={true}\r\n                        />\r\n                    ) : error ? (\r\n                        <ErrorMessage \r\n                            message={error} \r\n                            variant=\"danger\"\r\n                            title=\"Veri Yükleme Hatası\"\r\n                            dismissible={true}\r\n                            onDismiss={() => setError('')}\r\n                        >\r\n                            <button className=\"btn btn-primary btn-sm mt-2\" onClick={() => window.location.reload()}>\r\n                                Yeniden Dene\r\n                            </button>\r\n                        </ErrorMessage>\r\n                    ) : (\r\n                        <div className=\"row\">\r\n                            {/* Paket Seçimi */}\r\n                            <div className=\"col-lg-6 mb-4\">\r\n                                <div className=\"card border-0 shadow-sm\">\r\n                                    <div className=\"card-header bg-primary text-white\">\r\n                                        <FontAwesomeIcon icon={faTag} className=\"me-2\" />\r\n                                        <span className=\"fw-bold\">Kullanım Paketi Seçin</span>\r\n                                    </div>\r\n                                    <div className=\"card-body\">\r\n                                        <div className=\"row g-3\">\r\n                                            {packages.map((pkg) => (\r\n                                                <div key={pkg.id} className=\"col-12\">\r\n                                                    <div \r\n                                                        className={`card border ${selectedPackage?.id === pkg.id ? 'border-primary bg-primary bg-opacity-10' : 'border-light'} cursor-pointer`}\r\n                                                        onClick={() => setSelectedPackage(pkg)}\r\n                                                        style={{ cursor: 'pointer', transition: 'all 0.2s' }}\r\n                                                    >\r\n                                                        <div className=\"card-body py-3\">\r\n                                                            <div className=\"d-flex justify-content-between align-items-center\">\r\n                                                                <div>\r\n                                                                    <h6 className=\"card-title mb-1 fw-bold\">\r\n                                                                        <FontAwesomeIcon icon={faClock} className=\"me-2 text-primary\" />\r\n                                                                        {pkg.days} Gün\r\n                                                                        {pkg.isTest && (\r\n                                                                            <span className=\"badge bg-warning ms-2\">TEST</span>\r\n                                                                        )}\r\n                                                                    </h6>\r\n                                                                    <small className=\"text-muted\">{pkg.name}</small>\r\n                                                                </div>\r\n                                                                <div className=\"text-end\">\r\n                                                                    {pkg.isTest ? (\r\n                                                                        <div className=\"fw-bold text-warning\">\r\n                                                                            <FontAwesomeIcon icon={faTurkishLiraSign} className=\"me-1\" />\r\n                                                                            ₺{pkg.priceTL}\r\n                                                                        </div>\r\n                                                                    ) : (\r\n                                                                        <>\r\n                                                                            <div className=\"fw-bold text-primary\">\r\n                                                                                <FontAwesomeIcon icon={faEuroSign} className=\"me-1\" />\r\n                                                                                €{pkg.priceEUR}\r\n                                                                            </div>\r\n                                                                            <small className=\"text-muted\">\r\n                                                                                <FontAwesomeIcon icon={faTurkishLiraSign} className=\"me-1\" />\r\n                                                                                ₺{(pkg.priceEUR * exchangeRate).toFixed(2)}\r\n                                                                            </small>\r\n                                                                        </>\r\n                                                                    )}\r\n                                                                </div>\r\n                                                            </div>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                </div>\r\n                                            ))}\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n\r\n                            {/* Cihaz Seçimi ve Ödeme */}\r\n                            <div className=\"col-lg-6 mb-4\">\r\n                                <div className=\"card border-0 shadow-sm\">\r\n                                    <div className=\"card-header bg-success text-white\">\r\n                                        <FontAwesomeIcon icon={faMicrochip} className=\"me-2\" />\r\n                                        <span className=\"fw-bold\">Cihazınızı Seçin</span>\r\n                                    </div>\r\n                                    <div className=\"card-body\">\r\n                                        {userDevices.length > 0 ? (\r\n                                            <div className=\"mb-3\">\r\n                                                <label className=\"form-label small fw-bold\">Kredi Eklenecek Cihaz:</label>\r\n                                                <select \r\n                                                    className=\"form-select\"\r\n                                                    value={selectedDeviceForCredit}\r\n                                                    onChange={(e) => setSelectedDeviceForCredit(e.target.value)}\r\n                                                >\r\n                                                    <option value=\"\">Cihaz seçiniz...</option>\r\n                                                    {userDevices.map((device) => (\r\n                                                        <option key={device.ID} value={device.CihazID}>\r\n                                                            {device.CihazID} - {device.durum} - {device.kredi_gun}\r\n                                                        </option>\r\n                                                    ))}\r\n                                                </select>\r\n                                            </div>\r\n                                        ) : (\r\n                                            <div className=\"alert alert-warning\">\r\n                                                <FontAwesomeIcon icon={faMicrochip} className=\"me-2\" />\r\n                                                Size ait cihaz bulunamadı. Lütfen önce bir cihaz edinin.\r\n                                            </div>\r\n                                        )}\r\n\r\n                                        {/* Seçili Paket ve Cihaz Özeti */}\r\n                                        {selectedPackage && selectedDeviceForCredit && (\r\n                                            <div className=\"card bg-light border-0 mb-3\">\r\n                                                <div className=\"card-body\">\r\n                                                    <h6 className=\"card-title text-success\">\r\n                                                        <FontAwesomeIcon icon={faCoins} className=\"me-2\" />\r\n                                                        Ödeme Özeti\r\n                                                    </h6>\r\n                                                    <hr />\r\n                                                    <div className=\"row\">\r\n                                                        <div className=\"col-6\">\r\n                                                            <small className=\"text-muted\">Paket:</small>\r\n                                                            <div className=\"fw-bold\">{selectedPackage.days} Gün</div>\r\n                                                        </div>\r\n                                                        <div className=\"col-6\">\r\n                                                            <small className=\"text-muted\">Cihaz:</small>\r\n                                                            <div className=\"fw-bold\">{selectedDeviceForCredit}</div>\r\n                                                        </div>\r\n                                                        {selectedPackage.isTest ? (\r\n                                                            <div className=\"col-12 mt-2\">\r\n                                                                <small className=\"text-muted\">Test Tutarı:</small>\r\n                                                                <div className=\"fw-bold text-warning\">\r\n                                                                    ₺{selectedPackage.priceTL}\r\n                                                                    <span className=\"badge bg-warning ms-2\">TEST</span>\r\n                                                                </div>\r\n                                                            </div>\r\n                                                        ) : (\r\n                                                            <>\r\n                                                                <div className=\"col-6 mt-2\">\r\n                                                                    <small className=\"text-muted\">Euro:</small>\r\n                                                                    <div className=\"fw-bold text-primary\">€{selectedPackage.priceEUR}</div>\r\n                                                                </div>\r\n                                                                <div className=\"col-6 mt-2\">\r\n                                                                    <small className=\"text-muted\">Türk Lirası:</small>\r\n                                                                    <div className=\"fw-bold text-success\">₺{(selectedPackage.priceEUR * exchangeRate).toFixed(2)}</div>\r\n                                                                </div>\r\n                                                            </>\r\n                                                        )}\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        )}\r\n\r\n                                        {/* Ödeme Butonu */}\r\n                                        {selectedPackage && selectedDeviceForCredit && (\r\n                                            <button \r\n                                                className=\"btn btn-success w-100 py-3 fw-bold\"\r\n                                                onClick={() => setShowParamPOS(true)}\r\n                                                disabled={processingPayment}\r\n                                            >\r\n                                                <FontAwesomeIcon icon={faCreditCard} className=\"me-2\" />\r\n                                                {processingPayment ? 'İşlem Yapılıyor...' : 'Güvenli Ödeme Yap'}\r\n                                            </button>\r\n                                        )}\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n\r\n                            {/* Kullanım Durumu ve Geçmiş */}\r\n                            <div className=\"col-12 mb-4\">\r\n                                <div className=\"card border-0 shadow-sm\">\r\n                                    <div className=\"card-header bg-info text-white\">\r\n                                        <FontAwesomeIcon icon={faClock} className=\"me-2\" />\r\n                                        <span className=\"fw-bold\">Hesap Durumu</span>\r\n                                        <button\r\n                                            className=\"btn btn-sm btn-outline-light ms-auto\"\r\n                                            onClick={() => setShowHistory(!showHistory)}\r\n                                        >\r\n                                            <FontAwesomeIcon icon={faHistory} className=\"me-1\" />\r\n                                            {showHistory ? 'Gizle' : 'Geçmiş'}\r\n                                        </button>\r\n                                    </div>\r\n                                    <div className=\"card-body\">\r\n                                        <div className=\"row\">\r\n                                            <div className=\"col-md-3\">\r\n                                                <div className=\"text-center\">\r\n                                                    <div className=\"display-6 text-primary fw-bold\">\r\n                                                        {remainingUsage?.remainingDays || 0}\r\n                                                    </div>\r\n                                                    <small className=\"text-muted\">Kalan Gün</small>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div className=\"col-md-3\">\r\n                                                <div className=\"text-center\">\r\n                                                    <div className=\"display-6 text-success fw-bold\">\r\n                                                        {userDevices.length}\r\n                                                    </div>\r\n                                                    <small className=\"text-muted\">Aktif Cihaz</small>\r\n                                                </div>\r\n                                            </div>\r\n                                            <div className=\"col-md-6\">\r\n                                                <h6 className=\"text-muted\">Son Kullanım:</h6>\r\n                                                <p className=\"mb-0\">\r\n                                                    {remainingUsage?.expiryDate ? \r\n                                                        new Date(remainingUsage.expiryDate).toLocaleDateString('tr-TR') : \r\n                                                        'Belirtilmemiş'\r\n                                                    }\r\n                                                </p>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n\r\n                        </div>\r\n                    )}\r\n\r\n                    {/* PayTR Modal */}\r\n                    {showParamPOS && selectedPackage && selectedDeviceForCredit && (\r\n                        <div className=\"modal show d-block\" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>\r\n                            <div className=\"modal-dialog modal-xl\">\r\n                                <div className=\"modal-content\">\r\n                                    <div className=\"modal-header\">\r\n                                        <h5 className=\"modal-title\">PayTR Güvenli Ödeme</h5>\r\n                                        <button\r\n                                            type=\"button\"\r\n                                            className=\"btn-close\"\r\n                                            onClick={() => setShowParamPOS(false)}\r\n                                        ></button>\r\n                                    </div>\r\n                                    <div className=\"modal-body\">\r\n                                        <PayTRForm \r\n                                            amount={selectedPackage.isTest ? selectedPackage.priceTL : (selectedPackage.priceEUR * exchangeRate)}\r\n                                            currency=\"TL\"\r\n                                            packageInfo={selectedPackage}\r\n                                            deviceId={selectedDeviceForCredit}\r\n                                            onSuccess={handlePaymentSuccess}\r\n                                            onError={handlePaymentError}\r\n                                            disabled={processingPayment}\r\n                                        />\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    )}\r\n                </main>\r\n                <Footer />\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Payment;\r\n\r\n"], "names": ["Sidebar", "location", "useLocation", "isAdmin", "getUserRole", "_user$user", "_user$user2", "user", "JSON", "parse", "localStorage", "getItem", "role", "gorev", "_unused", "_jsx", "className", "children", "_jsxs", "tabIndex", "id", "type", "Link", "concat", "pathname", "to", "FontAwesomeIcon", "icon", "faDolly", "faFolderPlus", "faAnchor", "faFlag<PERSON><PERSON><PERSON><PERSON>", "faDesktop", "faBell", "faUser", "faStreetView", "faCreditCard", "faFileLines", "Footer", "Header", "navigate", "useNavigate", "userName", "setUserName", "useState", "loading", "setLoading", "useEffect", "async", "_storedUser$user", "_storedUser$user2", "_storedUser$user3", "_storedUser$user4", "storedUser", "userId", "musteri_ID", "name", "musteri_adi", "console", "warn", "userData", "kullaniciService", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setItem", "stringify", "apiError", "error", "fetchUserData", "src", "alt", "height", "onClick", "handleLogout", "removeItem", "faRightFromBracket", "faBars", "_ref", "amount", "currency", "onSuccess", "onError", "packageInfo", "disabled", "cardData", "setCardData", "cardNumber", "expiry<PERSON><PERSON><PERSON>", "expiryYear", "cvv", "cardHolderName", "showCvv", "setShowCvv", "processing", "setProcessing", "setError", "validationErrors", "setValidationErrors", "paramConfig", "setParamConfig", "merchantId", "process", "REACT_APP_PARAM_MERCHANT_ID", "terminalId", "REACT_APP_PARAM_TERMINAL_ID", "apiUrl", "REACT_APP_PARAM_API_URL", "testMode", "isValidCardNumber", "sum", "isEven", "i", "length", "digit", "parseInt", "char<PERSON>t", "handleInputChange", "field", "value", "formattedValue", "replace", "formatCardNumber", "slice", "toUpperCase", "prev", "_objectSpread", "undefined", "onSubmit", "event", "preventDefault", "validateCard", "errors", "currentDate", "Date", "expiry", "trim", "Object", "keys", "paymentResult", "Promise", "resolve", "setTimeout", "paymentIntent", "Math", "random", "toString", "substring", "status", "toLowerCase", "created", "floor", "now", "description", "paymentIntentResponse", "fetch", "method", "headers", "body", "clientSecret", "backendError", "json", "Error", "message", "confirmResponse", "paymentMethod", "card", "number", "padStart", "billingDetails", "window", "open", "next_action_url", "err", "errorMessage", "faShieldAlt", "faLock", "ErrorMessage", "variant", "dismissible", "on<PERSON><PERSON><PERSON>", "toFixed", "getCardIcon", "startsWith", "getCardType", "placeholder", "onChange", "e", "target", "max<PERSON><PERSON><PERSON>", "faEyeSlash", "faEye", "_Fragment", "faSpinner", "spin", "deviceId", "paymentToken", "setPaymentToken", "iframeUrl", "setIframeUrl", "paymentFormData", "setPaymentFormData", "createPaymentToken", "userInfo", "getUserInfo", "_storedUser$user5", "email", "phone", "tel", "log", "paymentData", "user_id", "device_id", "package_info", "user_name", "user_email", "user_phone", "user_basket", "price", "round", "quantity", "response", "ok", "errorData", "includes", "parseError", "statusText", "result", "loadPayTRIframe", "tokenResponse", "success", "iframe_token", "merchant_oid", "handlePayTRMessage", "origin", "data", "paytr_status", "payment_method", "errorMsg", "failed_reason_msg", "addEventListener", "removeEventListener", "script", "document", "createElement", "onload", "iFrameResize", "head", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "LoadingSpinner", "size", "faCheckCircle", "width", "frameBorder", "scrolling", "style", "border", "borderRadius", "title", "onDownloadReceipt", "onSendEmail", "onPrintReceipt", "onReturnHome", "gtag", "transaction_id", "items", "item_id", "item_name", "category", "copyToClipboard", "text", "navigator", "clipboard", "writeText", "then", "alert", "faClipboard", "Intl", "NumberFormat", "format", "timestamp", "toLocaleString", "year", "month", "day", "hour", "minute", "formatDate", "duration", "features", "map", "feature", "index", "faDownload", "faEnvelope", "faPrint", "faHome", "formatAmount", "Payment", "packages", "setPackages", "selected<PERSON><PERSON><PERSON>", "setSelectedPackage", "exchangeRate", "setExchangeRate", "eurRate", "setEurRate", "userDevices", "setUserDevices", "selectedDeviceForCredit", "setSelectedDeviceForCredit", "paymentHistory", "setPaymentHistory", "remainingUsage", "setRemainingUsage", "showHistory", "setShowHistory", "processingPayment", "setProcessingPayment", "paymentSuccess", "setPaymentSuccess", "paymentError", "setPaymentError", "completedPayment", "setCompletedPayment", "showParamPOS", "setShowParamPOS", "selectedPackageForPayment", "setSelectedPackageForPayment", "fetchUserDevices", "cardForm", "setCardForm", "cardHolder", "agree", "urlParams", "URLSearchParams", "hash", "split", "paymentStatus", "get", "orderId", "checkPaymentStatus", "contentType", "history", "replaceState", "_storedUser", "_storedUser2", "_storedUser3", "_storedUser3$user", "_storedUser4", "_storedUser4$user", "rateData", "packagesData", "usageData", "historyData", "paymentService", "getExchangeRate", "rateError", "getPaymentPackages", "packagesError", "days", "priceEUR", "priceTL", "isTest", "getRemainingUsage", "usageError", "remainingDays", "expiryDate", "toISOString", "getPaymentHistory", "historyError", "packageName", "amountEUR", "amountTRY", "date", "EUR", "selling", "fallback", "fallbackRate", "fetchEURRate", "defaultPackage", "find", "pkg", "fetchData", "handlePaymentSuccess", "paymentId", "_storedUser$user6", "updatedUsage", "updatedHistory", "all", "handlePaymentError", "handleDownloadReceipt", "receiptContent", "blob", "Blob", "url", "URL", "createObjectURL", "a", "href", "download", "click", "revokeObjectURL", "handleSendEmail", "_JSON$parse", "_JSON$parse$user", "paymentIntentId", "userEmail", "handlePrintReceipt", "printWindow", "write", "close", "print", "handleReturnHome", "PaymentSuccess", "ParamPOSForm", "faTurkishLiraSign", "centered", "reload", "faTag", "cursor", "transition", "faClock", "faEuroSign", "faMicrochip", "device", "CihazID", "durum", "kredi_gun", "ID", "faCoins", "faHistory", "toLocaleDateString", "backgroundColor", "PayTRForm"], "sourceRoot": ""}