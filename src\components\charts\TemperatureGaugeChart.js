import React from 'react';
import {
  <PERSON><PERSON><PERSON>ar<PERSON>hart,
  RadialBar,
  ResponsiveContainer,
  Legend,
  Cell
} from 'recharts';

/**
 * Sıcaklık ölçer grafik bileşeni
 * Context7 referansı: /recharts/recharts - RadialBarChart for gauge displays
 * @param {Object} props - Component props
 * @param {number} props.temperature - Mevcut sıcaklık
 * @param {number} props.minTemp - Minimum sıcaklık
 * @param {number} props.maxTemp - Maximum sıcaklık
 * @param {string} props.title - Grafik başlığı
 * @param {number} props.height - Grafik yüksekliği
 */
const TemperatureGaugeChart = ({ 
  temperature = 20, 
  minTemp = -10, 
  maxTemp = 50,
  title = 'Sıcaklık Ölçer',
  height = 300 
}) => {
  // Sıcaklığı 0-100 aralığına normalize et
  const normalizedTemp = Math.max(0, Math.min(100, 
    ((temperature - minTemp) / (maxTemp - minTemp)) * 100
  ));

  // Sıcaklık durumuna göre renk belirle
  const getTemperatureColor = (temp) => {
    if (temp < 0) return '#1976d2'; // Çok soğuk - mavi
    if (temp < 15) return '#2196f3'; // Soğuk - açık mavi
    if (temp < 25) return '#4caf50'; // Normal - yeşil
    if (temp < 35) return '#ff9800'; // Sıcak - turuncu
    return '#f44336'; // Çok sıcak - kırmızı
  };

  // Gauge için veri hazırla
  const data = [
    {
      name: 'Sıcaklık',
      value: normalizedTemp,
      fill: getTemperatureColor(temperature)
    }
  ];

  // Custom label component
  const renderCustomLabel = ({ cx, cy }) => {
    return (
      <g>
        <text 
          x={cx} 
          y={cy - 10} 
          fill={getTemperatureColor(temperature)} 
          textAnchor="middle" 
          fontSize="24" 
          fontWeight="bold"
        >
          {temperature.toFixed(1)}°C
        </text>
        <text 
          x={cx} 
          y={cy + 15} 
          fill="#666" 
          textAnchor="middle" 
          fontSize="12"
        >
          {getTemperatureStatus(temperature)}
        </text>
      </g>
    );
  };

  // Sıcaklık durumu metni
  const getTemperatureStatus = (temp) => {
    if (temp < 0) return 'Çok Soğuk';
    if (temp < 15) return 'Soğuk';
    if (temp < 25) return 'Normal';
    if (temp < 35) return 'Sıcak';
    return 'Çok Sıcak';
  };

  // Custom legend
  const renderLegend = () => {
    return (
      <div className="text-center mt-2">
        <div className="d-flex justify-content-center align-items-center">
          <small className="text-muted me-3">
            Min: {minTemp}°C
          </small>
          <small className="text-muted me-3">
            Maks: {maxTemp}°C
          </small>
          <small className="text-muted">
            Durum: {getTemperatureStatus(temperature)}
          </small>
        </div>
      </div>
    );
  };

  return (
    <div className="w-100">
      <h5 className="text-center mb-3">{title}</h5>
      <ResponsiveContainer width="100%" height={height}>
        <RadialBarChart
          cx="50%"
          cy="50%"
          innerRadius="60%"
          outerRadius="90%"
          startAngle={180}
          endAngle={0}
          data={data}
        >
          <RadialBar
            dataKey="value"
            cornerRadius={10}
            fill={getTemperatureColor(temperature)}
            label={renderCustomLabel}
          />
        </RadialBarChart>
      </ResponsiveContainer>
      {renderLegend()}
    </div>
  );
};

export default TemperatureGaugeChart;