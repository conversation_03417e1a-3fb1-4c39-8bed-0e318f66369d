# MGZ24 API HTTPS Sunucusu Başlatma Scripti (PowerShell)
Write-Host "MGZ24 API HTTPS Sunucusu Başlatılıyor..." -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# SSL sertifika dosyalarının varlığını kontrol et
$privkeyPath = "/etc/letsencrypt/live/ffl21.fun/privkey.pem"
$certPath = "/etc/letsencrypt/live/ffl21.fun/fullchain.pem"

if (-not (Test-Path $privkeyPath)) {
    Write-Host "HATA: SSL private key dosyası bulunamadı!" -ForegroundColor Red
    Write-Host "Dosya yolu: $privkeyPath" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path $certPath)) {
    Write-Host "HATA: SSL certificate dosyası bulunamadı!" -ForegroundColor Red
    Write-Host "Dosya yolu: $certPath" -ForegroundColor Red
    exit 1
}

Write-Host "SSL sertifika dosyaları kontrol edildi ✓" -ForegroundColor Green

# Node.js modüllerini yükle
Write-Host "Node.js modülleri yükleniyor..." -ForegroundColor Yellow
npm install

# Port 80 ve 443'ün kullanılabilir olduğunu kontrol et
$port80InUse = Get-NetTCPConnection -LocalPort 80 -ErrorAction SilentlyContinue
$port3001InUse = Get-NetTCPConnection -LocalPort 3001 -ErrorAction SilentlyContinue

if ($port80InUse) {
    Write-Host "UYARI: Port 80 zaten kullanımda. HTTP yönlendirme başlatılmayacak." -ForegroundColor Yellow
    $HTTP_REDIRECT = $false
}
else {
    $HTTP_REDIRECT = $true
}

if ($port3001InUse) {
    Write-Host "HATA: Port 3001 zaten kullanımda!" -ForegroundColor Red
    Write-Host "Lütfen port 3001'i kullanan servisi durdurun." -ForegroundColor Red
    exit 1
}

Write-Host "Portlar kontrol edildi ✓" -ForegroundColor Green

# HTTPS sunucusunu başlat
Write-Host "HTTPS sunucusu başlatılıyor..." -ForegroundColor Yellow
Start-Process -FilePath "node" -ArgumentList "mgz24api.js" -NoNewWindow

# HTTP yönlendirme sunucusunu başlat (eğer port 80 boşsa)
if ($HTTP_REDIRECT) {
    Write-Host "HTTP yönlendirme sunucusu başlatılıyor..." -ForegroundColor Yellow
    Start-Process -FilePath "node" -ArgumentList "http-redirect.js" -NoNewWindow
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "MGZ24 API Sunucusu Başarıyla Başlatıldı!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host "HTTPS API: https://ffl21.fun:3001" -ForegroundColor Cyan
Write-Host "Health Check: https://ffl21.fun:3001/health" -ForegroundColor Cyan
Write-Host ""
Write-Host "Sunucuları durdurmak için: Get-Process node | Stop-Process" -ForegroundColor Yellow
Write-Host "" 