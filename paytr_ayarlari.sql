-- PayTR Ödeme Sistemi Ayarları
-- Bu dosyayı veritabanında çalıştırarak PayTR API ayarlarını sistem_ayarlari tablosuna ekleyebilirsiniz

INSERT INTO sistem_ayarlari (ayar_anahtari, ayar_degeri, ayar_tipi, aciklama, herkese_acik) 
VALUES 
('paytr_merchant_id', '', 'metin', 'PayTR Mağaza Numarası (Merchant ID)', 0),
('paytr_merchant_key', '', 'metin', 'PayTR Mağaza Parolası (Merchant Key)', 0),
('paytr_merchant_salt', '', 'metin', 'PayTR Mağaza Gizli Anahtarı (Merchant Salt)', 0),
('paytr_test_mode', '1', 'boolean', 'PayTR Test Modu (1: Test, 0: Canlı)', 0),
('paytr_ok_url', '/payment/success', 'metin', 'PayTR Başarılı Ödeme Return URL', 0),
('paytr_fail_url', '/payment/fail', 'metin', 'PayTR Başarısız Ödeme Return URL', 0),
('paytr_timeout_limit', '30', 'sayi', 'PayTR Ödeme Zaman Aşımı (dakika)', 0),
('paytr_installment_limit', '0', 'sayi', 'PayTR Maksimum Taksit Sayısı (0: Tek Çekim)', 0),
('paytr_lang', 'tr', 'metin', 'PayTR Dil Ayarı (tr, en)', 0),
('paytr_debug_on', '0', 'boolean', 'PayTR Debug Modu (1: Açık, 0: Kapalı)', 0);

-- PayTR ayarlarını görmek için:
-- SELECT * FROM sistem_ayarlari WHERE ayar_anahtari LIKE 'paytr_%';

-- PayTR bilgilerinizi güncellemek için örnek komutlar:
-- UPDATE sistem_ayarlari SET ayar_degeri = 'XXXXXX' WHERE ayar_anahtari = 'paytr_merchant_id';
-- UPDATE sistem_ayarlari SET ayar_degeri = 'YYYYYY' WHERE ayar_anahtari = 'paytr_merchant_key';
-- UPDATE sistem_ayarlari SET ayar_degeri = 'ZZZZZZ' WHERE ayar_anahtari = 'paytr_merchant_salt';