import React, { Suspense, lazy } from 'react';
import LoadingSpinner from './LoadingSpinner';

// Google Map component'ini lazy load et
const GoogleMapComponent = lazy(() => import('./GoogleMap'));

/**
 * Lazy loading wrapper for GoogleMap component
 * @param {Object} props - Props to pass to GoogleMap
 */
const LazyGoogleMap = (props) => {
  return (
    <Suspense 
      fallback={
        <div style={{ height: '400px', position: 'relative' }}>
          <LoadingSpinner 
            size="lg" 
            variant="primary" 
            message="Harita yükleniyor..." 
            centered={true}
          />
        </div>
      }
    >
      <GoogleMapComponent {...props} />
    </Suspense>
  );
};

export default LazyGoogleMap;