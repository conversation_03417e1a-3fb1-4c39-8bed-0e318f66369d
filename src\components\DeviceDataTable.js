import React, { useState, useEffect } from 'react';
import { fetchDeviceHistory } from '../services/deviceService';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
    faThermometerHalf,
    faEye,
    faMapMarkerAlt,
    faDownload,
    faSync
} from '@fortawesome/free-solid-svg-icons';
import LoadingSpinner from './LoadingSpinner';
import ErrorMessage from './ErrorMessage';

/**
 * Cihaz verilerini tablo halinde gösteren bileşen
 * Son 100 veriyi tarih, sıcaklık, nem ve konum bilgileriyle birlikte gösterir
 */
const DeviceDataTable = ({ cihazID, limit = 100 }) => {
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [lastUpdate, setLastUpdate] = useState(null);

    // Veri çekme fonksiyonu
    const fetchData = async () => {
        try {
            setLoading(true);
            setError(null);

            const result = await fetchDeviceHistory(cihazID, limit);

            if (result.success) {
                setData(result.data);
                setLastUpdate(new Date());
            } else {
                setError(result.message || 'Veri alınamadı');
            }
        } catch (err) {
            console.error('Veri tablosu yükleme hatası:', err);
            setError('Veriler yüklenirken bir hata oluştu.');
        } finally {
            setLoading(false);
        }
    };

    // Component mount edildiğinde veri çek
    useEffect(() => {
        if (cihazID) {
            fetchData();
        }
    }, [cihazID, limit]);

    // Tarih formatı
    const formatDate = (dateString) => {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('tr-TR', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    };

    // Sıcaklık rengi
    const getTemperatureColor = (temp) => {
        if (temp === null || temp === undefined) return 'text-muted';
        if (temp > 8) return 'text-danger';
        if (temp > 5) return 'text-warning';
        if (temp < -2) return 'text-info';
        return 'text-success';
    };

    // Nem rengi
    const getHumidityColor = (humidity) => {
        if (humidity === null || humidity === undefined) return 'text-muted';
        if (humidity > 80) return 'text-danger';
        if (humidity > 60) return 'text-warning';
        return 'text-success';
    };

    // Veri export fonksiyonu
    const exportData = () => {
        if (data.length === 0) return;

        const csvContent = [
            ['Tarih', 'Sıcaklık (°C)', 'Nem (%)', 'Enlem', 'Boylam'],
            ...data.map(item => [
                formatDate(item.tarih),
                item.sensorler?.sicaklik || '-',
                item.sensorler?.nem || '-',
                item.konum?.enlem || '-',
                item.konum?.boylam || '-'
            ])
        ].map(row => row.join(',')).join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `cihaz_${cihazID}_verileri.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    if (loading) {
        return (
            <div className="card">
                <div className="card-body">
                    <LoadingSpinner
                        size="md"
                        variant="primary"
                        message="Veriler yükleniyor..."
                        centered={true}
                    />
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="card">
                <div className="card-body">
                    <ErrorMessage
                        message={error}
                        variant="danger"
                        dismissible={true}
                        onDismiss={() => setError('')}
                    />
                    <button
                        className="btn btn-outline-primary mt-3"
                        onClick={fetchData}
                    >
                        <FontAwesomeIcon icon={faSync} className="me-2" />
                        Yeniden Dene
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="card">
            <div className="card-header bg-primary text-white">
                <div className="d-flex justify-content-between align-items-center">
                    <h5 className="mb-0">
                        <FontAwesomeIcon icon={faThermometerHalf} className="me-2" />
                        Cihaz Veri Geçmişi - {cihazID}
                    </h5>
                    <div className="d-flex gap-2">
                        <button
                            className="btn btn-outline-light btn-sm"
                            onClick={fetchData}
                            title="Verileri yenile"
                        >
                            <FontAwesomeIcon icon={faSync} />
                        </button>
                        <button
                            className="btn btn-outline-light btn-sm"
                            onClick={exportData}
                            disabled={data.length === 0}
                            title="CSV olarak indir"
                        >
                            <FontAwesomeIcon icon={faDownload} />
                        </button>
                    </div>
                </div>
                {lastUpdate && (
                    <small className="text-light">
                        Son güncelleme: {lastUpdate.toLocaleTimeString('tr-TR')}
                    </small>
                )}
            </div>
            <div className="card-body p-0">
                <div className="table-responsive">
                    <table className="table table-striped table-hover mb-0">
                        <thead className="table-dark">
                            <tr>
                                <th style={{ minWidth: '180px' }}>Tarih</th>
                                <th style={{ minWidth: '120px' }}>
                                    <FontAwesomeIcon icon={faThermometerHalf} className="me-1" />
                                    Sıcaklık (°C)
                                </th>
                                <th style={{ minWidth: '100px' }}>
                                    <FontAwesomeIcon icon={faEye} className="me-1" />
                                    Nem (%)
                                </th>
                                <th style={{ minWidth: '120px' }}>
                                    <FontAwesomeIcon icon={faMapMarkerAlt} className="me-1" />
                                    Enlem
                                </th>
                                <th style={{ minWidth: '120px' }}>
                                    <FontAwesomeIcon icon={faMapMarkerAlt} className="me-1" />
                                    Boylam
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {data.length === 0 ? (
                                <tr>
                                    <td colSpan="5" className="text-center text-muted py-4">
                                        <FontAwesomeIcon icon={faThermometerHalf} className="me-2" />
                                        Henüz veri bulunmuyor
                                    </td>
                                </tr>
                            ) : (
                                data.map((item, index) => (
                                    <tr key={index} className="align-middle">
                                        <td className="fw-medium">
                                            {formatDate(item.tarih)}
                                        </td>
                                        <td>
                                            <span className={`fw-bold ${getTemperatureColor(item.sensorler?.sicaklik)}`}>
                                                {item.sensorler?.sicaklik !== null && item.sensorler?.sicaklik !== undefined
                                                    ? `${item.sensorler.sicaklik.toFixed(1)}°C`
                                                    : '-'
                                                }
                                            </span>
                                        </td>
                                        <td>
                                            <span className={`fw-bold ${getHumidityColor(item.sensorler?.nem)}`}>
                                                {item.sensorler?.nem !== null && item.sensorler?.nem !== undefined
                                                    ? `%${item.sensorler.nem.toFixed(1)}`
                                                    : '-'
                                                }
                                            </span>
                                        </td>
                                        <td className="text-primary">
                                            {item.konum?.enlem !== null && item.konum?.enlem !== undefined
                                                ? item.konum.enlem.toFixed(6)
                                                : '-'
                                            }
                                        </td>
                                        <td className="text-primary">
                                            {item.konum?.boylam !== null && item.konum?.boylam !== undefined
                                                ? item.konum.boylam.toFixed(6)
                                                : '-'
                                            }
                                        </td>
                                    </tr>
                                ))
                            )}
                        </tbody>
                    </table>
                </div>
            </div>
            {data.length > 0 && (
                <div className="card-footer bg-light">
                    <div className="d-flex justify-content-between align-items-center">
                        <small className="text-muted">
                            Toplam {data.length} kayıt gösteriliyor
                        </small>
                        <small className="text-muted">
                            Son güncelleme: {lastUpdate?.toLocaleTimeString('tr-TR')}
                        </small>
                    </div>
                </div>
            )}
        </div>
    );
};

export default DeviceDataTable; 