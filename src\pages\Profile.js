import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUser, faSave, faUndo, faEnvelope, faPhone, faBuilding, faUserTag } from '@fortawesome/free-solid-svg-icons';
import Header from '../components/Header';
import Sidebar from '../components/Sidebar';
import Footer from '../components/Footer';
import { kullaniciService } from '../api/dbService';

const Profile = () => {
    const navigate = useNavigate();
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [error, setError] = useState(null);
    const [successMessage, setSuccessMessage] = useState('');

    // Kullanıcı bilgileri için state
    const [userProfile, setUserProfile] = useState({
        musteri_ID: '',
        kullanici: '',
        email: '',
        musteri_adi: '',
        tel: '',
        firma: '',
        adres: '',
        gorev: ''
    });

    // Form değerlerini saklamak için state
    const [formData, setFormData] = useState({
        username: '',
        email: '',
        full_name: '',
        phone: '',
        company: '',
        address: '',
        current_password: '',
        new_password: '',
        confirm_password: ''
    });

    // Kullanıcı bilgilerini yükle
    useEffect(() => {
        const fetchUserData = async () => {
            try {
                setLoading(true);
                setError(null);

                // Local storage'dan kullanıcı bilgilerini al
                const storedUser = JSON.parse(localStorage.getItem('user'));
                const userObj = storedUser?.user;

                if (!userObj || !(userObj.id || userObj.musteri_ID)) {
                    setError('Oturum bilgileriniz bulunamadı. Lütfen tekrar giriş yapın.');
                    navigate('/login');
                    return;
                }

                // Kullanıcı ID'sini al (id veya musteri_ID)
                const userId = userObj.id || userObj.musteri_ID;

                // API'den kullanıcı bilgilerini al
                try {
                    const userData = await kullaniciService.getKullanici(userId);

                    // State'leri güncelle
                    setUserProfile(userData);
                    setFormData({
                        username: userData.kullanici || '',
                        email: userData.email || '',
                        full_name: userData.musteri_adi || '',
                        phone: userData.tel || '',
                        company: userData.firma || '',
                        address: userData.adres || '',
                        current_password: '',
                        new_password: '',
                        confirm_password: ''
                    });

                    setLoading(false);
                } catch (apiError) {
                    console.error('API erişim hatası:', apiError);

                    // API hatası durumunda local storage'daki bilgileri kullan
                    const storedUser = JSON.parse(localStorage.getItem('user'));
                    const userObj = storedUser?.user || {};
                    setUserProfile({
                        musteri_ID: userObj.id || userObj.musteri_ID || '1',
                        kullanici: userObj.username || userObj.email?.split('@')[0] || 'kullanici',
                        email: userObj.email || '<EMAIL>',
                        musteri_adi: userObj.name || userObj.musteri_adi || 'Misafir Kullanıcı',
                        tel: userObj.tel || '',
                        firma: userObj.firma || '',
                        adres: userObj.adres || '',
                        gorev: userObj.role || userObj.gorev || 'kullanici'
                    });

                    setFormData({
                        username: userObj.username || userObj.email?.split('@')[0] || 'kullanici',
                        email: userObj.email || '<EMAIL>',
                        full_name: userObj.name || userObj.musteri_adi || 'Misafir Kullanıcı',
                        phone: userObj.tel || '',
                        company: userObj.firma || '',
                        address: userObj.adres || '',
                        current_password: '',
                        new_password: '',
                        confirm_password: ''
                    });

                    // Demo verisi mi yoksa gerçek bir hata mı görelim
                    if (apiError.message && apiError.message.includes('demo')) {
                        setError('Sunucuya erişilemiyor. Demo veri gösteriliyor.');
                    } else if (apiError.response && apiError.response.status === 404) {
                        setError('Kullanıcı bilgisi veritabanında bulunamadı. Yerel bilgiler kullanılıyor.');
                    } else if (apiError.code === 'ECONNABORTED') {
                        setError('Sunucu yanıt vermedi. Yerel bilgiler kullanılıyor.');
                    } else if (apiError.message === 'Network Error') {
                        setError('Ağ hatası. Sunucuya erişilemiyor. Yerel bilgiler kullanılıyor.');
                    } else {
                        setError('Kullanıcı bilgileri yüklenirken bir hata oluştu. Yerel bilgiler kullanılıyor.');
                    }

                    setLoading(false);
                }
            } catch (err) {
                console.error('Genel bir hata oluştu:', err);
                setError('Beklenmeyen bir hata oluştu. Lütfen sayfayı yenileyin.');
                setLoading(false);
            }
        };

        fetchUserData();
    }, [navigate]);

    // Form değişikliklerini izle
    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prevState => ({
            ...prevState,
            [name]: value
        }));
    };

    // Formu gönder
    const handleSubmit = async (e) => {
        e.preventDefault();

        // Hata ve başarı mesajlarını temizle
        setError(null);
        setSuccessMessage('');

        // Şifre kontrolü
        if (formData.new_password || formData.confirm_password) {
            if (!formData.current_password) {
                setError('Şifrenizi değiştirmek için mevcut şifrenizi girmelisiniz.');
                return;
            }

            if (formData.new_password !== formData.confirm_password) {
                setError('Yeni şifre ve şifre tekrarı aynı olmalıdır.');
                return;
            }

            if (formData.new_password.length < 6) {
                setError('Yeni şifre en az 6 karakter olmalıdır.');
                return;
            }
        }

        try {
            setSaving(true);

            // API'ye gönderilecek veriyi hazırla
            const updateData = {
                username: formData.username,
                email: formData.email,
                full_name: formData.full_name,
                phone: formData.phone,
                company: formData.company,
                address: formData.address
            };

            // Şifre değiştirme isteği varsa
            if (formData.current_password && formData.new_password) {
                updateData.current_password = formData.current_password;
                updateData.new_password = formData.new_password;
            }

            // API'ye güncelleme isteği gönder
            try {
                await kullaniciService.updateKullanici(userProfile.musteri_ID, updateData);

                // LocalStorage'daki kullanıcı bilgilerini güncelle
                const storedUser = JSON.parse(localStorage.getItem('user'));
                if (storedUser) {
                    storedUser.name = formData.full_name;
                    storedUser.email = formData.email;
                    localStorage.setItem('user', JSON.stringify(storedUser));
                }

                // Şifre alanlarını temizle
                setFormData(prevState => ({
                    ...prevState,
                    current_password: '',
                    new_password: '',
                    confirm_password: ''
                }));

                setSuccessMessage('Profil bilgileriniz başarıyla güncellendi.');
                setSaving(false);

                // 3 saniye sonra başarı mesajını temizle
                setTimeout(() => {
                    setSuccessMessage('');
                }, 3000);
            } catch (apiError) {
                console.error('API güncelleme hatası:', apiError);

                // API yanıt hatalarını kontrol et
                let errorMessage = 'Profil güncellenirken bir hata oluştu.';

                if (apiError.response) {
                    if (apiError.response.status === 401) {
                        errorMessage = 'Mevcut şifreniz yanlış.';
                    } else if (apiError.response.status === 409) {
                        errorMessage = 'Bu e-posta adresi veya kullanıcı adı başka bir kullanıcı tarafından kullanılıyor.';
                    } else if (apiError.response.data && apiError.response.data.message) {
                        errorMessage = apiError.response.data.message;
                    }
                } else if (apiError.code === 'ECONNABORTED') {
                    errorMessage = 'Sunucu yanıt vermedi. Lütfen daha sonra tekrar deneyin.';
                } else if (apiError.message === 'Network Error') {
                    errorMessage = 'Ağ hatası. Sunucuya erişilemiyor.';
                }

                setError(errorMessage);
                setSaving(false);
            }
        } catch (err) {
            console.error('Beklenmeyen bir hata oluştu:', err);
            setError('Beklenmeyen bir hata oluştu. Lütfen sayfayı yenileyin ve tekrar deneyin.');
            setSaving(false);
        }
    };

    // Formu sıfırla
    const handleReset = () => {
        // Kullanıcı profilindeki bilgilerle formu sıfırla
        setFormData({
            username: userProfile.kullanici || '',
            email: userProfile.email || '',
            full_name: userProfile.musteri_adi || '',
            phone: userProfile.tel || '',
            company: userProfile.firma || '',
            address: userProfile.adres || '',
            current_password: '',
            new_password: '',
            confirm_password: ''
        });

        // Hata ve başarı mesajlarını temizle
        setError(null);
        setSuccessMessage('');
    };

    // Yükleniyor durumu
    if (loading) {
        return (
            <>
                <Header />
                <div className="container-fluid">
                    <div className="row">
                        <Sidebar />
                        <main className="col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white">
                            <div className="d-flex justify-content-center align-items-center" style={{ height: '80vh' }}>
                                <div className="spinner-border text-primary" role="status">
                                    <span className="visually-hidden">Yükleniyor...</span>
                                </div>
                                <span className="ms-2">Kullanıcı bilgileri yükleniyor...</span>
                            </div>
                        </main>
                    </div>
                </div>
            </>
        );
    }

    return (
        <>
            <Header />
            <div className="container-fluid">
                <div className="row">
                    <Sidebar />
                    <main className="col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white">
                        <div className="pt-3 pb-1 mt-3 mb-3 border-bottom">
                            <h1 className="h4 text-dark mb-0">
                                <FontAwesomeIcon icon={faUser} className="me-2 text-primary" />
                                Kullanıcı Profilim
                            </h1>
                        </div>

                        {/* Hata mesajı */}
                        {error && (
                            <div className="alert alert-danger">
                                <FontAwesomeIcon icon={faUser} className="me-2" />
                                {error}
                            </div>
                        )}

                        {/* Başarı mesajı */}
                        {successMessage && (
                            <div className="alert alert-success">
                                <FontAwesomeIcon icon={faUser} className="me-2" />
                                {successMessage}
                            </div>
                        )}

                        <div className="row mb-4">
                            <div className="col-md-12">
                                <div className="card">
                                    <div className="card-header bg-dark-subtle py-2">
                                        <h5 className="mb-0 fw-bold">Profil Bilgilerim</h5>
                                    </div>
                                    <div className="card-body">
                                        <form onSubmit={handleSubmit}>
                                            <div className="row mb-3">
                                                <div className="col-md-6">
                                                    <div className="form-group mb-3">
                                                        <label htmlFor="username" className="form-label">
                                                            <FontAwesomeIcon icon={faUser} className="me-2" />
                                                            Kullanıcı Adı
                                                        </label>
                                                        <input
                                                            type="text"
                                                            className="form-control"
                                                            id="username"
                                                            name="username"
                                                            value={formData.username}
                                                            onChange={handleChange}
                                                            required
                                                        />
                                                    </div>

                                                    <div className="form-group mb-3">
                                                        <label htmlFor="email" className="form-label">
                                                            <FontAwesomeIcon icon={faEnvelope} className="me-2" />
                                                            E-posta Adresi
                                                        </label>
                                                        <input
                                                            type="email"
                                                            className="form-control"
                                                            id="email"
                                                            name="email"
                                                            value={formData.email}
                                                            onChange={handleChange}
                                                            required
                                                        />
                                                    </div>

                                                    <div className="form-group mb-3">
                                                        <label htmlFor="full_name" className="form-label">
                                                            <FontAwesomeIcon icon={faUser} className="me-2" />
                                                            Ad Soyad
                                                        </label>
                                                        <input
                                                            type="text"
                                                            className="form-control"
                                                            id="full_name"
                                                            name="full_name"
                                                            value={formData.full_name}
                                                            onChange={handleChange}
                                                            required
                                                        />
                                                    </div>
                                                </div>

                                                <div className="col-md-6">
                                                    <div className="form-group mb-3">
                                                        <label htmlFor="phone" className="form-label">
                                                            <FontAwesomeIcon icon={faPhone} className="me-2" />
                                                            Telefon
                                                        </label>
                                                        <input
                                                            type="text"
                                                            className="form-control"
                                                            id="phone"
                                                            name="phone"
                                                            value={formData.phone}
                                                            onChange={handleChange}
                                                        />
                                                    </div>

                                                    <div className="form-group mb-3">
                                                        <label htmlFor="company" className="form-label">
                                                            <FontAwesomeIcon icon={faBuilding} className="me-2" />
                                                            Firma Adı
                                                        </label>
                                                        <input
                                                            type="text"
                                                            className="form-control"
                                                            id="company"
                                                            name="company"
                                                            value={formData.company}
                                                            onChange={handleChange}
                                                        />
                                                    </div>

                                                    <div className="form-group mb-3">
                                                        <label htmlFor="address" className="form-label">
                                                            <FontAwesomeIcon icon={faBuilding} className="me-2" />
                                                            Adres
                                                        </label>
                                                        <input
                                                            type="text"
                                                            className="form-control"
                                                            id="address"
                                                            name="address"
                                                            value={formData.address}
                                                            onChange={handleChange}
                                                        />
                                                    </div>

                                                    <div className="form-group mb-3">
                                                        <label htmlFor="role" className="form-label">
                                                            <FontAwesomeIcon icon={faUserTag} className="me-2" />
                                                            Kullanıcı Rolü
                                                        </label>
                                                        <input
                                                            type="text"
                                                            className="form-control"
                                                            id="role"
                                                            value={userProfile.gorev === 'admin' ? 'Yönetici' : userProfile.gorev === 'manager' ? 'Müdür' : 'Kullanıcı'}
                                                            disabled
                                                        />
                                                    </div>
                                                </div>
                                            </div>

                                            <hr className="my-4" />

                                            <div className="row mb-3">
                                                <div className="col-md-12">
                                                    <h5>Şifre Değiştirme</h5>
                                                    <p className="text-muted small">Şifrenizi değiştirmek istemiyorsanız bu alanları boş bırakabilirsiniz.</p>
                                                </div>

                                                <div className="col-md-4">
                                                    <div className="form-group mb-3">
                                                        <label htmlFor="current_password" className="form-label">Mevcut Şifre</label>
                                                        <input
                                                            type="password"
                                                            className="form-control"
                                                            id="current_password"
                                                            name="current_password"
                                                            value={formData.current_password}
                                                            onChange={handleChange}
                                                        />
                                                    </div>
                                                </div>

                                                <div className="col-md-4">
                                                    <div className="form-group mb-3">
                                                        <label htmlFor="new_password" className="form-label">Yeni Şifre</label>
                                                        <input
                                                            type="password"
                                                            className="form-control"
                                                            id="new_password"
                                                            name="new_password"
                                                            value={formData.new_password}
                                                            onChange={handleChange}
                                                            minLength="6"
                                                        />
                                                    </div>
                                                </div>

                                                <div className="col-md-4">
                                                    <div className="form-group mb-3">
                                                        <label htmlFor="confirm_password" className="form-label">Yeni Şifre (Tekrar)</label>
                                                        <input
                                                            type="password"
                                                            className="form-control"
                                                            id="confirm_password"
                                                            name="confirm_password"
                                                            value={formData.confirm_password}
                                                            onChange={handleChange}
                                                            minLength="6"
                                                        />
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="row">
                                                <div className="col-md-12">
                                                    <div className="d-flex justify-content-end gap-2">
                                                        <button
                                                            type="button"
                                                            className="btn btn-secondary"
                                                            onClick={handleReset}
                                                            disabled={saving}
                                                        >
                                                            <FontAwesomeIcon icon={faUndo} className="me-2" />
                                                            Sıfırla
                                                        </button>
                                                        <button
                                                            type="submit"
                                                            className="btn btn-primary"
                                                            disabled={saving}
                                                        >
                                                            {saving ? (
                                                                <>
                                                                    <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                                                    Kaydediliyor...
                                                                </>
                                                            ) : (
                                                                <>
                                                                    <FontAwesomeIcon icon={faSave} className="me-2" />
                                                                    Kaydet
                                                                </>
                                                            )}
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </main>
                </div>
            </div>
            <Footer />
        </>
    );
};

export default Profile; 