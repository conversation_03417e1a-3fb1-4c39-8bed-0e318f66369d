import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import Header from '../components/Header';
import Sidebar from '../components/Sidebar';
import Footer from '../components/Footer';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';
import { sevkiyatService, cihazBilgiService } from '../api/dbService';

const Home = () => {
    const [shipments, setShipments] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [masterData] = useState({});
    const navigate = useNavigate();

    // Verileri API'den getir
    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);
                setError(null);

                // Local storage'dan kullanıcı bilgilerini al
                let storedUser = JSON.parse(localStorage.getItem('user'));
                let userId = storedUser?.user?.musteri_ID || storedUser?.user?.id;

                // Kullanıcı bilgisi yoksa demo kullanıcı oluştur
                if (!userId) {
                    console.log('Oturum bilgisi bulunamadı. Demo kullanıcı oluşturuluyor...');

                    // Demo kullanıcı oluştur
                    const demoUser = {
                        user: {
                            id: 1,
                            musteri_ID: 1,
                            name: 'Demo Kullanıcı',
                            email: '<EMAIL>',
                            username: 'demo',
                            role: 'user'
                        }
                    };

                    // Demo kullanıcıyı localStorage'a kaydet
                    localStorage.setItem('user', JSON.stringify(demoUser));
                    storedUser = demoUser;
                    userId = 1;
                }

                // Aktif kullanıcının sevkiyatlarını getir
                const sevkiyatData = await sevkiyatService.getSevkiyatlarByMusteriId(userId);

                // Veri dönüşümü
                const processedData = await Promise.all(sevkiyatData.map(async (item) => {
                    // Her sevkiyat için son sensör verilerini al - uzak API'den mgz24_kodu ile
                    let lastSensor = null;
                    try {
                        if (item.mgz24_kodu) {
                            const response = await cihazBilgiService.getCihazFromExternalAPI(item.mgz24_kodu);
                            if (response.success && response.data) {
                                // Uzak API'den gelen veri formatını düzenle
                                const sensorData = response.data.sonSensorler || response.data;
                                if (sensorData) {
                                    lastSensor = {
                                        sicaklik: sensorData.sicaklik,
                                        nem: sensorData.nem,
                                        enlem: sensorData.enlem || response.data.konum?.enlem,
                                        boylam: sensorData.boylam || response.data.konum?.boylam,
                                        zaman: sensorData.zaman || response.data.sonGuncelleme
                                    };
                                }
                            }
                        }
                    } catch (error) {
                        console.warn(`Sevkiyat ${item.id} (MGZ24: ${item.mgz24_kodu}) için sensör verisi alınamadı:`, error);
                    }

                    // Sıcaklık kontrolü ve durumu belirle
                    let tempStatus = 'normal';
                    if (lastSensor?.sicaklik) {
                        const temp = parseFloat(lastSensor.sicaklik);
                        const [minTemp, maxTemp] = (item.sicaklik_araligi || '15-25°C')
                            .replace('°C', '')
                            .split('-')
                            .map(t => parseFloat(t.trim()));

                        if (temp < minTemp || temp > maxTemp) {
                            tempStatus = 'warning';
                        }
                    }

                    // Doğrudan sevkiyat nesnesindeki metin alanlarını kullan
                    const fromName = item.cikis_lokasyon || `Lokasyon ${item.cikis_lokasyon_id || ''}`;
                    const toName = item.varis_lokasyon || `Lokasyon ${item.varis_lokasyon_id || ''}`;
                    const carrierName = item.nakliyeci || `Nakliyeci ${item.nakliyeci_id || ''}`;
                    const productName = item.urun || `Ürün ${item.urun_id || ''}`;

                    return {
                        id: item.id,
                        sevkiyatID: item.sevkiyat_ID || '-',
                        mgzKodu: item.mgz24_kodu || '-',
                        name: item.sevkiyat_adi || 'İsimsiz Sevkiyat',
                        plate: item.plaka_no || '-',
                        from: fromName,
                        to: toName,
                        carrier: carrierName,
                        product: productName,
                        orderNo: item.mgz24_kodu || '-',
                        pallet: item.palet_sayisi?.toString() || '-',
                        net: item.net_agirlik?.toString() || '-',
                        gross: item.brut_agirlik?.toString() || '-',
                        added: formatDate(item.olusturma_zamani) || '-',
                        location: lastSensor ? `${lastSensor.enlem}, ${lastSensor.boylam}` : 'Konum bilgisi yok',
                        lastData: lastSensor ? formatDate(lastSensor.zaman, true) : '-',
                        temperature: lastSensor?.sicaklik?.toString() || '-',
                        tempStatus: tempStatus
                    };
                }));

                setShipments(processedData);
                setLoading(false);
            } catch (error) {
                console.error('Sevkiyat verileri alınırken hata:', error);
                
                // 503 veya 500 hataları için özel mesajlar
                if (error.response?.status === 503) {
                    setError('Sunucu şu anda hizmet veremiyor. Lütfen birkaç dakika sonra tekrar deneyin.');
                } else if (error.response?.status === 500) {
                    setError('Sunucu hatası oluştu. Lütfen sistem yöneticisine başvurun.');
                } else if (error.response?.status === 404) {
                    // 404 durumunda boş liste göster, hata gösterme
                    setShipments([]);
                    setLoading(false);
                    return;
                } else if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
                    setError('Bağlantı zaman aşımına uğradı. Lütfen internet bağlantınızı kontrol edin.');
                } else if (error.message.includes('Network Error')) {
                    setError('Sunucu bağlantı hatası. Lütfen internet bağlantınızı kontrol edin.');
                } else {
                    setError('Sevkiyat verileri yüklenirken hata oluştu: ' + (error.response?.data?.message || error.message));
                }
                setLoading(false);
            }
        };

        fetchData();
    }, [masterData, navigate]);

    // Tarih formatla
    const formatDate = (dateString, includeTime = false) => {
        if (!dateString) return '';

        const date = new Date(dateString);
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();

        if (includeTime) {
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            return `${day}.${month}.${year} ${hours}:${minutes}`;
        }

        return `${day}.${month}.${year}`;
    };

    return (
        <>
            <Header />
            <div className="container-fluid">
                <div className="row">
                    <Sidebar />

                    {/* main sütun */}
                    <main className="col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white">
                        <div className="pt-3 pb-1 mt-3 mb-3 border-bottom">
                            <h1 className="h4 text-dark">Aktif Sevkiyatlar</h1>
                        </div>

                        {loading ? (
                            <LoadingSpinner 
                                size="lg" 
                                variant="primary" 
                                message="Sevkiyat verileri yükleniyor..." 
                                centered={true}
                            />
                        ) : error ? (
                            <ErrorMessage 
                                message={error} 
                                variant="danger"
                                title="Veri Yükleme Hatası"
                                dismissible={true}
                                onDismiss={() => setError('')}
                            >
                                <button className="btn btn-primary btn-sm mt-2" onClick={() => window.location.reload()}>
                                    Yeniden Dene
                                </button>
                            </ErrorMessage>
                        ) : (
                            <div className="row">
                                <div className="col-12">

                                    {/* aktif cihaz tablo */}
                                    <div className="card border-tertiary-subtle px-3 py-3 mb-5">
                                        {shipments.length === 0 ? (
                                            <div className="text-center py-5">
                                                <div className="mb-4">
                                                    <i className="fas fa-shipping-fast fa-3x text-muted mb-3"></i>
                                                    <h5 className="text-muted">Aktif Sevkiyat Bulunamadı</h5>
                                                    <p className="text-muted">
                                                        Henüz aktif sevkiyatınız bulunmamaktadır. 
                                                        Yeni bir sevkiyat oluşturmak için aşağıdaki butonu kullanabilirsiniz.
                                                    </p>
                                                </div>
                                                <Link to="/add" className="btn btn-primary">
                                                    <i className="fas fa-plus me-2"></i>
                                                    Yeni Sevkiyat Oluştur
                                                </Link>
                                            </div>
                                        ) : (
                                            <div className="table-responsive" style={{ overflowX: 'unset' }}>
                                                <table className="table table-dark-subtle table-bordered table-hover table-striped table-sm" style={{ fontSize: '0.85rem' }}>
                                                    <thead className="table-light">
                                                        <tr>
                                                            <th>Sevkiyat ID</th>
                                                            <th>Sevkiyat Adı</th>
                                                            <th>Plaka No</th>
                                                            <th>Nereden</th>
                                                            <th>Nereye</th>
                                                            <th>Nakliyeci</th>
                                                            <th>Ürün</th>
                                                            <th>Sipariş No</th>
                                                            <th>Palet</th>
                                                            <th>Net</th>
                                                            <th>Brüt</th>
                                                            <th>Eklenme</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {shipments.map((shipment, index) => (
                                                            <tr key={index}>
                                                                <td>
                                                                    <Link to={`/view/${shipment.id}`}>
                                                                        {shipment.sevkiyatID}
                                                                        {shipment.mgzKodu &&
                                                                            <span className="ms-1 badge bg-primary text-white">
                                                                                {shipment.mgzKodu}
                                                                            </span>
                                                                        }
                                                                    </Link>
                                                                </td>
                                                                <td>{shipment.name}</td>
                                                                <td>{shipment.plate}</td>
                                                                <td>{shipment.from}</td>
                                                                <td>{shipment.to}</td>
                                                                <td>{shipment.carrier}</td>
                                                                <td>{shipment.product}</td>
                                                                <td>{shipment.orderNo}</td>
                                                                <td>{shipment.pallet}</td>
                                                                <td>{shipment.net}</td>
                                                                <td>{shipment.gross}</td>
                                                                <td>{shipment.added}</td>
                                                            </tr>
                                                        ))}
                                                    </tbody>
                                                </table>
                                            </div>
                                        )}
                                    </div>
                                    {/* /aktif cihaz tablo */}
                                </div>
                            </div>
                        )}
                    </main>
                    {/* /main sütun */}

                    <Footer />
                </div>
            </div>
        </>
    );
};

export default Home; 