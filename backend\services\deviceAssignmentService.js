import mysql from 'mysql2/promise';
import ExternalApiService from './externalApiService.js';

class DeviceAssignmentService {
    constructor() {
        this.pool = mysql.createPool({
            host: process.env.DB_HOST || 'localhost',
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || '',
            database: process.env.DB_NAME || 'mgz24db',
            waitForConnections: true,
            connectionLimit: 10,
            queueLimit: 0
        });
        this.externalApiService = new ExternalApiService();
    }

    // Müşteriye ait kullanılabilir cihazları getir
    async getAvailableDevices(musteriID) {
        try {
            const [devices] = await this.pool.execute(`
                SELECT 
                    c.CihazID,
                    c.ICCID,
                    c.GoldCihaz,
                    c.<PERSON>,
                    c.<PERSON>,
                    c.<PERSON>,
                    c.<PERSON>,
                    cb.mgz24_kodu,
                    cb.durum as cihaz_durum,
                    cb.sevkiyat_id,
                    cb.pil_seviyesi,
                    cb.son_kontrol,
                    CASE 
                        WHEN cb.sevkiyat_id IS NULL THEN 'available'
                        ELSE 'assigned'
                    END as availability_status
                FROM cihazID c
                LEFT JOIN cihazBilgi cb ON c.CihazID = cb.mgz24_kodu
                WHERE 
                    c.MusteriID = ? 
                    AND c.KullanimBitti = 0
                    AND c.KalanSure > 0
                ORDER BY 
                    c.GoldCihaz DESC,
                    cb.son_kontrol DESC,
                    c.BaslamaTarihi DESC
            `, [musteriID]);

            // Her cihaz için external API'den güncel durumu getir
            const devicesWithStatus = await Promise.all(devices.map(async (device) => {
                try {
                    const externalStatus = await this.externalApiService.fetchDeviceStatus(device.CihazID);
                    return {
                        ...device,
                        externalStatus: externalStatus,
                        lastUpdate: externalStatus.sonGuncellenme,
                        batteryLevel: externalStatus.batarya.mevcut,
                        connectionStatus: externalStatus.durum
                    };
                } catch (error) {
                    console.warn(`Could not fetch external status for device ${device.CihazID}:`, error.message);
                    return {
                        ...device,
                        externalStatus: null,
                        lastUpdate: device.son_kontrol,
                        batteryLevel: device.pil_seviyesi,
                        connectionStatus: 'unknown'
                    };
                }
            }));

            return devicesWithStatus;

        } catch (error) {
            console.error('Error fetching available devices:', error);
            throw error;
        }
    }

    // Cihazı sevkiyata ata
    async assignDeviceToShipment(cihazID, sevkiyatID, userId) {
        const connection = await this.pool.getConnection();

        try {
            await connection.beginTransaction();

            // Cihazın kullanılabilir olup olmadığını kontrol et
            const [deviceCheck] = await connection.execute(`
                SELECT 
                    c.CihazID,
                    c.MusteriID,
                    c.KullanimBitti,
                    c.KalanSure,
                    cb.sevkiyat_id,
                    s.musteri_ID as shipment_customer_id
                FROM cihazID c
                LEFT JOIN cihazBilgi cb ON c.CihazID = cb.mgz24_kodu
                LEFT JOIN sevkiyatlar s ON s.id = ?
                WHERE c.CihazID = ?
            `, [sevkiyatID, cihazID]);

            if (deviceCheck.length === 0) {
                throw new Error('Cihaz bulunamadı');
            }

            const device = deviceCheck[0];

            // Validasyonlar
            if (device.KullanimBitti === 1) {
                throw new Error('Cihazın kullanım süresi dolmuş');
            }

            if (device.KalanSure <= 0) {
                throw new Error('Cihazın kalan süresi yeterli değil');
            }

            if (device.sevkiyat_id !== null) {
                throw new Error('Cihaz zaten başka bir sevkiyata atanmış');
            }

            if (device.MusteriID !== device.shipment_customer_id) {
                throw new Error('Cihaz bu müşteriye ait değil');
            }

            // Cihazı sevkiyata ata
            await connection.execute(
                'UPDATE cihazBilgi SET sevkiyat_id = ? WHERE cihaz_kodu = ?',
                [sevkiyatID, cihazID]
            );

            // Sevkiyat tablosunu güncelle
            await connection.execute(
                'UPDATE sevkiyatlar SET cihaz_kodu = ? WHERE id = ?',
                [cihazID, sevkiyatID]
            );

            // Assignment log kaydı
            await connection.execute(`
                INSERT INTO device_assignments (
                    cihaz_id, sevkiyat_id, assigned_by, assigned_at, status
                ) VALUES (?, ?, ?, NOW(), 'assigned')
            `, [cihazID, sevkiyatID, userId]);

            // External API'den güncel veriyi çek ve kaydet
            await this.externalApiService.syncDeviceToLocal(cihazID);

            await connection.commit();
            console.log(`Device ${cihazID} assigned to shipment ${sevkiyatID} by user ${userId}`);

            return {
                success: true,
                cihazID: cihazID,
                sevkiyatID: sevkiyatID,
                assignedAt: new Date(),
                message: 'Cihaz başarıyla sevkiyata atandı'
            };

        } catch (error) {
            await connection.rollback();
            console.error(`Error assigning device ${cihazID} to shipment ${sevkiyatID}:`, error);
            throw error;
        } finally {
            connection.release();
        }
    }

    // Cihazın sevkiyat atamasını kaldır
    async unassignDeviceFromShipment(cihazID, sevkiyatID, userId, reason = 'manual') {
        const connection = await this.pool.getConnection();

        try {
            await connection.beginTransaction();

            // Mevcut atamayı kontrol et
            const [assignmentCheck] = await connection.execute(`
                SELECT sevkiyat_id FROM cihazBilgi WHERE cihaz_kodu = ?
            `, [cihazID]);

            if (assignmentCheck.length === 0 || assignmentCheck[0].sevkiyat_id !== sevkiyatID) {
                throw new Error('Cihaz bu sevkiyata atanmamış');
            }

            // Cihazın atamasını kaldır
            await connection.execute(
                'UPDATE cihazBilgi SET sevkiyat_id = NULL WHERE cihaz_kodu = ?',
                [cihazID]
            );

            // Sevkiyat tablosunu güncelle
            await connection.execute(
                'UPDATE sevkiyatlar SET cihaz_kodu = NULL WHERE id = ?',
                [sevkiyatID]
            );

            // Assignment log kaydını güncelle
            await connection.execute(`
                UPDATE device_assignments 
                SET status = 'unassigned', unassigned_by = ?, unassigned_at = NOW(), reason = ?
                WHERE cihaz_id = ? AND sevkiyat_id = ? AND status = 'assigned'
            `, [userId, reason, cihazID, sevkiyatID]);

            await connection.commit();
            console.log(`Device ${cihazID} unassigned from shipment ${sevkiyatID} by user ${userId}, reason: ${reason}`);

            return {
                success: true,
                cihazID: cihazID,
                sevkiyatID: sevkiyatID,
                unassignedAt: new Date(),
                reason: reason,
                message: 'Cihaz ataması başarıyla kaldırıldı'
            };

        } catch (error) {
            await connection.rollback();
            console.error(`Error unassigning device ${cihazID} from shipment ${sevkiyatID}:`, error);
            throw error;
        } finally {
            connection.release();
        }
    }

    // Sevkiyattaki cihazı değiştir
    async changeDeviceInShipment(oldCihazID, newCihazID, sevkiyatID, userId) {
        const connection = await this.pool.getConnection();

        try {
            await connection.beginTransaction();

            // Önce eski cihazı kaldır
            await this.unassignDeviceFromShipment(oldCihazID, sevkiyatID, userId, 'device_change');

            // Yeni cihazı ata
            await this.assignDeviceToShipment(newCihazID, sevkiyatID, userId);

            await connection.commit();
            console.log(`Device changed in shipment ${sevkiyatID}: ${oldCihazID} -> ${newCihazID}`);

            return {
                success: true,
                oldCihazID: oldCihazID,
                newCihazID: newCihazID,
                sevkiyatID: sevkiyatID,
                changedAt: new Date(),
                message: 'Cihaz değişikliği başarıyla tamamlandı'
            };

        } catch (error) {
            await connection.rollback();
            console.error(`Error changing device in shipment ${sevkiyatID}:`, error);
            throw error;
        } finally {
            connection.release();
        }
    }

    // Cihaz atama geçmişini getir
    async getDeviceAssignmentHistory(cihazID, limit = 50) {
        try {
            const [history] = await this.pool.execute(`
                SELECT 
                    da.*,
                    s.sevkiyat_adi,
                    s.plaka_no,
                    u1.username as assigned_by_username,
                    u2.username as unassigned_by_username
                FROM device_assignments da
                LEFT JOIN sevkiyatlar s ON da.sevkiyat_id = s.id
                LEFT JOIN kullanicilar u1 ON da.assigned_by = u1.id
                LEFT JOIN kullanicilar u2 ON da.unassigned_by = u2.id
                WHERE da.cihaz_id = ?
                ORDER BY da.assigned_at DESC
                LIMIT ?
            `, [cihazID, limit]);

            return history;

        } catch (error) {
            console.error(`Error fetching assignment history for device ${cihazID}:`, error);
            throw error;
        }
    }

    // Sevkiyat atama istatistiklerini getir
    async getAssignmentStats(musteriID) {
        try {
            const [stats] = await this.pool.execute(`
                SELECT 
                    COUNT(DISTINCT c.CihazID) as total_devices,
                    COUNT(DISTINCT CASE WHEN cb.sevkiyat_id IS NOT NULL THEN c.CihazID END) as assigned_devices,
                    COUNT(DISTINCT CASE WHEN cb.sevkiyat_id IS NULL THEN c.CihazID END) as available_devices,
                    COUNT(DISTINCT CASE WHEN c.KullanimBitti = 1 THEN c.CihazID END) as expired_devices,
                    COUNT(DISTINCT CASE WHEN c.GoldCihaz = 1 THEN c.CihazID END) as gold_devices,
                    AVG(c.KalanSure) as avg_remaining_time
                FROM cihazID c
                LEFT JOIN cihazBilgi cb ON c.CihazID = cb.mgz24_kodu
                WHERE c.MusteriID = ?
            `, [musteriID]);

            return stats[0] || {};

        } catch (error) {
            console.error(`Error fetching assignment stats for customer ${musteriID}:`, error);
            throw error;
        }
    }

    // Otomatik cihaz atama (en uygun cihazı seç)
    async autoAssignDevice(sevkiyatID, userId) {
        try {
            // Sevkiyat bilgisini al
            const [shipment] = await this.pool.execute(
                'SELECT musteri_ID FROM sevkiyatlar WHERE id = ?',
                [sevkiyatID]
            );

            if (shipment.length === 0) {
                throw new Error('Sevkiyat bulunamadı');
            }

            const musteriID = shipment[0].musteri_ID;

            // Kullanılabilir cihazları getir
            const availableDevices = await this.getAvailableDevices(musteriID);

            // Sadece atanmamış cihazları filtrele
            const unassignedDevices = availableDevices.filter(device =>
                device.availability_status === 'available' &&
                device.connectionStatus === 'online' &&
                device.batteryLevel > 30
            );

            if (unassignedDevices.length === 0) {
                throw new Error('Kullanılabilir cihaz bulunamadı');
            }

            // En uygun cihazı seç (Gold cihaz öncelikli, sonra batarya seviyesi)
            const bestDevice = unassignedDevices.sort((a, b) => {
                if (a.GoldCihaz !== b.GoldCihaz) return b.GoldCihaz - a.GoldCihaz;
                return b.batteryLevel - a.batteryLevel;
            })[0];

            // Cihazı ata
            const result = await this.assignDeviceToShipment(bestDevice.CihazID, sevkiyatID, userId);

            return {
                ...result,
                deviceInfo: {
                    cihazID: bestDevice.CihazID,
                    isGoldDevice: bestDevice.GoldCihaz === 1,
                    batteryLevel: bestDevice.batteryLevel,
                    connectionStatus: bestDevice.connectionStatus
                },
                message: 'En uygun cihaz otomatik olarak atandı'
            };

        } catch (error) {
            console.error(`Error in auto-assign for shipment ${sevkiyatID}:`, error);
            throw error;
        }
    }

    // Bağlantı havuzunu kapat
    async closeConnection() {
        await this.pool.end();
    }
}

export default DeviceAssignmentService;