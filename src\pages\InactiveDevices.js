import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Header from '../components/Header';
import Sidebar from '../components/Sidebar';
import Footer from '../components/Footer';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
    faDesktop, faBatteryQuarter, faBatteryHalf, faBatteryThreeQuarters, 
    faBatteryFull, faBatteryEmpty, faCalendarAlt, faThermometerHalf,
    faTint, faLightbulb, faMapMarkerAlt, faShippingFast, faInfoCircle,
    faEye, faEyeSlash, faChevronDown, faChevronUp, faExclamationTriangle,
    faClock, faCreditCard
} from '@fortawesome/free-solid-svg-icons';
import { inactiveDevicesService } from '../api/dbService';

// CSS stilleri
const styles = `
.device-row:hover {
    background-color: #f8f9fa !important;
}

.device-details-row {
    border-top: 2px solid #dee2e6;
}

.device-details-row td {
    border-top: none !important;
}

.badge {
    font-size: 0.75em;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}
`;

const InactiveDevices = () => {
    const [devices, setDevices] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [expandedDevices, setExpandedDevices] = useState(new Set());

    // Kontor sonu tarihinden kalan süreyi hesaplama
    const calculateRemainingTime = (kontorSonu) => {
        if (!kontorSonu) {
            return { days: 0, hours: 0, isExpired: true, displayText: '0 gün' };
        }

        const now = new Date();
        const endDate = new Date(kontorSonu);
        const diffMs = endDate - now;

        if (diffMs <= 0) {
            return { days: 0, hours: 0, isExpired: true, displayText: '0 gün' };
        }

        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

        let displayText = '';
        if (diffDays > 0) {
            displayText = `${diffDays} gün`;
            if (diffHours > 0) {
                displayText += ` ${diffHours} saat`;
            }
        } else if (diffHours > 0) {
            displayText = `${diffHours} saat`;
        } else {
            displayText = '1 saatten az';
        }

        return {
            days: diffDays,
            hours: diffHours,
            isExpired: false,
            displayText,
            isWarning: diffDays < 5
        };
    };

    // Pil durumu ikonu göster
    const getBatteryIcon = (level) => {
        if (!level) return faBatteryEmpty;
        if (level <= 10) return faBatteryEmpty;
        if (level <= 25) return faBatteryQuarter;
        if (level <= 50) return faBatteryHalf;
        if (level <= 75) return faBatteryThreeQuarters;
        return faBatteryFull;
    };

    // Pil durumu rengi
    const getBatteryColor = (level) => {
        if (!level) return 'text-muted';
        if (level <= 10) return 'text-danger';
        if (level <= 25) return 'text-warning';
        if (level <= 50) return 'text-info';
        return 'text-success';
    };

    // Cihaz detaylarını genişletme/daraltma
    const toggleDeviceExpansion = (cihazKodu) => {
        const newExpanded = new Set(expandedDevices);
        if (newExpanded.has(cihazKodu)) {
            newExpanded.delete(cihazKodu);
        } else {
            newExpanded.add(cihazKodu);
        }
        setExpandedDevices(newExpanded);
    };

    // İnaktif cihazları getir
    const fetchInactiveDevices = async () => {
        try {
            setLoading(true);
            setError(null);
            
            const response = await inactiveDevicesService.getInactiveDevices();
            
            if (response.success) {
                setDevices(response.data);
            } else {
                setError('İnaktif cihazlar alınamadı');
            }
        } catch (err) {
            console.error('İnaktif cihazlar yüklenirken hata:', err);
            setError('İnaktif cihazlar alınamadı: ' + err.message);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchInactiveDevices();
    }, []);

    return (
        <>
            <style>{styles}</style>
            <Header />
            <div className="container-fluid">
                <div className="row">
                    <Sidebar />

                    <main className="col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white">
                        <div className="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                            <h1 className="h2">
                                <FontAwesomeIcon icon={faDesktop} className="me-2" />
                                İnaktif Cihazlar
                                <span className="badge bg-secondary ms-2">{devices.length}</span>
                            </h1>
                        </div>

                        {loading ? (
                            <LoadingSpinner size="lg" variant="primary" message="İnaktif cihazlar yükleniyor..." centered={true} />
                        ) : error ? (
                            <ErrorMessage
                                message={error}
                                variant="danger"
                                title="Veri Yükleme Hatası"
                                dismissible={true}
                                onDismiss={() => setError('')}
                            >
                                <button className="btn btn-primary btn-sm mt-2" onClick={fetchInactiveDevices}>
                                    Yeniden Dene
                                </button>
                            </ErrorMessage>
                        ) : (
                            <>
                                {devices.length === 0 ? (
                                    <div className="text-center py-5">
                                        <FontAwesomeIcon icon={faDesktop} className="fa-3x text-muted mb-3" />
                                        <h5 className="text-muted">İnaktif Cihaz Bulunamadı</h5>
                                        <p className="text-muted">Şu anda inaktif durumda cihaz bulunmamaktadır.</p>
                                    </div>
                                ) : (
                                    <div className="card">
                                        <div className="card-header">
                                            <h5 className="mb-0">İnaktif Cihazlar Listesi</h5>
                                        </div>
                                        <div className="card-body">
                                            <div className="table-responsive">
                                                <table className="table table-striped table-hover">
                                                    <thead>
                                                        <tr>
                                            <th>Cihaz Bilgileri</th>
                                            <th>Durum</th>
                                            <th>Pil Seviyesi</th>
                                            <th>Son Kullanım</th>
                                            <th>Kalan Kullanım Süresi</th>
                                            <th>En Son Sevkiyat</th>
                                            <th>İşlemler</th>
                                        </tr>
                                                    </thead>
                                                    <tbody>
                                        {devices.map((device, index) => (
                                            <React.Fragment key={index}>
                                                <tr className="device-row">
                                                    <td>
                                                        <div className="d-flex flex-column">
                                                            <div className="fw-bold text-primary mb-1">{device.cihazKodu}</div>
                                                            {device.cihazAdi && (
                                                                <div className="text-muted small">
                                                                    {device.cihazAdi}
                                                                </div>
                                                            )}
                                                            {device.model && (
                                                                <div className="text-muted small">
                                                                    <strong>Model:</strong> {device.model}
                                                                </div>
                                                            )}
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span className={`badge ${
                                                            device.durum === 'aktif' ? 'bg-success' :
                                                            device.durum === 'pasif' ? 'bg-secondary' :
                                                            device.durum === 'bakimda' ? 'bg-warning' : 'bg-secondary'
                                                        }`}>
                                                            {device.durum || 'Bilinmiyor'}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        {device.pilSeviyesi ? (
                                                            <span className={getBatteryColor(device.pilSeviyesi)}>
                                                                <FontAwesomeIcon 
                                                                    icon={getBatteryIcon(device.pilSeviyesi)} 
                                                                    className="me-2"
                                                                />
                                                                {device.pilSeviyesi}%
                                                            </span>
                                                        ) : (
                                                            <span className="text-muted">
                                                                <FontAwesomeIcon icon={faBatteryEmpty} className="me-2" />
                                                                Bilinmiyor
                                                            </span>
                                                        )}
                                                    </td>
                                                    <td>
                                                        {device.sonKullanim ? (
                                                            <span>
                                                                <FontAwesomeIcon icon={faCalendarAlt} className="me-2 text-muted" />
                                                                {device.sonKullanim}
                                                            </span>
                                                        ) : (
                                                            <span className="text-muted">
                                                                <FontAwesomeIcon icon={faCalendarAlt} className="me-2" />
                                                                Hiç kullanılmamış
                                                            </span>
                                                        )}
                                                    </td>
                                                    <td>
                                                        {(() => {
                                                            const remaining = calculateRemainingTime(device.kontorSonu);
                                                            return (
                                                                <div className="d-flex align-items-center">
                                                                    <FontAwesomeIcon 
                                                                        icon={remaining.isWarning ? faExclamationTriangle : faClock} 
                                                                        className={`me-2 ${
                                                                            remaining.isExpired || remaining.isWarning 
                                                                                ? 'text-danger' 
                                                                                : 'text-success'
                                                                        }`} 
                                                                    />
                                                                    <span className={
                                                                        remaining.isExpired || remaining.isWarning 
                                                                            ? 'text-danger fw-bold' 
                                                                            : 'text-success'
                                                                    }>
                                                                        {remaining.displayText}
                                                                    </span>
                                                                </div>
                                                            );
                                                        })()}
                                                    </td>
                                                    <td>
                                                        {device.enSonSevkiyat ? (
                                                            <div className="d-flex flex-column">
                                                                <div className="fw-bold small text-info">
                                                                    <FontAwesomeIcon icon={faShippingFast} className="me-1" />
                                                                    {device.enSonSevkiyat.sevkiyatNo}
                                                                </div>
                                                                <div className="text-muted small">
                                                                    {device.enSonSevkiyat.cikisLokasyon} → {device.enSonSevkiyat.varisLokasyon}
                                                                </div>
                                                                <div className="text-muted small">
                                                                    {device.enSonSevkiyat.sevkiyatTarihi}
                                                                    {device.enSonSevkiyat.bitisTarihi && ` - ${device.enSonSevkiyat.bitisTarihi}`}
                                                                </div>
                                                            </div>
                                                        ) : (
                                                            <span className="text-muted">
                                                                <FontAwesomeIcon icon={faShippingFast} className="me-2" />
                                                                Sevkiyat yok
                                                            </span>
                                                        )}
                                                    </td>
                                                    <td>
                                                        <div className="btn-group-vertical">
                                                            <Link 
                                                                to="/payment"
                                                                className="btn btn-sm btn-outline-primary mb-1"
                                                                title="Kullanım süresi satın al"
                                                            >
                                                                <FontAwesomeIcon icon={faCreditCard} className="me-1" />
                                                                Kullanım Süresi Satın Al
                                                            </Link>
                                                            <button 
                                                                className="btn btn-sm btn-outline-info"
                                                                onClick={() => toggleDeviceExpansion(device.cihazKodu)}
                                                                title={expandedDevices.has(device.cihazKodu) ? 'Detayları Gizle' : 'Detayları Göster'}
                                                            >
                                                                <FontAwesomeIcon 
                                                                    icon={expandedDevices.has(device.cihazKodu) ? faEyeSlash : faEye} 
                                                                    className="me-1"
                                                                />
                                                                {expandedDevices.has(device.cihazKodu) ? 'Gizle' : 'Detay'}
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                                {expandedDevices.has(device.cihazKodu) && (
                                                    <tr className="device-details-row">
                                                        <td colSpan="7" className="bg-light">
                                                            <div className="p-3">
                                                                <div className="row">
                                                                    <div className="col-md-6">
                                                                        <h6 className="text-primary mb-3">
                                                                            <FontAwesomeIcon icon={faInfoCircle} className="me-2" />
                                                                            Sensör Verileri
                                                                        </h6>
                                                                        <div className="row">
                                                                            <div className="col-6 mb-2">
                                                                                <div className="d-flex align-items-center">
                                                                                    <FontAwesomeIcon icon={faBatteryHalf} className="text-warning me-2" />
                                                                                    <span className="fw-bold">Pil Gerilimi:</span>
                                                                                </div>
                                                                                <div className="ms-4">
                                                                                    {device.pilGerilimi ? `${device.pilGerilimi}V (${device.pilSeviyesi}%)` : 'Veri yok'}
                                                                                </div>
                                                                            </div>
                                                                            <div className="col-6 mb-2">
                                                                                <div className="d-flex align-items-center">
                                                                                    <FontAwesomeIcon icon={faTint} className="text-info me-2" />
                                                                                    <span className="fw-bold">Nem:</span>
                                                                                </div>
                                                                                <div className="ms-4">
                                                                                    {device.nem ? `${device.nem}%` : 'Veri yok'}
                                                                                </div>
                                                                            </div>
                                                                            <div className="col-6 mb-2">
                                                                                <div className="d-flex align-items-center">
                                                                                    <FontAwesomeIcon icon={faLightbulb} className="text-warning me-2" />
                                                                                    <span className="fw-bold">Işık:</span>
                                                                                </div>
                                                                                <div className="ms-4">
                                                                                    {device.isik ? device.isik : 'Veri yok'}
                                                                                </div>
                                                                            </div>
                                                                            <div className="col-6 mb-2">
                                                                                <div className="d-flex align-items-center">
                                                                                    <FontAwesomeIcon icon={faMapMarkerAlt} className="text-success me-2" />
                                                                                    <span className="fw-bold">Konum:</span>
                                                                                </div>
                                                                                <div className="ms-4">
                                                                                    {device.konum && device.konum.enlem && device.konum.boylam ? (
                                                                                        <div>
                                                                                            <div>Enlem: {device.konum.enlem}</div>
                                                                                            <div>Boylam: {device.konum.boylam}</div>
                                                                                            {device.konum.yukseklik && <div>Yükseklik: {device.konum.yukseklik}m</div>}
                                                                                        </div>
                                                                                    ) : 'Konum bilgisi yok'}
                                                                                </div>
                                                                            </div>
                                                                            <div className="col-6 mb-2">
                                                                                <div className="d-flex align-items-center">
                                                                                    <FontAwesomeIcon icon={faClock} className="text-primary me-2" />
                                                                                    <span className="fw-bold">Kontor Sonu:</span>
                                                                                </div>
                                                                                <div className="ms-4">
                                                                                    {(() => {
                                                                                        const remaining = calculateRemainingTime(device.kontorSonu);
                                                                                        return (
                                                                                            <div>
                                                                                                <div className={
                                                                                                    remaining.isExpired || remaining.isWarning 
                                                                                                        ? 'text-danger fw-bold' 
                                                                                                        : 'text-success'
                                                                                                }>
                                                                                                    {remaining.displayText}
                                                                                                </div>
                                                                                                {device.kontorSonu && (
                                                                                                    <div className="text-muted small">
                                                                                                        {new Date(device.kontorSonu).toLocaleString('tr-TR')}
                                                                                                    </div>
                                                                                                )}
                                                                                            </div>
                                                                                        );
                                                                                    })()}
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div className="col-md-6">
                                                                        <h6 className="text-primary mb-3">
                                                                            <FontAwesomeIcon icon={faShippingFast} className="me-2" />
                                                                            Son Sevkiyat Detayları
                                                                        </h6>
                                                                        {device.enSonSevkiyat ? (
                                                                            <div>
                                                                                <div className="mb-2">
                                                                                    <strong>Sevkiyat No:</strong> {device.enSonSevkiyat.sevkiyatNo}
                                                                                </div>
                                                                                <div className="mb-2">
                                                                                    <strong>Çıkış:</strong> {device.enSonSevkiyat.cikisLokasyon}
                                                                                </div>
                                                                                <div className="mb-2">
                                                                                    <strong>Varış:</strong> {device.enSonSevkiyat.varisLokasyon}
                                                                                </div>
                                                                                <div className="mb-2">
                                                                                    <strong>Ürün:</strong> {device.enSonSevkiyat.urunBilgisi}
                                                                                </div>
                                                                                <div className="mb-2">
                                                                                    <strong>Nakliyeci:</strong> {device.enSonSevkiyat.nakliyeci}
                                                                                </div>
                                                                                <div className="mb-2">
                                                                                    <strong>Başlangıç:</strong> {device.enSonSevkiyat.sevkiyatTarihi}
                                                                                </div>
                                                                                {device.enSonSevkiyat.bitisTarihi && (
                                                                                    <div className="mb-2">
                                                                                        <strong>Bitiş:</strong> {device.enSonSevkiyat.bitisTarihi}
                                                                                    </div>
                                                                                )}
                                                                            </div>
                                                                        ) : (
                                                                            <div className="text-muted">
                                                                                Bu cihaz için henüz sevkiyat kaydı bulunmamaktadır.
                                                                            </div>
                                                                        )}
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                )}
                                            </React.Fragment>
                                        ))}
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </>
                        )}
                    </main>

                    <Footer />
                </div>
            </div>
        </>
    );
};

export default InactiveDevices;