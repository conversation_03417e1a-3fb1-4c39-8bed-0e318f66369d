import React from 'react';

/**
 * Reusable Loading Spinner Component
 * @param {Object} props - Component props
 * @param {string} props.size - Size variant: 'sm', 'md', 'lg'
 * @param {string} props.variant - Color variant: 'primary', 'secondary', 'success', 'danger', 'warning', 'info'
 * @param {string} props.message - Loading message to display
 * @param {boolean} props.overlay - Whether to show as overlay
 * @param {boolean} props.centered - Whether to center the spinner
 */
const LoadingSpinner = ({ 
  size = 'md', 
  variant = 'primary', 
  message = 'Yükleniyor...', 
  overlay = false,
  centered = true 
}) => {
  // Size mappings
  const sizeClasses = {
    sm: 'spinner-border-sm',
    md: '',
    lg: 'spinner-border-lg'
  };

  // Container classes
  const containerClasses = [
    overlay ? 'position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center' : '',
    centered && !overlay ? 'd-flex justify-content-center align-items-center' : '',
    overlay ? 'bg-dark bg-opacity-50' : '',
    overlay ? 'z-index-9999' : ''
  ].filter(Boolean).join(' ');

  // Spinner classes
  const spinnerClasses = [
    'spinner-border',
    `text-${variant}`,
    sizeClasses[size]
  ].filter(Boolean).join(' ');

  const content = (
    <div className="text-center">
      <div className={spinnerClasses} role="status" aria-hidden="true">
        <span className="visually-hidden">{message}</span>
      </div>
      {message && (
        <div className={`mt-2 text-${variant}`} style={{ fontSize: '0.9rem' }}>
          {message}
        </div>
      )}
    </div>
  );

  if (overlay) {
    return (
      <div className={containerClasses} style={{ zIndex: 9999 }}>
        <div className="bg-white rounded p-4 shadow">
          {content}
        </div>
      </div>
    );
  }

  return (
    <div className={containerClasses}>
      {content}
    </div>
  );
};

export default LoadingSpinner;