import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import logoDark from '../assets/img/logo-dark.png';
import backgroundImg from '../assets/img/on-the-road.jpg';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faEye, faEyeSlash } from '@fortawesome/free-solid-svg-icons';

const Login = () => {
    const navigate = useNavigate();
    const [formData, setFormData] = useState({
        email: '',
        password: '',
        rememberMe: false
    });

    const [showPassword, setShowPassword] = useState(false);
    const [error, setError] = useState('');

    // Geçici kullanıcı bilgileri
    const tempUser = {
        email: '<EMAIL>',
        password: 'mgz24321'
    };

    const handleChange = (e) => {
        const { name, value, type, checked } = e.target;
        setFormData({
            ...formData,
            [name]: type === 'checkbox' ? checked : value
        });
    };

    const togglePasswordVisibility = () => {
        setShowPassword(!showPassword);
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        setError('');

        // Geçici kullanıcı kontrolü
        if (formData.email === tempUser.email && formData.password === tempUser.password) {
            console.log('Giriş başarılı');
            // Başarılı giriş durumunda ana sayfaya yönlendir
            navigate('/');
        } else {
            setError('E-posta adresi veya parola hatalı! Geçici kullanıcı bilgileri: <EMAIL> / mgz24321');
        }
    };

    // Stil tanımlamaları
    const styles = {
        // Arkaplan stili
        loginContainer: {
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '100vh',
            backgroundImage: `url(${backgroundImg})`,
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'center center',
            backgroundSize: 'cover',
            position: 'relative'
        },
        // Arkaplan kaplama stili
        overlay: {
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(0, 0, 0, 0.1)',
            zIndex: 1
        },
        // Kart stili
        card: {
            width: '100%',
            maxWidth: '450px',
            padding: '30px 25px',
            backgroundColor: '#fff',
            borderRadius: '8px',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.2)',
            border: '1px solid #e0e0e0',
            position: 'relative',
            zIndex: 2
        },
        // Logo stili
        logo: {
            maxWidth: '250px',
            marginBottom: '1rem'
        },
        // Form kontrol stili
        formControl: {
            height: '48px',
            borderRadius: '4px',
            border: '1px solid #ced4da',
            padding: '10px 15px',
            fontSize: '16px',
            width: '100%'
        },
        // Giriş butonu stili
        btnInfo: {
            backgroundColor: '#00c3f7',
            borderColor: '#00c3f7',
            color: '#fff',
            fontWeight: 500,
            borderRadius: '4px',
            height: '48px',
            fontSize: '16px',
            width: '100%',
            border: 'none',
            cursor: 'pointer',
            marginBottom: '1rem'
        },
        // Form grubu stili
        formGroup: {
            marginBottom: '1rem',
            position: 'relative'
        },
        // Check box container
        checkGroup: {
            marginBottom: '1rem',
            display: 'flex',
            alignItems: 'center',
            backgroundColor: '#fff'
        },
        // Check box input
        checkInput: {
            marginRight: '8px'
        },
        // Password toggle icon
        passwordToggle: {
            position: 'absolute',
            right: '15px',
            top: '15px',
            cursor: 'pointer',
            color: '#777'
        },
        // Error message
        errorMessage: {
            color: '#dc3545',
            fontSize: '14px',
            marginBottom: '15px',
            textAlign: 'center',
            backgroundColor: '#f8d7da',
            padding: '8px',
            borderRadius: '4px',
            border: '1px solid #f5c6cb'
        },
        // Kullanıcı bilgileri alanı
        tempUserInfo: {
            marginTop: '20px',
            padding: '10px',
            backgroundColor: '#f8f9fa',
            borderRadius: '4px',
            border: '1px solid #dee2e6',
            fontSize: '14px',
            color: '#6c757d',
            textAlign: 'center'
        }
    };

    return (
        <div style={styles.loginContainer}>
            <div style={styles.overlay}></div>
            <div style={styles.card}>
                <div style={{ textAlign: 'center', marginBottom: '1.5rem' }}>
                    <img src={logoDark} alt="MGZ24 Logo" style={styles.logo} />
                </div>

                <form onSubmit={handleSubmit}>
                    {error && <div style={styles.errorMessage}>{error}</div>}

                    <div style={styles.formGroup}>
                        <input
                            type="email"
                            style={styles.formControl}
                            placeholder="Email adresiniz"
                            name="email"
                            value={formData.email}
                            onChange={handleChange}
                            required
                        />
                    </div>

                    <div style={styles.formGroup}>
                        <input
                            type={showPassword ? "text" : "password"}
                            style={styles.formControl}
                            placeholder="Parola"
                            name="password"
                            value={formData.password}
                            onChange={handleChange}
                            required
                        />
                        <FontAwesomeIcon
                            icon={showPassword ? faEyeSlash : faEye}
                            style={styles.passwordToggle}
                            onClick={togglePasswordVisibility}
                        />
                    </div>

                    <div style={styles.checkGroup}>
                        <input
                            type="checkbox"
                            style={styles.checkInput}
                            id="rememberMe"
                            name="rememberMe"
                            checked={formData.rememberMe}
                            onChange={handleChange}
                        />
                        <label htmlFor="rememberMe" style={{ color: '#333', backgroundColor: 'transparent' }}>
                            Beni hatırla
                        </label>
                    </div>

                    <button
                        type="submit"
                        style={styles.btnInfo}
                    >
                        Giriş
                    </button>

                    <div style={styles.tempUserInfo}>
                        <strong>Geçici Kullanıcı Bilgileri:</strong>
                        <br />
                        Email: <EMAIL><br />
                        Şifre: mgz24321
                    </div>

                    <div style={{ textAlign: 'center', margin: '1.5rem 0' }}>
                        <span>Hesabınız yoksa buradan </span>
                        <Link to="/register" style={{ color: '#007f97', textDecoration: 'none' }}>
                            <strong>kayıt</strong>
                        </Link>
                        <span> olun</span>
                    </div>

                    <hr />

                    <div style={{ textAlign: 'center', color: '#6c757d' }}>
                        &copy; 2025 Inkatech Ölçüm Sistemleri
                    </div>
                </form>
            </div>
        </div>
    );
};

export default Login; 