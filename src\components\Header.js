import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faRightFromBracket, faBars, faUser } from '@fortawesome/free-solid-svg-icons';
import logoDark from '../assets/img/logo.png';
import { kullaniciService } from '../api/dbService';

const Header = () => {
    const navigate = useNavigate();
    const [userName, setUserName] = useState('Misafir');
    const [loading, setLoading] = useState(true);

    // Kullanıcı bilgilerini veritabanından al
    useEffect(() => {
        const fetchUserData = async () => {
            try {
                // Local storage'dan kullanıcı bilgisini al
                const storedUser = JSON.parse(localStorage.getItem('user'));
                const userId = storedUser?.user?.musteri_ID || storedUser?.user?.id;
                const userName = storedUser?.user?.name || storedUser?.user?.musteri_adi;

                if (!userId) {
                    console.warn('Oturum bilgisi bulunamadı');
                    setUserName('Misafir');
                    setLoading(false);
                    return;
                }

                // Eğer localStorage'da isim varsa direkt kullan
                if (userName) {
                    setUserName(userName);
                    setLoading(false);
                    return;
                }

                // API'den kullanıcı bilgilerini getir (backend çalışıyorsa)
                try {
                    const userData = await kullaniciService.getKullanici(userId);

                    // musteri_adi alanını kullan
                    if (userData && userData.musteri_adi) {
                        setUserName(userData.musteri_adi);

                        // localStorage'ı güncelle
                        if (storedUser?.user) {
                            storedUser.user.name = userData.musteri_adi;
                            localStorage.setItem('user', JSON.stringify(storedUser));
                        }
                    }
                } catch (apiError) {
                    console.warn('API\'den kullanıcı bilgisi alınamadı, localStorage kullanılıyor');
                    // API hatası durumunda localStorage'daki adı kullan
                    setUserName(userName || 'Kullanıcı');
                }
            } catch (error) {
                console.error('Kullanıcı bilgileri alınırken hata:', error);
                // Hata durumunda varsayılan isim kullan
                setUserName('Kullanıcı');
            } finally {
                setLoading(false);
            }
        };

        fetchUserData();
    }, []);

    // Çıkış işlemi
    const handleLogout = () => {
        // Local storage'dan kullanıcı bilgilerini temizle
        localStorage.removeItem('user');

        // Login sayfasına yönlendir
        navigate('/login');
    };

    return (
        <header className="navbar sticky-top bg-darkblue flex-md-nowrap p-0 shadow" data-bs-theme="dark">
            <Link className="navbar-brand col-md-4 col-lg-2 me-0 px-3 py-2 fs-6 text-white" to="/">
                <img src={logoDark} alt="MGZ24 Logo" height="40" />
            </Link>
            <ul className="navbar-nav flex-row align-items-center">
                <li className="nav-item text-nowrap d-none d-md-block me-3">
                    <span className="text-white">
                        <FontAwesomeIcon icon={faUser} className="me-2" />
                        {loading ? 'Yükleniyor...' : userName}
                    </span>
                </li>
                <li className="nav-item text-nowrap px-1">
                    <button
                        onClick={handleLogout}
                        className="nav-link px-3"
                        data-bs-toggle="tooltip"
                        data-bs-placement="left"
                        data-bs-title="Çıkış yap!"
                    >
                        <FontAwesomeIcon icon={faRightFromBracket} />
                    </button>
                </li>
                <li className="nav-item text-nowrap d-md-none">
                    <button
                        className="nav-link px-3"
                        type="button"
                        data-bs-toggle="offcanvas"
                        data-bs-target="#yanMenu"
                        aria-controls="yanMenu"
                        aria-expanded="false"
                        aria-label="menu aç/kapa"
                    >
                        <FontAwesomeIcon icon={faBars} />
                    </button>
                </li>
            </ul>
        </header>
    );
};

export default Header; 