import { EventEmitter } from 'events';
import ExternalApiService from './externalApiService.js';
import mysql from 'mysql2/promise';

class RealTimePollingService extends EventEmitter {
    constructor() {
        super();
        this.pool = mysql.createPool({
            host: process.env.DB_HOST || 'localhost',
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || '',
            database: process.env.DB_NAME || 'mgz24db',
            waitForConnections: true,
            connectionLimit: 10,
            queueLimit: 0
        });
        this.externalApiService = new ExternalApiService();
        this.pollingIntervals = new Map();
        this.isPolling = false;
        this.defaultPollingInterval = 30000; // 30 saniye
        this.fastPollingInterval = 10000; // 10 saniye (acil durumlar için)
        this.slowPollingInterval = 60000; // 1 dakika (inaktif cihazlar için)
    }

    // Polling servisini başlat
    async startPolling() {
        if (this.isPolling) {
            console.log('Polling service is already running');
            return;
        }

        this.isPolling = true;
        console.log('Starting real-time polling service...');

        try {
            // Aktif sevkiyatlardaki cihazları getir
            const activeDevices = await this.getActiveDevices();
            
            // Her cihaz için polling başlat
            for (const device of activeDevices) {
                await this.startDevicePolling(device);
            }

            // Genel sistem durumu kontrolü
            this.startSystemHealthCheck();

            console.log(`Real-time polling started for ${activeDevices.length} devices`);
            this.emit('pollingStarted', { deviceCount: activeDevices.length });

        } catch (error) {
            console.error('Error starting polling service:', error);
            this.emit('pollingError', error);
        }
    }

    // Polling servisini durdur
    async stopPolling() {
        if (!this.isPolling) {
            console.log('Polling service is not running');
            return;
        }

        this.isPolling = false;
        console.log('Stopping real-time polling service...');

        // Tüm polling interval'larını temizle
        for (const [deviceId, intervalId] of this.pollingIntervals) {
            clearInterval(intervalId);
        }
        this.pollingIntervals.clear();

        // Sistem sağlık kontrolünü durdur
        if (this.systemHealthInterval) {
            clearInterval(this.systemHealthInterval);
        }

        console.log('Real-time polling service stopped');
        this.emit('pollingStopped');
    }

    // Aktif cihazları getir
    async getActiveDevices() {
        try {
            const [devices] = await this.pool.execute(`
                SELECT 
                    c.cihaz_kodu,
                    c.kullanici_ID,
                    c.pil_seviyesi,
                    c.son_kullanim_tarihi,
                    c.aktif
                FROM cihazBilgi c
                WHERE c.aktif = 1
                ORDER BY c.son_kullanim_tarihi DESC
            `);

            return devices;

        } catch (error) {
            console.error('Error fetching active devices:', error);
            throw error;
        }
    }

    // Belirli bir cihaz için polling başlat
    async startDevicePolling(device) {
        if (this.pollingIntervals.has(device.cihaz_kodu)) {
            // Zaten polling yapılıyorsa durdur
            clearInterval(this.pollingIntervals.get(device.cihaz_kodu));
        }

        // Polling interval'ını belirle
        const pollingInterval = this.determinePollingInterval(device);
        
        console.log(`Starting polling for device ${device.cihaz_kodu} with ${pollingInterval}ms interval`);

        const intervalId = setInterval(async () => {
            try {
                await this.pollDevice(device);
            } catch (error) {
                console.error(`Error polling device ${device.cihaz_kodu}:`, error);
                this.emit('devicePollingError', { device: device.cihaz_kodu, error });
            }
        }, pollingInterval);

        this.pollingIntervals.set(device.cihaz_kodu, intervalId);
    }

    // Cihaz için uygun polling interval'ını belirle
    determinePollingInterval(device) {
        // Gold cihazlar için daha hızlı polling
        if (device.GoldCihaz === 1) {
            return this.fastPollingInterval;
        }

        // Batarya seviyesi düşükse daha hızlı polling
        if (device.pil_seviyesi < 20) {
            return this.fastPollingInterval;
        }

        // Son güncelleme uzun süre önceyse yavaş polling
        if (device.minutes_since_last_update > 60) {
            return this.slowPollingInterval;
        }

        return this.defaultPollingInterval;
    }

    // Tek bir cihazı poll et
    async pollDevice(device) {
        try {
            // External API'den güncel durumu getir
            const deviceStatus = await this.externalApiService.fetchDeviceStatus(device.cihaz_kodu);
            
            // Lokal database'i güncelle
            await this.updateLocalDeviceData(device.cihaz_kodu, deviceStatus);

            // Kritik durum kontrolü
            const alerts = await this.checkCriticalAlerts(device.cihaz_kodu, deviceStatus);
            
            if (alerts.length > 0) {
                this.emit('criticalAlerts', { device: device.cihaz_kodu, alerts });
            }

            // Başarılı polling olayını yayınla
            this.emit('deviceUpdated', { 
                device: device.cihaz_kodu, 
                status: deviceStatus,
                timestamp: new Date()
            });

        } catch (error) {
            console.error(`Error polling device ${device.cihaz_kodu}:`, error);
            
            // Hata sayacını artır
            await this.incrementErrorCount(device.cihaz_kodu);
            
            throw error;
        }
    }

    // Lokal database'i güncelle
    async updateLocalDeviceData(cihazKodu, deviceStatus) {
        try {
            await this.pool.execute(`
                UPDATE cihazBilgi 
                SET 
                    sicaklik = ?,
                    nem = ?,
                    pil_seviyesi = ?,
                    enlem = ?,
                    boylam = ?,
                    son_kontrol = NOW(),
                    durum = CASE 
                        WHEN ? = 'online' THEN 'aktif'
                        WHEN ? = 'warning' THEN 'uyari'
                        ELSE 'pasif'
                    END
                WHERE cihaz_kodu = ?
            `, [
                deviceStatus.sonSensorler.sicaklik,
                deviceStatus.sonSensorler.nem,
                deviceStatus.batarya.mevcut,
                deviceStatus.konum.enlem,
                deviceStatus.konum.boylam,
                deviceStatus.durum,
                deviceStatus.durum,
                cihazKodu
            ]);

            // Sensör verilerini sensor_history tablosuna kaydet
            await this.pool.execute(`
                INSERT INTO sensor_history (
                    cihaz_kodu, sicaklik, nem, pil_seviyesi, enlem, boylam,
                    okuma_zamani, durum
                ) VALUES (?, ?, ?, ?, ?, ?, NOW(), ?)
            `, [
                cihazKodu,
                deviceStatus.sonSensorler.sicaklik,
                deviceStatus.sonSensorler.nem,
                deviceStatus.batarya.mevcut,
                deviceStatus.konum.enlem,
                deviceStatus.konum.boylam,
                deviceStatus.durum
            ]);

        } catch (error) {
            console.error(`Error updating local data for device ${cihazKodu}:`, error);
            throw error;
        }
    }

    // Kritik alarm kontrolü
    async checkCriticalAlerts(cihazKodu, deviceStatus) {
        const alerts = [];

        // Sıcaklık alarm kontrolü
        if (deviceStatus.sonSensorler.sicaklik !== null) {
            // Sevkiyat sıcaklık aralığını kontrol et
            const [tempRange] = await this.pool.execute(`
                SELECT s.sicaklik_araligi 
                FROM sevkiyatlar s 
                JOIN cihazBilgi c ON s.id = c.sevkiyat_id 
                WHERE c.cihaz_kodu = ?
            `, [cihazKodu]);

            if (tempRange.length > 0 && tempRange[0].sicaklik_araligi) {
                const range = tempRange[0].sicaklik_araligi;
                const [minTemp, maxTemp] = range.replace('°C', '').split('-').map(t => parseFloat(t.trim()));
                const currentTemp = deviceStatus.sonSensorler.sicaklik;

                if (currentTemp < minTemp || currentTemp > maxTemp) {
                    alerts.push({
                        type: 'temperature',
                        severity: 'high',
                        message: `Sıcaklık aralık dışında: ${currentTemp}°C (Normal: ${range})`,
                        value: currentTemp,
                        expectedRange: range
                    });
                }
            }
        }

        // Batarya alarm kontrolü
        if (deviceStatus.batarya.mevcut < 15) {
            alerts.push({
                type: 'battery',
                severity: deviceStatus.batarya.mevcut < 5 ? 'critical' : 'medium',
                message: `Düşük batarya: ${deviceStatus.batarya.mevcut}%`,
                value: deviceStatus.batarya.mevcut
            });
        }

        // Bağlantı alarm kontrolü
        if (deviceStatus.durum === 'offline') {
            alerts.push({
                type: 'connection',
                severity: 'high',
                message: `Cihaz bağlantısı kesildi (${deviceStatus.dakikalarOnce} dakika önce)`,
                value: deviceStatus.dakikalarOnce
            });
        }

        return alerts;
    }

    // Hata sayacını artır
    async incrementErrorCount(cihazKodu) {
        try {
            await this.pool.execute(`
                UPDATE cihazBilgi 
                SET error_count = COALESCE(error_count, 0) + 1,
                    last_error_time = NOW()
                WHERE cihaz_kodu = ?
            `, [cihazKodu]);

        } catch (error) {
            console.error(`Error incrementing error count for device ${cihazKodu}:`, error);
        }
    }

    // Sistem sağlık kontrolünü başlat
    startSystemHealthCheck() {
        this.systemHealthInterval = setInterval(async () => {
            try {
                await this.performSystemHealthCheck();
            } catch (error) {
                console.error('System health check error:', error);
                this.emit('systemHealthError', error);
            }
        }, 300000); // 5 dakika
    }

    // Sistem sağlık kontrolü yap
    async performSystemHealthCheck() {
        try {
            // Aktif cihaz sayısını kontrol et
            const [activeCount] = await this.pool.execute(`
                SELECT COUNT(*) as count FROM cihazBilgi c
                JOIN sevkiyatlar s ON c.sevkiyat_id = s.id
                WHERE s.durum IN ('hazırlanıyor', 'yolda') AND c.aktif = 1
            `);

            // Polling yapılan cihaz sayısını kontrol et
            const pollingCount = this.pollingIntervals.size;

            // Uyumsuzluk varsa polling'i yeniden başlat
            if (activeCount[0].count !== pollingCount) {
                console.log(`Device count mismatch: DB=${activeCount[0].count}, Polling=${pollingCount}. Restarting polling...`);
                await this.restartPolling();
            }

            // Sistem durumu raporu
            const healthReport = {
                timestamp: new Date(),
                activeDevices: activeCount[0].count,
                pollingDevices: pollingCount,
                memoryUsage: process.memoryUsage(),
                uptime: process.uptime()
            };

            this.emit('systemHealthReport', healthReport);

        } catch (error) {
            console.error('Error in system health check:', error);
            throw error;
        }
    }

    // Polling'i yeniden başlat
    async restartPolling() {
        console.log('Restarting polling service...');
        
        await this.stopPolling();
        await new Promise(resolve => setTimeout(resolve, 5000)); // 5 saniye bekle
        await this.startPolling();
    }

    // Belirli bir cihazı polling listesine ekle
    async addDeviceToPolling(cihazKodu) {
        try {
            const [device] = await this.pool.execute(`
                SELECT 
                    c.cihaz_kodu,
                    c.sevkiyat_id,
                    c.pil_seviyesi,
                    c.son_kontrol,
                    s.sevkiyat_adi,
                    s.durum as sevkiyat_durum,
                    s.musteri_ID,
                    ci.GoldCihaz,
                    ci.KalanSure,
                    TIMESTAMPDIFF(MINUTE, c.son_kontrol, NOW()) as minutes_since_last_update
                FROM cihazBilgi c
                JOIN sevkiyatlar s ON c.sevkiyat_id = s.id
                LEFT JOIN cihazID ci ON c.cihaz_kodu = ci.CihazID
                WHERE c.cihaz_kodu = ?
            `, [cihazKodu]);

            if (device.length > 0) {
                await this.startDevicePolling(device[0]);
                console.log(`Device ${cihazKodu} added to polling`);
                this.emit('deviceAddedToPolling', { device: cihazKodu });
            }

        } catch (error) {
            console.error(`Error adding device ${cihazKodu} to polling:`, error);
            throw error;
        }
    }

    // Belirli bir cihazı polling listesinden çıkar
    removeDeviceFromPolling(cihazKodu) {
        if (this.pollingIntervals.has(cihazKodu)) {
            clearInterval(this.pollingIntervals.get(cihazKodu));
            this.pollingIntervals.delete(cihazKodu);
            console.log(`Device ${cihazKodu} removed from polling`);
            this.emit('deviceRemovedFromPolling', { device: cihazKodu });
        }
    }

    // Polling durumunu getir
    getPollingStatus() {
        return {
            isPolling: this.isPolling,
            deviceCount: this.pollingIntervals.size,
            devices: Array.from(this.pollingIntervals.keys()),
            uptime: this.isPolling ? process.uptime() : 0
        };
    }

    // Bağlantı havuzunu kapat
    async closeConnection() {
        await this.stopPolling();
        await this.pool.end();
        await this.externalApiService.closeConnection();
    }
}

export default RealTimePollingService;