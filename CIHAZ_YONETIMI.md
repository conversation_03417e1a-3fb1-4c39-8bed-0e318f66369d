# Cihaz Yönetimi Sistemi

## Özellikler

### 1. Cihaz Sorgulama
- Kullanıcı cihaz kodu girer (örn: 55814642)
- Sistem harici API'den cihaz bilgilerini sorgular
- Başarısız durumda demo veri gösterilir

### 2. Kullanıcı Atama
- Kullanıcı listesinden seçim yapılır
- Seçilen kullanıcının `musteri_ID`'si `cihazBilgi` tablosuna yazılır
- Ekranda kullanıcı adı (`musteri_adi`) gösterilir

### 3. Harici API Entegrasyonu
- Primary: `https://ffl21.fun:3001/api/cihaz/{cihazKodu}`
- Fallback: Demo veri (Mixed Content hatası durumunda)

## Teknik Detaylar

### API Endpoints
```javascript
// Cihaz sorgulama
GET /external-api/cihaz/{cihazKodu}

// Cihaz güncelleme
PUT /cihaz-bilgi/{cihazKodu}
```

### Veritabanı Yapısı
```sql
-- cihazBilgi tablosu
cihaz_kodu VARCHAR(50)
model VARCHAR(100)
kullanici_ID INT -- musteri_ID reference

-- kullanicilar tablosu
musteri_ID INT PRIMARY KEY
musteri_adi VARCHAR(255)
```

### Hata Çözümleri

#### Mixed Content Hatası
- **Sebep**: HTTPS sayfası HTTP API'ye istek yapıyor
- **Çözüm**: Proxy endpoint kullanımı, başarısızsa gerçek hata gösterilir

#### Service Worker 404
- **Sebep**: `/sw.js` dosyası bulunamıyor
- **Çözüm**: Hata yakalanıyor ve sessiz geçiliyor

#### CSS Chunk Loading
- **Sebep**: Eksik CSS dosyaları
- **Çözüm**: Build process ile çözülür

## Kullanım

1. Cihaz Yönetimi sayfasına git
2. Cihaz kodunu gir (örn: 55814642)
3. "Cihazı Sorgula" butonuna tıkla
4. Kullanıcı listesinden birini seç
5. "Ata" butonuna tıkla

## Geliştirme Notları

- **Demo veri modu aktif** (`enableDemoData: true`) - Backend bağlantı sorunları nedeniyle geçici olarak
- **Fallback demo veri** kullanılır - API bağlantı hatalarında demo veri gösterilir
- Retry sayısı 1'e düşürüldü (console spam önleme)
- Harici API için timeout: 10 saniye
- Başarısız isteklerde demo veri fallback kullanılır

## Mevcut Durum

Backend API bağlantıları çalışmadığı için demo veri modu aktif edildi:
- Sevkiyat verileri: Demo sevkiyat listesi
- Müşteri verileri: Demo kullanıcı listesi
- Cihaz verileri: Demo cihaz bilgileri
- Cihaz atama: Demo başarı mesajları

**Not**: Gerçek API bağlantıları düzeltildiğinde `enableDemoData: false` yapılabilir.