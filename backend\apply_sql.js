// Veritabanı SQL değişikliklerini uygulayan yardımcı betik
import mysql from 'mysql2/promise';
import fs from 'fs';
import dotenv from 'dotenv';
import path from 'path';

// .env dosyasını yükle
dotenv.config();

async function applySqlChanges() {
    try {
        console.log('SQL değişiklikleri uygulanmaya başlıyor...');
        console.log('Ç<PERSON>ış<PERSON> dizini:', process.cwd());

        // Veritabanı bağlantı ayarları
        const dbConfig = {
            host: process.env.DB_HOST || '************',
            user: process.env.DB_USER || 'mehmet',
            password: process.env.DB_PASSWORD || 'Inka.mS_335265933s.tech',
            database: process.env.DB_NAME || 'mgz24db',
            waitForConnections: true,
            connectionLimit: 1,
            queueLimit: 0
        };

        console.log('Veritabanı bağlantı ayarları:', {
            host: dbConfig.host,
            user: dbConfig.user,
            database: dbConfig.database
        });

        const pool = mysql.createPool(dbConfig);

        // SQL dosyasını oku
        const sqlFilePath = path.resolve('../add_text_fields.sql');
        console.log(`SQL dosyası okunuyor: ${sqlFilePath}`);

        if (!fs.existsSync(sqlFilePath)) {
            throw new Error(`SQL dosyası bulunamadı: ${sqlFilePath}`);
        }

        const sql = fs.readFileSync(sqlFilePath, 'utf8');
        console.log('SQL dosyası içeriği:');
        console.log(sql);

        // SQL ifadelerini ayır (noktali virgülle ayrılmış)
        const statements = sql
            .split(';')
            .map(stmt => stmt.trim())
            .filter(stmt => stmt.length > 0);

        // Her bir SQL ifadesini çalıştır
        const connection = await pool.getConnection();
        try {
            console.log('Veritabanına bağlanıldı.');
            console.log(`${statements.length} adet SQL ifadesi çalıştırılacak...`);

            for (let i = 0; i < statements.length; i++) {
                const stmt = statements[i];
                console.log(`[${i + 1}/${statements.length}] SQL ifadesi çalıştırılıyor:`);
                console.log(stmt);

                try {
                    await connection.query(stmt);
                    console.log(`[${i + 1}/${statements.length}] İfade başarıyla çalıştırıldı.`);
                } catch (err) {
                    if (err.code === 'ER_DUP_FIELDNAME') {
                        console.log(`[${i + 1}/${statements.length}] Alan zaten mevcut, devam ediliyor.`);
                    } else {
                        console.error(`[${i + 1}/${statements.length}] Hata:`, err);
                        throw err;
                    }
                }
            }

            console.log('Tüm SQL ifadeleri başarıyla çalıştırıldı!');
        } finally {
            // Bağlantıyı serbest bırak
            connection.release();
        }

        // Havuzu kapat ve işlemi sonlandır
        await pool.end();

    } catch (err) {
        console.error('SQL değişikliklerini uygularken hata oluştu:', err);
        process.exit(1);
    }
}

// Betik doğrudan çalıştırıldığında SQL değişikliklerini uygula
applySqlChanges().then(() => {
    console.log('İşlem tamamlandı.');
    process.exit(0);
}).catch(err => {
    console.error('Beklenmeyen hata:', err);
    process.exit(1);
}); 