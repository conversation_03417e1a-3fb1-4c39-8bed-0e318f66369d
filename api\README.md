# MGZ24 API - HTTPS Sunucusu

Bu API, MGZ24 IoT cihazlarının yönetimi için HTTPS üzerinden çalışan RESTful API'dir.

## 🚀 Özellikler

- ✅ HTTPS desteği (SSL/TLS)
- ✅ CORS yapılandırması
- ✅ Güvenlik headers
- ✅ Health check endpoint
- ✅ HTTP'den HTTPS'e otomatik yönlendirme
- ✅ Rate limiting ve güvenlik önlemleri

## 📋 Gereksinimler

- Node.js 16.0.0 veya üzeri
- SSL sertifika dosyaları (`/etc/letsencrypt/live/ffl21.fun/`)
- MySQL veritabanı bağlantısı

## 🔧 Kurulum

1. **Bağımlılıkları yükleyin:**

   ```bash
   npm install
   ```

2. **SSL sertifika dosyalarını kontrol edin:**

   ```bash
   ls -la /etc/letsencrypt/live/ffl21.fun/
   ```

3. **Veritabanı bağlantısını yapılandırın:**
   `../inc/db.js` dosyasını düzenleyin.

## 🚀 Başlatma

### Linux/macOS

```bash
./start-https.sh
```

### Windows PowerShell

```powershell
.\start-https.ps1
```

### Manuel başlatma

```bash
# HTTPS sunucusu
node mgz24api.js

# HTTP yönlendirme (ayrı terminal)
node http-redirect.js
```

## 🌐 API Endpoints

### Health Check

- `GET https://ffl21.fun:3001/health`

### Cihaz Yönetimi

- `GET https://ffl21.fun:3001/api/cihazlar` - Tüm cihazları listele
- `GET https://ffl21.fun:3001/api/cihaz/:cihazID` - Cihaz detayı
- `GET https://ffl21.fun:3001/api/cihaz/:cihazID/status` - Cihaz durumu
- `GET https://ffl21.fun:3001/api/cihaz/:cihazID/history` - Cihaz geçmişi
- `GET https://ffl21.fun:3001/api/cihaz/:cihazID/report` - Cihaz raporu

### SIM Kart Yönetimi

- `GET https://ffl21.fun:3001/api/sim/:cihazID` - SIM kart bilgileri
- `GET https://ffl21.fun:3001/api/sim/musteri/:musteriID` - Müşteri SIM kartları

### Arama ve Filtreleme

- `GET https://ffl21.fun:3001/api/cihazlar/search` - Cihaz arama
- `GET https://ffl21.fun:3001/api/cihazlar/filters` - Filtre seçenekleri

### Sensör Verileri

- `GET https://ffl21.fun:3001/api/konum/:cihazID` - Konum bilgileri
- `GET https://ffl21.fun:3001/api/sicaklik-nem/:cihazID` - Sıcaklık ve nem
- `GET https://ffl21.fun:3001/api/son-veriler/:cihazID` - Son veriler

## 🔒 Güvenlik

- SSL/TLS şifreleme
- CORS yapılandırması
- Güvenlik headers
- Rate limiting
- Input validation

## 📝 Loglar

Sunucu logları konsola yazdırılır. Production ortamında log dosyalarına yönlendirmek için:

```bash
node mgz24api.js > api.log 2>&1
```

## 🛠️ Sorun Giderme

### SSL Sertifika Hatası

```bash
# Sertifika dosyalarını kontrol edin
ls -la /etc/letsencrypt/live/ffl21.fun/
```

### Port Kullanımda Hatası

```bash
# Port 3001'i kullanan servisleri kontrol edin
sudo lsof -i :3001
```

### Veritabanı Bağlantı Hatası

- `../inc/db.js` dosyasındaki bağlantı ayarlarını kontrol edin
- MySQL servisinin çalıştığından emin olun

## 📞 Destek

Sorunlar için lütfen MGZ24 ekibi ile iletişime geçin.
