/* Ion.RangeSlider, Flat UI Skin
// css version 2.0.3
// © <PERSON>, 2014    https://github.com/IonDen
// ===================================================================================================================*/

/* =====================================================================================================================
// Skin details */

.irs {
    height: 40px;
}

.irs-with-grid {
    height: 60px;
}

.irs-line {
    height: 12px;
    top: 25px;
    background: #e1e4e9;
    border-radius: 6px;
}

.irs-line-left {
    height: 12px;
}

.irs-line-mid {
    height: 12px;
}

.irs-line-right {
    height: 12px;
}

.irs-bar {
    height: 12px;
    top: 25px;
    background: #0d6efd;
}

.irs-bar-edge {
    top: 25px;
    height: 12px;
    width: 9px;
    background: #0d6efd;
    border-radius: 6px 0 0 6px;
}

.irs-shadow {
    height: 3px;
    top: 34px;
    background: #000;
    opacity: 0.25;
}

.irs-slider {
    width: 20px;
    height: 20px;
    top: 21px;
    background: #fff;
    border: 2px solid #0d6efd;
    border-radius: 50%;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    cursor: pointer;
}

.irs-slider.state_hover,
.irs-slider:hover {
    background: #f0f6ff;
}

.irs-min,
.irs-max {
    color: #999;
    font-size: 10px;
    line-height: 1.333;
    text-shadow: none;
    top: 0;
    padding: 1px 3px;
    background: #e1e4e9;
    border-radius: 3px;
}

.irs-from,
.irs-to,
.irs-single {
    color: #fff;
    font-size: 10px;
    line-height: 1.333;
    text-shadow: none;
    padding: 1px 5px;
    background: #0d6efd;
    border-radius: 3px;
}

.irs-from:after,
.irs-to:after,
.irs-single:after {
    position: absolute;
    display: block;
    content: "";
    bottom: -6px;
    left: 50%;
    width: 0;
    height: 0;
    margin-left: -3px;
    overflow: hidden;
    border: 3px solid transparent;
    border-top-color: #0d6efd;
}

.irs-grid-pol {
    background: #e1e4e9;
}

.irs-grid-text {
    color: #999;
}

.irs-disabled {}