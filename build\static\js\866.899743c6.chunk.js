"use strict";(self.webpackChunkmgz24=self.webpackChunkmgz24||[]).push([[866],{421:(e,a,s)=>{s.d(a,{A:()=>r});s(5043);var l=s(9002),i=s(3910),n=s(7929),t=s(579);const r=()=>{const e=(0,l.zy)(),a="admin"===(()=>{try{var e,a;const s=JSON.parse(localStorage.getItem("user"));return(null===s||void 0===s||null===(e=s.user)||void 0===e?void 0:e.role)||(null===s||void 0===s||null===(a=s.user)||void 0===a?void 0:a.gorev)||"user"}catch(s){return"user"}})();return(0,t.jsx)("div",{className:"sidebar col-md-4 col-lg-2 p-0 bg-light-subtle",children:(0,t.jsxs)("div",{className:"offcanvas-md offcanvas-end",tabIndex:"-1",id:"yanMenu","aria-labelledby":"yanMenu",children:[(0,t.jsxs)("div",{className:"offcanvas-header",children:[(0,t.jsx)("h5",{className:"offcanvas-title",id:"yanMenu",children:"MGZ24 Gold"}),(0,t.jsx)("button",{type:"button",className:"btn-close","data-bs-dismiss":"offcanvas","data-bs-target":"#yanMenu","aria-label":"Kapat"})]}),(0,t.jsxs)("div",{className:"offcanvas-body d-md-flex flex-column p-0 pt-lg-3 overflow-y-auto",children:[(0,t.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase",children:"Sevkiyat \u0130\u015flemleri"}),(0,t.jsxs)("ul",{className:"nav nav-pills flex-column",children:[(0,t.jsx)("li",{className:"nav-item",children:(0,t.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/"===e.pathname?"active":""),to:"/",children:[(0,t.jsx)(i.g,{icon:n.msb}),"Aktif Sevkiyatlar"]})}),(0,t.jsx)("li",{className:"nav-item",children:(0,t.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/add"===e.pathname?"active":""),to:"/add",children:[(0,t.jsx)(i.g,{icon:n.E5r}),"Yeni Sevkiyat Olu\u015ftur"]})}),(0,t.jsx)("li",{className:"nav-item",children:(0,t.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/inactive-devices"===e.pathname?"active":""),to:"/inactive-devices",children:[(0,t.jsx)(i.g,{icon:n.fH7}),"\u0130naktif Cihazlar"]})}),(0,t.jsx)("li",{className:"nav-item",children:(0,t.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/history"===e.pathname?"active":""),to:"/history",children:[(0,t.jsx)(i.g,{icon:n.o9H}),"Ge\xe7mi\u015f Sevkiyatlar"]})})]}),(0,t.jsx)("hr",{className:"my-3"}),(0,t.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase",children:"Sistem Y\xf6netimi"}),(0,t.jsxs)("ul",{className:"nav nav-pills flex-column",children:[a&&(0,t.jsx)("li",{className:"nav-item",children:(0,t.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/devices"===e.pathname?"active":""),to:"/devices",children:[(0,t.jsx)(i.g,{icon:n.ArK}),"Cihaz Y\xf6netimi"]})}),(0,t.jsx)("li",{className:"nav-item",children:(0,t.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/notifications"===e.pathname?"active":""),to:"/notifications",children:[(0,t.jsx)(i.g,{icon:n.z$e}),"Bildirimler"]})})]}),(0,t.jsx)("hr",{className:"my-3"}),(0,t.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase",children:"Kullan\u0131c\u0131 Ayarlar\u0131"}),(0,t.jsxs)("ul",{className:"nav nav-pills flex-column",children:[(0,t.jsx)("li",{className:"nav-item",children:(0,t.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/profile"===e.pathname?"active":""),to:"/profile",children:[(0,t.jsx)(i.g,{icon:n.X46}),"Kullan\u0131c\u0131 Profilim"]})}),(0,t.jsx)("li",{className:"nav-item",children:(0,t.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/viewers"===e.pathname?"active":""),to:"/viewers",children:[(0,t.jsx)(i.g,{icon:n.yLE}),"\u0130zleyici \u0130\u015flemleri"]})})]}),(0,t.jsx)("hr",{className:"my-3"}),(0,t.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase",children:"\xd6deme ve Yap\u0131land\u0131rma"}),(0,t.jsxs)("ul",{className:"nav nav-pills flex-column",children:[(0,t.jsx)("li",{className:"nav-item",children:(0,t.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/payment"===e.pathname?"active":""),to:"/payment",children:[(0,t.jsx)(i.g,{icon:n.$O8}),"\xd6deme Yap"]})}),(0,t.jsx)("li",{className:"nav-item",children:(0,t.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/invoices"===e.pathname?"active":""),to:"/invoices",children:[(0,t.jsx)(i.g,{icon:n.bLf}),"Faturalar\u0131m"]})})]}),(0,t.jsx)("hr",{className:"my-3"}),(0,t.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-body-secondary text-uppercase",children:"Kredi Durumu"})]})]})})}},834:(e,a,s)=>{s.d(a,{A:()=>i});s(5043);var l=s(579);const i=()=>(0,l.jsx)("footer",{className:"py-5 border-top",children:(0,l.jsx)("p",{className:"text-center",children:"\xa92025 Inkatech \xd6l\xe7\xfcm Sistemleri"})})},1899:(e,a,s)=>{s.d(a,{A:()=>c});var l=s(5043),i=s(9002),n=s(3910),t=s(7929);var r=s(4713),o=s(579);const c=()=>{const e=(0,i.Zp)(),[a,s]=(0,l.useState)("Misafir"),[c,d]=(0,l.useState)(!0);(0,l.useEffect)((()=>{(async()=>{try{var e,a,l,i;const t=JSON.parse(localStorage.getItem("user")),o=(null===t||void 0===t||null===(e=t.user)||void 0===e?void 0:e.musteri_ID)||(null===t||void 0===t||null===(a=t.user)||void 0===a?void 0:a.id),c=(null===t||void 0===t||null===(l=t.user)||void 0===l?void 0:l.name)||(null===t||void 0===t||null===(i=t.user)||void 0===i?void 0:i.musteri_adi);if(!o)return console.warn("Oturum bilgisi bulunamad\u0131"),s("Misafir"),void d(!1);if(c)return s(c),void d(!1);try{const e=await r.Qj.getKullanici(o);e&&e.musteri_adi&&(s(e.musteri_adi),null!==t&&void 0!==t&&t.user&&(t.user.name=e.musteri_adi,localStorage.setItem("user",JSON.stringify(t))))}catch(n){console.warn("API'den kullan\u0131c\u0131 bilgisi al\u0131namad\u0131, localStorage kullan\u0131l\u0131yor"),s(c||"Kullan\u0131c\u0131")}}catch(t){console.error("Kullan\u0131c\u0131 bilgileri al\u0131n\u0131rken hata:",t),s("Kullan\u0131c\u0131")}finally{d(!1)}})()}),[]);return(0,o.jsxs)("header",{className:"navbar sticky-top bg-darkblue flex-md-nowrap p-0 shadow","data-bs-theme":"dark",children:[(0,o.jsx)(i.N_,{className:"navbar-brand col-md-4 col-lg-2 me-0 px-3 py-2 fs-6 text-white",to:"/",children:(0,o.jsx)("img",{src:"data:image/png;base64,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",alt:"MGZ24 Logo",height:"40"})}),(0,o.jsxs)("ul",{className:"navbar-nav flex-row align-items-center",children:[(0,o.jsx)("li",{className:"nav-item text-nowrap d-none d-md-block me-3",children:(0,o.jsxs)("span",{className:"text-white",children:[(0,o.jsx)(n.g,{icon:t.X46,className:"me-2"}),c?"Y\xfckleniyor...":a]})}),(0,o.jsx)("li",{className:"nav-item text-nowrap px-1",children:(0,o.jsx)("button",{onClick:()=>{localStorage.removeItem("user"),e("/login")},className:"nav-link px-3","data-bs-toggle":"tooltip","data-bs-placement":"left","data-bs-title":"\xc7\u0131k\u0131\u015f yap!",children:(0,o.jsx)(n.g,{icon:t.yBu})})}),(0,o.jsx)("li",{className:"nav-item text-nowrap d-md-none",children:(0,o.jsx)("button",{className:"nav-link px-3",type:"button","data-bs-toggle":"offcanvas","data-bs-target":"#yanMenu","aria-controls":"yanMenu","aria-expanded":"false","aria-label":"menu a\xe7/kapa",children:(0,o.jsx)(n.g,{icon:t.ckx})})})]})]})}},8866:(e,a,s)=>{s.r(a),s.d(a,{default:()=>N});var l=s(5043),i=s(9002),n=s(3910),t=s(7929),r=s(2916),o=s(1899),c=s(421),d=s(834),m=s(7762),h=s(4857),u=s(579);const x=e=>{let{cihazID:a,tableData:s,onReportGenerated:i}=e;const[r,o]=(0,l.useState)(!1),[c,d]=(0,l.useState)(null);return(0,u.jsx)("div",{className:"card",children:(0,u.jsxs)("div",{className:"card-body",children:[c&&(0,u.jsx)("div",{className:"alert alert-danger",children:(0,u.jsx)("small",{children:c})}),(0,u.jsxs)("div",{className:"d-flex align-items-center justify-content-between",children:[(0,u.jsx)("div",{children:(0,u.jsx)("button",{className:"btn btn-danger",onClick:async()=>{try{if(o(!0),d(null),!s||0===s.length)return void d("Rapor olu\u015fturmak i\xe7in veri bulunamad\u0131.");const e=new m.Ay;e.setFont("helvetica","normal"),e.setLanguage("tr");const l=e=>e.replace(/\u011f/g,"g").replace(/\u011e/g,"G").replace(/\xfc/g,"u").replace(/\xdc/g,"U").replace(/\u015f/g,"s").replace(/\u015e/g,"S").replace(/\u0131/g,"i").replace(/\u0130/g,"I").replace(/\xf6/g,"o").replace(/\xd6/g,"O").replace(/\xe7/g,"c").replace(/\xc7/g,"C");e.setFontSize(16),e.setFont("helvetica","bold");const n=e.internal.pageSize.width,t=l("MGZ24 Cihaz Veri Raporu"),r=(n-e.getTextWidth(t))/2;e.text(t,r,20),e.setFontSize(10),e.setFont("helvetica","normal");const c=(new Date).toLocaleDateString("tr-TR"),u=s.length,x=l("Cihaz ID: ".concat(a,"    Rapor Tarihi: ").concat(c,"    KAYIT SAYISI: ").concat(u)),g=(n-e.getTextWidth(x))/2;e.text(x,g,30);const v=e=>{const a=parseFloat(e);if(isNaN(a))return 0;const s=30+(a-2.5)/(3.4-2.5)*70;return Math.round(Math.max(30,Math.min(100,s)))},j=["Tarih","Sicaklik","Nem","Pil (%)","Enlem","Boylam"].map((e=>l(e))),p=s.map((e=>{var a,s,l,i,n;return[e.tarih||e.zaman||(new Date).toLocaleDateString("tr-TR"),null!==(a=e.sensorler)&&void 0!==a&&a.sicaklik?"".concat(e.sensorler.sicaklik,"\xb0C"):"-",null!==(s=e.sensorler)&&void 0!==s&&s.nem?"".concat(e.sensorler.nem,"%"):"-",null!==(l=e.sensorler)&&void 0!==l&&l.pil?"".concat(v(e.sensorler.pil),"%"):"-",(null===(i=e.konum)||void 0===i?void 0:i.enlem)||"-",(null===(n=e.konum)||void 0===n?void 0:n.boylam)||"-"]}));(0,h.Ay)(e,{head:[j],body:p,startY:40,styles:{fontSize:8,cellPadding:3},headStyles:{fillColor:[41,128,185],textColor:255,fontStyle:"bold"},alternateRowStyles:{fillColor:[245,245,245]},margin:{top:40,left:14,right:14}});const b="MGZ24_Cihaz_Raporu_".concat(a,"_").concat((new Date).toISOString().split("T")[0],".pdf");e.save(b),i&&i("pdf",b)}catch(c){console.error("PDF rapor olu\u015fturma hatas\u0131:",c),d("PDF raporu olu\u015fturulurken hata olu\u015ftu: "+c.message)}finally{o(!1)}},disabled:r,children:r?(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(n.g,{icon:t.z1G,className:"me-2 fa-spin"}),"Olu\u015fturuluyor..."]}):(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(n.g,{icon:t.O_q,className:"me-2"}),"PDF \u0130ndir"]})})}),(0,u.jsx)("div",{children:(0,u.jsx)("small",{className:"text-muted",children:"Sadece g\xf6sterilen veriler raporlan\u0131r."})})]})]})})};var g=s(9379);const v=e=>{let{cihazID:a,dateRange:s={}}=e;const[i,o]=(0,l.useState)(null),[c,d]=(0,l.useState)(!0),[m,h]=(0,l.useState)(null),[x,v]=(0,l.useState)(null),j=async()=>{if(!a)return h("Cihaz ID bulunamad\u0131"),void d(!1);try{h(null);const t=await fetch("https://ffl21.fun:3001/api/cihaz/".concat(a));if(!t.ok)throw new Error("HTTP ".concat(t.status,": ").concat(t.statusText));const r=await t.json();if(!r.success||!r.data)throw new Error(r.message||"Cihaz verisi al\u0131namad\u0131");{var e,s,l,i,n;const a=(0,g.A)((0,g.A)({},r.data),{},{sicaklik:(null===(e=r.data.sonSensorler)||void 0===e?void 0:e.sicaklik)||r.data.sicaklik,nem:(null===(s=r.data.sonSensorler)||void 0===s?void 0:s.nem)||r.data.nem,pil:(null===(l=r.data.sonSensorler)||void 0===l?void 0:l.pil)||r.data.pil,isik:(null===(i=r.data.sonSensorler)||void 0===i?void 0:i.isik)||r.data.isik,basinc:(null===(n=r.data.sonSensorler)||void 0===n?void 0:n.basinc)||r.data.basinc});o(a),v((new Date).toLocaleTimeString())}}catch(t){console.error("Cihaz verisi getirme hatas\u0131:",t),h(t.message)}finally{d(!1)}};(0,l.useEffect)((()=>{j()}),[a]);const p=e=>{const a=parseFloat(e);return a<0?"text-primary":a<15?"text-info":a>30?"text-danger":"text-success"},b=e=>{const a=parseFloat(e);return a>80?"text-danger":a>60?"text-warning":"text-success"},y=e=>{const a=parseFloat(e);if(isNaN(a))return 0;const s=30+(a-2.5)/(3.4-2.5)*70;return Math.round(Math.max(30,Math.min(100,s)))},N=e=>{const a=parseFloat(e);return a<35?"text-danger":a<60?"text-warning":"text-success"};return a?c?(0,u.jsx)("div",{className:"row mb-4",children:(0,u.jsx)("div",{className:"col-12",children:(0,u.jsx)("div",{className:"card border-0 shadow-sm",children:(0,u.jsxs)("div",{className:"card-body text-center py-4",children:[(0,u.jsx)("div",{className:"spinner-border text-primary",role:"status",children:(0,u.jsx)("span",{className:"visually-hidden",children:"Cihaz verileri y\xfckleniyor..."})}),(0,u.jsx)("div",{className:"mt-2",children:"Cihaz verileri y\xfckleniyor..."})]})})})}):m?(0,u.jsx)("div",{className:"row mb-4",children:(0,u.jsx)("div",{className:"col-12",children:(0,u.jsxs)("div",{className:"card border-0 shadow-sm",children:[(0,u.jsxs)("div",{className:"card-header bg-warning text-dark d-flex justify-content-between align-items-center",children:[(0,u.jsxs)("h6",{className:"mb-0",children:[(0,u.jsx)(n.g,{icon:t.Rog,className:"me-2"}),"Cihaz Verileri - ",a]}),(0,u.jsxs)("div",{className:"d-flex gap-2",children:[(0,u.jsx)("span",{className:"badge bg-danger",children:"Hata"}),(0,u.jsx)("button",{className:"btn btn-sm btn-outline-dark",onClick:j,title:"Yeniden Dene",children:(0,u.jsx)(n.g,{icon:t.VNe})})]})]}),(0,u.jsx)("div",{className:"card-body",children:(0,u.jsxs)("div",{className:"alert alert-warning mb-0",children:[(0,u.jsx)("strong",{children:"Veri al\u0131namad\u0131:"})," ",m,(0,u.jsx)("br",{}),(0,u.jsxs)("small",{children:["API endpointi kontrol edilsin: https://ffl21.fun:3001/api/cihaz/",a]})]})})]})})}):(0,u.jsx)("div",{className:"row mb-4",children:(0,u.jsx)("div",{className:"col-12",children:(0,u.jsxs)("div",{className:"card border-0 shadow-sm",children:[(0,u.jsxs)("div",{className:"card-header bg-gradient-primary text-white d-flex justify-content-between align-items-center",children:[(0,u.jsxs)("h6",{className:"mb-0",children:[(0,u.jsx)(n.g,{icon:t.Rog,className:"me-2"}),"En Son Veriler - ",a]}),(0,u.jsxs)("div",{className:"d-flex gap-2",children:[(0,u.jsx)("span",{className:"badge bg-success",children:"Canl\u0131"}),(0,u.jsx)("button",{className:"btn btn-sm btn-outline-light",onClick:j,title:"Yenile",children:(0,u.jsx)(n.g,{icon:t.VNe})})]})]}),(0,u.jsxs)("div",{className:"card-body",children:[(0,u.jsxs)("div",{className:"row",children:[(0,u.jsx)("div",{className:"col-md-3 mb-3",children:(0,u.jsxs)("div",{className:"d-flex align-items-center p-3 border-end",children:[(0,u.jsx)("div",{className:"me-3",children:(0,u.jsx)(n.g,{icon:t.Rog,className:p(i.sicaklik||"0"),size:"lg"})}),(0,u.jsxs)("div",{children:[(0,u.jsx)("div",{className:"text-muted small",children:"S\u0131cakl\u0131k"}),(0,u.jsxs)("div",{className:"fw-bold ".concat(p(i.sicaklik||"0")),children:[i.sicaklik||"0","\xb0C"]})]})]})}),(0,u.jsx)("div",{className:"col-md-3 mb-3",children:(0,u.jsxs)("div",{className:"d-flex align-items-center p-3 border-end",children:[(0,u.jsx)("div",{className:"me-3",children:(0,u.jsx)(n.g,{icon:t.yzd,className:b(i.nem||"0"),size:"lg"})}),(0,u.jsxs)("div",{children:[(0,u.jsx)("div",{className:"text-muted small",children:"Nem"}),(0,u.jsxs)("div",{className:"fw-bold ".concat(b(i.nem||"0")),children:["%",i.nem||"0"]})]})]})}),(0,u.jsx)("div",{className:"col-md-3 mb-3",children:(0,u.jsxs)("div",{className:"d-flex align-items-center p-3 border-end",children:[(0,u.jsx)("div",{className:"me-3",children:(0,u.jsx)(n.g,{icon:t.W6R,className:N(y(i.pil||"0")),size:"lg"})}),(0,u.jsxs)("div",{children:[(0,u.jsx)("div",{className:"text-muted small",children:"Pil"}),(0,u.jsxs)("div",{className:"fw-bold ".concat(N(y(i.pil||"0"))),children:["%",y(i.pil||"0")]}),y(i.pil||"0")<35&&(0,u.jsx)("div",{className:"text-danger small fw-bold",children:"\u26a0\ufe0f Pil seviyesi d\xfc\u015f\xfck"})]})]})}),(0,u.jsx)("div",{className:"col-md-3 mb-3",children:(0,u.jsxs)("div",{className:"d-flex align-items-center p-3",children:[(0,u.jsx)("div",{className:"me-3",children:(0,u.jsx)(n.g,{icon:r.rC2,className:"text-primary",size:"lg"})}),(0,u.jsxs)("div",{children:[(0,u.jsx)("div",{className:"text-muted small",children:"I\u015f\u0131k"}),(0,u.jsx)("div",{className:"fw-bold text-primary",children:i.isik||"0"})]})]})})]}),(0,u.jsxs)("div",{className:"row mt-3 pt-3 border-top",children:[(0,u.jsx)("div",{className:"col-md-6",children:(0,u.jsxs)("div",{className:"d-flex align-items-center",children:[(0,u.jsx)(n.g,{icon:t.Pcr,className:"text-primary me-2"}),(0,u.jsx)("span",{className:"text-muted",children:"Konum:"}),(0,u.jsx)("span",{className:"ms-2 fw-bold text-primary",children:i.enlem&&i.boylam?"".concat(parseFloat(i.enlem).toFixed(6),", ").concat(parseFloat(i.boylam).toFixed(6)):"Konum bilgisi yok"})]})}),(0,u.jsxs)("div",{className:"col-md-6 text-end",children:[(0,u.jsx)("span",{className:"text-muted",children:"Durum:"}),(0,u.jsx)("span",{className:"ms-2 badge ".concat(i.aktif?"bg-success":"bg-secondary"),children:i.aktif?"aktif":"pasif"}),x&&(0,u.jsxs)("span",{className:"ms-3 text-muted",children:["Son G\xfcncelleme: ",x]})]})]})]})]})})}):null},j=e=>{let{cihazID:a,onHistoryDataChange:s,dateRange:i={}}=e;const[r,o]=(0,l.useState)([]),[c,d]=(0,l.useState)(!1),[m,h]=(0,l.useState)(null),[x,g]=(0,l.useState)(10),[v,j]=(0,l.useState)(0),p=e=>{const a=parseFloat(e);return a<35?"text-danger":a<60?"text-warning":"text-success"},b=(0,l.useCallback)((async()=>{if(a)try{d(!0),h(null);const r=new URLSearchParams;i.baslangic_tarihi&&(r.append("baslangicTarihi",i.baslangic_tarihi),console.log("DeviceHistoryTable - Ba\u015flang\u0131\xe7 tarihi:",i.baslangic_tarihi)),i.bitis_tarihi&&(r.append("bitisTarihi",i.bitis_tarihi),console.log("DeviceHistoryTable - Biti\u015f tarihi:",i.bitis_tarihi)),"all"!==x&&r.append("limit",x);const c=r.toString()?"?".concat(r.toString()):"";console.log("\ud83d\udd0d DeviceHistoryTable - API URL:","https://ffl21.fun:3001/api/cihaz/".concat(a,"/history").concat(c)),console.log("\ud83d\udcc5 DeviceHistoryTable - Tarih Aral\u0131\u011f\u0131 Kontrol\xfc:",{baslangic:i.baslangic_tarihi,bitis:i.bitis_tarihi,expectedResult:"".concat(i.baslangic_tarihi," ile ").concat(i.bitis_tarihi," aras\u0131 veriler"),dateRangeEmpty:0===Object.keys(i).length}),i.baslangic_tarihi&&i.bitis_tarihi||console.error("\u274c TAR\u0130H F\u0130LTRES\u0130 UYGULANMIYOR! dateRange eksik:",i);const m=await fetch("https://ffl21.fun:3001/api/cihaz/".concat(a,"/history").concat(c));if(!m.ok)throw new Error("HTTP ".concat(m.status,": ").concat(m.statusText));const u=await m.json();if(!u.success||!u.data)throw new Error(u.message||"Cihaz ge\xe7mi\u015fi al\u0131namad\u0131");{var e,l,n,t;const a=u.data.gecmisVeriler||[];console.log("\ud83c\udd95 DeviceHistoryTable - API'den gelen yeni veriler:",{kayitSayisi:a.length,ilkKayit:null===(e=a[0])||void 0===e?void 0:e.tarih,sonKayit:null===(l=a[a.length-1])||void 0===l?void 0:l.tarih,totalRecords:null===(n=u.data.pagination)||void 0===n?void 0:n.totalRecords}),o(a),j((null===(t=u.data.pagination)||void 0===t?void 0:t.totalRecords)||0),s&&s(a)}}catch(r){console.error("\ud83d\udea8 Cihaz ge\xe7mi\u015fi getirme hatas\u0131:",r),h(r.message),o([])}finally{d(!1)}else h("Cihaz ID bulunamad\u0131")}),[a,x,null===i||void 0===i?void 0:i.baslangic_tarihi,null===i||void 0===i?void 0:i.bitis_tarihi,s]);(0,l.useEffect)((()=>{b()}),[b]);const y=e=>{if(!e)return"Bilinmiyor";return new Date(e).toLocaleString("tr-TR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})};return a?(0,u.jsx)("div",{className:"row mb-4",children:(0,u.jsx)("div",{className:"col-12",children:(0,u.jsxs)("div",{className:"card border-0 shadow-sm",children:[(0,u.jsxs)("div",{className:"card-header bg-light d-flex justify-content-between align-items-center",children:[(0,u.jsxs)("h6",{className:"mb-0",children:[(0,u.jsx)(n.g,{icon:t.Int,className:"me-2 text-primary"}),"Cihaz Veri Ge\xe7mi\u015fi - ",a]}),(0,u.jsxs)("div",{className:"d-flex gap-2 align-items-center",children:[(0,u.jsxs)("select",{className:"form-select form-select-sm",style:{width:"auto"},value:x,onChange:e=>{g("all"===e.target.value?"all":parseInt(e.target.value))},disabled:c,children:[(0,u.jsx)("option",{value:10,children:"Son 10"}),(0,u.jsx)("option",{value:50,children:"Son 50"}),(0,u.jsx)("option",{value:100,children:"Son 100"}),(0,u.jsx)("option",{value:500,children:"Son 500"}),(0,u.jsx)("option",{value:"all",children:"T\xfcm\xfc"})]}),(0,u.jsx)("button",{className:"btn btn-sm btn-outline-primary",onClick:b,disabled:c,title:"Yenile",children:(0,u.jsx)(n.g,{icon:t.VNe,className:c?"fa-spin":""})})]})]}),(0,u.jsxs)("div",{className:"card-body p-0",children:[c&&(0,u.jsxs)("div",{className:"text-center py-4",children:[(0,u.jsx)("div",{className:"spinner-border text-primary",role:"status",children:(0,u.jsx)("span",{className:"visually-hidden",children:"Veriler y\xfckleniyor..."})}),(0,u.jsx)("div",{className:"mt-2",children:"Cihaz ge\xe7mi\u015fi y\xfckleniyor..."})]}),m&&(0,u.jsxs)("div",{className:"alert alert-warning m-3",children:[(0,u.jsx)("strong",{children:"Veri al\u0131namad\u0131:"})," ",m,(0,u.jsx)("br",{}),(0,u.jsxs)("small",{children:["API endpointi kontrol edilsin: https://ffl21.fun:3001/api/cihaz/",a,"/history"]})]}),!c&&!m&&0===r.length&&(0,u.jsxs)("div",{className:"alert alert-info m-3",children:[(0,u.jsx)(n.g,{icon:t.Int,className:"me-2"}),"Bu cihaz i\xe7in hen\xfcz veri kayd\u0131 bulunmuyor."]}),!c&&!m&&r.length>0&&(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)("div",{className:"table-responsive",children:(0,u.jsxs)("table",{className:"table table-hover mb-0",children:[(0,u.jsx)("thead",{className:"table-dark",children:(0,u.jsxs)("tr",{children:[(0,u.jsxs)("th",{scope:"col",children:[(0,u.jsx)(n.g,{icon:t.BEE,className:"me-1"}),"Tarih"]}),(0,u.jsxs)("th",{scope:"col",children:[(0,u.jsx)(n.g,{icon:t.Rog,className:"me-1"}),"S\u0131cakl\u0131k"]}),(0,u.jsxs)("th",{scope:"col",children:[(0,u.jsx)(n.g,{icon:t.yzd,className:"me-1"}),"Nem"]}),(0,u.jsxs)("th",{scope:"col",children:[(0,u.jsx)(n.g,{icon:t.W6R,className:"me-1"}),"Pil"]}),(0,u.jsxs)("th",{scope:"col",children:[(0,u.jsx)(n.g,{icon:t.Pcr,className:"me-1"}),"Konum"]})]})}),(0,u.jsx)("tbody",{children:r.map(((e,a)=>{var s,l,i,n,t,r;0===a&&console.log("\ud83d\udd04 Tabloda render edilen ilk kay\u0131t:",{tarih:e.tarih,sicaklik:null===(r=e.sensorler)||void 0===r?void 0:r.sicaklik,index:a});const o=(e=>{const a=parseFloat(e);if(isNaN(a))return 0;const s=30+(a-2.5)/(3.4-2.5)*70;return Math.round(Math.max(30,Math.min(100,s)))})((null===(s=e.sensorler)||void 0===s?void 0:s.pil)||0),c=(null===(l=e.sensorler)||void 0===l?void 0:l.sicaklik)||0,d=(null===(i=e.sensorler)||void 0===i?void 0:i.nem)||0,m=null===(n=e.konum)||void 0===n?void 0:n.enlem,h=null===(t=e.konum)||void 0===t?void 0:t.boylam;return(0,u.jsxs)("tr",{children:[(0,u.jsx)("td",{children:(0,u.jsx)("small",{children:y(e.tarih)})}),(0,u.jsx)("td",{children:(0,u.jsxs)("span",{className:c<0?"text-primary":c>30?"text-danger":"text-success",children:[c,"\xb0C"]})}),(0,u.jsx)("td",{children:(0,u.jsxs)("span",{className:d>80?"text-danger":d>60?"text-warning":"text-success",children:[d,"%"]})}),(0,u.jsxs)("td",{children:[(0,u.jsxs)("span",{className:p(o),children:[o,"%"]}),o<35&&(0,u.jsx)("small",{className:"text-danger d-block",children:"\u26a0\ufe0f D\xfc\u015f\xfck"})]}),(0,u.jsx)("td",{children:m&&h?(0,u.jsxs)("small",{className:"text-muted",children:[parseFloat(m).toFixed(4),", ",parseFloat(h).toFixed(4)]}):(0,u.jsx)("small",{className:"text-muted",children:"Konum yok"})})]},a)}))})]})}),(0,u.jsx)("div",{className:"card-footer bg-light",children:(0,u.jsxs)("div",{className:"d-flex justify-content-between align-items-center",children:[(0,u.jsx)("small",{className:"text-muted",children:i.baslangic_tarihi&&i.bitis_tarihi?"Sevkiyat d\xf6nemi kay\u0131tlar\u0131, g\xf6sterilen: ".concat(r.length):v>0?"Toplam ".concat(v," kay\u0131t, g\xf6sterilen: ").concat(r.length):"".concat(r.length," kay\u0131t g\xf6steriliyor")}),(0,u.jsxs)("small",{className:"text-muted",children:["Son g\xfcncelleme: ",(new Date).toLocaleTimeString("tr-TR")]})]})})]})]})]})})}):null};var p=s(9942),b=s(4713);const y=["marker"],N=()=>{var e,a,s,m,h,g,N,k,f;const{id:A}=(0,i.g)(),S=(0,i.Zp)(),[w,z]=(0,l.useState)(!0),[C,I]=(0,l.useState)(null),K=localStorage.getItem("user"),T=K?JSON.parse(K):null,D=T?T.user:null,[R,Z]=(0,l.useState)({lat:41.0082,lng:28.9784}),[M,O]=(0,l.useState)(7),E=(0,l.useRef)(null),[F,G]=(0,l.useState)(!1),[P,B]=(0,l.useState)(""),[Q,J]=(0,l.useState)(null),[H,U]=(0,l.useState)(!1),[L,W]=(0,l.useState)(!1),[Y,q]=(0,l.useState)(""),[V,X]=(0,l.useState)([]),[_,$]=(0,l.useState)(!1),{isLoaded:ee,loadError:ae}=(0,p.KD)({id:"google-map-script",googleMapsApiKey:"AIzaSyA2cfEmiPMyvcGfRiCyB9khWrccCgqpxKs",libraries:y}),se=(0,l.useCallback)((e=>{E.current=e,e&&e.addListener("zoom_changed",(()=>{O(e.getZoom())}))}),[]),le=(0,l.useCallback)((e=>{console.log("History Locations from DeviceHistoryTable:",e),X(e)}),[]),ie=(0,l.useMemo)((()=>{if(null===Q||void 0===Q||!Q.olusturmaZamani)return console.warn("DeviceHistoryTable - olusturmaZamani yok, tarih filtresi uygulanmayacak!"),{};const e=e=>{if(!e)return null;const a=new Date(e),s=new Date(a.getTime()+72e5).toISOString().replace("T"," ").split(".")[0];return console.log("\ud83d\udcc5 Tarih d\xf6n\xfc\u015f\xfcm\xfc F\u0130X:",{original:e,jsDate:a.toISOString(),turkiyeSaati:s,expectedDB:"Database: 17:24:40 \u2192 Formatted: 17:24:40"}),s};let a,s;return Q.isCompleted&&Q.tamamlanmaZamani?(a=e(Q.olusturmaZamani),s=e(Q.tamamlanmaZamani)):(a=e(Q.olusturmaZamani),s=e((new Date).toISOString())),console.log("\ud83c\udfaf ShipmentView - Hesaplanan tarih aral\u0131\u011f\u0131:",{baslangic_tarihi:a,bitis_tarihi:s,isCompleted:Q.isCompleted,originalStart:Q.olusturmaZamani,originalEnd:Q.tamamlanmaZamani,sevkiyatTipi:Q.isCompleted?"Ge\xe7mi\u015f Sevkiyat":"Aktif Sevkiyat",tarihlervMevcut:!!Q.olusturmaZamani}),{baslangic_tarihi:a,bitis_tarihi:s}}),[null===Q||void 0===Q?void 0:Q.olusturmaZamani,null===Q||void 0===Q?void 0:Q.tamamlanmaZamani,null===Q||void 0===Q?void 0:Q.isCompleted]);(0,l.useEffect)((()=>{(async()=>{try{z(!0),I(null);const v=await b.eg.getSevkiyatById(A);let j=[];try{if(v.mgz24_kodu){const e=await fetch("https://ffl21.fun:3001/api/cihaz/".concat(v.mgz24_kodu));if(e.ok){const a=await e.json();a.success&&a.data&&(j=[a.data])}}}catch(g){console.warn("Sens\xf6r verileri al\u0131namad\u0131 (MGZ24: ".concat(v.mgz24_kodu,"):"),g)}const p=j&&j.length>0?j[0]:null;if(console.log("API Sensor Response:",j),console.log("Latest Sensor Data:",p),null!==p&&void 0!==p&&p.sonKonum&&console.log("Son Konum Verisi:",p.sonKonum),console.log("Sevkiyat Tarihleri - Olu\u015fturma:",v.olusturma_zamani,"Tamamlanma:",v.tamamlanma_zamani),v.olusturma_zamani||console.error("UYARI: olusturma_zamani de\u011feri yok!",{olusturma_zamani:v.olusturma_zamani,sevkiyat_id:v.id||A}),v){var e,a,s,l,i,n,t,r,o,c,d,m,h,u,x;const g=v.nereden||v.cikis_lokasyon||v.gonderen_firma||"Bilinmiyor",j=v.nereye||v.varis_lokasyon||v.alici_firma||"Bilinmiyor",b=v.nakliyeci||v.surucu_adi||"Bilinmiyor",y=v.urun||v.urun_bilgisi||"Bilinmiyor",N=1===v.tamamlandi_mi||"1"===v.tamamlandi_mi;J({id:v.id||A,sevkiyatID:v.sevkiyat_ID||"Bilinmiyor",mgzKodu:v.mgz24_kodu||"Bilinmiyor",name:v.sevkiyat_adi||"Bilinmeyen Sevkiyat",plate:v.plaka_no||"Plaka yok",from:g,to:j,carrier:b,product:y,orderNo:v.mgz24_kodu||"Sipari\u015f no yok",pallet:(null===(e=v.palet_sayisi)||void 0===e?void 0:e.toString())||"0",net:(null===(a=v.net_agirlik)||void 0===a?void 0:a.toString())||"0",gross:(null===(s=v.brut_agirlik)||void 0===s?void 0:s.toString())||"0",added:ne(v.olusturma_zamani)||"Bilinmiyor",lastUpdate:ne(v.guncelleme_zamani,!0)||"Bilinmiyor",isCompleted:N,olusturmaZamani:v.olusturma_zamani,tamamlanmaZamani:v.tamamlanma_zamani,location:{lat:null!==p&&void 0!==p&&null!==(l=p.sonKonum)&&void 0!==l&&l.enlem?parseFloat(p.sonKonum.enlem):41.0082,lng:null!==p&&void 0!==p&&null!==(i=p.sonKonum)&&void 0!==i&&i.boylam?parseFloat(p.sonKonum.boylam):28.9784,address:"\u0130stanbul, T\xfcrkiye"},temperature:{value:(null===p||void 0===p||null===(n=p.sonSensorler)||void 0===n||null===(t=n.sicaklik)||void 0===t?void 0:t.toString())||"0",min:(v.sicaklik_araligi||"15-25\xb0C").split("-")[0].replace("\xb0C","").trim(),max:(null===(r=(v.sicaklik_araligi||"15-25\xb0C").split("-")[1])||void 0===r?void 0:r.replace("\xb0C","").trim())||"25"},humidity:{value:(null===p||void 0===p||null===(o=p.sonSensorler)||void 0===o||null===(c=o.nem)||void 0===c?void 0:c.toString())||"0",min:"5",max:"95"},light:{value:(null===p||void 0===p||null===(d=p.sonSensorler)||void 0===d||null===(m=d.isik)||void 0===m?void 0:m.toString())||"0",message:"acik"===(null===p||void 0===p||null===(h=p.sonSensorler)||void 0===h?void 0:h.kapi)?"Kapak a\xe7\u0131ld\u0131!":"Kapak kapal\u0131"}}),console.log("\u2705 ShipmentDetail SET ED\u0130LD\u0130:",{olusturmaZamani:v.olusturma_zamani,tamamlanmaZamani:v.tamamlanma_zamani,isCompleted:N}),null!==p&&void 0!==p&&null!==(u=p.sonKonum)&&void 0!==u&&u.enlem&&null!==p&&void 0!==p&&null!==(x=p.sonKonum)&&void 0!==x&&x.boylam&&Z({lat:parseFloat(p.sonKonum.enlem),lng:parseFloat(p.sonKonum.boylam)})}z(!1)}catch(v){console.error("Veri getirme hatas\u0131:",v);let e="Sevkiyat bilgileri y\xfcklenirken hata olu\u015ftu.",a="Sunucu Hatas\u0131";v.response?404===v.response.status?(a="Kay\u0131t Bulunamad\u0131",e=v.message||"".concat(A," numaral\u0131 sevkiyat kayd\u0131 bulunamad\u0131. Silinen veya hi\xe7 olu\u015fturulmam\u0131\u015f bir kay\u0131t olabilir.")):(e+=" Sunucu hatas\u0131: ".concat(v.response.status),v.response.data&&v.response.data.message&&(e+=" - ".concat(v.response.data.message))):"ECONNABORTED"===v.code?(a="Zaman A\u015f\u0131m\u0131",e="Sunucu yan\u0131t vermedi, istek zaman a\u015f\u0131m\u0131na u\u011frad\u0131. L\xfctfen daha sonra tekrar deneyin."):"ECONNRESET"===v.code?(a="Ba\u011flant\u0131 Hatas\u0131",e="Sunucu ba\u011flant\u0131s\u0131 s\u0131f\u0131rland\u0131. L\xfctfen internet ba\u011flant\u0131n\u0131z\u0131 kontrol edin ve tekrar deneyin."):v.request&&(a="A\u011f Hatas\u0131",e="Sunucuya ba\u011flan\u0131lamad\u0131. L\xfctfen a\u011f ba\u011flant\u0131n\u0131z\u0131 kontrol edin."),I({type:a,message:e}),z(!1);J({id:A||"51692580",sevkiyatID:"SVK000001",mgzKodu:"200000780",name:"Demo Veri - Moldova",plate:"34 FH 3581",from:"D\xfczce",to:"Moldova",carrier:"Transgood",product:"Limon",orderNo:"200000780",pallet:"46",net:"8932",gross:"11061",added:"30.04.2025",lastUpdate:"04.05.2025 23:20",isCompleted:!1,olusturmaZamani:"2025-04-30T10:00:00",tamamlanmaZamani:null,location:{lat:41.0082,lng:28.9784,address:"\u0130stanbul, T\xfcrkiye"},temperature:{value:"21.6",min:"15",max:"25"},humidity:{value:"45",min:"5",max:"95"},light:{value:"0.5",message:"Kapak kapal\u0131"}})}})()}),[A]),(0,l.useEffect)((()=>{const e=console.warn;return console.warn=function(){for(var a=arguments.length,s=new Array(a),l=0;l<a;l++)s[l]=arguments[l];s[0]&&"string"===typeof s[0]&&s[0].includes("google.maps.Marker is deprecated")||e.apply(console,s)},()=>{console.warn=e}}),[]);const ne=function(e){let a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e)return"";const s=new Date(e),l=s.getDate().toString().padStart(2,"0"),i=(s.getMonth()+1).toString().padStart(2,"0"),n=s.getFullYear();if(a){const e=s.getHours().toString().padStart(2,"0"),a=s.getMinutes().toString().padStart(2,"0");return"".concat(l,".").concat(i,".").concat(n," ").concat(e,":").concat(a)}return"".concat(l,".").concat(i,".").concat(n)},te=()=>{if(!Q)return"#";const{lat:e,lng:a}=Q.location;return"https://www.google.com/maps/dir/?api=1&destination=".concat(e,",").concat(a)};if(w||!Q)return(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(o.A,{}),(0,u.jsx)("div",{className:"container-fluid",children:(0,u.jsxs)("div",{className:"row",children:[(0,u.jsx)(c.A,{}),(0,u.jsx)("main",{className:"col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white",children:(0,u.jsxs)("div",{className:"d-flex justify-content-center align-items-center",style:{height:"80vh"},children:[(0,u.jsx)("div",{className:"spinner-border text-primary",role:"status",children:(0,u.jsx)("span",{className:"visually-hidden",children:"Y\xfckleniyor..."})}),(0,u.jsx)("span",{className:"ms-2",children:"Sevkiyat bilgileri y\xfckleniyor..."})]})})]})})]});if(C)return(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(o.A,{}),(0,u.jsx)("div",{className:"container-fluid",children:(0,u.jsxs)("div",{className:"row",children:[(0,u.jsx)(c.A,{}),(0,u.jsxs)("main",{className:"col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white",children:[(0,u.jsx)("div",{className:"pt-3 pb-1 mt-3 mb-3 border-bottom",children:(0,u.jsxs)("h1",{className:"h4 text-dark mb-0",children:[(0,u.jsx)(n.g,{icon:t.raf,className:"me-2 text-primary"}),"#",A," Sevkiyat Takip"]})}),(0,u.jsx)("div",{className:"row mb-4",children:(0,u.jsx)("div",{className:"col-12",children:(0,u.jsxs)("div",{className:"alert alert-danger",children:[(0,u.jsxs)("h5",{className:"alert-heading",children:[(0,u.jsx)(n.g,{icon:t.z$e,className:"me-2"}),"API Ba\u011flant\u0131 Hatas\u0131"]}),(0,u.jsx)("p",{children:"Sunucuya ba\u011flan\u0131lamad\u0131. L\xfctfen internet ba\u011flant\u0131n\u0131z\u0131 kontrol edin."}),(0,u.jsx)("hr",{}),(0,u.jsxs)("div",{className:"d-flex justify-content-between align-items-center",children:[(0,u.jsx)("p",{className:"mb-0",children:"Ger\xe7ek veriler i\xe7in sunucu ba\u011flant\u0131s\u0131 gereklidir."}),(0,u.jsx)("div",{className:"btn-group",children:(0,u.jsxs)("button",{className:"btn btn-primary",onClick:()=>window.location.reload(),children:[(0,u.jsx)(n.g,{icon:t.z$e,className:"me-2"}),"Yeniden Dene"]})})]})]})})}),Q&&(0,u.jsx)(u.Fragment,{children:(0,u.jsx)("div",{className:"row mb-4",children:(0,u.jsx)("div",{className:"col-12",children:(0,u.jsxs)("div",{className:"card bg-light border",children:[(0,u.jsx)("div",{className:"card-header bg-dark-subtle py-2",children:(0,u.jsxs)("div",{className:"d-flex justify-content-between align-items-center",children:[(0,u.jsx)("h5",{className:"mb-0 fw-bold",children:Q.name}),(0,u.jsx)("small",{children:"Demo Veri"})]})}),(0,u.jsx)("div",{className:"card-body",children:(0,u.jsx)("div",{className:"table-responsive mb-0",children:(0,u.jsxs)("table",{className:"table table-light table-bordered table-striped mb-0",children:[(0,u.jsx)("thead",{children:(0,u.jsxs)("tr",{children:[(0,u.jsx)("th",{children:"Sistem ID"}),(0,u.jsx)("th",{children:"Sevkiyat ID"}),(0,u.jsx)("th",{children:"MGZ24 Kodu"}),(0,u.jsx)("th",{children:"Sevkiyat Ad\u0131"}),(0,u.jsx)("th",{children:"Plaka No"}),(0,u.jsx)("th",{children:"Nereden"}),(0,u.jsx)("th",{children:"Nereye"}),(0,u.jsx)("th",{children:"Nakliyeci"}),(0,u.jsx)("th",{children:"\xdcr\xfcn"})]})}),(0,u.jsx)("tbody",{children:(0,u.jsxs)("tr",{children:[(0,u.jsx)("td",{children:Q.id}),(0,u.jsx)("td",{children:Q.sevkiyatID}),(0,u.jsx)("td",{children:(0,u.jsx)("span",{className:"badge bg-primary text-white",children:Q.mgzKodu})}),(0,u.jsx)("td",{children:Q.name}),(0,u.jsx)("td",{children:Q.plate}),(0,u.jsx)("td",{children:Q.from}),(0,u.jsx)("td",{children:Q.to}),(0,u.jsx)("td",{children:Q.carrier}),(0,u.jsx)("td",{children:Q.product})]})})]})})})]})})})})]})]})})]});return(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(o.A,{}),(0,u.jsx)("div",{className:"container-fluid",children:(0,u.jsxs)("div",{className:"row",children:[(0,u.jsx)(c.A,{}),(0,u.jsxs)("main",{className:"col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white",children:[(0,u.jsx)("div",{className:"pt-3 pb-1 mt-3 mb-3 border-bottom",children:(0,u.jsx)("div",{className:"d-flex justify-content-between align-items-center",children:(0,u.jsxs)("h1",{className:"h4 text-dark mb-0",children:[(0,u.jsx)(n.g,{icon:t.raf,className:"me-2 text-primary"}),"Sevkiyat: ",(0,u.jsx)("span",{className:"fw-bold",children:Q.sevkiyatID}),Q.mgzKodu&&"Bilinmiyor"!==Q.mgzKodu&&(0,u.jsxs)("span",{className:"ms-2 badge bg-primary",children:["MGZ24: ",Q.mgzKodu]})]})})}),(0,u.jsx)(v,{cihazID:null===Q||void 0===Q?void 0:Q.mgzKodu,dateRange:{baslangic_tarihi:null===Q||void 0===Q?void 0:Q.olusturmaZamani,bitis_tarihi:null===Q||void 0===Q?void 0:Q.tamamlanmaZamani}}),(0,u.jsx)("div",{className:"row mb-4",children:(0,u.jsx)("div",{className:"col-12",children:(0,u.jsxs)("div",{className:"card border-tertiary-subtle px-3 py-3",children:[(0,u.jsx)("div",{className:"card-header bg-light d-flex justify-content-between align-items-center",children:(0,u.jsxs)("h5",{className:"mb-0 text-dark",children:[(0,u.jsx)(n.g,{icon:t.raf,className:"me-2 text-primary"}),"Antalya Sevkiyat\u0131"]})}),(0,u.jsx)("div",{className:"card-body p-0",children:(0,u.jsx)("div",{className:"table-responsive",children:(0,u.jsxs)("table",{className:"table table-striped table-hover mb-0",children:[(0,u.jsx)("thead",{className:"table-dark",children:(0,u.jsxs)("tr",{children:[(0,u.jsx)("th",{children:"Sistem ID"}),(0,u.jsx)("th",{children:"Sevkiyat ID"}),(0,u.jsx)("th",{children:"MGZ24 Kodu"}),(0,u.jsx)("th",{children:"Sevkiyat Ad\u0131"}),(0,u.jsx)("th",{children:"Plaka No"}),(0,u.jsx)("th",{children:"Nereden"}),(0,u.jsx)("th",{children:"Nereye"}),(0,u.jsx)("th",{children:"Nakliyeci"}),(0,u.jsx)("th",{children:"\xdcr\xfcn"}),(0,u.jsx)("th",{children:"Palet"}),(0,u.jsx)("th",{children:"Net"}),(0,u.jsx)("th",{children:"Br\xfct"}),(0,u.jsx)("th",{children:"Eklenme"}),(0,u.jsx)("th",{children:"Sevkiyat Tamamland\u0131"})]})}),(0,u.jsx)("tbody",{children:(0,u.jsxs)("tr",{children:[(0,u.jsx)("td",{children:Q.id}),(0,u.jsx)("td",{children:Q.sevkiyatID}),(0,u.jsx)("td",{children:(0,u.jsx)("span",{className:"badge bg-primary",children:Q.mgzKodu})}),(0,u.jsx)("td",{children:Q.name}),(0,u.jsx)("td",{children:Q.plate}),(0,u.jsx)("td",{children:Q.from}),(0,u.jsx)("td",{children:Q.to}),(0,u.jsx)("td",{children:Q.carrier||"Bilinmiyor"}),(0,u.jsx)("td",{children:Q.product}),(0,u.jsx)("td",{children:Q.pallet}),(0,u.jsx)("td",{children:Q.net}),(0,u.jsx)("td",{children:Q.gross}),(0,u.jsx)("td",{children:ne(Q.olusturmaZamani,!0)}),(0,u.jsx)("td",{children:(0,u.jsxs)("div",{className:"form-check",children:[(0,u.jsx)("input",{className:"form-check-input",type:"checkbox",checked:Q.isCompleted,onChange:()=>{W(!0)},disabled:H||Q.isCompleted}),(0,u.jsx)("label",{className:"form-check-label",children:"Sevkiyat Tamamland\u0131"})]})})]})})]})})})]})})}),(0,u.jsx)("div",{className:"row my-4",children:(0,u.jsx)("div",{className:"col-12",children:(0,u.jsx)("div",{className:"card border-tertiary-subtle px-3 py-3",children:(0,u.jsxs)("div",{className:"card-body p-0",style:{height:"60vh",boxShadow:"inset 0 0 20px rgba(0, 0, 0, 0.1)"},children:[ae&&(0,u.jsx)("div",{className:"alert alert-danger",children:"Google Maps y\xfcklenemedi. L\xfctfen daha sonra tekrar deneyin."}),ee?(0,u.jsxs)("div",{style:{position:"relative",width:"100%",height:"100%"},children:[V.length>0&&(0,u.jsx)("div",{style:{position:"absolute",top:16,left:16,zIndex:2},children:(0,u.jsxs)("button",{onClick:()=>$(!_),className:"btn btn-sm btn-primary shadow-sm",style:{fontSize:"12px"},children:[(0,u.jsx)(n.g,{icon:t.gKm,className:"me-1"}),_?"Konumlar\u0131 Gizle":"T\xfcm Konumlar\u0131 G\xf6ster (".concat(V.length,")")]})}),(0,u.jsxs)(p.u6,{mapContainerStyle:{width:"100%",height:"100%",borderRadius:"4px"},center:R,zoom:M,onLoad:se,options:{mapTypeId:"roadmap",disableDefaultUI:!0,zoomControl:!0,mapTypeControl:!1,scaleControl:!1,streetViewControl:!1,rotateControl:!1,clickableIcons:!0,fullscreenControl:!0,gestureHandling:"greedy",minZoom:2,maxZoom:20,zoomControlOptions:{position:null===(e=window.google)||void 0===e||null===(a=e.maps)||void 0===a||null===(s=a.ControlPosition)||void 0===s?void 0:s.TOP_RIGHT},fullscreenControlOptions:{position:null===(m=window.google)||void 0===m||null===(h=m.maps)||void 0===h||null===(g=h.ControlPosition)||void 0===g?void 0:g.TOP_RIGHT}},children:[(0,u.jsx)(p.pH,{position:Q.location,onClick:()=>{B("location"),G(!F)},animation:null===(N=window.google)||void 0===N||null===(k=N.maps)||void 0===k||null===(f=k.Animation)||void 0===f?void 0:f.DROP,icon:{url:"data:image/svg+xml;charset=UTF-8,"+encodeURIComponent('\n                                                                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">\n                                                                    <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z" fill="#007f97" stroke="#ffffff" stroke-width="1.5"/>\n                                                                </svg>\n                                                            '),scaledSize:window.google&&window.google.maps?new window.google.maps.Size(32,32):void 0,anchor:window.google&&window.google.maps?new window.google.maps.Point(16,32):void 0},children:F&&(0,u.jsx)(p.Fu,{position:Q.location,onCloseClick:()=>G(!1),children:(0,u.jsx)("div",{className:"shadow-sm",style:{borderRadius:"8px",overflow:"hidden"},children:(e=>{switch(e){case"temperature":return(0,u.jsxs)("div",{className:"card-body p-2",children:[(0,u.jsxs)("h6",{className:"card-title mb-1 fw-bold",children:[(0,u.jsx)(n.g,{icon:t.Xe_})," S\u0131cakl\u0131k"]}),(0,u.jsxs)("p",{className:"mb-1 fs-4 fw-light",children:[Q.temperature.value," \xb0C"]}),(0,u.jsxs)("small",{children:["Min/Max: ",Q.temperature.min," - ",Q.temperature.max," \xb0C"]})]});case"humidity":return(0,u.jsxs)("div",{className:"card-body p-2",children:[(0,u.jsxs)("h6",{className:"card-title mb-1 fw-bold",children:[(0,u.jsx)(n.g,{icon:t.yzd})," Nem"]}),(0,u.jsxs)("p",{className:"mb-1 fs-4 fw-light",children:[Q.humidity.value," %"]}),(0,u.jsxs)("small",{children:["Min/Max: ",Q.humidity.min," - ",Q.humidity.max," %"]})]});case"light":return(0,u.jsxs)("div",{className:"card-body p-2",children:[(0,u.jsxs)("h6",{className:"card-title mb-1 fw-bold",children:[(0,u.jsx)(n.g,{icon:r.rC2})," I\u015f\u0131k"]}),(0,u.jsx)("p",{className:"mb-1 fs-4 fw-light",children:Q.light.value}),(0,u.jsx)("small",{children:Q.light.message})]});case"location":return(0,u.jsxs)("div",{className:"card-body p-2",children:[(0,u.jsxs)("h6",{className:"card-title mb-1 fw-bold",children:[(0,u.jsx)(n.g,{icon:t.gKm})," Konum"]}),(0,u.jsx)("p",{className:"mb-1 fs-5 fw-light",children:Q.location.address}),(0,u.jsxs)("small",{children:["Son G\xfcncelleme: ",Q.lastUpdate]}),(0,u.jsx)("div",{className:"mt-2",children:(0,u.jsxs)("a",{href:te(),target:"_blank",rel:"noopener noreferrer",className:"btn btn-sm btn-primary",children:[(0,u.jsx)(n.g,{icon:t.gKm,className:"me-1"})," Yol Tarifi Al"]})})]});default:return null}})(P)})})}),_&&V.map(((e,a)=>{var s,l,i,n;const t=null===(s=e.konum)||void 0===s?void 0:s.enlem,r=null===(l=e.konum)||void 0===l?void 0:l.boylam;return console.log("Location ".concat(a,":"),e,"Enlem:",t,"Boylam:",r),t&&r?(0,u.jsx)(p.pH,{position:{lat:parseFloat(t),lng:parseFloat(r)},icon:{url:"data:image/svg+xml;charset=UTF-8,"+encodeURIComponent('\n                                                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">\n                                                                            <circle cx="12" cy="12" r="8" fill="#ff6b35" stroke="#ffffff" stroke-width="2"/>\n                                                                            <circle cx="12" cy="12" r="3" fill="#ffffff"/>\n                                                                        </svg>\n                                                                    '),scaledSize:window.google&&window.google.maps?new window.google.maps.Size(20,20):void 0,anchor:window.google&&window.google.maps?new window.google.maps.Point(10,10):void 0},title:"".concat(e.tarih," - ").concat((null===(i=e.sensorler)||void 0===i?void 0:i.sicaklik)||0,"\xb0C, %").concat((null===(n=e.sensorler)||void 0===n?void 0:n.nem)||0)},a):null}))]})]}):(0,u.jsxs)("div",{className:"d-flex justify-content-center align-items-center h-100",children:[(0,u.jsx)("div",{className:"spinner-border text-primary",role:"status",children:(0,u.jsx)("span",{className:"visually-hidden",children:"Harita y\xfckleniyor..."})}),(0,u.jsx)("span",{className:"ms-2",children:"Harita y\xfckleniyor..."})]})]})})})}),(0,u.jsx)(j,{cihazID:null===Q||void 0===Q?void 0:Q.mgzKodu,onHistoryDataChange:le,dateRange:ie}),(null===Q||void 0===Q?void 0:Q.mgzKodu)&&(0,u.jsx)("div",{className:"row mb-4",children:(0,u.jsx)("div",{className:"col-12",children:(0,u.jsx)(x,{cihazID:Q.mgzKodu,tableData:V,onReportGenerated:(e,a)=>{console.log("".concat(e," raporu olu\u015fturuldu: ").concat(a))}})})})]}),(0,u.jsx)(d.A,{})]})}),(0,u.jsx)("style",{children:"\n                    @keyframes pulse {\n                        0% {\n                            transform: scale(0.8);\n                            opacity: 0.8;\n                        }\n                        70% {\n                            transform: scale(1.5);\n                            opacity: 0;\n                        }\n                        100% {\n                            transform: scale(1.8);\n                            opacity: 0;\n                        }\n                    }\n                "}),L&&(0,u.jsx)("div",{className:"modal fade show",style:{display:"block",backgroundColor:"rgba(0,0,0,0.5)"},tabIndex:"-1",children:(0,u.jsx)("div",{className:"modal-dialog modal-dialog-centered",children:(0,u.jsxs)("div",{className:"modal-content",children:[(0,u.jsxs)("div",{className:"modal-header",children:[(0,u.jsxs)("h5",{className:"modal-title",children:[(0,u.jsx)(n.g,{icon:t.e68,className:"me-2 text-success"}),"Sevkiyat Tamamlama Onay\u0131"]}),(0,u.jsx)("button",{type:"button",className:"btn-close",onClick:()=>W(!1),disabled:H})]}),(0,u.jsxs)("div",{className:"modal-body",children:[(0,u.jsxs)("div",{className:"alert alert-warning",children:[(0,u.jsx)(n.g,{icon:t.z$e,className:"me-2"}),(0,u.jsx)("strong",{children:"Dikkat!"})," Bu i\u015flemle birlikte sevkiyat tamamlanm\u0131\u015f say\u0131lacak ve ge\xe7mi\u015f sevkiyatlara ta\u015f\u0131nacakt\u0131r. Devam etmek istiyor musunuz?"]}),(0,u.jsxs)("div",{className:"mb-3",children:[(0,u.jsx)("label",{htmlFor:"completeNotes",className:"form-label",children:"Tamamlama Notlar\u0131 (\u0130ste\u011fe ba\u011fl\u0131):"}),(0,u.jsx)("textarea",{id:"completeNotes",className:"form-control",rows:"3",value:Y,onChange:e=>q(e.target.value),placeholder:"Sevkiyat tamamlama ile ilgili notlar\u0131n\u0131z\u0131 buraya yazabilirsiniz...",disabled:H})]})]}),(0,u.jsxs)("div",{className:"modal-footer",children:[(0,u.jsx)("button",{type:"button",className:"btn btn-secondary",onClick:()=>W(!1),disabled:H,children:"\u0130ptal"}),(0,u.jsx)("button",{type:"button",className:"btn btn-success",onClick:async()=>{try{var e,a;U(!0);const l=(null===D||void 0===D||null===(e=D.user)||void 0===e?void 0:e.musteri_ID)||(null===D||void 0===D||null===(a=D.user)||void 0===a?void 0:a.id);if(!l)throw new Error("Kullan\u0131c\u0131 bilgisi bulunamad\u0131");const i=await b.eg.completeSevkiyat(Q.id,l,Y);if(!i.success)throw new Error(i.message||"Sevkiyat tamamlan\u0131rken hata olu\u015ftu");try{const e=Q.mgzKodu||"D-".concat(Q.id);await b.eT.updateCihazStatus(e,{aktif:!1,son_kullanim_tarihi:(new Date).toISOString(),notlar:"".concat(Q.sevkiyatID," sevkiyat\u0131 tamamland\u0131")}),console.log("Cihaz ba\u015far\u0131yla inaktif duruma getirildi")}catch(s){console.warn("Cihaz durumu g\xfcncellenirken hata:",s)}alert("Sevkiyat ba\u015far\u0131yla tamamland\u0131! Cihaz inaktif cihazlar kategorisine ta\u015f\u0131nd\u0131."),S("/")}catch(C){console.error("Sevkiyat tamamlama hatas\u0131:",C),alert("Sevkiyat tamamlan\u0131rken hata olu\u015ftu: "+C.message)}finally{U(!1),W(!1),q("")}},disabled:H,children:H?(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)("div",{className:"spinner-border spinner-border-sm me-2",role:"status",children:(0,u.jsx)("span",{className:"visually-hidden",children:"Loading..."})}),"Tamamlan\u0131yor..."]}):(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(n.g,{icon:t.e68,className:"me-1"}),"Sevkiyat\u0131 Tamamla"]})})]})]})})})]})}}}]);
//# sourceMappingURL=866.899743c6.chunk.js.map