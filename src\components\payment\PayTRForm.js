import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCreditCard,
  faLock,
  faShieldAlt,
  faSpinner,
  faCheckCircle,
  faExclamationTriangle
} from '@fortawesome/free-solid-svg-icons';
import LoadingSpinner from '../LoadingSpinner';
import ErrorMessage from '../ErrorMessage';

/**
 * PayTR Ödeme Formu Bileşeni
 * PayTR API entegrasyonu için ödeme formu
 */
const PayTRForm = ({
  amount,
  currency = 'TL',
  onSuccess,
  onError,
  packageInfo,
  deviceId,
  disabled = false
}) => {
  // Form state
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState('');
  const [paymentToken, setPaymentToken] = useState(null);

  // PayTR iframe URL'si ve form data
  const [iframeUrl, setIframeUrl] = useState('');
  const [paymentFormData, setPaymentFormData] = useState(null);

  // Kullanıcı bilgilerini al
  const getUserInfo = () => {
    try {
      const storedUser = JSON.parse(localStorage.getItem('user'));
      return {
        userId: storedUser?.user?.musteri_ID || storedUser?.user?.id,
        email: storedUser?.user?.email || '',
        name: storedUser?.user?.musteri_adi || 'Müşteri',
        phone: storedUser?.user?.tel || ''
      };
    } catch {
      return {
        userId: null,
        email: '',
        name: 'Müşteri',
        phone: ''
      };
    }
  };

  // PayTR ödeme token'ı oluştur
  const createPaymentToken = async () => {
    const userInfo = getUserInfo();

    console.log('User info:', userInfo);

    if (!userInfo.userId) {
      throw new Error('Kullanıcı bilgisi bulunamadı');
    }

    const paymentData = {
      user_id: userInfo.userId,
      device_id: deviceId,
      package_info: packageInfo,
      amount: amount,
      currency: currency,
      user_name: userInfo.name,
      user_email: userInfo.email,
      user_phone: userInfo.phone,
      user_basket: JSON.stringify([{
        name: packageInfo?.name || 'MGZ24 Kullanım Paketi',
        price: Math.round(amount * 100).toString(),
        quantity: '1'
      }])
    };

    console.log('PayTR payment data:', paymentData);

    const response = await fetch(`${process.env.REACT_APP_API_URL || '//mgz24.com/api'}/payments/paytr/create-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      },
      body: JSON.stringify(paymentData)
    });

    console.log('PayTR API response status:', response.status);

    if (!response.ok) {
      let errorMessage = 'Ödeme token oluşturulamadı';

      try {
        const errorData = await response.json();
        console.error('PayTR API error:', errorData);

        // payment_type hatası için özel mesaj
        if (errorData.message && errorData.message.includes('payment_type')) {
          errorMessage = 'Ödeme türü hatası - Lütfen sayfayı yenileyip tekrar deneyin';
        } else {
          errorMessage = errorData.message || errorMessage;
        }
      } catch (parseError) {
        console.error('PayTR API error response parse hatası:', parseError);
        // HTML yanıtı alındıysa genel hata mesajı kullan
        if (response.status === 404) {
          errorMessage = 'PayTR servisi bulunamadı';
        } else if (response.status === 500) {
          errorMessage = 'Sunucu hatası oluştu';
        } else {
          errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        }
      }

      throw new Error(errorMessage);
    }

    let result;
    try {
      result = await response.json();
      console.log('PayTR API success result:', result);
    } catch (parseError) {
      console.error('PayTR API success response parse hatası:', parseError);
      throw new Error('PayTR yanıtı işlenirken hata oluştu');
    }

    return result;
  };

  // PayTR iframe yükle
  const loadPayTRIframe = async () => {
    try {
      setProcessing(true);
      setError('');

      const tokenResponse = await createPaymentToken();

      if (tokenResponse.success && tokenResponse.iframe_token) {
        setPaymentToken(tokenResponse.merchant_oid);

        // Iframe token'ı ile iframe URL'si oluştur
        console.log('PayTR iframe token alındı:', tokenResponse.iframe_token);
        const iframeUrl = `https://www.paytr.com/odeme/guvenli/${tokenResponse.iframe_token}`;
        setIframeUrl(iframeUrl);

        // Form data'yı temizle, iframe kullanılacak
        setPaymentFormData(null);
      } else {
        throw new Error(tokenResponse.message || 'PayTR iframe token alınamadı');
      }
    } catch (err) {
      console.error('PayTR iframe yükleme hatası:', err);
      setError(err.message);
      onError && onError(err);
    } finally {
      setProcessing(false);
    }
  };

  // PayTR callback mesajlarını dinle
  useEffect(() => {
    const handlePayTRMessage = (event) => {
      // PayTR'den gelen mesajları kontrol et
      if (event.origin !== 'https://www.paytr.com') {
        return;
      }

      const data = event.data;

      if (data.paytr_status === 'success') {
        // Ödeme başarılı
        onSuccess && onSuccess({
          paymentIntent: {
            id: data.merchant_oid,
            status: 'succeeded',
            amount: amount * 100,
            currency: currency.toLowerCase(),
            created: Math.floor(Date.now() / 1000),
            payment_method: 'paytr'
          }
        });
      } else if (data.paytr_status === 'failed') {
        // Ödeme başarısız
        let errorMsg = data.failed_reason_msg || 'Ödeme işlemi başarısız';

        // payment_type hatası için özel mesaj
        if (errorMsg.includes('payment_type')) {
          errorMsg = 'Ödeme türü hatası - Lütfen sayfayı yenileyip tekrar deneyin';
        }

        setError(errorMsg);
        onError && onError(new Error(errorMsg));
      }
    };

    window.addEventListener('message', handlePayTRMessage);

    return () => {
      window.removeEventListener('message', handlePayTRMessage);
    };
  }, [amount, currency, onSuccess, onError]);

  // Component yüklendiğinde PayTR iframe'i hazırla
  useEffect(() => {
    console.log('PayTRForm useEffect triggered:', { disabled, deviceId, packageInfo, amount });
    if (!disabled && deviceId && packageInfo && amount > 0) {
      console.log('Starting PayTR iframe load...');
      loadPayTRIframe();
    } else {
      console.log('PayTR iframe load skipped - conditions not met');
    }
  }, [disabled, deviceId, packageInfo, amount]);

  // PayTR iframe resize script'ini yükle
  useEffect(() => {
    if (iframeUrl) {
      const script = document.createElement('script');
      script.src = 'https://www.paytr.com/js/iframeResizer.min.js';
      script.onload = () => {
        // Script yüklendikten sonra iframe'i resize et
        if (window.iFrameResize) {
          setTimeout(() => {
            window.iFrameResize({}, '#paytriframe');
          }, 1000);
        }
      };
      document.head.appendChild(script);

      return () => {
        // Cleanup: script'i kaldır
        document.head.removeChild(script);
      };
    }
  }, [iframeUrl]);

  if (processing) {
    return (
      <div className="text-center py-5">
        <LoadingSpinner
          size="lg"
          variant="primary"
          message="PayTR ödeme sistemi hazırlanıyor..."
        />
      </div>
    );
  }

  if (error) {
    return (
      <div>
        <ErrorMessage
          message={error}
          variant="danger"
          dismissible={true}
          onDismiss={() => setError('')}
        />
        <div className="text-center mt-3">
          <button
            className="btn btn-outline-primary"
            onClick={loadPayTRIframe}
            disabled={processing}
          >
            <FontAwesomeIcon icon={faSpinner} spin={processing} className="me-2" />
            Tekrar Dene
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="paytr-form">
      {/* PayTR Bilgi Kartı */}
      <div className="card border-0 shadow-sm mb-3">
        <div className="card-header bg-primary text-white">
          <h6 className="mb-0">
            <FontAwesomeIcon icon={faShieldAlt} className="me-2" />
            PayTR Güvenli Ödeme
          </h6>
        </div>
        <div className="card-body">
          <div className="row">
            <div className="col-md-6">
              <small className="text-muted">Paket:</small>
              <div className="fw-bold">{packageInfo?.name}</div>
            </div>
            <div className="col-md-6">
              <small className="text-muted">Tutar:</small>
              <div className="fw-bold text-primary">{amount.toFixed(2)} {currency}</div>
            </div>
          </div>

          <div className="alert alert-info mt-3 mb-0">
            <FontAwesomeIcon icon={faCheckCircle} className="me-2" />
            <small>
              PayTR güvenli ödeme sistemi ile tüm kredi kartları kabul edilir.
              3D Secure ile korunmuş işlem yapacaksınız.
            </small>
          </div>
        </div>
      </div>


      {/* PayTR Iframe */}
      {iframeUrl && (
        <div className="paytr-iframe-container">
          <iframe
            id="paytriframe"
            src={iframeUrl}
            width="100%"
            height="600"
            frameBorder="0"
            scrolling="no"
            style={{ border: '1px solid #ddd', borderRadius: '8px', width: '100%' }}
            title="PayTR Güvenli Ödeme"
          />
          
          <div className="alert alert-success mt-3">
            <FontAwesomeIcon icon={faCheckCircle} className="me-2" />
            <small>
              Ödeme tamamlandıktan sonra otomatik olarak yönlendirileceksiniz.
            </small>
          </div>
        </div>
      )}

      {/* Güvenlik Bilgisi */}
      <div className="alert alert-light border mt-3">
        <small className="text-muted">
          <FontAwesomeIcon icon={faLock} className="me-2" />
          Bu işlem SSL sertifikası ile şifrelenmiş ve PayTR güvenli ödeme altyapısı tarafından korunmaktadır.
        </small>
      </div>
    </div>
  );
};

export default PayTRForm;