"use strict";(self.webpackChunkmgz24=self.webpackChunkmgz24||[]).push([[122],{421:(e,a,s)=>{s.d(a,{A:()=>t});s(5043);var l=s(9002),n=s(3910),r=s(7929),i=s(579);const t=()=>{const e=(0,l.zy)(),a="admin"===(()=>{try{var e,a;const s=JSON.parse(localStorage.getItem("user"));return(null===s||void 0===s||null===(e=s.user)||void 0===e?void 0:e.role)||(null===s||void 0===s||null===(a=s.user)||void 0===a?void 0:a.gorev)||"user"}catch(s){return"user"}})();return(0,i.jsx)("div",{className:"sidebar col-md-4 col-lg-2 p-0 bg-light-subtle",children:(0,i.jsxs)("div",{className:"offcanvas-md offcanvas-end",tabIndex:"-1",id:"yanMenu","aria-labelledby":"yanMenu",children:[(0,i.jsxs)("div",{className:"offcanvas-header",children:[(0,i.jsx)("h5",{className:"offcanvas-title",id:"yanMenu",children:"MGZ24 Gold"}),(0,i.jsx)("button",{type:"button",className:"btn-close","data-bs-dismiss":"offcanvas","data-bs-target":"#yanMenu","aria-label":"Kapat"})]}),(0,i.jsxs)("div",{className:"offcanvas-body d-md-flex flex-column p-0 pt-lg-3 overflow-y-auto",children:[(0,i.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase",children:"Sevkiyat \u0130\u015flemleri"}),(0,i.jsxs)("ul",{className:"nav nav-pills flex-column",children:[(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/"===e.pathname?"active":""),to:"/",children:[(0,i.jsx)(n.g,{icon:r.msb}),"Aktif Sevkiyatlar"]})}),(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/add"===e.pathname?"active":""),to:"/add",children:[(0,i.jsx)(n.g,{icon:r.E5r}),"Yeni Sevkiyat Olu\u015ftur"]})}),(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/inactive-devices"===e.pathname?"active":""),to:"/inactive-devices",children:[(0,i.jsx)(n.g,{icon:r.fH7}),"\u0130naktif Cihazlar"]})}),(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/history"===e.pathname?"active":""),to:"/history",children:[(0,i.jsx)(n.g,{icon:r.o9H}),"Ge\xe7mi\u015f Sevkiyatlar"]})})]}),(0,i.jsx)("hr",{className:"my-3"}),(0,i.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase",children:"Sistem Y\xf6netimi"}),(0,i.jsxs)("ul",{className:"nav nav-pills flex-column",children:[a&&(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/devices"===e.pathname?"active":""),to:"/devices",children:[(0,i.jsx)(n.g,{icon:r.ArK}),"Cihaz Y\xf6netimi"]})}),(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/notifications"===e.pathname?"active":""),to:"/notifications",children:[(0,i.jsx)(n.g,{icon:r.z$e}),"Bildirimler"]})})]}),(0,i.jsx)("hr",{className:"my-3"}),(0,i.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase",children:"Kullan\u0131c\u0131 Ayarlar\u0131"}),(0,i.jsxs)("ul",{className:"nav nav-pills flex-column",children:[(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/profile"===e.pathname?"active":""),to:"/profile",children:[(0,i.jsx)(n.g,{icon:r.X46}),"Kullan\u0131c\u0131 Profilim"]})}),(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/viewers"===e.pathname?"active":""),to:"/viewers",children:[(0,i.jsx)(n.g,{icon:r.yLE}),"\u0130zleyici \u0130\u015flemleri"]})})]}),(0,i.jsx)("hr",{className:"my-3"}),(0,i.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase",children:"\xd6deme ve Yap\u0131land\u0131rma"}),(0,i.jsxs)("ul",{className:"nav nav-pills flex-column",children:[(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/payment"===e.pathname?"active":""),to:"/payment",children:[(0,i.jsx)(n.g,{icon:r.$O8}),"\xd6deme Yap"]})}),(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/invoices"===e.pathname?"active":""),to:"/invoices",children:[(0,i.jsx)(n.g,{icon:r.bLf}),"Faturalar\u0131m"]})})]}),(0,i.jsx)("hr",{className:"my-3"}),(0,i.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-body-secondary text-uppercase",children:"Kredi Durumu"})]})]})})}},834:(e,a,s)=>{s.d(a,{A:()=>n});s(5043);var l=s(579);const n=()=>(0,l.jsx)("footer",{className:"py-5 border-top",children:(0,l.jsx)("p",{className:"text-center",children:"\xa92025 Inkatech \xd6l\xe7\xfcm Sistemleri"})})},1899:(e,a,s)=>{s.d(a,{A:()=>o});var l=s(5043),n=s(9002),r=s(3910),i=s(7929);var t=s(4713),c=s(579);const o=()=>{const e=(0,n.Zp)(),[a,s]=(0,l.useState)("Misafir"),[o,m]=(0,l.useState)(!0);(0,l.useEffect)((()=>{(async()=>{try{var e,a,l,n;const i=JSON.parse(localStorage.getItem("user")),c=(null===i||void 0===i||null===(e=i.user)||void 0===e?void 0:e.musteri_ID)||(null===i||void 0===i||null===(a=i.user)||void 0===a?void 0:a.id),o=(null===i||void 0===i||null===(l=i.user)||void 0===l?void 0:l.name)||(null===i||void 0===i||null===(n=i.user)||void 0===n?void 0:n.musteri_adi);if(!c)return console.warn("Oturum bilgisi bulunamad\u0131"),s("Misafir"),void m(!1);if(o)return s(o),void m(!1);try{const e=await t.Qj.getKullanici(c);e&&e.musteri_adi&&(s(e.musteri_adi),null!==i&&void 0!==i&&i.user&&(i.user.name=e.musteri_adi,localStorage.setItem("user",JSON.stringify(i))))}catch(r){console.warn("API'den kullan\u0131c\u0131 bilgisi al\u0131namad\u0131, localStorage kullan\u0131l\u0131yor"),s(o||"Kullan\u0131c\u0131")}}catch(i){console.error("Kullan\u0131c\u0131 bilgileri al\u0131n\u0131rken hata:",i),s("Kullan\u0131c\u0131")}finally{m(!1)}})()}),[]);return(0,c.jsxs)("header",{className:"navbar sticky-top bg-darkblue flex-md-nowrap p-0 shadow","data-bs-theme":"dark",children:[(0,c.jsx)(n.N_,{className:"navbar-brand col-md-4 col-lg-2 me-0 px-3 py-2 fs-6 text-white",to:"/",children:(0,c.jsx)("img",{src:"data:image/png;base64,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",alt:"MGZ24 Logo",height:"40"})}),(0,c.jsxs)("ul",{className:"navbar-nav flex-row align-items-center",children:[(0,c.jsx)("li",{className:"nav-item text-nowrap d-none d-md-block me-3",children:(0,c.jsxs)("span",{className:"text-white",children:[(0,c.jsx)(r.g,{icon:i.X46,className:"me-2"}),o?"Y\xfckleniyor...":a]})}),(0,c.jsx)("li",{className:"nav-item text-nowrap px-1",children:(0,c.jsx)("button",{onClick:()=>{localStorage.removeItem("user"),e("/login")},className:"nav-link px-3","data-bs-toggle":"tooltip","data-bs-placement":"left","data-bs-title":"\xc7\u0131k\u0131\u015f yap!",children:(0,c.jsx)(r.g,{icon:i.yBu})})}),(0,c.jsx)("li",{className:"nav-item text-nowrap d-md-none",children:(0,c.jsx)("button",{className:"nav-link px-3",type:"button","data-bs-toggle":"offcanvas","data-bs-target":"#yanMenu","aria-controls":"yanMenu","aria-expanded":"false","aria-label":"menu a\xe7/kapa",children:(0,c.jsx)(r.g,{icon:i.ckx})})})]})]})}},3122:(e,a,s)=>{s.r(a),s.d(a,{default:()=>x});var l=s(9379),n=s(5043),r=s(9002),i=s(3910),t=s(7929),c=s(1899),o=s(421),m=s(834),d=s(4713),u=s(579);const x=()=>{const e=(0,r.Zp)(),[a,s]=(0,n.useState)(!0),[x,h]=(0,n.useState)(!1),[A,p]=(0,n.useState)(null),[v,g]=(0,n.useState)(""),[j,N]=(0,n.useState)({musteri_ID:"",kullanici:"",email:"",musteri_adi:"",tel:"",firma:"",adres:"",gorev:""}),[f,y]=(0,n.useState)({username:"",email:"",full_name:"",phone:"",company:"",address:"",current_password:"",new_password:"",confirm_password:""});(0,n.useEffect)((()=>{(async()=>{try{s(!0),p(null);const r=JSON.parse(localStorage.getItem("user")),i=null===r||void 0===r?void 0:r.user;if(!i||!i.id&&!i.musteri_ID)return p("Oturum bilgileriniz bulunamad\u0131. L\xfctfen tekrar giri\u015f yap\u0131n."),void e("/login");const t=i.id||i.musteri_ID;try{const e=await d.Qj.getKullanici(t);N(e),y({username:e.kullanici||"",email:e.email||"",full_name:e.musteri_adi||"",phone:e.tel||"",company:e.firma||"",address:e.adres||"",current_password:"",new_password:"",confirm_password:""}),s(!1)}catch(n){var a,l;console.error("API eri\u015fim hatas\u0131:",n);const e=JSON.parse(localStorage.getItem("user")),r=(null===e||void 0===e?void 0:e.user)||{};N({musteri_ID:r.id||r.musteri_ID||"1",kullanici:r.username||(null===(a=r.email)||void 0===a?void 0:a.split("@")[0])||"kullanici",email:r.email||"<EMAIL>",musteri_adi:r.name||r.musteri_adi||"Misafir Kullan\u0131c\u0131",tel:r.tel||"",firma:r.firma||"",adres:r.adres||"",gorev:r.role||r.gorev||"kullanici"}),y({username:r.username||(null===(l=r.email)||void 0===l?void 0:l.split("@")[0])||"kullanici",email:r.email||"<EMAIL>",full_name:r.name||r.musteri_adi||"Misafir Kullan\u0131c\u0131",phone:r.tel||"",company:r.firma||"",address:r.adres||"",current_password:"",new_password:"",confirm_password:""}),n.message&&n.message.includes("demo")?p("Sunucuya eri\u015filemiyor. Demo veri g\xf6steriliyor."):n.response&&404===n.response.status?p("Kullan\u0131c\u0131 bilgisi veritaban\u0131nda bulunamad\u0131. Yerel bilgiler kullan\u0131l\u0131yor."):"ECONNABORTED"===n.code?p("Sunucu yan\u0131t vermedi. Yerel bilgiler kullan\u0131l\u0131yor."):"Network Error"===n.message?p("A\u011f hatas\u0131. Sunucuya eri\u015filemiyor. Yerel bilgiler kullan\u0131l\u0131yor."):p("Kullan\u0131c\u0131 bilgileri y\xfcklenirken bir hata olu\u015ftu. Yerel bilgiler kullan\u0131l\u0131yor."),s(!1)}}catch(r){console.error("Genel bir hata olu\u015ftu:",r),p("Beklenmeyen bir hata olu\u015ftu. L\xfctfen sayfay\u0131 yenileyin."),s(!1)}})()}),[e]);const b=e=>{const{name:a,value:s}=e.target;y((e=>(0,l.A)((0,l.A)({},e),{},{[a]:s})))};return a?(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(c.A,{}),(0,u.jsx)("div",{className:"container-fluid",children:(0,u.jsxs)("div",{className:"row",children:[(0,u.jsx)(o.A,{}),(0,u.jsx)("main",{className:"col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white",children:(0,u.jsxs)("div",{className:"d-flex justify-content-center align-items-center",style:{height:"80vh"},children:[(0,u.jsx)("div",{className:"spinner-border text-primary",role:"status",children:(0,u.jsx)("span",{className:"visually-hidden",children:"Y\xfckleniyor..."})}),(0,u.jsx)("span",{className:"ms-2",children:"Kullan\u0131c\u0131 bilgileri y\xfckleniyor..."})]})})]})})]}):(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(c.A,{}),(0,u.jsx)("div",{className:"container-fluid",children:(0,u.jsxs)("div",{className:"row",children:[(0,u.jsx)(o.A,{}),(0,u.jsxs)("main",{className:"col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white",children:[(0,u.jsx)("div",{className:"pt-3 pb-1 mt-3 mb-3 border-bottom",children:(0,u.jsxs)("h1",{className:"h4 text-dark mb-0",children:[(0,u.jsx)(i.g,{icon:t.X46,className:"me-2 text-primary"}),"Kullan\u0131c\u0131 Profilim"]})}),A&&(0,u.jsxs)("div",{className:"alert alert-danger",children:[(0,u.jsx)(i.g,{icon:t.X46,className:"me-2"}),A]}),v&&(0,u.jsxs)("div",{className:"alert alert-success",children:[(0,u.jsx)(i.g,{icon:t.X46,className:"me-2"}),v]}),(0,u.jsx)("div",{className:"row mb-4",children:(0,u.jsx)("div",{className:"col-md-12",children:(0,u.jsxs)("div",{className:"card",children:[(0,u.jsx)("div",{className:"card-header bg-dark-subtle py-2",children:(0,u.jsx)("h5",{className:"mb-0 fw-bold",children:"Profil Bilgilerim"})}),(0,u.jsx)("div",{className:"card-body",children:(0,u.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),p(null),g(""),f.new_password||f.confirm_password){if(!f.current_password)return void p("\u015eifrenizi de\u011fi\u015ftirmek i\xe7in mevcut \u015fifrenizi girmelisiniz.");if(f.new_password!==f.confirm_password)return void p("Yeni \u015fifre ve \u015fifre tekrar\u0131 ayn\u0131 olmal\u0131d\u0131r.");if(f.new_password.length<6)return void p("Yeni \u015fifre en az 6 karakter olmal\u0131d\u0131r.")}try{h(!0);const e={username:f.username,email:f.email,full_name:f.full_name,phone:f.phone,company:f.company,address:f.address};f.current_password&&f.new_password&&(e.current_password=f.current_password,e.new_password=f.new_password);try{await d.Qj.updateKullanici(j.musteri_ID,e);const a=JSON.parse(localStorage.getItem("user"));a&&(a.name=f.full_name,a.email=f.email,localStorage.setItem("user",JSON.stringify(a))),y((e=>(0,l.A)((0,l.A)({},e),{},{current_password:"",new_password:"",confirm_password:""}))),g("Profil bilgileriniz ba\u015far\u0131yla g\xfcncellendi."),h(!1),setTimeout((()=>{g("")}),3e3)}catch(a){console.error("API g\xfcncelleme hatas\u0131:",a);let e="Profil g\xfcncellenirken bir hata olu\u015ftu.";a.response?401===a.response.status?e="Mevcut \u015fifreniz yanl\u0131\u015f.":409===a.response.status?e="Bu e-posta adresi veya kullan\u0131c\u0131 ad\u0131 ba\u015fka bir kullan\u0131c\u0131 taraf\u0131ndan kullan\u0131l\u0131yor.":a.response.data&&a.response.data.message&&(e=a.response.data.message):"ECONNABORTED"===a.code?e="Sunucu yan\u0131t vermedi. L\xfctfen daha sonra tekrar deneyin.":"Network Error"===a.message&&(e="A\u011f hatas\u0131. Sunucuya eri\u015filemiyor."),p(e),h(!1)}}catch(s){console.error("Beklenmeyen bir hata olu\u015ftu:",s),p("Beklenmeyen bir hata olu\u015ftu. L\xfctfen sayfay\u0131 yenileyin ve tekrar deneyin."),h(!1)}},children:[(0,u.jsxs)("div",{className:"row mb-3",children:[(0,u.jsxs)("div",{className:"col-md-6",children:[(0,u.jsxs)("div",{className:"form-group mb-3",children:[(0,u.jsxs)("label",{htmlFor:"username",className:"form-label",children:[(0,u.jsx)(i.g,{icon:t.X46,className:"me-2"}),"Kullan\u0131c\u0131 Ad\u0131"]}),(0,u.jsx)("input",{type:"text",className:"form-control",id:"username",name:"username",value:f.username,onChange:b,required:!0})]}),(0,u.jsxs)("div",{className:"form-group mb-3",children:[(0,u.jsxs)("label",{htmlFor:"email",className:"form-label",children:[(0,u.jsx)(i.g,{icon:t.y_8,className:"me-2"}),"E-posta Adresi"]}),(0,u.jsx)("input",{type:"email",className:"form-control",id:"email",name:"email",value:f.email,onChange:b,required:!0})]}),(0,u.jsxs)("div",{className:"form-group mb-3",children:[(0,u.jsxs)("label",{htmlFor:"full_name",className:"form-label",children:[(0,u.jsx)(i.g,{icon:t.X46,className:"me-2"}),"Ad Soyad"]}),(0,u.jsx)("input",{type:"text",className:"form-control",id:"full_name",name:"full_name",value:f.full_name,onChange:b,required:!0})]})]}),(0,u.jsxs)("div",{className:"col-md-6",children:[(0,u.jsxs)("div",{className:"form-group mb-3",children:[(0,u.jsxs)("label",{htmlFor:"phone",className:"form-label",children:[(0,u.jsx)(i.g,{icon:t.KKr,className:"me-2"}),"Telefon"]}),(0,u.jsx)("input",{type:"text",className:"form-control",id:"phone",name:"phone",value:f.phone,onChange:b})]}),(0,u.jsxs)("div",{className:"form-group mb-3",children:[(0,u.jsxs)("label",{htmlFor:"company",className:"form-label",children:[(0,u.jsx)(i.g,{icon:t.URI,className:"me-2"}),"Firma Ad\u0131"]}),(0,u.jsx)("input",{type:"text",className:"form-control",id:"company",name:"company",value:f.company,onChange:b})]}),(0,u.jsxs)("div",{className:"form-group mb-3",children:[(0,u.jsxs)("label",{htmlFor:"address",className:"form-label",children:[(0,u.jsx)(i.g,{icon:t.URI,className:"me-2"}),"Adres"]}),(0,u.jsx)("input",{type:"text",className:"form-control",id:"address",name:"address",value:f.address,onChange:b})]}),(0,u.jsxs)("div",{className:"form-group mb-3",children:[(0,u.jsxs)("label",{htmlFor:"role",className:"form-label",children:[(0,u.jsx)(i.g,{icon:t.jd5,className:"me-2"}),"Kullan\u0131c\u0131 Rol\xfc"]}),(0,u.jsx)("input",{type:"text",className:"form-control",id:"role",value:"admin"===j.gorev?"Y\xf6netici":"manager"===j.gorev?"M\xfcd\xfcr":"Kullan\u0131c\u0131",disabled:!0})]})]})]}),(0,u.jsx)("hr",{className:"my-4"}),(0,u.jsxs)("div",{className:"row mb-3",children:[(0,u.jsxs)("div",{className:"col-md-12",children:[(0,u.jsx)("h5",{children:"\u015eifre De\u011fi\u015ftirme"}),(0,u.jsx)("p",{className:"text-muted small",children:"\u015eifrenizi de\u011fi\u015ftirmek istemiyorsan\u0131z bu alanlar\u0131 bo\u015f b\u0131rakabilirsiniz."})]}),(0,u.jsx)("div",{className:"col-md-4",children:(0,u.jsxs)("div",{className:"form-group mb-3",children:[(0,u.jsx)("label",{htmlFor:"current_password",className:"form-label",children:"Mevcut \u015eifre"}),(0,u.jsx)("input",{type:"password",className:"form-control",id:"current_password",name:"current_password",value:f.current_password,onChange:b})]})}),(0,u.jsx)("div",{className:"col-md-4",children:(0,u.jsxs)("div",{className:"form-group mb-3",children:[(0,u.jsx)("label",{htmlFor:"new_password",className:"form-label",children:"Yeni \u015eifre"}),(0,u.jsx)("input",{type:"password",className:"form-control",id:"new_password",name:"new_password",value:f.new_password,onChange:b,minLength:"6"})]})}),(0,u.jsx)("div",{className:"col-md-4",children:(0,u.jsxs)("div",{className:"form-group mb-3",children:[(0,u.jsx)("label",{htmlFor:"confirm_password",className:"form-label",children:"Yeni \u015eifre (Tekrar)"}),(0,u.jsx)("input",{type:"password",className:"form-control",id:"confirm_password",name:"confirm_password",value:f.confirm_password,onChange:b,minLength:"6"})]})})]}),(0,u.jsx)("div",{className:"row",children:(0,u.jsx)("div",{className:"col-md-12",children:(0,u.jsxs)("div",{className:"d-flex justify-content-end gap-2",children:[(0,u.jsxs)("button",{type:"button",className:"btn btn-secondary",onClick:()=>{y({username:j.kullanici||"",email:j.email||"",full_name:j.musteri_adi||"",phone:j.tel||"",company:j.firma||"",address:j.adres||"",current_password:"",new_password:"",confirm_password:""}),p(null),g("")},disabled:x,children:[(0,u.jsx)(i.g,{icon:t.U23,className:"me-2"}),"S\u0131f\u0131rla"]}),(0,u.jsx)("button",{type:"submit",className:"btn btn-primary",disabled:x,children:x?(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)("span",{className:"spinner-border spinner-border-sm me-2",role:"status","aria-hidden":"true"}),"Kaydediliyor..."]}):(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(i.g,{icon:t.hSh,className:"me-2"}),"Kaydet"]})})]})})})]})})]})})})]})]})}),(0,u.jsx)(m.A,{})]})}}}]);
//# sourceMappingURL=122.3f66346a.chunk.js.map