import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';
import reportWebVitals from './reportWebVitals';
import { APIProvider } from '@vis.gl/react-google-maps';
import { register as registerSW } from './utils/serviceWorkerRegistration';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <APIProvider apiKey="AIzaSyA2cfEmiPMyvcGfRiCyB9khWrccCgqpxKs">
      <App />
    </APIProvider>
  </React.StrictMode>
);

// Register service worker for caching and offline support
registerSW();

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
