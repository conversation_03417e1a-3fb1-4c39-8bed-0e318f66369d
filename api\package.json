{"name": "mgz24-api", "version": "1.0.0", "description": "MGZ24 IoT Cihaz Yönetim API'si", "main": "mgz24api.js", "type": "module", "scripts": {"start": "node mgz24api.js", "start:https": "node mgz24api.js", "start:http-redirect": "node http-redirect.js", "dev": "node --watch mgz24api.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["iot", "api", "mgz24", "cih<PERSON>-yo<PERSON><PERSON>i", "https"], "author": "MGZ24 Team", "license": "MIT", "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "mysql2": "^3.14.2"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "https://github.com/mgz24/api.git"}}