const axios = require('axios');

// Test PayTR integration
async function testPayTR() {
  try {
    // Simulate frontend request to PayTR create-token endpoint
    const testData = {
      user_id: 1,
      device_id: 'TEST001',
      package_info: {
        id: 0,
        name: 'Test Paketi (1 Gün)',
        days: 1,
        isTest: true
      },
      amount: 1, // 1 TL
      currency: 'TL',
      user_name: 'Test Kullanıcı',
      user_email: '<EMAIL>',
      user_phone: '5555555555',
      user_basket: JSON.stringify([{
        name: 'Test Paketi (1 Gün)',
        price: '100', // 1 TL = 100 kuruş
        quantity: '1'
      }])
    };

    console.log('Test isteği gönderiliyor...');
    console.log('Test Data:', JSON.stringify(testData, null, 2));

    const response = await axios.post('http://localhost:3000/api/payments/paytr/create-token', testData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer dummy-token' // Test için
      }
    });

    console.log('PayTR API başarılı yanıt:', response.data);
  } catch (error) {
    console.error('PayTR Test Hatası:');
    console.error('Status:', error.response?.status);
    console.error('Data:', error.response?.data);
    console.error('Message:', error.message);
  }
}

testPayTR();