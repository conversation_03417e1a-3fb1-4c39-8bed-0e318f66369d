// Dynamic Import Utilities for Code Splitting
import React from 'react';

/**
 * Preload a component for better performance
 * @param {Function} importFunction - Dynamic import function
 */
export const preloadComponent = (importFunction) => {
  const componentImport = importFunction();
  return componentImport;
};

/**
 * Create a retry-enabled dynamic import
 * @param {Function} importFunction - Dynamic import function
 * @param {number} retries - Number of retries (default: 3)
 */
export const retryImport = (importFunction, retries = 3) => {
  return new Promise((resolve, reject) => {
    importFunction()
      .then(resolve)
      .catch((error) => {
        if (retries > 0) {
          console.warn(`Import failed, retrying... (${retries} attempts left)`);
          setTimeout(() => {
            retryImport(importFunction, retries - 1)
              .then(resolve)
              .catch(reject);
          }, 1000);
        } else {
          reject(error);
        }
      });
  });
};

/**
 * Create a lazy component with error boundary
 * @param {Function} importFunction - Dynamic import function
 * @param {string} componentName - Component name for debugging
 */
export const createLazyComponent = (importFunction, componentName = 'Component') => {
  return React.lazy(() => 
    retryImport(importFunction).catch((error) => {
      console.error(`Failed to load ${componentName}:`, error);
      // Return a fallback component
      return {
        default: () => React.createElement('div', {
          className: 'alert alert-danger',
          children: `Failed to load ${componentName}. Please refresh the page.`
        })
      };
    })
  );
};

/**
 * Preload critical route components
 * This should be called when user is likely to navigate to these routes
 */
export const preloadCriticalRoutes = () => {
  // Preload Home component (most likely to be visited)
  preloadComponent(() => import('../pages/Home'));
  
  // Preload commonly used components after a delay
  setTimeout(() => {
    preloadComponent(() => import('../pages/ShipmentView'));
    preloadComponent(() => import('../pages/ShipmentAdd'));
  }, 2000);
};

/**
 * Preload components based on user role
 * @param {string} userRole - User role (admin, manager, user)
 */
export const preloadByUserRole = (userRole) => {
  switch (userRole) {
    case 'admin':
      // Admins likely use all features
      preloadComponent(() => import('../pages/InactiveDevices'));
      preloadComponent(() => import('../pages/Payment'));
      preloadComponent(() => import('../pages/Profile'));
      break;
    case 'manager':
      // Managers likely use shipment features
      preloadComponent(() => import('../pages/ShipmentHistory'));
      preloadComponent(() => import('../pages/InactiveDevices'));
      break;
    case 'user':
    default:
      // Regular users likely use basic features
      preloadComponent(() => import('../pages/ShipmentView'));
      break;
  }
};