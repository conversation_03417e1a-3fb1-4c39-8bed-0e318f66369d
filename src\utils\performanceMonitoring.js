// Performance Monitoring Utilities

/**
 * Web Vitals performance monitoring
 */
export const initPerformanceMonitoring = () => {
  // Monitor Largest Contentful Paint (LCP)
  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.entryType === 'largest-contentful-paint') {
        console.log('LCP:', entry.startTime);
        // Send to analytics if needed
      }
    }
  });

  try {
    observer.observe({ type: 'largest-contentful-paint', buffered: true });
  } catch (e) {
    // Fallback for browsers that don't support this
    console.warn('LCP monitoring not supported');
  }

  // Monitor First Input Delay (FID)
  if ('PerformanceEventTiming' in window) {
    const fidObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.name === 'first-input') {
          console.log('FID:', entry.processingStart - entry.startTime);
        }
      }
    });

    try {
      fidObserver.observe({ type: 'first-input', buffered: true });
    } catch (e) {
      console.warn('FID monitoring not supported');
    }
  }
};

/**
 * Resource loading performance tracker
 */
export const trackResourceLoading = () => {
  window.addEventListener('load', () => {
    // Track all resource loading times
    const resources = performance.getEntriesByType('resource');
    const slowResources = resources.filter(resource => resource.duration > 1000);
    
    if (slowResources.length > 0) {
      console.warn('Slow loading resources:', slowResources.map(r => ({
        name: r.name,
        duration: r.duration,
        size: r.transferSize
      })));
    }

    // Track total page load time
    const pageLoadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
    console.log('Page load time:', pageLoadTime + 'ms');
  });
};

/**
 * Memory usage monitoring
 */
export const monitorMemoryUsage = () => {
  if ('memory' in performance) {
    const logMemory = () => {
      const memory = performance.memory;
      console.log('Memory usage:', {
        used: Math.round(memory.usedJSHeapSize / 1048576) + 'MB',
        total: Math.round(memory.totalJSHeapSize / 1048576) + 'MB',
        limit: Math.round(memory.jsHeapSizeLimit / 1048576) + 'MB'
      });
    };

    // Log memory usage every 30 seconds
    setInterval(logMemory, 30000);
    
    // Log initial memory usage
    logMemory();
  }
};

/**
 * Track route change performance
 */
export const trackRouteChange = (routeName) => {
  const start = performance.now();
  
  return () => {
    const end = performance.now();
    const duration = end - start;
    console.log(`Route ${routeName} loaded in ${duration.toFixed(2)}ms`);
    
    // If route takes too long to load, log warning
    if (duration > 2000) {
      console.warn(`Slow route loading: ${routeName} took ${duration.toFixed(2)}ms`);
    }
  };
};

/**
 * Bundle size analyzer (development only)
 */
export const analyzeBundleSize = () => {
  if (process.env.NODE_ENV === 'development') {
    // Track JavaScript bundle loading
    const scripts = document.querySelectorAll('script[src]');
    scripts.forEach(script => {
      if (script.src.includes('static/js/')) {
        console.log('JavaScript bundle:', script.src);
      }
    });

    // Track CSS bundle loading  
    const links = document.querySelectorAll('link[rel="stylesheet"]');
    links.forEach(link => {
      if (link.href.includes('static/css/')) {
        console.log('CSS bundle:', link.href);
      }
    });
  }
};

/**
 * Network connection quality monitoring
 */
export const monitorConnection = () => {
  if ('connection' in navigator) {
    const connection = navigator.connection;
    
    const logConnection = () => {
      console.log('Connection info:', {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink + 'Mbps',
        rtt: connection.rtt + 'ms',
        saveData: connection.saveData
      });
    };

    logConnection();
    
    // Monitor connection changes
    connection.addEventListener('change', logConnection);
  }
};