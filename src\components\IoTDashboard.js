import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faThermometerHalf,
  faTint,
  faLightbulb,
  faBolt,
  faBatteryFull,
  faExclamationTriangle,
  faCheckCircle,
  faWrench
} from '@fortawesome/free-solid-svg-icons';
import SensorDataChart from './charts/SensorDataChart';
import DeviceStatusChart from './charts/DeviceStatusChart';
import TemperatureGaugeChart from './charts/TemperatureGaugeChart';
import LoadingSpinner from './LoadingSpinner';
import ErrorMessage from './ErrorMessage';

/**
 * IoT Dashboard bileşeni - Sensör verilerini ve cihaz durumlarını gösterir
 * Context7 referansı: /recharts/recharts - Dashboard with multiple chart types
 */
const IoTDashboard = ({ sevkiyatId, onRefresh }) => {
  const [sensorData, setSensorData] = useState([]);
  const [deviceStats, setDeviceStats] = useState([]);
  const [currentReadings, setCurrentReadings] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(null);

  // Veri simülasyonu - gerçek API'dan veri gelene kadar
  useEffect(() => {
    const generateMockData = () => {
      const now = new Date();
      const data = [];

      // Son 24 saatlik veri simülasyonu
      for (let i = 23; i >= 0; i--) {
        const time = new Date(now.getTime() - i * 60 * 60 * 1000);
        data.push({
          zaman: time.toISOString(),
          sicaklik: 15 + Math.random() * 20 + Math.sin(i / 4) * 5,
          nem: 40 + Math.random() * 30,
          darbe_buyuklugu: Math.random() * 2,
          isik: 100 + Math.random() * 400,
          pil_seviyesi: Math.max(20, 100 - i * 2)
        });
      }

      return data;
    };

    const generateDeviceStats = () => {
      const total = 25;
      const aktif = Math.floor(total * 0.7);
      const pasif = Math.floor(total * 0.15);
      const bakimda = Math.floor(total * 0.1);
      const pil_dusuk = total - aktif - pasif - bakimda;

      return [
        { status: 'aktif', count: aktif, percentage: (aktif / total) * 100 },
        { status: 'pasif', count: pasif, percentage: (pasif / total) * 100 },
        { status: 'bakimda', count: bakimda, percentage: (bakimda / total) * 100 },
        { status: 'pil_dusuk', count: pil_dusuk, percentage: (pil_dusuk / total) * 100 }
      ];
    };

    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Simulated API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        const mockSensorData = generateMockData();
        const mockDeviceStats = generateDeviceStats();
        const latest = mockSensorData[mockSensorData.length - 1];

        setSensorData(mockSensorData);
        setDeviceStats(mockDeviceStats);
        setCurrentReadings(latest);
        setLastUpdate(new Date());

      } catch (err) {
        console.error('IoT veri alınırken hata:', err);
        setError('IoT verileri yüklenirken bir hata oluştu.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();

    // Her 30 saniyede bir güncelle
    const interval = setInterval(fetchData, 30000);

    return () => clearInterval(interval);
  }, [sevkiyatId, onRefresh]);

  // Sensör kartı bileşeni
  const SensorCard = ({ title, value, unit, icon, color, status }) => (
    <div className="col-md-3 mb-3">
      <div className="card h-100">
        <div className="card-body text-center">
          <div className={`text-${color} mb-2`}>
            <FontAwesomeIcon icon={icon} size="2x" />
          </div>
          <h6 className="card-title">{title}</h6>
          <h4 className={`text-${color}`}>
            {value !== null && value !== undefined ? `${parseFloat(value).toFixed(1)}${unit}` : 'N/A'}
          </h4>
          {status && (
            <small className={`badge bg-${color === 'danger' ? 'danger' : 'success'}`}>
              {status}
            </small>
          )}
        </div>
      </div>
    </div>
  );

  // Durum badge rengi
  const getStatusColor = (temp) => {
    if (temp < 0 || temp > 40) return 'danger';
    if (temp < 5 || temp > 35) return 'warning';
    return 'success';
  };

  if (loading) {
    return (
      <LoadingSpinner
        size="lg"
        variant="primary"
        message="IoT verileri yükleniyor..."
        centered={true}
      />
    );
  }

  if (error) {
    return (
      <ErrorMessage
        message={error}
        variant="danger"
        dismissible={true}
        onDismiss={() => setError('')}
      />
    );
  }

  return (
    <div className="container-fluid">
      {/* Başlık ve son güncelleme */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-flex justify-content-between align-items-center">
            <h4>IoT Sensör Dashboard</h4>
            {lastUpdate && (
              <small className="text-muted">
                Son güncelleme: {lastUpdate.toLocaleTimeString('tr-TR')}
              </small>
            )}
          </div>
        </div>
      </div>

      {/* Grafikler */}
      <div className="row mb-4">
        {/* Sensör verileri çizgi grafik */}
        <div className="col-lg-8 mb-4">
          <div className="card">
            <div className="card-body">
              <SensorDataChart
                data={sensorData}
                title="24 Saatlik Sensör Verileri"
                height={400}
              />
            </div>
          </div>
        </div>

        {/* Sıcaklık gauge */}
        <div className="col-lg-4 mb-4">
          <div className="card">
            <div className="card-body">
              <TemperatureGaugeChart
                temperature={currentReadings.sicaklik || 20}
                minTemp={-10}
                maxTemp={50}
                title="Anlık Sıcaklık"
                height={300}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Cihaz durumları */}
      <div className="row mb-4">
        <div className="col-12 mb-4">
          <div className="card">
            <div className="card-body">
              <DeviceStatusChart
                data={deviceStats}
                title="Cihaz Durumları"
                height={300}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IoTDashboard;