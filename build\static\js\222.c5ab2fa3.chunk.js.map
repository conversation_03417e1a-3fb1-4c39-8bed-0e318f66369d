{"version": 3, "file": "static/js/222.c5ab2fa3.chunk.js", "mappings": "gKASA,MAwGA,EAxGgBA,KACZ,MAAMC,GAAWC,EAAAA,EAAAA,MAaXC,EAAuB,UAVTC,MAChB,IAAK,IAADC,EAAAC,EACA,MAAMC,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C,OAAW,OAAJJ,QAAI,IAAJA,GAAU,QAANF,EAAJE,EAAMA,YAAI,IAAAF,OAAN,EAAJA,EAAYO,QAAY,OAAJL,QAAI,IAAJA,GAAU,QAAND,EAAJC,EAAMA,YAAI,IAAAD,OAAN,EAAJA,EAAYO,QAAS,MACpD,CAAE,MAAAC,GACE,MAAO,MACX,GAGaV,GAGjB,OACIW,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC1DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6BAA6BG,SAAS,KAAKC,GAAG,UAAU,kBAAgB,UAASH,SAAA,EAC5FC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,EAC7BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,kBAAkBI,GAAG,UAASH,SAAC,gBAC7CF,EAAAA,EAAAA,KAAA,UAAQM,KAAK,SAASL,UAAU,YAAY,kBAAgB,YAAY,iBAAe,WAAW,aAAW,cAEjHE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mEAAkEC,SAAA,EAC7EF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,kCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,MAAtBtB,EAASuB,SAAmB,SAAW,IAAMC,GAAG,IAAGR,SAAA,EAC5GF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMC,EAAAA,MAAW,0BAG1Cb,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,SAAtBtB,EAASuB,SAAsB,SAAW,IAAMC,GAAG,OAAMR,SAAA,EAClHF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAME,EAAAA,MAAgB,mCAG/Cd,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,sBAAtBtB,EAASuB,SAAmC,SAAW,IAAMC,GAAG,oBAAmBR,SAAA,EAC5IF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMG,EAAAA,MAAY,8BAG3Cf,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMI,EAAAA,MAAmB,sCAKtDhB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wBACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,CAEpCd,IACGY,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMK,EAAAA,MAAa,0BAIhDjB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,mBAAtBtB,EAASuB,SAAgC,SAAW,IAAMC,GAAG,iBAAgBR,SAAA,EACtIF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMM,EAAAA,MAAU,uBAK7ClB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,uCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMO,EAAAA,MAAU,qCAGzCnB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMQ,EAAAA,MAAgB,6CAKnDpB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wCACpIC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4BAA2BC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,aAAtBtB,EAASuB,SAA0B,SAAW,IAAMC,GAAG,WAAUR,SAAA,EAC1HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMS,EAAAA,MAAgB,qBAG/CrB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,WAAUC,UACpBC,EAAAA,EAAAA,MAACI,EAAAA,GAAI,CAACN,UAAS,4CAAAO,OAAoE,cAAtBtB,EAASuB,SAA2B,SAAW,IAAMC,GAAG,YAAWR,SAAA,EAC5HF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMU,EAAAA,MAAe,4BAKlDtB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,UACdD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sHAAqHC,SAAC,wBAG1I,C,sDC3Gd,MAQA,EAReqB,KAEPvB,EAAAA,EAAAA,KAAA,UAAQC,UAAU,kBAAiBC,UAC/BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,cAAaC,SAAC,iD,qGCEvC,MA6GA,EA7GesB,KACX,MAAMC,GAAWC,EAAAA,EAAAA,OACVC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,YAClCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAGvCG,EAAAA,EAAAA,YAAU,KACgBC,WAClB,IAAK,IAADC,EAAAC,EAAAC,EAAAC,EAEA,MAAMC,EAAa7C,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C2C,GAAmB,OAAVD,QAAU,IAAVA,GAAgB,QAANJ,EAAVI,EAAY9C,YAAI,IAAA0C,OAAN,EAAVA,EAAkBM,cAAwB,OAAVF,QAAU,IAAVA,GAAgB,QAANH,EAAVG,EAAY9C,YAAI,IAAA2C,OAAN,EAAVA,EAAkB9B,IAC3DsB,GAAqB,OAAVW,QAAU,IAAVA,GAAgB,QAANF,EAAVE,EAAY9C,YAAI,IAAA4C,OAAN,EAAVA,EAAkBK,QAAkB,OAAVH,QAAU,IAAVA,GAAgB,QAAND,EAAVC,EAAY9C,YAAI,IAAA6C,OAAN,EAAVA,EAAkBK,aAE7D,IAAKH,EAID,OAHAI,QAAQC,KAAK,kCACbhB,EAAY,gBACZG,GAAW,GAKf,GAAIJ,EAGA,OAFAC,EAAYD,QACZI,GAAW,GAKf,IACI,MAAMc,QAAiBC,EAAAA,GAAiBC,aAAaR,GAGjDM,GAAYA,EAASH,cACrBd,EAAYiB,EAASH,aAGP,OAAVJ,QAAU,IAAVA,GAAAA,EAAY9C,OACZ8C,EAAW9C,KAAKiD,KAAOI,EAASH,YAChC/C,aAAaqD,QAAQ,OAAQvD,KAAKwD,UAAUX,KAGxD,CAAE,MAAOY,GACLP,QAAQC,KAAK,gGAEbhB,EAAYD,GAAY,sBAC5B,CACJ,CAAE,MAAOwB,GACLR,QAAQQ,MAAM,0DAAuCA,GAErDvB,EAAY,sBAChB,CAAC,QACGG,GAAW,EACf,GAGJqB,EAAe,GAChB,IAWH,OACIjD,EAAAA,EAAAA,MAAA,UAAQF,UAAU,0DAA0D,gBAAc,OAAMC,SAAA,EAC5FF,EAAAA,EAAAA,KAACO,EAAAA,GAAI,CAACN,UAAU,gEAAgES,GAAG,IAAGR,UAClFF,EAAAA,EAAAA,KAAA,OAAKqD,I,60RAAeC,IAAI,aAAaC,OAAO,UAEhDpD,EAAAA,EAAAA,MAAA,MAAIF,UAAU,yCAAwCC,SAAA,EAClDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8CAA6CC,UACvDC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,aAAYC,SAAA,EACxBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMO,EAAAA,IAAQlB,UAAU,SACxC6B,EAAU,mBAAkBH,QAGrC3B,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4BAA2BC,UACrCF,EAAAA,EAAAA,KAAA,UACIwD,QAtBCC,KAEjB9D,aAAa+D,WAAW,QAGxBjC,EAAS,SAAS,EAkBFxB,UAAU,gBACV,iBAAe,UACf,oBAAkB,OAClB,gBAAc,+BAAYC,UAE1BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM+C,EAAAA,WAG/B3D,EAAAA,EAAAA,KAAA,MAAIC,UAAU,iCAAgCC,UAC1CF,EAAAA,EAAAA,KAAA,UACIC,UAAU,gBACVK,KAAK,SACL,iBAAe,YACf,iBAAe,WACf,gBAAc,UACd,gBAAc,QACd,aAAW,kBAAcJ,UAEzBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMgD,EAAAA,eAI9B,C,oJC9FjB,MA+lBA,EA/lBoBC,KAEhB,MAAOC,EAAYC,IAAiBlC,EAAAA,EAAAA,UAAS,OAGtCmC,EAAkBC,IAAuBpC,EAAAA,EAAAA,UAAS,KAClDqC,EAAgBC,IAAqBtC,EAAAA,EAAAA,WAAS,IAC9CuC,EAAgBC,IAAqBxC,EAAAA,EAAAA,UAAS,OAG9CyC,EAAUC,IAAe1C,EAAAA,EAAAA,UAAS,CACrC2C,WAAY,GACZC,aAAc,GACdC,SAAU,GACVC,eAAgB,GAChBC,eAAgB,GAChBC,WAAY,GACZC,eAAgB,GAChBC,aAAc,GACdC,aAAc,GACdC,YAAa,GACbC,aAAc,GACdC,iBAAkB,kBAClB3C,WAAY,GACZ4C,SAAU,GACVC,kBAAmB,GACnBC,eAAgB,MAGbC,EAAWC,IAAgB3D,EAAAA,EAAAA,UAAS,CACvC4D,IAAK,EACLC,IAAK,IAGHC,GAAYC,EAAAA,EAAAA,QAAO,MACnBC,GAAiBD,EAAAA,EAAAA,QAAO,OAG9B5D,EAAAA,EAAAA,YAAU,KACN,MAAMxC,EAAOC,KAAKC,MAAMC,aAAaC,QAAQ,SAC7C,GAAIJ,EAAM,CAAC,IAADF,EAAAC,EACNwE,EAAcvE,GACd,MAAM+C,GAAa,OAAJ/C,QAAI,IAAJA,GAAU,QAANF,EAAJE,EAAMA,YAAI,IAAAF,OAAN,EAAJA,EAAYkD,cAAkB,OAAJhD,QAAI,IAAJA,GAAU,QAAND,EAAJC,EAAMA,YAAI,IAAAD,OAAN,EAAJA,EAAYc,MAAU,OAAJb,QAAI,IAAJA,OAAI,EAAJA,EAAMa,IAGjEkE,GAAYuB,IAASC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACdD,GAAS,IACZtD,WAAYD,MAIhByD,EAAqBzD,EACzB,IACD,IAGH,MAAMyD,EAAuB/D,UACzB,IACIkC,GAAkB,GAClB,MAAM8B,QAAgBC,EAAAA,GAAeC,mBAAmB5D,GAGxD0B,EAAoBgC,EACxB,CAAE,MAAO9C,GACLR,QAAQQ,MAAM,gCAA8BA,GAC5Cc,EAAoB,GACxB,CAAC,QACGE,GAAkB,EACtB,IAcJnC,EAAAA,EAAAA,YAAU,KACN,GAAI2D,EAAUS,SAAWC,OAAOC,OAAQ,CACpC,MACMC,GAAUC,EADNH,OAAOC,QACCX,EAAUS,SA8B5B,OA3BAG,EAAQE,eAAe,CACnBnG,KAAM,SACNoG,MAAM,EACNjB,KAAM,GACNC,IAAK,GACLiB,KAAMpB,EAAUE,IAChB/E,GAAI6E,EAAUG,IACdkB,KAAM,GACNC,QAAS,SACTC,kBAAkB,EAClBC,KAAM,OACNC,SAAWC,IACPzB,EAAa,CACTC,IAAKwB,EAAKN,KACVjB,IAAKuB,EAAKvG,KAEd6D,GAAY2C,IAAQnB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACbmB,GAAQ,IACX/B,iBAAiB,GAAD3E,OAAKyG,EAAKN,KAAI,YAAAnG,OAAQyG,EAAKvG,GAAE,YAC9C,IAKXmF,EAAeO,QAAUG,EAAQU,KAAK,kBAG/B,KACCpB,EAAeO,SACfP,EAAeO,QAAQe,SAC3B,CAER,IACD,KAGHnF,EAAAA,EAAAA,YAAU,KACNuC,GAAY6C,IAAIrB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACTqB,GAAI,IACPjC,iBAAiB,GAAD3E,OAAK+E,EAAUE,IAAG,YAAAjF,OAAQ+E,EAAUG,IAAG,YACxD,GAEJ,CAACH,IAEJ,MAAM8B,EAAgBC,IAClB,MAAM,KAAE7E,EAAI,MAAE8E,EAAK,KAAEjH,EAAI,QAAEkH,GAAYF,EAAEG,OAEzC,GAAa,oBAATnH,EAA4B,CAC5B,MAAMoH,EAAkBC,MAAMhB,KAAKa,GAC9BI,QAAOC,GAAUA,EAAOC,WACxBC,KAAIF,GAAUA,EAAON,QAE1BhD,GAAWwB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EACNzB,GAAQ,IACX,CAAC7B,GAAOiF,IAEhB,MAEI,GAAa,eAATjF,GAAyB8E,EAAO,CAChC,MAAMnD,EAAiBJ,EAAiBgE,MAAKC,GAAUA,EAAOC,UAAYX,IAC1EhD,GAAWwB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EACNzB,GAAQ,IACX,CAAC7B,GAAO8E,EACRnC,SAAUhB,EAAiBA,EAAe8D,QAAU,KAE5D,MACI3D,GAAWwB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EACNzB,GAAQ,IACX,CAAC7B,GAAO8E,IAGpB,EAoFJ,OACIpH,EAAAA,EAAAA,MAAAgI,EAAAA,SAAA,CAAAjI,SAAA,EACIF,EAAAA,EAAAA,KAACwB,EAAAA,EAAM,KACPxB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAChBF,EAAAA,EAAAA,KAACf,EAAAA,EAAO,KAGRkB,EAAAA,EAAAA,MAAA,QAAMF,UAAU,iFAAgFC,SAAA,EAC5FC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sBAAqBC,SAAA,EAChCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yDAAwDC,SAAA,EACnEC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,kBAAiBC,SAAA,EAC3BF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMwH,EAAAA,IAAQnI,UAAU,SAAS,gCAGrD6D,IACG3D,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sEAAqEC,SAAA,EAChFF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMyH,EAAAA,IAASpI,UAAU,UAC1CE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACIF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,qBAAoBC,SAAC,qBACtCF,EAAAA,EAAAA,KAAA,UAAAE,SAAS4D,EAAWrB,OAAc,KAACtC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,aAAYC,SAAA,CAAC,IAAE4D,EAAWzD,gBAK9FL,EAAAA,EAAAA,KAAA,KAAGC,UAAU,aAAYC,SAAC,sGAI9BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACrBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,SAAQC,UACnBC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,UAAUqI,SAhHjCrG,UAIjB,GAHAqF,EAAEiB,iBAGGjE,EAAS9B,WAMd,GAAgC,IAA5BwB,EAAiBwE,OAMrB,GAAKlE,EAASE,WAAd,CAKA7B,QAAQ8F,IAAI,sBAAuBnE,GAEnC,IAEI,MAAMoE,GAAO3C,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACNzB,GAAQ,IAEXE,WAAYF,EAASE,YAAc,KACnCK,WAAYP,EAASO,YAAc,KACnCC,eAAgBR,EAASQ,gBAAkB,KAC3CC,aAAcT,EAASS,cAAgB,KACvCC,aAAcV,EAASU,cAAgB,KACvCC,YAAaX,EAASW,aAAe,KACrCC,aAAcZ,EAASY,cAAgB,KACvCC,iBAAkBb,EAASa,kBAAoB,KAC/CE,kBAAmBf,EAASe,mBAAqB,KACjDC,eAAgBhB,EAASgB,gBAAkB,OAIzCqD,QAAiBC,MAAM,GAADpI,OAAIqI,wBAAkD,gBAAgB,CAC9FC,OAAQ,OACRC,QAAS,CACL,eAAgB,oBAEpBC,KAAMvJ,KAAKwD,UAAUyF,KAGnBzB,QAAa0B,EAASM,OAE5B,IAAKN,EAASO,GAAI,CAId,GAHAvG,QAAQQ,MAAM,sBAAuB8D,GAGjCA,EAAKkC,QAAUlC,EAAKkC,OAAOX,OAAS,EAAG,CACvC,MAAMY,EAAgBnC,EAAKkC,OAAOpB,KAAIsB,GAAG,GAAA7I,OAClC6I,EAAIC,MAAK,MAAA9I,OAAK6I,EAAIE,WACvBC,KAAK,MAEP,MAAM,IAAIC,MAAM,8BAADjJ,OAA0B4I,EAAa,QAAA5I,OAAOyG,EAAKyC,SAAW,IACjF,CAEA,MAAM,IAAID,MAAMxC,EAAK9D,OAAS8D,EAAKsC,SAAW,2CAClD,CAEAI,MAAM,iDAGV,CAAE,MAAOxG,GACLR,QAAQQ,MAAM,4BAA6BA,GAG3C,MAAMyG,EAAWzG,EAAMoG,QAAQf,OAAS,IAClCrF,EAAMoG,QAAQM,UAAU,EAAG,KAAO,gDAClC1G,EAAMoG,QAEZI,MAAM,SAADnJ,OAAUoJ,GACnB,CA3DA,MAFID,MAAM,oDANNA,MAAM,wFANNA,MAAM,gFAyEV,EAkCyEzJ,SAAA,EAE7CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,SAAQC,UACnBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACnDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC1DC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,YAAWC,SAAA,EACrBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMkJ,EAAAA,IAAU7J,UAAU,SAAS,iCAI5DD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iBAAgBC,UAC3BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,UAASC,SAAA,EACpBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACrBF,EAAAA,EAAAA,KAAA,SAAO+J,QAAQ,aAAa9J,UAAU,kFAAiFC,SAAC,iBACxHC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBC,SAAA,EAClCC,EAAAA,EAAAA,MAAA,UACIF,UAAU,qBACVwC,KAAK,aACLpC,GAAG,aACHkH,MAAOjD,EAASE,WAChBwC,SAAUK,EACV2C,UAAQ,EACRC,SAAU/F,GAA8C,IAA5BF,EAAiBwE,OAAatI,SAAA,EAE1DF,EAAAA,EAAAA,KAAA,UAAQuH,MAAM,GAAErH,SACXgE,EACK,4BAC4B,IAA5BF,EAAiBwE,OACb,gCACA,qBAGbxE,EAAiB+D,KAAKE,IACnB9H,EAAAA,EAAAA,MAAA,UAAwBoH,MAAOU,EAAOC,QAAQhI,SAAA,CACzC+H,EAAOC,QAAQ,MAAID,EAAOiC,YADlBjC,EAAOkC,UAK5BnK,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mCAAkCC,UAC9CF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMkJ,EAAAA,WAGF,IAA5B9F,EAAiBwE,SAAiBtE,IAC/B/D,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBC,SAAA,EAClCF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMwJ,EAAAA,IAAanK,UAAU,SAAS,yFAMnEE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACrBF,EAAAA,EAAAA,KAAA,SAAO+J,QAAQ,eAAe9J,UAAU,kFAAiFC,SAAC,wBAC1HC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACIM,KAAK,OACLL,UAAU,sBACVwC,KAAK,eACLpC,GAAG,eACHkH,MAAOjD,EAASG,aAChBuC,SAAUK,EACV2C,UAAQ,EACRK,YAAY,oCAEhBrK,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mCAAkCC,UAACF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM0J,EAAAA,eAIlFnK,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACrBF,EAAAA,EAAAA,KAAA,SAAO+J,QAAQ,WAAW9J,UAAU,kFAAiFC,SAAC,eACtHC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACIM,KAAK,OACLL,UAAU,sBACVwC,KAAK,WACLpC,GAAG,WACHkH,MAAOjD,EAASI,SAChBsC,SAAUK,EACV2C,UAAQ,EACRK,YAAY,wBAEhBrK,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mCAAkCC,UAACF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM0J,EAAAA,yBASlGtK,EAAAA,EAAAA,KAAA,OAAKC,UAAU,SAAQC,UACnBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACnDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC1DC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,YAAWC,SAAA,EACrBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM2J,EAAAA,IAAetK,UAAU,SAAS,6CAIjED,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iBAAgBC,UAC3BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,UAASC,SAAA,EACpBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAC9BF,EAAAA,EAAAA,KAAA,SAAO+J,QAAQ,iBAAiB9J,UAAU,kFAAiFC,SAAC,mCAC5HC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACIM,KAAK,OACLL,UAAU,sBACVwC,KAAK,iBACLpC,GAAG,iBACHkH,MAAOjD,EAASK,eAChBqC,SAAUK,EACV2C,UAAQ,EACRK,YAAY,2BAEhBrK,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mCAAkCC,UAACF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM2J,EAAAA,eAIlFpK,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAC9BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oBAAmBC,UAC9BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sCAAsCuK,MAAO,CAAEC,IAAK,OAAQC,KAAM,QAASC,OAAQ,KAAMzK,UACpGF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMgK,EAAAA,IAAc3K,UAAU,sBAGvDD,EAAAA,EAAAA,KAAA,SAAO+J,QAAQ,iBAAiB9J,UAAU,kFAAiFC,SAAC,2BAC5HC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACIM,KAAK,OACLL,UAAU,sBACVwC,KAAK,iBACLpC,GAAG,iBACHkH,MAAOjD,EAASM,eAChBoC,SAAUK,EACV2C,UAAQ,EACRK,YAAY,oBAEhBrK,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mCAAkCC,UAACF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM2J,EAAAA,eAIlFpK,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAC9BF,EAAAA,EAAAA,KAAA,SAAO+J,QAAQ,aAAa9J,UAAU,kFAAiFC,SAAC,4BACxHC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACIM,KAAK,OACLL,UAAU,sBACVwC,KAAK,aACLpC,GAAG,aACHkH,MAAOjD,EAASO,WAChBmC,SAAUK,EACVgD,YAAY,+BAEhBrK,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mCAAkCC,UAACF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMyH,EAAAA,eAIlFlI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAC9BF,EAAAA,EAAAA,KAAA,SAAO+J,QAAQ,iBAAiB9J,UAAU,kFAAiFC,SAAC,2BAC5HC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACIM,KAAK,OACLL,UAAU,sBACVwC,KAAK,iBACLpC,GAAG,iBACHkH,MAAOjD,EAASQ,eAChBkC,SAAUK,EACVgD,YAAY,4BAEhBrK,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mCAAkCC,UAACF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMiK,EAAAA,eAIlF1K,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAC9BF,EAAAA,EAAAA,KAAA,SAAO+J,QAAQ,eAAe9J,UAAU,kFAAiFC,SAAC,wBAC1HC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACIM,KAAK,OACLL,UAAU,sBACVwC,KAAK,eACLpC,GAAG,eACHkH,MAAOjD,EAASS,aAChBiC,SAAUK,EACVgD,YAAY,mBAEhBrK,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mCAAkCC,UAACF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMkK,EAAAA,eAIlF3K,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAC9BF,EAAAA,EAAAA,KAAA,SAAO+J,QAAQ,eAAe9J,UAAU,kFAAiFC,SAAC,4BAC1HC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACIM,KAAK,OACLL,UAAU,sBACVwC,KAAK,eACLpC,GAAG,eACHkH,MAAOjD,EAASU,aAChBgC,SAAUK,EACVgD,YAAY,gBAEhBrK,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mCAAkCC,UAACF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMmK,EAAAA,eAIlF5K,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAC9BF,EAAAA,EAAAA,KAAA,SAAO+J,QAAQ,cAAc9J,UAAU,kFAAiFC,SAAC,qCACzHC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACIM,KAAK,OACLL,UAAU,sBACVwC,KAAK,cACLpC,GAAG,cACHkH,MAAOjD,EAASW,YAChB+B,SAAUK,EACVgD,YAAY,kBAEhBrK,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mCAAkCC,UAACF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMoK,EAAAA,eAIlF7K,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAC9BF,EAAAA,EAAAA,KAAA,SAAO+J,QAAQ,eAAe9J,UAAU,kFAAiFC,SAAC,yCAC1HC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACIM,KAAK,OACLL,UAAU,sBACVwC,KAAK,eACLpC,GAAG,eACHkH,MAAOjD,EAASY,aAChB8B,SAAUK,EACVgD,YAAY,mBAEhBrK,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mCAAkCC,UAACF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMqK,EAAAA,yBASlGjL,EAAAA,EAAAA,KAAA,OAAKC,UAAU,SAAQC,UACnBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACnDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC1DC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,YAAWC,SAAA,EACrBF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMyH,EAAAA,IAASpI,UAAU,SAAS,wDAI3DD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iBAAgBC,UAC3BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,UAASC,SAAA,EACpBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACrBF,EAAAA,EAAAA,KAAA,SAAO+J,QAAQ,WAAW9J,UAAU,kFAAiFC,SAAC,iCACtHC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0CAAyCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,kCAAiCC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA8B,8EAG/CE,EAAAA,EAAAA,MAAA,UACI+K,UAAQ,EACRjL,UAAU,uBACVwC,KAAK,WACLpC,GAAG,WACHkH,MAAOjD,EAAS6G,SAChBnE,SAAUK,EACVmD,MAAO,CAAEjH,OAAQ,SAAUrD,SAAA,EAE3BF,EAAAA,EAAAA,KAAA,UAAQuH,MAAM,mBAAkBrH,SAAC,oCACjCF,EAAAA,EAAAA,KAAA,UAAQuH,MAAM,mBAAkBrH,SAAC,oCACjCF,EAAAA,EAAAA,KAAA,UAAQuH,MAAM,mBAAkBrH,SAAC,gCACjCF,EAAAA,EAAAA,KAAA,UAAQuH,MAAM,mBAAkBrH,SAAC,iDAK7CC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACrBC,EAAAA,EAAAA,MAAA,SAAO4J,QAAQ,kBAAkB9J,UAAU,kFAAiFC,SAAA,EACxHF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMwK,EAAAA,IAAmBnL,UAAU,SAAS,sDAIjEE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0CAAyCC,SAAA,EACpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACtBF,EAAAA,EAAAA,KAAA,SACIqL,IAAK1F,EACLrF,KAAK,OACLL,UAAU,kBACVwC,KAAK,kBACL8E,MAAM,QAIdpH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,EAC7BF,EAAAA,EAAAA,KAAA,SACIM,KAAK,OACLL,UAAU,sBACVwC,KAAK,kBACLpC,GAAG,kBACHkH,MAAOjD,EAASgH,gBAChBtE,SAAUK,EACVkE,UAAQ,KAEZvL,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mCAAkCC,UAACF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAMwK,EAAAA,YAE9EjL,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA8B,iGAUvED,EAAAA,EAAAA,KAAA,OAAKC,UAAU,SAAQC,UACnBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6BAA4BC,UACvCC,EAAAA,EAAAA,MAAA,UAAQG,KAAK,SAASL,UAAU,gDAA+CC,SAAA,EAC3EF,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CAACC,KAAM4K,EAAAA,IAAevL,UAAU,SAAS,iDAUrFD,EAAAA,EAAAA,KAACuB,EAAAA,EAAM,WAGhB,C", "sources": ["components/Sidebar.js", "components/Footer.js", "components/Header.js", "pages/ShipmentAdd.js"], "sourcesContent": ["import React from 'react';\r\nimport { Link, useLocation } from 'react-router-dom';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport {\r\n    faDolly, faFolderPlus, faAnchor, faFlag<PERSON>he<PERSON>ed,\r\n    faUser, faStreetView, faCreditCard, faFileLines,\r\n    faBell, faDesktop\r\n} from '@fortawesome/free-solid-svg-icons';\r\n\r\nconst Sidebar = () => {\r\n    const location = useLocation();\r\n    \r\n    // Get user role from localStorage\r\n    const getUserRole = () => {\r\n        try {\r\n            const user = JSON.parse(localStorage.getItem('user'));\r\n            return user?.user?.role || user?.user?.gorev || 'user';\r\n        } catch {\r\n            return 'user';\r\n        }\r\n    };\r\n    \r\n    const userRole = getUserRole();\r\n    const isAdmin = userRole === 'admin';\r\n\r\n    return (\r\n        <div className=\"sidebar col-md-4 col-lg-2 p-0 bg-light-subtle\">\r\n            <div className=\"offcanvas-md offcanvas-end\" tabIndex=\"-1\" id=\"yanMenu\" aria-labelledby=\"yanMenu\">\r\n                <div className=\"offcanvas-header\">\r\n                    <h5 className=\"offcanvas-title\" id=\"yanMenu\">MGZ24 Gold</h5>\r\n                    <button type=\"button\" className=\"btn-close\" data-bs-dismiss=\"offcanvas\" data-bs-target=\"#yanMenu\" aria-label=\"Kapat\"></button>\r\n                </div>\r\n                <div className=\"offcanvas-body d-md-flex flex-column p-0 pt-lg-3 overflow-y-auto\">\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Sevkiyat İşlemleri</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/' ? 'active' : ''}`} to=\"/\">\r\n                                <FontAwesomeIcon icon={faDolly} />Aktif Sevkiyatlar\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/add' ? 'active' : ''}`} to=\"/add\">\r\n                                <FontAwesomeIcon icon={faFolderPlus} />Yeni Sevkiyat Oluştur\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/inactive-devices' ? 'active' : ''}`} to=\"/inactive-devices\">\r\n                                <FontAwesomeIcon icon={faAnchor} />İnaktif Cihazlar\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/history' ? 'active' : ''}`} to=\"/history\">\r\n                                <FontAwesomeIcon icon={faFlagCheckered} />Geçmiş Sevkiyatlar\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Sistem Yönetimi</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        {/* Cihaz Yönetimi - Sadece admin kullanıcılarına göster */}\r\n                        {isAdmin && (\r\n                            <li className=\"nav-item\">\r\n                                <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/devices' ? 'active' : ''}`} to=\"/devices\">\r\n                                    <FontAwesomeIcon icon={faDesktop} />Cihaz Yönetimi\r\n                                </Link>\r\n                            </li>\r\n                        )}\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/notifications' ? 'active' : ''}`} to=\"/notifications\">\r\n                                <FontAwesomeIcon icon={faBell} />Bildirimler\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Kullanıcı Ayarları</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/profile' ? 'active' : ''}`} to=\"/profile\">\r\n                                <FontAwesomeIcon icon={faUser} />Kullanıcı Profilim\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/viewers' ? 'active' : ''}`} to=\"/viewers\">\r\n                                <FontAwesomeIcon icon={faStreetView} />İzleyici İşlemleri\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase\">Ödeme ve Yapılandırma</h6>\r\n                    <ul className=\"nav nav-pills flex-column\">\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/payment' ? 'active' : ''}`} to=\"/payment\">\r\n                                <FontAwesomeIcon icon={faCreditCard} />Ödeme Yap\r\n                            </Link>\r\n                        </li>\r\n                        <li className=\"nav-item\">\r\n                            <Link className={`nav-link d-flex align-items-center gap-2 ${location.pathname === '/invoices' ? 'active' : ''}`} to=\"/invoices\">\r\n                                <FontAwesomeIcon icon={faFileLines} />Faturalarım\r\n                            </Link>\r\n                        </li>\r\n                    </ul>\r\n\r\n                    <hr className=\"my-3\" />\r\n                    <h6 className=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-body-secondary text-uppercase\">Kredi Durumu</h6>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Sidebar;", "import React from 'react';\r\n\r\nconst Footer = () => {\r\n    return (\r\n        <footer className=\"py-5 border-top\">\r\n            <p className=\"text-center\">&copy;2025 Inkatech Ölçüm Sistemleri</p>\r\n        </footer>\r\n    );\r\n};\r\n\r\nexport default Footer; ", "import React, { useState, useEffect } from 'react';\r\nimport { Link, useNavigate } from 'react-router-dom';\r\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\r\nimport { faRightFromBracket, faBars, faUser } from '@fortawesome/free-solid-svg-icons';\r\nimport logoDark from '../assets/img/logo.png';\r\nimport { kullaniciService } from '../api/dbService';\r\n\r\nconst Header = () => {\r\n    const navigate = useNavigate();\r\n    const [userName, setUserName] = useState('Misafir');\r\n    const [loading, setLoading] = useState(true);\r\n\r\n    // Kullanıcı bilgilerini veritabanından al\r\n    useEffect(() => {\r\n        const fetchUserData = async () => {\r\n            try {\r\n                // Local storage'dan kullanıcı bilgisini al\r\n                const storedUser = JSON.parse(localStorage.getItem('user'));\r\n                const userId = storedUser?.user?.musteri_ID || storedUser?.user?.id;\r\n                const userName = storedUser?.user?.name || storedUser?.user?.musteri_adi;\r\n\r\n                if (!userId) {\r\n                    console.warn('Oturum bilgisi bulunamadı');\r\n                    setUserName('Misafir');\r\n                    setLoading(false);\r\n                    return;\r\n                }\r\n\r\n                // Eğer localStorage'da isim varsa direkt kullan\r\n                if (userName) {\r\n                    setUserName(userName);\r\n                    setLoading(false);\r\n                    return;\r\n                }\r\n\r\n                // API'den kullanıcı bilgilerini getir (backend çalışıyorsa)\r\n                try {\r\n                    const userData = await kullaniciService.getKullanici(userId);\r\n\r\n                    // musteri_adi alanını kullan\r\n                    if (userData && userData.musteri_adi) {\r\n                        setUserName(userData.musteri_adi);\r\n\r\n                        // localStorage'ı güncelle\r\n                        if (storedUser?.user) {\r\n                            storedUser.user.name = userData.musteri_adi;\r\n                            localStorage.setItem('user', JSON.stringify(storedUser));\r\n                        }\r\n                    }\r\n                } catch (apiError) {\r\n                    console.warn('API\\'den kullanıcı bilgisi alınamadı, localStorage kullanılıyor');\r\n                    // API hatası durumunda localStorage'daki adı kullan\r\n                    setUserName(userName || 'Kullanıcı');\r\n                }\r\n            } catch (error) {\r\n                console.error('Kullanıcı bilgileri alınırken hata:', error);\r\n                // Hata durumunda varsayılan isim kullan\r\n                setUserName('Kullanıcı');\r\n            } finally {\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        fetchUserData();\r\n    }, []);\r\n\r\n    // Çıkış işlemi\r\n    const handleLogout = () => {\r\n        // Local storage'dan kullanıcı bilgilerini temizle\r\n        localStorage.removeItem('user');\r\n\r\n        // Login sayfasına yönlendir\r\n        navigate('/login');\r\n    };\r\n\r\n    return (\r\n        <header className=\"navbar sticky-top bg-darkblue flex-md-nowrap p-0 shadow\" data-bs-theme=\"dark\">\r\n            <Link className=\"navbar-brand col-md-4 col-lg-2 me-0 px-3 py-2 fs-6 text-white\" to=\"/\">\r\n                <img src={logoDark} alt=\"MGZ24 Logo\" height=\"40\" />\r\n            </Link>\r\n            <ul className=\"navbar-nav flex-row align-items-center\">\r\n                <li className=\"nav-item text-nowrap d-none d-md-block me-3\">\r\n                    <span className=\"text-white\">\r\n                        <FontAwesomeIcon icon={faUser} className=\"me-2\" />\r\n                        {loading ? 'Yükleniyor...' : userName}\r\n                    </span>\r\n                </li>\r\n                <li className=\"nav-item text-nowrap px-1\">\r\n                    <button\r\n                        onClick={handleLogout}\r\n                        className=\"nav-link px-3\"\r\n                        data-bs-toggle=\"tooltip\"\r\n                        data-bs-placement=\"left\"\r\n                        data-bs-title=\"Çıkış yap!\"\r\n                    >\r\n                        <FontAwesomeIcon icon={faRightFromBracket} />\r\n                    </button>\r\n                </li>\r\n                <li className=\"nav-item text-nowrap d-md-none\">\r\n                    <button\r\n                        className=\"nav-link px-3\"\r\n                        type=\"button\"\r\n                        data-bs-toggle=\"offcanvas\"\r\n                        data-bs-target=\"#yanMenu\"\r\n                        aria-controls=\"yanMenu\"\r\n                        aria-expanded=\"false\"\r\n                        aria-label=\"menu aç/kapa\"\r\n                    >\r\n                        <FontAwesomeIcon icon={faBars} />\r\n                    </button>\r\n                </li>\r\n            </ul>\r\n        </header>\r\n    );\r\n};\r\n\r\nexport default Header; ", "import React, { useState, useEffect, useRef } from 'react';\nimport { FontAwesomeIcon } from '@fortawesome/react-fontawesome';\nimport { faCircleCheck } from '@fortawesome/free-regular-svg-icons';\nimport {\n    faTruck, faLocationDot, faIndustry, faPhoneVolume,\n    faLeaf, faPallet, faWeightScale, faWeightHanging,\n    faUsers, faTemperatureHalf, faIdCard,\n    faArrowRight, faPlus, faMicrochip, faBattery2, faCoins\n} from '@fortawesome/free-solid-svg-icons';\n\n// Statik CSS import\nimport '../styles/ionRangeSlider.css';\n\nimport Header from '../components/Header';\nimport Sidebar from '../components/Sidebar';\nimport Footer from '../components/Footer';\nimport { cihazIDService } from '../api/dbService';\n\nconst ShipmentAdd = () => {\n    // Aktif kullanıcı bilgisini al\n    const [activeUser, setActiveUser] = useState(null);\n\n    // İnaktif cihazlar listesi\n    const [availableDevices, setAvailableDevices] = useState([]);\n    const [loadingDevices, setLoadingDevices] = useState(false);\n    const [selectedDevice, setSelectedDevice] = useState(null);\n\n    // Form başlangıç durumunu tanımla (backend database şemasına uygun)\n    const [formData, setFormData] = useState({\n        mgz24_kodu: '',\n        sevkiyat_adi: '',\n        plaka_no: '',\n        cikis_lokasyon: '',\n        varis_lokasyon: '',\n        surucu_adi: '',\n        surucu_telefon: '',\n        urun_bilgisi: '',\n        palet_sayisi: '',\n        net_agirlik: '',\n        brut_agirlik: '',\n        sicaklik_araligi: '2°C - 8°C',\n        musteri_ID: '', // Kullanıcı ID'si için alan ekledik\n        cihaz_id: '', // Seçilen cihaz ID'si\n        gonderen_firma_id: '',\n        alici_firma_id: ''\n    });\n\n    const [tempRange, setTempRange] = useState({\n        min: 2,\n        max: 8\n    });\n\n    const sliderRef = useRef(null);\n    const sliderInstance = useRef(null);\n\n    // Kullanıcı bilgilerini localStorage'dan al ve inaktif cihazları yükle\n    useEffect(() => {\n        const user = JSON.parse(localStorage.getItem('user'));\n        if (user) {\n            setActiveUser(user);\n            const userId = user?.user?.musteri_ID || user?.user?.id || user?.id;\n\n            // Kullanıcı ID'sini formData'ya ekle\n            setFormData(prevState => ({\n                ...prevState,\n                musteri_ID: userId\n            }));\n\n            // İnaktif cihazları yükle\n            loadAvailableDevices(userId);\n        }\n    }, []);\n\n    // Kullanılabilir cihazları yükle\n    const loadAvailableDevices = async (userId) => {\n        try {\n            setLoadingDevices(true);\n            const devices = await cihazIDService.getInaktifCihazlar(userId);\n\n            // Tüm inaktif cihazları göster (filtreleme kaldırıldı)\n            setAvailableDevices(devices);\n        } catch (error) {\n            console.error('Cihazlar yüklenirken hata:', error);\n            setAvailableDevices([]);\n        } finally {\n            setLoadingDevices(false);\n        }\n    };\n\n    // Cihaz seçimi\n    const handleDeviceSelect = (device) => {\n        setSelectedDevice(device);\n        setFormData(prevState => ({\n            ...prevState,\n            cihaz_id: device.CihazID,\n            mgz24_kodu: device.CihazID // MGZ24 kodunu da otomatik doldur\n        }));\n    };\n\n    // IonRangeSlider'ı başlat\n    useEffect(() => {\n        if (sliderRef.current && window.jQuery) {\n            const $ = window.jQuery;\n            const $slider = $(sliderRef.current);\n\n            // IonRangeSlider'ı başlat\n            $slider.ionRangeSlider({\n                type: 'double',\n                grid: true,\n                min: -25,\n                max: 35,\n                from: tempRange.min,\n                to: tempRange.max,\n                step: 0.5,\n                postfix: ' °C',\n                prettify_enabled: true,\n                skin: \"flat\",\n                onChange: (data) => {\n                    setTempRange({\n                        min: data.from,\n                        max: data.to\n                    });\n                    setFormData(prevData => ({\n                        ...prevData,\n                        sicaklik_araligi: `${data.from}°C - ${data.to}°C`\n                    }));\n                }\n            });\n\n            // Slider'ı sakla\n            sliderInstance.current = $slider.data(\"ionRangeSlider\");\n\n            // Temizlik fonksiyonu\n            return () => {\n                if (sliderInstance.current) {\n                    sliderInstance.current.destroy();\n                }\n            };\n        }\n    }, []);\n\n    // tempRange değiştiğinde formData'yı güncelle\n    useEffect(() => {\n        setFormData(prev => ({\n            ...prev,\n            sicaklik_araligi: `${tempRange.min}°C - ${tempRange.max}°C`\n        }));\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [tempRange]);\n\n    const handleChange = (e) => {\n        const { name, value, type, options } = e.target;\n\n        if (type === 'select-multiple') {\n            const selectedOptions = Array.from(options)\n                .filter(option => option.selected)\n                .map(option => option.value);\n\n            setFormData({\n                ...formData,\n                [name]: selectedOptions\n            });\n        } else {\n            // MGZ24 kodu seçildiğinde cihaz_id alanını da doldur\n            if (name === 'mgz24_kodu' && value) {\n                const selectedDevice = availableDevices.find(device => device.CihazID === value);\n                setFormData({\n                    ...formData,\n                    [name]: value,\n                    cihaz_id: selectedDevice ? selectedDevice.CihazID : ''\n                });\n            } else {\n                setFormData({\n                    ...formData,\n                    [name]: value\n                });\n            }\n        }\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n\n        // Kullanıcı bilgisi yoksa uyarı ver\n        if (!formData.musteri_ID) {\n            alert('Oturum bilgileriniz bulunamadı. Lütfen yeniden giriş yapın.');\n            return;\n        }\n\n        // İnaktif cihaz kontrolü\n        if (availableDevices.length === 0) {\n            alert('Kullanılabilir inaktif cihaz bulunamadı. Sevkiyat oluşturulamaz.');\n            return;\n        }\n\n        // MGZ24 kodu seçilmiş mi kontrol et\n        if (!formData.mgz24_kodu) {\n            alert('Lütfen bir MGZ24 cihazı seçiniz.');\n            return;\n        }\n\n        console.log('Sevkiyat Bilgileri:', formData);\n\n        try {\n            // Form verileri zaten database şemasına uygun alan adlarında\n            const apiData = {\n                ...formData,\n                // Boş alanları null olarak gönder\n                mgz24_kodu: formData.mgz24_kodu || null,\n                surucu_adi: formData.surucu_adi || null,\n                surucu_telefon: formData.surucu_telefon || null,\n                urun_bilgisi: formData.urun_bilgisi || null,\n                palet_sayisi: formData.palet_sayisi || null,\n                net_agirlik: formData.net_agirlik || null,\n                brut_agirlik: formData.brut_agirlik || null,\n                sicaklik_araligi: formData.sicaklik_araligi || null,\n                gonderen_firma_id: formData.gonderen_firma_id || null,\n                alici_firma_id: formData.alici_firma_id || null\n            };\n\n            // Servisi kullanarak API'ye verileri gönder\n            const response = await fetch(`${process.env.REACT_APP_API_URL || '//mgz24.com/api'}/sevkiyatlar`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                },\n                body: JSON.stringify(apiData)\n            });\n\n            const data = await response.json();\n\n            if (!response.ok) {\n                console.error('API Response Error:', data);\n\n                // Validation hatalarını detaylı göster\n                if (data.errors && data.errors.length > 0) {\n                    const errorMessages = data.errors.map(err =>\n                        `${err.field}: ${err.message}`\n                    ).join('\\n');\n\n                    throw new Error(`Validation Hataları:\\n${errorMessages}\\n\\n${data.details || ''}`);\n                }\n\n                throw new Error(data.error || data.message || 'Sevkiyat eklenirken bir hata oluştu');\n            }\n\n            alert('Sevkiyat başarıyla oluşturuldu!');\n            // İşlem başarılı olduğunda gerekirse yönlendirme yapılabilir\n            // window.location.href = '/';\n        } catch (error) {\n            console.error('Sevkiyat eklenirken hata:', error);\n\n            // Hata mesajını alert ile göster (uzun hatalar için scrollable)\n            const errorMsg = error.message.length > 200\n                ? error.message.substring(0, 200) + '...\\n\\nDetaylar için konsolu kontrol edin.'\n                : error.message;\n\n            alert(`Hata: ${errorMsg}`);\n        }\n    };\n\n    return (\n        <>\n            <Header />\n            <div className=\"container-fluid\">\n                <div className=\"row\">\n                    <Sidebar />\n\n                    {/* main sütun */}\n                    <main className=\"col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white\">\n                        <div className=\"pt-4 pb-2 mt-3 mb-4\">\n                            <div className=\"d-flex justify-content-between align-items-center mb-2\">\n                                <h1 className=\"h3 text-primary\">\n                                    <FontAwesomeIcon icon={faPlus} className=\"me-2\" />\n                                    Yeni Sevkiyat Oluştur\n                                </h1>\n                                {activeUser && (\n                                    <div className=\"badge bg-light text-dark border py-2 px-3 d-flex align-items-center\">\n                                        <FontAwesomeIcon icon={faUsers} className=\"me-2\" />\n                                        <div>\n                                            <small className=\"d-block text-muted\">Müşteri</small>\n                                            <strong>{activeUser.name}</strong> <small className=\"text-muted\">#{activeUser.id}</small>\n                                        </div>\n                                    </div>\n                                )}\n                            </div>\n                            <p className=\"text-muted\">Yeni bir sevkiyat kaydı oluşturmak için aşağıdaki formu doldurun.</p>\n                        </div>\n\n                        {/* sevkiyat bilgi */}\n                        <div className=\"row mb-4\">\n                            <div className=\"col-12\">\n                                <form className=\"row g-4\" onSubmit={handleSubmit}>\n                                    {/* Temel Sevkiyat Bilgileri Bölümü */}\n                                    <div className=\"col-12\">\n                                        <div className=\"card shadow-sm border-0 rounded-3 mb-4\">\n                                            <div className=\"card-header bg-primary bg-opacity-10 border-0\">\n                                                <h5 className=\"mb-0 py-2\">\n                                                    <FontAwesomeIcon icon={faIdCard} className=\"me-2\" />\n                                                    Temel Sevkiyat Bilgileri\n                                                </h5>\n                                            </div>\n                                            <div className=\"card-body pt-4\">\n                                                <div className=\"row g-4\">\n                                                    <div className=\"col-lg-3\">\n                                                        <label htmlFor=\"mgz24_kodu\" className=\"form-label small text-uppercase fw-bold mb-1 text-secondary bg-transparent ps-0\">MGZ24 Kodu*</label>\n                                                        <div className=\"input-group shadow-sm\">\n                                                            <select\n                                                                className=\"form-select border\"\n                                                                name=\"mgz24_kodu\"\n                                                                id=\"mgz24_kodu\"\n                                                                value={formData.mgz24_kodu}\n                                                                onChange={handleChange}\n                                                                required\n                                                                disabled={loadingDevices || availableDevices.length === 0}\n                                                            >\n                                                                <option value=\"\">\n                                                                    {loadingDevices\n                                                                        ? 'Cihazlar yükleniyor...'\n                                                                        : availableDevices.length === 0\n                                                                            ? 'Kullanılabilir cihaz yok'\n                                                                            : 'Cihaz seçiniz'\n                                                                    }\n                                                                </option>\n                                                                {availableDevices.map((device) => (\n                                                                    <option key={device.ID} value={device.CihazID}>\n                                                                        {device.CihazID} - {device.kredi_gun}\n                                                                    </option>\n                                                                ))}\n                                                            </select>\n                                                            <span className=\"input-group-text bg-white border\">\n                                                                <FontAwesomeIcon icon={faIdCard} />\n                                                            </span>\n                                                        </div>\n                                                        {availableDevices.length === 0 && !loadingDevices && (\n                                                            <div className=\"form-text text-danger\">\n                                                                <FontAwesomeIcon icon={faMicrochip} className=\"me-1\" />\n                                                                Kullanılabilir inaktif cihaz bulunamadı. Sevkiyat oluşturulamaz.\n                                                            </div>\n                                                        )}\n                                                    </div>\n\n                                                    <div className=\"col-lg-6\">\n                                                        <label htmlFor=\"sevkiyat_adi\" className=\"form-label small text-uppercase fw-bold mb-1 text-secondary bg-transparent ps-0\">Sevkiyat Adı*</label>\n                                                        <div className=\"input-group shadow-sm\">\n                                                            <input\n                                                                type=\"text\"\n                                                                className=\"form-control border\"\n                                                                name=\"sevkiyat_adi\"\n                                                                id=\"sevkiyat_adi\"\n                                                                value={formData.sevkiyat_adi}\n                                                                onChange={handleChange}\n                                                                required\n                                                                placeholder=\"Örn: Moldova Sevkiyatı\"\n                                                            />\n                                                            <span className=\"input-group-text bg-white border\"><FontAwesomeIcon icon={faTruck} /></span>\n                                                        </div>\n                                                    </div>\n\n                                                    <div className=\"col-lg-3\">\n                                                        <label htmlFor=\"plaka_no\" className=\"form-label small text-uppercase fw-bold mb-1 text-secondary bg-transparent ps-0\">Plaka no*</label>\n                                                        <div className=\"input-group shadow-sm\">\n                                                            <input\n                                                                type=\"text\"\n                                                                className=\"form-control border\"\n                                                                name=\"plaka_no\"\n                                                                id=\"plaka_no\"\n                                                                value={formData.plaka_no}\n                                                                onChange={handleChange}\n                                                                required\n                                                                placeholder=\"Örn: 34 AB 1234\"\n                                                            />\n                                                            <span className=\"input-group-text bg-white border\"><FontAwesomeIcon icon={faTruck} /></span>\n                                                        </div>\n                                                    </div>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </div>\n\n                                    {/* Nakliye ve Ürün Detayları Bölümü */}\n                                    <div className=\"col-12\">\n                                        <div className=\"card shadow-sm border-0 rounded-3 mb-4\">\n                                            <div className=\"card-header bg-primary bg-opacity-10 border-0\">\n                                                <h5 className=\"mb-0 py-2\">\n                                                    <FontAwesomeIcon icon={faLocationDot} className=\"me-2\" />\n                                                    Nakliye ve Ürün Detayları\n                                                </h5>\n                                            </div>\n                                            <div className=\"card-body pt-4\">\n                                                <div className=\"row g-4\">\n                                                    <div className=\"col-md-6 col-lg-3\">\n                                                        <label htmlFor=\"cikis_lokasyon\" className=\"form-label small text-uppercase fw-bold mb-1 text-secondary bg-transparent ps-0\">Çıkış Yeri*</label>\n                                                        <div className=\"input-group shadow-sm\">\n                                                            <input\n                                                                type=\"text\"\n                                                                className=\"form-control border\"\n                                                                name=\"cikis_lokasyon\"\n                                                                id=\"cikis_lokasyon\"\n                                                                value={formData.cikis_lokasyon}\n                                                                onChange={handleChange}\n                                                                required\n                                                                placeholder=\"Örn: İstanbul\"\n                                                            />\n                                                            <span className=\"input-group-text bg-white border\"><FontAwesomeIcon icon={faLocationDot} /></span>\n                                                        </div>\n                                                    </div>\n\n                                                    <div className=\"col-md-6 col-lg-3\">\n                                                        <div className=\"position-relative\">\n                                                            <div className=\"d-none d-lg-block position-absolute\" style={{ top: \"20px\", left: \"-20px\", zIndex: \"1\" }}>\n                                                                <FontAwesomeIcon icon={faArrowRight} className=\"text-primary\" />\n                                                            </div>\n                                                        </div>\n                                                        <label htmlFor=\"varis_lokasyon\" className=\"form-label small text-uppercase fw-bold mb-1 text-secondary bg-transparent ps-0\">Varış Yeri*</label>\n                                                        <div className=\"input-group shadow-sm\">\n                                                            <input\n                                                                type=\"text\"\n                                                                className=\"form-control border\"\n                                                                name=\"varis_lokasyon\"\n                                                                id=\"varis_lokasyon\"\n                                                                value={formData.varis_lokasyon}\n                                                                onChange={handleChange}\n                                                                required\n                                                                placeholder=\"Örn: Ankara\"\n                                                            />\n                                                            <span className=\"input-group-text bg-white border\"><FontAwesomeIcon icon={faLocationDot} /></span>\n                                                        </div>\n                                                    </div>\n\n                                                    <div className=\"col-md-6 col-lg-3\">\n                                                        <label htmlFor=\"surucu_adi\" className=\"form-label small text-uppercase fw-bold mb-1 text-secondary bg-transparent ps-0\">Şoför Adı</label>\n                                                        <div className=\"input-group shadow-sm\">\n                                                            <input\n                                                                type=\"text\"\n                                                                className=\"form-control border\"\n                                                                name=\"surucu_adi\"\n                                                                id=\"surucu_adi\"\n                                                                value={formData.surucu_adi}\n                                                                onChange={handleChange}\n                                                                placeholder=\"Örn: Ahmet Yılmaz\"\n                                                            />\n                                                            <span className=\"input-group-text bg-white border\"><FontAwesomeIcon icon={faUsers} /></span>\n                                                        </div>\n                                                    </div>\n\n                                                    <div className=\"col-md-6 col-lg-3\">\n                                                        <label htmlFor=\"surucu_telefon\" className=\"form-label small text-uppercase fw-bold mb-1 text-secondary bg-transparent ps-0\">Şoför Telefon</label>\n                                                        <div className=\"input-group shadow-sm\">\n                                                            <input\n                                                                type=\"text\"\n                                                                className=\"form-control border\"\n                                                                name=\"surucu_telefon\"\n                                                                id=\"surucu_telefon\"\n                                                                value={formData.surucu_telefon}\n                                                                onChange={handleChange}\n                                                                placeholder=\"Örn: 05XX XXX XX XX\"\n                                                            />\n                                                            <span className=\"input-group-text bg-white border\"><FontAwesomeIcon icon={faPhoneVolume} /></span>\n                                                        </div>\n                                                    </div>\n\n                                                    <div className=\"col-md-6 col-lg-3\">\n                                                        <label htmlFor=\"urun_bilgisi\" className=\"form-label small text-uppercase fw-bold mb-1 text-secondary bg-transparent ps-0\">Ürün Bilgisi</label>\n                                                        <div className=\"input-group shadow-sm\">\n                                                            <input\n                                                                type=\"text\"\n                                                                className=\"form-control border\"\n                                                                name=\"urun_bilgisi\"\n                                                                id=\"urun_bilgisi\"\n                                                                value={formData.urun_bilgisi}\n                                                                onChange={handleChange}\n                                                                placeholder=\"Örn: Limon\"\n                                                            />\n                                                            <span className=\"input-group-text bg-white border\"><FontAwesomeIcon icon={faLeaf} /></span>\n                                                        </div>\n                                                    </div>\n\n                                                    <div className=\"col-md-6 col-lg-3\">\n                                                        <label htmlFor=\"palet_sayisi\" className=\"form-label small text-uppercase fw-bold mb-1 text-secondary bg-transparent ps-0\">Palet Sayısı</label>\n                                                        <div className=\"input-group shadow-sm\">\n                                                            <input\n                                                                type=\"text\"\n                                                                className=\"form-control border\"\n                                                                name=\"palet_sayisi\"\n                                                                id=\"palet_sayisi\"\n                                                                value={formData.palet_sayisi}\n                                                                onChange={handleChange}\n                                                                placeholder=\"Örn: 46\"\n                                                            />\n                                                            <span className=\"input-group-text bg-white border\"><FontAwesomeIcon icon={faPallet} /></span>\n                                                        </div>\n                                                    </div>\n\n                                                    <div className=\"col-md-6 col-lg-3\">\n                                                        <label htmlFor=\"net_agirlik\" className=\"form-label small text-uppercase fw-bold mb-1 text-secondary bg-transparent ps-0\">Net Ağırlık (kg)</label>\n                                                        <div className=\"input-group shadow-sm\">\n                                                            <input\n                                                                type=\"text\"\n                                                                className=\"form-control border\"\n                                                                name=\"net_agirlik\"\n                                                                id=\"net_agirlik\"\n                                                                value={formData.net_agirlik}\n                                                                onChange={handleChange}\n                                                                placeholder=\"Örn: 8932\"\n                                                            />\n                                                            <span className=\"input-group-text bg-white border\"><FontAwesomeIcon icon={faWeightScale} /></span>\n                                                        </div>\n                                                    </div>\n\n                                                    <div className=\"col-md-6 col-lg-3\">\n                                                        <label htmlFor=\"brut_agirlik\" className=\"form-label small text-uppercase fw-bold mb-1 text-secondary bg-transparent ps-0\">Brüt Ağırlık (kg)</label>\n                                                        <div className=\"input-group shadow-sm\">\n                                                            <input\n                                                                type=\"text\"\n                                                                className=\"form-control border\"\n                                                                name=\"brut_agirlik\"\n                                                                id=\"brut_agirlik\"\n                                                                value={formData.brut_agirlik}\n                                                                onChange={handleChange}\n                                                                placeholder=\"Örn: 11061\"\n                                                            />\n                                                            <span className=\"input-group-text bg-white border\"><FontAwesomeIcon icon={faWeightHanging} /></span>\n                                                        </div>\n                                                    </div>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </div>\n\n                                    {/* İzleme ve Sıcaklık Ayarları Bölümü */}\n                                    <div className=\"col-12\">\n                                        <div className=\"card shadow-sm border-0 rounded-3 mb-4\">\n                                            <div className=\"card-header bg-primary bg-opacity-10 border-0\">\n                                                <h5 className=\"mb-0 py-2\">\n                                                    <FontAwesomeIcon icon={faUsers} className=\"me-2\" />\n                                                    İzleme ve Sıcaklık Ayarları\n                                                </h5>\n                                            </div>\n                                            <div className=\"card-body pt-4\">\n                                                <div className=\"row g-4\">\n                                                    <div className=\"col-lg-6\">\n                                                        <label htmlFor=\"izleyici\" className=\"form-label small text-uppercase fw-bold mb-1 text-secondary bg-transparent ps-0\">Sevkiyat izleme yetkilileri</label>\n                                                        <div className=\"shadow-sm border rounded-3 p-1 bg-light\">\n                                                            <p className=\"text-muted small mb-2 mt-2 px-2\">\n                                                                <i className=\"fas fa-info-circle me-1\"></i>\n                                                                CTRL tuşuna basık tutup birden fazla seçim yapabilirsiniz.\n                                                            </p>\n                                                            <select\n                                                                multiple\n                                                                className=\"form-select border-0\"\n                                                                name=\"izleyici\"\n                                                                id=\"izleyici\"\n                                                                value={formData.izleyici}\n                                                                onChange={handleChange}\n                                                                style={{ height: \"150px\" }}\n                                                            >\n                                                                <option value=\"<EMAIL>\"><EMAIL> (CCL markets)</option>\n                                                                <option value=\"<EMAIL>\"><EMAIL> (CCL markets)</option>\n                                                                <option value=\"<EMAIL>\"><EMAIL> (Nakliye)</option>\n                                                                <option value=\"<EMAIL>\"><EMAIL> (Müşteri)</option>\n                                                            </select>\n                                                        </div>\n                                                    </div>\n\n                                                    <div className=\"col-lg-6\">\n                                                        <label htmlFor=\"sicaklik_aralik\" className=\"form-label small text-uppercase fw-bold mb-1 text-secondary bg-transparent ps-0\">\n                                                            <FontAwesomeIcon icon={faTemperatureHalf} className=\"me-2\" />\n                                                            Sıcaklık limit aralığı\n                                                        </label>\n\n                                                        <div className=\"shadow-sm border rounded-3 p-4 bg-light\">\n                                                            <div className=\"mb-4 mt-2\">\n                                                                <input\n                                                                    ref={sliderRef}\n                                                                    type=\"text\"\n                                                                    className=\"js-range-slider\"\n                                                                    name=\"sicaklik_slider\"\n                                                                    value=\"\"\n                                                                />\n                                                            </div>\n\n                                                            <div className=\"input-group mt-3\">\n                                                                <input\n                                                                    type=\"text\"\n                                                                    className=\"form-control border\"\n                                                                    name=\"sicaklik_aralik\"\n                                                                    id=\"sicaklik_aralik\"\n                                                                    value={formData.sicaklik_aralik}\n                                                                    onChange={handleChange}\n                                                                    readOnly\n                                                                />\n                                                                <span className=\"input-group-text bg-white border\"><FontAwesomeIcon icon={faTemperatureHalf} /></span>\n                                                            </div>\n                                                            <small className=\"form-text text-muted d-block mt-2\">\n                                                                <i className=\"fas fa-info-circle me-1\"></i>\n                                                                Önerilen değer: 2°C - 8°C (Gıda ürünleri için)\n                                                            </small>\n                                                        </div>\n                                                    </div>\n                                                </div>\n                                            </div>\n                                        </div>\n                                    </div>\n\n                                    <div className=\"col-12\">\n                                        <div className=\"d-flex justify-content-end\">\n                                            <button type=\"submit\" className=\"btn btn-lg btn-primary px-5 fw-bold shadow-sm\">\n                                                <FontAwesomeIcon icon={faCircleCheck} className=\"me-2\" /> Sevkiyatı oluştur\n                                            </button>\n                                        </div>\n                                    </div>\n                                </form>\n                            </div>\n                        </div>\n                    </main>\n                    {/* /main sütun */}\n\n                    <Footer />\n                </div>\n            </div>\n        </>\n    );\n};\n\nexport default ShipmentAdd; "], "names": ["Sidebar", "location", "useLocation", "isAdmin", "getUserRole", "_user$user", "_user$user2", "user", "JSON", "parse", "localStorage", "getItem", "role", "gorev", "_unused", "_jsx", "className", "children", "_jsxs", "tabIndex", "id", "type", "Link", "concat", "pathname", "to", "FontAwesomeIcon", "icon", "faDolly", "faFolderPlus", "faAnchor", "faFlag<PERSON><PERSON><PERSON><PERSON>", "faDesktop", "faBell", "faUser", "faStreetView", "faCreditCard", "faFileLines", "Footer", "Header", "navigate", "useNavigate", "userName", "setUserName", "useState", "loading", "setLoading", "useEffect", "async", "_storedUser$user", "_storedUser$user2", "_storedUser$user3", "_storedUser$user4", "storedUser", "userId", "musteri_ID", "name", "musteri_adi", "console", "warn", "userData", "kullaniciService", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setItem", "stringify", "apiError", "error", "fetchUserData", "src", "alt", "height", "onClick", "handleLogout", "removeItem", "faRightFromBracket", "faBars", "ShipmentAdd", "activeUser", "setActiveUser", "availableDevices", "setAvailableDevices", "loadingDevices", "setLoadingDevices", "selected<PERSON><PERSON><PERSON>", "setSelectedDevice", "formData", "setFormData", "mgz24_kodu", "sevkiyat_adi", "plaka_no", "cikis_lokasyon", "varis_lokasyon", "surucu_adi", "surucu_telefon", "u<PERSON>_bilgisi", "palet_sayisi", "net_agirlik", "brut_agirlik", "sicaklik_araligi", "cihaz_id", "gonderen_firma_id", "alici_firma_id", "tempRange", "setTempRange", "min", "max", "sliderRef", "useRef", "sliderInstance", "prevState", "_objectSpread", "loadAvailableDevices", "devices", "cihazIDService", "getInaktifCihazlar", "current", "window", "j<PERSON><PERSON><PERSON>", "$slider", "$", "ionRangeSlider", "grid", "from", "step", "postfix", "prettify_enabled", "skin", "onChange", "data", "prevData", "destroy", "prev", "handleChange", "e", "value", "options", "target", "selectedOptions", "Array", "filter", "option", "selected", "map", "find", "device", "CihazID", "_Fragment", "faPlus", "faUsers", "onSubmit", "preventDefault", "length", "log", "apiData", "response", "fetch", "process", "method", "headers", "body", "json", "ok", "errors", "errorMessages", "err", "field", "message", "join", "Error", "details", "alert", "errorMsg", "substring", "faIdCard", "htmlFor", "required", "disabled", "kredi_gun", "ID", "faMicrochip", "placeholder", "faTruck", "faLocationDot", "style", "top", "left", "zIndex", "faArrowRight", "faPhoneVolume", "faLeaf", "faPallet", "faWeightScale", "faWeightHanging", "multiple", "<PERSON><PERSON><PERSON><PERSON>", "faTemperatureHalf", "ref", "sicaklik_aralik", "readOnly", "faCircleCheck"], "sourceRoot": ""}