import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faThermometerHalf, faEye, faBatteryHalf, faMapMarkerAlt, faCheck, faSync, faDroplet } from '@fortawesome/free-solid-svg-icons';
import { faLightbulb } from '@fortawesome/free-regular-svg-icons';

const RealTimeDeviceData = ({ cihazID, dateRange = {} }) => {
    const [deviceData, setDeviceData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [lastUpdate, setLastUpdate] = useState(null);

    // Cihaz verilerini API'den getir
    const fetchDeviceData = async () => {
        if (!cihazID) {
            setError('Cihaz ID bulunamadı');
            setLoading(false);
            return;
        }

        try {
            setError(null);
            const response = await fetch(`https://ffl21.fun:3001/api/cihaz/${cihazID}`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            
            if (data.success && data.data) {
                // API'den gelen veriyi işle - sonSensorler objesi içindeki verileri kullan
                const processedData = {
                    ...data.data,
                    sicaklik: data.data.sonSensorler?.sicaklik || data.data.sicaklik,
                    nem: data.data.sonSensorler?.nem || data.data.nem,
                    pil: data.data.sonSensorler?.pil || data.data.pil,
                    isik: data.data.sonSensorler?.isik || data.data.isik,
                    basinc: data.data.sonSensorler?.basinc || data.data.basinc
                };
                setDeviceData(processedData);
                setLastUpdate(new Date().toLocaleTimeString());
            } else {
                throw new Error(data.message || 'Cihaz verisi alınamadı');
            }
        } catch (err) {
            console.error('Cihaz verisi getirme hatası:', err);
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    // İlk yükleme ve cihazID değiştiğinde veri getir
    useEffect(() => {
        fetchDeviceData();
    }, [cihazID]);

    // Otomatik güncelleme kaldırıldı - sadece manuel yenileme

    // Sıcaklık renk belirleme
    const getTemperatureColor = (temp) => {
        const temperature = parseFloat(temp);
        if (temperature < 0) return 'text-primary';
        if (temperature < 15) return 'text-info';
        if (temperature > 30) return 'text-danger';
        return 'text-success';
    };

    // Nem renk belirleme
    const getHumidityColor = (humidity) => {
        const hum = parseFloat(humidity);
        if (hum > 80) return 'text-danger';
        if (hum > 60) return 'text-warning';
        return 'text-success';
    };

    // Pil voltajını yüzdeye çevirme (2.5V = %30, 3.4V = %100)
    const calculateBatteryPercentage = (voltage) => {
        const volt = parseFloat(voltage);
        if (isNaN(volt)) return 0;
        
        const minVoltage = 2.5; // %30
        const maxVoltage = 3.4; // %100
        const minPercentage = 30;
        const maxPercentage = 100;
        
        // Doğrusal interpolasyon
        const percentage = minPercentage + ((volt - minVoltage) / (maxVoltage - minVoltage)) * (maxPercentage - minPercentage);
        
        // Yüzdeyi 30-100 arasında sınırla
        return Math.round(Math.max(minPercentage, Math.min(maxPercentage, percentage)));
    };

    // Pil renk belirleme (yüzdeye göre)
    const getBatteryColor = (batteryPercentage) => {
        const bat = parseFloat(batteryPercentage);
        if (bat < 35) return 'text-danger';
        if (bat < 60) return 'text-warning';
        return 'text-success';
    };

    if (!cihazID) {
        return null;
    }

    if (loading) {
        return (
            <div className="row mb-4">
                <div className="col-12">
                    <div className="card border-0 shadow-sm">
                        <div className="card-body text-center py-4">
                            <div className="spinner-border text-primary" role="status">
                                <span className="visually-hidden">Cihaz verileri yükleniyor...</span>
                            </div>
                            <div className="mt-2">Cihaz verileri yükleniyor...</div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="row mb-4">
                <div className="col-12">
                    <div className="card border-0 shadow-sm">
                        <div className="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                            <h6 className="mb-0">
                                <FontAwesomeIcon icon={faThermometerHalf} className="me-2" />
                                Cihaz Verileri - {cihazID}
                            </h6>
                            <div className="d-flex gap-2">
                                <span className="badge bg-danger">Hata</span>
                                <button 
                                    className="btn btn-sm btn-outline-dark"
                                    onClick={fetchDeviceData}
                                    title="Yeniden Dene"
                                >
                                    <FontAwesomeIcon icon={faSync} />
                                </button>
                            </div>
                        </div>
                        <div className="card-body">
                            <div className="alert alert-warning mb-0">
                                <strong>Veri alınamadı:</strong> {error}
                                <br />
                                <small>API endpointi kontrol edilsin: https://ffl21.fun:3001/api/cihaz/{cihazID}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="row mb-4">
            <div className="col-12">
                <div className="card border-0 shadow-sm">
                    <div className="card-header bg-gradient-primary text-white d-flex justify-content-between align-items-center">
                        <h6 className="mb-0">
                            <FontAwesomeIcon icon={faThermometerHalf} className="me-2" />
                            En Son Veriler - {cihazID}
                        </h6>
                        <div className="d-flex gap-2">
                            <span className="badge bg-success">Canlı</span>
                            <button 
                                className="btn btn-sm btn-outline-light"
                                onClick={fetchDeviceData}
                                title="Yenile"
                            >
                                <FontAwesomeIcon icon={faSync} />
                            </button>
                        </div>
                    </div>
                    <div className="card-body">
                        <div className="row">
                            <div className="col-md-3 mb-3">
                                <div className="d-flex align-items-center p-3 border-end">
                                    <div className="me-3">
                                        <FontAwesomeIcon 
                                            icon={faThermometerHalf} 
                                            className={getTemperatureColor(deviceData.sicaklik || '0')} 
                                            size="lg" 
                                        />
                                    </div>
                                    <div>
                                        <div className="text-muted small">Sıcaklık</div>
                                        <div className={`fw-bold ${getTemperatureColor(deviceData.sicaklik || '0')}`}>
                                            {deviceData.sicaklik || '0'}°C
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="col-md-3 mb-3">
                                <div className="d-flex align-items-center p-3 border-end">
                                    <div className="me-3">
                                        <FontAwesomeIcon 
                                            icon={faDroplet} 
                                            className={getHumidityColor(deviceData.nem || '0')} 
                                            size="lg" 
                                        />
                                    </div>
                                    <div>
                                        <div className="text-muted small">Nem</div>
                                        <div className={`fw-bold ${getHumidityColor(deviceData.nem || '0')}`}>
                                            %{deviceData.nem || '0'}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="col-md-3 mb-3">
                                <div className="d-flex align-items-center p-3 border-end">
                                    <div className="me-3">
                                        <FontAwesomeIcon 
                                            icon={faBatteryHalf} 
                                            className={getBatteryColor(calculateBatteryPercentage(deviceData.pil || '0'))} 
                                            size="lg" 
                                        />
                                    </div>
                                    <div>
                                        <div className="text-muted small">Pil</div>
                                        <div className={`fw-bold ${getBatteryColor(calculateBatteryPercentage(deviceData.pil || '0'))}`}>
                                            %{calculateBatteryPercentage(deviceData.pil || '0')}
                                        </div>
                                        {calculateBatteryPercentage(deviceData.pil || '0') < 35 && (
                                            <div className="text-danger small fw-bold">
                                                ⚠️ Pil seviyesi düşük
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                            <div className="col-md-3 mb-3">
                                <div className="d-flex align-items-center p-3">
                                    <div className="me-3">
                                        <FontAwesomeIcon icon={faLightbulb} className="text-primary" size="lg" />
                                    </div>
                                    <div>
                                        <div className="text-muted small">Işık</div>
                                        <div className="fw-bold text-primary">
                                            {deviceData.isik || '0'}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="row mt-3 pt-3 border-top">
                            <div className="col-md-6">
                                <div className="d-flex align-items-center">
                                    <FontAwesomeIcon icon={faMapMarkerAlt} className="text-primary me-2" />
                                    <span className="text-muted">Konum:</span>
                                    <span className="ms-2 fw-bold text-primary">
                                        {deviceData.enlem && deviceData.boylam 
                                            ? `${parseFloat(deviceData.enlem).toFixed(6)}, ${parseFloat(deviceData.boylam).toFixed(6)}`
                                            : 'Konum bilgisi yok'
                                        }
                                    </span>
                                </div>
                            </div>
                            <div className="col-md-6 text-end">
                                <span className="text-muted">Durum:</span>
                                <span className={`ms-2 badge ${deviceData.aktif ? 'bg-success' : 'bg-secondary'}`}>
                                    {deviceData.aktif ? 'aktif' : 'pasif'}
                                </span>
                                {lastUpdate && (
                                    <span className="ms-3 text-muted">Son Güncelleme: {lastUpdate}</span>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default RealTimeDeviceData;