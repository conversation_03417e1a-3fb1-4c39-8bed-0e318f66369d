import express from 'express';
import http from 'http';

const app = express();
const port = 80;

// HTTP'den HTTPS'e yönlendirme
app.use((req, res) => {
    const httpsUrl = `https://ffl21.fun:3001${req.url}`;
    res.redirect(301, httpsUrl);
});

// HTTP sunucusunu başlat
http.createServer(app).listen(port, () => {
    console.log(`HTTP yönlendirme sunucusu port ${port}'de çalışıyor`);
    console.log(`Tüm HTTP istekleri https://ffl21.fun'e yönlendiriliyor`);
}); 