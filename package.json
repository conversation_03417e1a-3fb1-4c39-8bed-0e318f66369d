{"name": "mgz24", "version": "0.1.0", "private": true, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@googlemaps/react-wrapper": "^1.2.0", "@react-google-maps/api": "^2.20.6", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jquery": "^3.5.32", "@vis.gl/react-google-maps": "^1.5.2", "axios": "^1.9.0", "bootstrap": "^5.3.6", "ion-rangeslider": "^2.3.1", "jquery": "^3.7.1", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "leaflet": "^1.9.4", "react": "^19.1.0", "react-bootstrap": "^2.10.10", "react-dom": "^19.1.0", "react-is": "^18.2.0", "react-leaflet": "^5.0.0", "react-router-dom": "^7.6.0", "react-scripts": "5.0.1", "react-toastify": "^11.0.5", "recharts": "^2.15.4", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "proxy": "https://ffl21.fun:3001", "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}