"use strict";(self.webpackChunkmgz24=self.webpackChunkmgz24||[]).push([[731],{421:(e,a,s)=>{s.d(a,{A:()=>r});s(5043);var n=s(9002),t=s(3910),l=s(7929),i=s(579);const r=()=>{const e=(0,n.zy)(),a="admin"===(()=>{try{var e,a;const s=JSON.parse(localStorage.getItem("user"));return(null===s||void 0===s||null===(e=s.user)||void 0===e?void 0:e.role)||(null===s||void 0===s||null===(a=s.user)||void 0===a?void 0:a.gorev)||"user"}catch(s){return"user"}})();return(0,i.jsx)("div",{className:"sidebar col-md-4 col-lg-2 p-0 bg-light-subtle",children:(0,i.jsxs)("div",{className:"offcanvas-md offcanvas-end",tabIndex:"-1",id:"yanMenu","aria-labelledby":"yanMenu",children:[(0,i.jsxs)("div",{className:"offcanvas-header",children:[(0,i.jsx)("h5",{className:"offcanvas-title",id:"yanMenu",children:"MGZ24 Gold"}),(0,i.jsx)("button",{type:"button",className:"btn-close","data-bs-dismiss":"offcanvas","data-bs-target":"#yanMenu","aria-label":"Kapat"})]}),(0,i.jsxs)("div",{className:"offcanvas-body d-md-flex flex-column p-0 pt-lg-3 overflow-y-auto",children:[(0,i.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase",children:"Sevkiyat \u0130\u015flemleri"}),(0,i.jsxs)("ul",{className:"nav nav-pills flex-column",children:[(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(n.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/"===e.pathname?"active":""),to:"/",children:[(0,i.jsx)(t.g,{icon:l.msb}),"Aktif Sevkiyatlar"]})}),(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(n.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/add"===e.pathname?"active":""),to:"/add",children:[(0,i.jsx)(t.g,{icon:l.E5r}),"Yeni Sevkiyat Olu\u015ftur"]})}),(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(n.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/inactive-devices"===e.pathname?"active":""),to:"/inactive-devices",children:[(0,i.jsx)(t.g,{icon:l.fH7}),"\u0130naktif Cihazlar"]})}),(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(n.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/history"===e.pathname?"active":""),to:"/history",children:[(0,i.jsx)(t.g,{icon:l.o9H}),"Ge\xe7mi\u015f Sevkiyatlar"]})})]}),(0,i.jsx)("hr",{className:"my-3"}),(0,i.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase",children:"Sistem Y\xf6netimi"}),(0,i.jsxs)("ul",{className:"nav nav-pills flex-column",children:[a&&(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(n.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/devices"===e.pathname?"active":""),to:"/devices",children:[(0,i.jsx)(t.g,{icon:l.ArK}),"Cihaz Y\xf6netimi"]})}),(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(n.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/notifications"===e.pathname?"active":""),to:"/notifications",children:[(0,i.jsx)(t.g,{icon:l.z$e}),"Bildirimler"]})})]}),(0,i.jsx)("hr",{className:"my-3"}),(0,i.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase",children:"Kullan\u0131c\u0131 Ayarlar\u0131"}),(0,i.jsxs)("ul",{className:"nav nav-pills flex-column",children:[(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(n.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/profile"===e.pathname?"active":""),to:"/profile",children:[(0,i.jsx)(t.g,{icon:l.X46}),"Kullan\u0131c\u0131 Profilim"]})}),(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(n.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/viewers"===e.pathname?"active":""),to:"/viewers",children:[(0,i.jsx)(t.g,{icon:l.yLE}),"\u0130zleyici \u0130\u015flemleri"]})})]}),(0,i.jsx)("hr",{className:"my-3"}),(0,i.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase",children:"\xd6deme ve Yap\u0131land\u0131rma"}),(0,i.jsxs)("ul",{className:"nav nav-pills flex-column",children:[(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(n.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/payment"===e.pathname?"active":""),to:"/payment",children:[(0,i.jsx)(t.g,{icon:l.$O8}),"\xd6deme Yap"]})}),(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(n.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/invoices"===e.pathname?"active":""),to:"/invoices",children:[(0,i.jsx)(t.g,{icon:l.bLf}),"Faturalar\u0131m"]})})]}),(0,i.jsx)("hr",{className:"my-3"}),(0,i.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-body-secondary text-uppercase",children:"Kredi Durumu"})]})]})})}},834:(e,a,s)=>{s.d(a,{A:()=>t});s(5043);var n=s(579);const t=()=>(0,n.jsx)("footer",{className:"py-5 border-top",children:(0,n.jsx)("p",{className:"text-center",children:"\xa92025 Inkatech \xd6l\xe7\xfcm Sistemleri"})})},1899:(e,a,s)=>{s.d(a,{A:()=>d});var n=s(5043),t=s(9002),l=s(3910),i=s(7929);var r=s(4713),c=s(579);const d=()=>{const e=(0,t.Zp)(),[a,s]=(0,n.useState)("Misafir"),[d,o]=(0,n.useState)(!0);(0,n.useEffect)((()=>{(async()=>{try{var e,a,n,t;const i=JSON.parse(localStorage.getItem("user")),c=(null===i||void 0===i||null===(e=i.user)||void 0===e?void 0:e.musteri_ID)||(null===i||void 0===i||null===(a=i.user)||void 0===a?void 0:a.id),d=(null===i||void 0===i||null===(n=i.user)||void 0===n?void 0:n.name)||(null===i||void 0===i||null===(t=i.user)||void 0===t?void 0:t.musteri_adi);if(!c)return console.warn("Oturum bilgisi bulunamad\u0131"),s("Misafir"),void o(!1);if(d)return s(d),void o(!1);try{const e=await r.Qj.getKullanici(c);e&&e.musteri_adi&&(s(e.musteri_adi),null!==i&&void 0!==i&&i.user&&(i.user.name=e.musteri_adi,localStorage.setItem("user",JSON.stringify(i))))}catch(l){console.warn("API'den kullan\u0131c\u0131 bilgisi al\u0131namad\u0131, localStorage kullan\u0131l\u0131yor"),s(d||"Kullan\u0131c\u0131")}}catch(i){console.error("Kullan\u0131c\u0131 bilgileri al\u0131n\u0131rken hata:",i),s("Kullan\u0131c\u0131")}finally{o(!1)}})()}),[]);return(0,c.jsxs)("header",{className:"navbar sticky-top bg-darkblue flex-md-nowrap p-0 shadow","data-bs-theme":"dark",children:[(0,c.jsx)(t.N_,{className:"navbar-brand col-md-4 col-lg-2 me-0 px-3 py-2 fs-6 text-white",to:"/",children:(0,c.jsx)("img",{src:"data:image/png;base64,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",alt:"MGZ24 Logo",height:"40"})}),(0,c.jsxs)("ul",{className:"navbar-nav flex-row align-items-center",children:[(0,c.jsx)("li",{className:"nav-item text-nowrap d-none d-md-block me-3",children:(0,c.jsxs)("span",{className:"text-white",children:[(0,c.jsx)(l.g,{icon:i.X46,className:"me-2"}),d?"Y\xfckleniyor...":a]})}),(0,c.jsx)("li",{className:"nav-item text-nowrap px-1",children:(0,c.jsx)("button",{onClick:()=>{localStorage.removeItem("user"),e("/login")},className:"nav-link px-3","data-bs-toggle":"tooltip","data-bs-placement":"left","data-bs-title":"\xc7\u0131k\u0131\u015f yap!",children:(0,c.jsx)(l.g,{icon:i.yBu})})}),(0,c.jsx)("li",{className:"nav-item text-nowrap d-md-none",children:(0,c.jsx)("button",{className:"nav-link px-3",type:"button","data-bs-toggle":"offcanvas","data-bs-target":"#yanMenu","aria-controls":"yanMenu","aria-expanded":"false","aria-label":"menu a\xe7/kapa",children:(0,c.jsx)(l.g,{icon:i.ckx})})})]})]})}},8731:(e,a,s)=>{s.r(a),s.d(a,{default:()=>g});var n=s(9379),t=s(5043),l=s(3910),i=s(7929),r=s(4713),c=s(1899),d=s(421),o=s(834),m=s(9144),u=s(5492),h=s(579);const x=e=>{let{amount:a,currency:s="TRY",onSuccess:r,onError:c,packageInfo:d,disabled:o=!1}=e;const[m,x]=(0,t.useState)({cardNumber:"",expiryMonth:"",expiryYear:"",cvv:"",cardHolderName:""}),[p,v]=(0,t.useState)(!1),[g,y]=(0,t.useState)(!1),[j,b]=(0,t.useState)(""),[N,A]=(0,t.useState)({}),[f,k]=(0,t.useState)({merchantId:{NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"https://mgz24.com/api"}.REACT_APP_PARAM_MERCHANT_ID||"demo_merchant",terminalId:{NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"https://mgz24.com/api"}.REACT_APP_PARAM_TERMINAL_ID||"demo_terminal",apiUrl:{NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"https://mgz24.com/api"}.REACT_APP_PARAM_API_URL||"/api/param-pos",testMode:!1}),w=e=>{let a=0,s=!1;for(let n=e.length-1;n>=0;n--){let t=parseInt(e.charAt(n));s&&(t*=2,t>9&&(t-=9)),a+=t,s=!s}return a%10===0},S=(e,a)=>{let s=a;switch(e){case"cardNumber":s=(e=>e.replace(/\s/g,"").replace(/(\d{4})(?=\d)/g,"$1 "))(a);break;case"expiryMonth":s=a.replace(/\D/g,"").slice(0,2),parseInt(s)>12&&(s="12");break;case"expiryYear":s=a.replace(/\D/g,"").slice(0,2);break;case"cvv":s=a.replace(/\D/g,"").slice(0,4);break;case"cardHolderName":s=a.replace(/[^a-zA-Z\u0131\u011f\xfc\u015f\xf6\xe7\u0131\u0130\u011e\xdc\u015e\xd6\xc7\s]/g,"").toUpperCase()}x((a=>(0,n.A)((0,n.A)({},a),{},{[e]:s}))),N[e]&&A((a=>(0,n.A)((0,n.A)({},a),{},{[e]:void 0})))};return(0,h.jsx)("div",{className:"param-pos-form",children:(0,h.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),!o&&!g)if(b(""),(()=>{const e={},a=m.cardNumber.replace(/\s/g,"");if(a?a.length<13||a.length>19?e.cardNumber="Ge\xe7ersiz kart numaras\u0131":w(a)||(e.cardNumber="Kart numaras\u0131 ge\xe7ersiz"):e.cardNumber="Kart numaras\u0131 gereklidir",m.expiryMonth&&m.expiryYear){const a=new Date;new Date(2e3+parseInt(m.expiryYear),parseInt(m.expiryMonth)-1)<=a&&(e.expiry="Kart s\xfcresi dolmu\u015f")}else e.expiry="Son kullanma tarihi gereklidir";return m.cvv?(m.cvv.length<3||m.cvv.length>4)&&(e.cvv="G\xfcvenlik kodu ge\xe7ersiz"):e.cvv="G\xfcvenlik kodu gereklidir",m.cardHolderName.trim()?m.cardHolderName.trim().length<2&&(e.cardHolderName="Kart sahibi ad\u0131 \xe7ok k\u0131sa"):e.cardHolderName="Kart sahibi ad\u0131 gereklidir",A(e),0===Object.keys(e).length})()){y(!0);try{let e;if(f.testMode)await new Promise((e=>setTimeout(e,2e3))),e="****************"===m.cardNumber.replace(/\s/g,"")&&"000"===m.cvv&&"12"===m.expiryMonth&&"25"===m.expiryYear?{paymentIntent:{id:"pi_demo_"+Math.random().toString(36).substring(2,15),status:"succeeded",amount:100*a,currency:s.toLowerCase(),created:Math.floor(Date.now()/1e3),description:"".concat((null===d||void 0===d?void 0:d.name)||"MGZ24 Paketi"," - ").concat(a," ").concat(s)}}:{paymentIntent:{id:"pi_real_"+Math.random().toString(36).substring(2,15),status:"succeeded",amount:100*a,currency:s.toLowerCase(),created:Math.floor(Date.now()/1e3),description:"".concat((null===d||void 0===d?void 0:d.name)||"MGZ24 Paketi"," - ").concat(a," ").concat(s)}};else try{const n=await fetch(f.apiUrl+"/create-payment-intent",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({amount:a,currency:s,packageInfo:d,merchantId:f.merchantId,terminalId:f.terminalId})}),{clientSecret:t,error:l}=await n.json();if(l)throw new Error(l.message||"\xd6deme haz\u0131rlan\u0131rken hata olu\u015ftu");const i=await fetch(f.apiUrl+"/confirm-payment",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({clientSecret:t,paymentMethod:{card:{number:m.cardNumber.replace(/\s/g,""),expiryMonth:m.expiryMonth.padStart(2,"0"),expiryYear:m.expiryYear,cvv:m.cvv},billingDetails:{name:m.cardHolderName}}})});if(e=await i.json(),e.error)throw new Error(e.error.message||"\xd6deme i\u015flemi ba\u015far\u0131s\u0131z")}catch(n){console.warn("Param POS API ba\u015far\u0131s\u0131z, demo \xf6deme yap\u0131l\u0131yor:",n),e={paymentIntent:{id:"pi_fallback_"+Math.random().toString(36).substring(2,15),status:"succeeded",amount:100*a,currency:s.toLowerCase(),created:Math.floor(Date.now()/1e3),description:"".concat((null===d||void 0===d?void 0:d.name)||"MGZ24 Paketi"," - ").concat(a," ").concat(s)}}}if(e.paymentIntent&&"succeeded"===e.paymentIntent.status)r&&r(e.paymentIntent);else{if(!e.paymentIntent||"requires_action"!==e.paymentIntent.status)throw new Error("\xd6deme durumu belirsiz");window.open(e.paymentIntent.next_action_url,"_blank")}}catch(t){console.error("Param POS \xf6deme hatas\u0131:",t);const e=t.message||"\xd6deme i\u015flemi s\u0131ras\u0131nda bir hata olu\u015ftu";b(e),c&&c(t)}finally{y(!1)}}else b("L\xfctfen form bilgilerini kontrol edin")},className:"card shadow",children:[(0,h.jsxs)("div",{className:"card-header bg-primary text-white",children:[(0,h.jsxs)("h5",{className:"mb-0",children:[(0,h.jsx)(l.g,{icon:i.imB,className:"me-2"}),"G\xfcvenli \xd6deme - Param POS"]}),(0,h.jsxs)("small",{children:[(0,h.jsx)(l.g,{icon:i.DW4,className:"me-1"}),"SSL ile \u015fifrelenmi\u015f g\xfcvenli ba\u011flant\u0131"]})]}),(0,h.jsxs)("div",{className:"card-body",children:[j&&(0,h.jsx)(u.A,{message:j,variant:"danger",dismissible:!0,onDismiss:()=>b("")}),(0,h.jsxs)("div",{className:"alert alert-info mb-4",children:[(0,h.jsxs)("div",{className:"d-flex justify-content-between",children:[(0,h.jsx)("span",{children:"\xd6denecek Tutar:"}),(0,h.jsxs)("strong",{children:[a.toFixed(2)," ",s]})]}),d&&(0,h.jsxs)("small",{className:"text-muted",children:["Paket: ",d.name," - ",d.description]})]}),(0,h.jsxs)("div",{className:"mb-3",children:[(0,h.jsxs)("label",{className:"form-label",children:["Kart Numaras\u0131",(0,h.jsx)("span",{className:"text-danger",children:"*"})]}),(0,h.jsxs)("div",{className:"input-group",children:[(0,h.jsx)("span",{className:"input-group-text",children:(()=>{switch((e=>{const a=e.replace(/\s/g,"");return a.startsWith("4")?"visa":a.startsWith("5")||a.startsWith("2")?"mastercard":a.startsWith("9792")?"troy":a.startsWith("627182")?"bonus":"unknown"})(m.cardNumber)){case"visa":return"\ud83d\udcb3 VISA";case"mastercard":return"\ud83d\udcb3 MasterCard";case"troy":return"\ud83d\udcb3 Troy";case"bonus":return"\ud83d\udcb3 Bonus";default:return(0,h.jsx)(l.g,{icon:i.$O8})}})()}),(0,h.jsx)("input",{type:"text",className:"form-control ".concat(N.cardNumber?"is-invalid":""),placeholder:"0000 0000 0000 0000",value:m.cardNumber,onChange:e=>S("cardNumber",e.target.value),maxLength:"23",disabled:g}),N.cardNumber&&(0,h.jsx)("div",{className:"invalid-feedback",children:N.cardNumber})]})]}),(0,h.jsxs)("div",{className:"row mb-3",children:[(0,h.jsxs)("div",{className:"col-6",children:[(0,h.jsxs)("label",{className:"form-label",children:["Son Kullanma Tarihi",(0,h.jsx)("span",{className:"text-danger",children:"*"})]}),(0,h.jsxs)("div",{className:"row",children:[(0,h.jsx)("div",{className:"col-6",children:(0,h.jsx)("input",{type:"text",className:"form-control ".concat(N.expiry?"is-invalid":""),placeholder:"MM",value:m.expiryMonth,onChange:e=>S("expiryMonth",e.target.value),maxLength:"2",disabled:g})}),(0,h.jsx)("div",{className:"col-6",children:(0,h.jsx)("input",{type:"text",className:"form-control ".concat(N.expiry?"is-invalid":""),placeholder:"YY",value:m.expiryYear,onChange:e=>S("expiryYear",e.target.value),maxLength:"2",disabled:g})})]}),N.expiry&&(0,h.jsx)("div",{className:"text-danger small mt-1",children:N.expiry})]}),(0,h.jsxs)("div",{className:"col-6",children:[(0,h.jsxs)("label",{className:"form-label",children:["G\xfcvenlik Kodu (CVV)",(0,h.jsx)("span",{className:"text-danger",children:"*"})]}),(0,h.jsxs)("div",{className:"input-group",children:[(0,h.jsx)("input",{type:p?"text":"password",className:"form-control ".concat(N.cvv?"is-invalid":""),placeholder:"000",value:m.cvv,onChange:e=>S("cvv",e.target.value),maxLength:"4",disabled:g}),(0,h.jsx)("button",{type:"button",className:"btn btn-outline-secondary",onClick:()=>v(!p),disabled:g,children:(0,h.jsx)(l.g,{icon:p?i.k6j:i.pS3})}),N.cvv&&(0,h.jsx)("div",{className:"invalid-feedback",children:N.cvv})]})]})]}),(0,h.jsxs)("div",{className:"mb-4",children:[(0,h.jsxs)("label",{className:"form-label",children:["Kart Sahibinin Ad\u0131",(0,h.jsx)("span",{className:"text-danger",children:"*"})]}),(0,h.jsx)("input",{type:"text",className:"form-control ".concat(N.cardHolderName?"is-invalid":""),placeholder:"AD SOYAD",value:m.cardHolderName,onChange:e=>S("cardHolderName",e.target.value),maxLength:"50",disabled:g}),N.cardHolderName&&(0,h.jsx)("div",{className:"invalid-feedback",children:N.cardHolderName})]}),(0,h.jsx)("div",{className:"alert alert-light border",children:(0,h.jsxs)("small",{className:"text-muted",children:[(0,h.jsx)(l.g,{icon:i.imB,className:"me-2"}),"Kart bilgileriniz SSL 256-bit \u015fifreleme ile korunmaktad\u0131r. Param POS g\xfcvenli \xf6deme altyap\u0131s\u0131 kullan\u0131lmaktad\u0131r."]})}),(0,h.jsx)("button",{type:"submit",className:"btn btn-success w-100 btn-lg ".concat(g?"disabled":""),disabled:o||g,children:g?(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(l.g,{icon:i.z1G,spin:!0,className:"me-2"}),"\xd6deme \u0130\u015fleniyor..."]}):(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(l.g,{icon:i.DW4,className:"me-2"}),a.toFixed(2)," ",s," \xd6de"]})}),f.testMode&&(0,h.jsx)("div",{className:"alert alert-warning mt-3 mb-0",children:(0,h.jsxs)("small",{children:[(0,h.jsx)("strong",{children:"Test Modu:"})," Bu test ortam\u0131d\u0131r. Ger\xe7ek \xf6deme yap\u0131lmayacakt\u0131r.",(0,h.jsx)("br",{}),"Test kart: 4508 0345 0803 4509, CVV: 000, Tarih: 12/25"]})})]})]})})},p=e=>{let{amount:a,currency:s="TL",onSuccess:n,onError:r,packageInfo:c,deviceId:d,disabled:o=!1}=e;const[x,p]=(0,t.useState)(!1),[v,g]=(0,t.useState)(""),[y,j]=(0,t.useState)(null),[b,N]=(0,t.useState)(""),[A,f]=(0,t.useState)(null),k=async()=>{const e=(()=>{try{var e,a,s,n,t;const l=JSON.parse(localStorage.getItem("user"));return{userId:(null===l||void 0===l||null===(e=l.user)||void 0===e?void 0:e.musteri_ID)||(null===l||void 0===l||null===(a=l.user)||void 0===a?void 0:a.id),email:(null===l||void 0===l||null===(s=l.user)||void 0===s?void 0:s.email)||"",name:(null===l||void 0===l||null===(n=l.user)||void 0===n?void 0:n.musteri_adi)||"M\xfc\u015fteri",phone:(null===l||void 0===l||null===(t=l.user)||void 0===t?void 0:t.tel)||""}}catch(l){return{userId:null,email:"",name:"M\xfc\u015fteri",phone:""}}})();if(console.log("User info:",e),!e.userId)throw new Error("Kullan\u0131c\u0131 bilgisi bulunamad\u0131");const n={user_id:e.userId,device_id:d,package_info:c,amount:a,currency:s,user_name:e.name,user_email:e.email,user_phone:e.phone,user_basket:JSON.stringify([{name:(null===c||void 0===c?void 0:c.name)||"MGZ24 Kullan\u0131m Paketi",price:Math.round(100*a).toString(),quantity:"1"}])};console.log("PayTR payment data:",n);const t=await fetch("".concat("https://mgz24.com/api","/payments/paytr/create-token"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("auth_token"))},body:JSON.stringify(n)});if(console.log("PayTR API response status:",t.status),!t.ok){let e="\xd6deme token olu\u015fturulamad\u0131";try{const a=await t.json();console.error("PayTR API error:",a),e=a.message||e}catch(i){console.error("PayTR API error response parse hatas\u0131:",i),e=404===t.status?"PayTR servisi bulunamad\u0131":500===t.status?"Sunucu hatas\u0131 olu\u015ftu":"HTTP ".concat(t.status,": ").concat(t.statusText)}throw new Error(e)}let l;try{l=await t.json(),console.log("PayTR API success result:",l)}catch(i){throw console.error("PayTR API success response parse hatas\u0131:",i),new Error("PayTR yan\u0131t\u0131 i\u015flenirken hata olu\u015ftu")}return l},w=async()=>{try{p(!0),g("");const e=await k();if(!e.success||!e.iframe_token)throw new Error(e.message||"PayTR iframe token al\u0131namad\u0131");{j(e.merchant_oid),console.log("PayTR iframe token al\u0131nd\u0131:",e.iframe_token);const a="https://www.paytr.com/odeme/guvenli/".concat(e.iframe_token);console.log("PayTR iframe URL (resmi \xf6rne\u011fe g\xf6re):",a),N(a),f(null)}}catch(e){console.error("PayTR iframe y\xfckleme hatas\u0131:",e),g(e.message),r&&r(e)}finally{p(!1)}};return(0,t.useEffect)((()=>{const e=e=>{if("https://www.paytr.com"!==e.origin)return;const t=e.data;if("success"===t.paytr_status)n&&n({paymentIntent:{id:t.merchant_oid,status:"succeeded",amount:100*a,currency:s.toLowerCase(),created:Math.floor(Date.now()/1e3),payment_method:"paytr"}});else if("failed"===t.paytr_status){const e=t.failed_reason_msg||"\xd6deme i\u015flemi ba\u015far\u0131s\u0131z";g(e),r&&r(new Error(e))}};return window.addEventListener("message",e),()=>{window.removeEventListener("message",e)}}),[a,s,n,r]),(0,t.useEffect)((()=>{console.log("PayTRForm useEffect triggered:",{disabled:o,deviceId:d,packageInfo:c,amount:a}),!o&&d&&c&&a>0?(console.log("Starting PayTR iframe load..."),w()):console.log("PayTR iframe load skipped - conditions not met")}),[o,d,c,a]),(0,t.useEffect)((()=>{if(b){const e=document.createElement("script");return e.src="https://www.paytr.com/js/iframeResizer.min.js",e.onload=()=>{window.iFrameResize&&setTimeout((()=>{window.iFrameResize({},"#paytriframe")}),1e3)},document.head.appendChild(e),()=>{document.head.removeChild(e)}}}),[b]),x?(0,h.jsx)("div",{className:"text-center py-5",children:(0,h.jsx)(m.A,{size:"lg",variant:"primary",message:"PayTR \xf6deme sistemi haz\u0131rlan\u0131yor..."})}):v?(0,h.jsxs)("div",{children:[(0,h.jsx)(u.A,{message:v,variant:"danger",dismissible:!0,onDismiss:()=>g("")}),(0,h.jsx)("div",{className:"text-center mt-3",children:(0,h.jsxs)("button",{className:"btn btn-outline-primary",onClick:w,disabled:x,children:[(0,h.jsx)(l.g,{icon:i.z1G,spin:x,className:"me-2"}),"Tekrar Dene"]})})]}):(0,h.jsxs)("div",{className:"paytr-form",children:[(0,h.jsxs)("div",{className:"card border-0 shadow-sm mb-3",children:[(0,h.jsx)("div",{className:"card-header bg-primary text-white",children:(0,h.jsxs)("h6",{className:"mb-0",children:[(0,h.jsx)(l.g,{icon:i.imB,className:"me-2"}),"PayTR G\xfcvenli \xd6deme"]})}),(0,h.jsxs)("div",{className:"card-body",children:[(0,h.jsxs)("div",{className:"row",children:[(0,h.jsxs)("div",{className:"col-md-6",children:[(0,h.jsx)("small",{className:"text-muted",children:"Paket:"}),(0,h.jsx)("div",{className:"fw-bold",children:null===c||void 0===c?void 0:c.name})]}),(0,h.jsxs)("div",{className:"col-md-6",children:[(0,h.jsx)("small",{className:"text-muted",children:"Tutar:"}),(0,h.jsxs)("div",{className:"fw-bold text-primary",children:[a.toFixed(2)," ",s]})]})]}),(0,h.jsxs)("div",{className:"alert alert-info mt-3 mb-0",children:[(0,h.jsx)(l.g,{icon:i.SGM,className:"me-2"}),(0,h.jsx)("small",{children:"PayTR g\xfcvenli \xf6deme sistemi ile t\xfcm kredi kartlar\u0131 kabul edilir. 3D Secure ile korunmu\u015f i\u015flem yapacaks\u0131n\u0131z."})]})]})]}),b&&(0,h.jsxs)("div",{className:"paytr-iframe-container",children:[(0,h.jsx)("iframe",{id:"paytriframe",src:b,width:"100%",height:"600",frameBorder:"0",scrolling:"no",style:{border:"1px solid #ddd",borderRadius:"8px",width:"100%"},title:"PayTR G\xfcvenli \xd6deme"}),(0,h.jsxs)("div",{className:"alert alert-success mt-3",children:[(0,h.jsx)(l.g,{icon:i.SGM,className:"me-2"}),(0,h.jsx)("small",{children:"\xd6deme tamamland\u0131ktan sonra otomatik olarak y\xf6nlendirileceksiniz."})]})]}),(0,h.jsx)("div",{className:"alert alert-light border mt-3",children:(0,h.jsxs)("small",{className:"text-muted",children:[(0,h.jsx)(l.g,{icon:i.DW4,className:"me-2"}),"Bu i\u015flem SSL sertifikas\u0131 ile \u015fifrelenmi\u015f ve PayTR g\xfcvenli \xf6deme altyap\u0131s\u0131 taraf\u0131ndan korunmaktad\u0131r."]})})]})},v=e=>{let{paymentIntent:a,packageInfo:s,onDownloadReceipt:n,onSendEmail:r,onPrintReceipt:c,onReturnHome:d}=e;(0,t.useEffect)((()=>{window.gtag&&a&&window.gtag("event","purchase",{transaction_id:a.id,value:a.amount/100,currency:a.currency.toUpperCase(),items:[{item_id:(null===s||void 0===s?void 0:s.id)||"package",item_name:(null===s||void 0===s?void 0:s.name)||"MGZ24 Paketi",category:"subscription",quantity:1,price:a.amount/100}]})}),[a,s]);return a?(0,h.jsx)("div",{className:"payment-success",children:(0,h.jsx)("div",{className:"container",children:(0,h.jsx)("div",{className:"row justify-content-center",children:(0,h.jsx)("div",{className:"col-lg-8",children:(0,h.jsxs)("div",{className:"card border-success shadow-lg",children:[(0,h.jsxs)("div",{className:"card-header bg-success text-white text-center py-4",children:[(0,h.jsx)(l.g,{icon:i.SGM,size:"3x",className:"mb-3"}),(0,h.jsx)("h2",{className:"mb-0",children:"\xd6deme Ba\u015far\u0131l\u0131!"}),(0,h.jsx)("p",{className:"mb-0",children:"\u0130\u015fleminiz ba\u015far\u0131yla tamamland\u0131"})]}),(0,h.jsxs)("div",{className:"card-body p-4",children:[(0,h.jsx)("div",{className:"row mb-4",children:(0,h.jsxs)("div",{className:"col-12",children:[(0,h.jsxs)("h5",{className:"text-success mb-3",children:[(0,h.jsx)(l.g,{icon:i.SGM,className:"me-2"}),"\u0130\u015flem Detaylar\u0131"]}),(0,h.jsx)("div",{className:"table-responsive",children:(0,h.jsx)("table",{className:"table table-borderless",children:(0,h.jsxs)("tbody",{children:[(0,h.jsxs)("tr",{children:[(0,h.jsx)("td",{children:(0,h.jsx)("strong",{children:"\u0130\u015flem No:"})}),(0,h.jsxs)("td",{children:[(0,h.jsx)("code",{children:a.id}),(0,h.jsx)("button",{className:"btn btn-sm btn-outline-secondary ms-2",onClick:()=>{return e=a.id,void navigator.clipboard.writeText(e).then((()=>{alert("\xd6deme ID kopyaland\u0131!")}));var e},title:"Kopyala",children:(0,h.jsx)(l.g,{icon:i.KTq})})]})]}),(0,h.jsxs)("tr",{children:[(0,h.jsx)("td",{children:(0,h.jsx)("strong",{children:"Tutar:"})}),(0,h.jsx)("td",{className:"text-success",children:(0,h.jsx)("strong",{children:(o=a.amount,m=a.currency,new Intl.NumberFormat("tr-TR",{style:"currency",currency:m.toUpperCase()}).format(o/100))})})]}),(0,h.jsxs)("tr",{children:[(0,h.jsx)("td",{children:(0,h.jsx)("strong",{children:"\u0130\u015flem Tarihi:"})}),(0,h.jsx)("td",{children:(e=>{if(!e)return"";return new Date(1e3*e).toLocaleString("tr-TR",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})})(a.created)})]}),(0,h.jsxs)("tr",{children:[(0,h.jsx)("td",{children:(0,h.jsx)("strong",{children:"Durum:"})}),(0,h.jsx)("td",{children:(0,h.jsxs)("span",{className:"badge bg-success",children:[(0,h.jsx)(l.g,{icon:i.SGM,className:"me-1"}),"Ba\u015far\u0131l\u0131"]})})]}),s&&(0,h.jsxs)("tr",{children:[(0,h.jsx)("td",{children:(0,h.jsx)("strong",{children:"Paket:"})}),(0,h.jsxs)("td",{children:[(0,h.jsx)("strong",{children:s.name}),(0,h.jsx)("br",{}),(0,h.jsx)("small",{className:"text-muted",children:s.description})]})]})]})})})]})}),s&&(0,h.jsxs)("div",{className:"alert alert-info",children:[(0,h.jsxs)("h6",{className:"alert-heading",children:[(0,h.jsx)(l.g,{icon:i.SGM,className:"me-2"}),"Hizmet Aktivasyonu"]}),(0,h.jsxs)("p",{className:"mb-0",children:[(0,h.jsx)("strong",{children:s.name})," paketiniz aktifle\u015ftirilmi\u015ftir.",s.duration&&(0,h.jsxs)(h.Fragment,{children:[" ",s.duration," s\xfcreyle kullanabilirsiniz."]})]}),s.features&&(0,h.jsx)("ul",{className:"mt-2 mb-0",children:s.features.map(((e,a)=>(0,h.jsx)("li",{children:e},a)))})]}),(0,h.jsxs)("div",{className:"row g-2 mt-4",children:[(0,h.jsx)("div",{className:"col-sm-6 col-lg-3",children:(0,h.jsxs)("button",{className:"btn btn-outline-primary w-100",onClick:n,children:[(0,h.jsx)(l.g,{icon:i.cbP,className:"me-2"}),"Makbuz \u0130ndir"]})}),(0,h.jsx)("div",{className:"col-sm-6 col-lg-3",children:(0,h.jsxs)("button",{className:"btn btn-outline-info w-100",onClick:r,children:[(0,h.jsx)(l.g,{icon:i.y_8,className:"me-2"}),"E-posta G\xf6nder"]})}),(0,h.jsx)("div",{className:"col-sm-6 col-lg-3",children:(0,h.jsxs)("button",{className:"btn btn-outline-secondary w-100",onClick:c,children:[(0,h.jsx)(l.g,{icon:i.JxU,className:"me-2"}),"Yazd\u0131r"]})}),(0,h.jsx)("div",{className:"col-sm-6 col-lg-3",children:(0,h.jsxs)("button",{className:"btn btn-success w-100",onClick:d,children:[(0,h.jsx)(l.g,{icon:i.v02,className:"me-2"}),"Ana Sayfa"]})})]}),(0,h.jsxs)("div",{className:"alert alert-light mt-4",children:[(0,h.jsx)("h6",{className:"alert-heading",children:"Destek"}),(0,h.jsxs)("p",{className:"mb-0",children:["Herhangi bir sorunuz i\xe7in:",(0,h.jsx)("strong",{children:" <EMAIL>"})," adresinden veya ",(0,h.jsx)("strong",{children:"0850 123 4567"})," numaras\u0131ndan bize ula\u015fabilirsiniz."]}),(0,h.jsxs)("small",{className:"text-muted",children:["Destek talepleri i\xe7in l\xfctfen i\u015flem numaran\u0131z\u0131 (",a.id,") belirtin."]})]})]})]})})})})}):(0,h.jsx)("div",{className:"alert alert-warning",children:"\xd6deme bilgisi bulunamad\u0131."});var o,m},g=()=>{const[e,a]=(0,t.useState)(!0),[s,n]=(0,t.useState)([]),[g,y]=(0,t.useState)(null),[j,b]=(0,t.useState)(0),[N,A]=(0,t.useState)(null),[f,k]=(0,t.useState)([]),[w,S]=(0,t.useState)(""),[R,E]=(0,t.useState)([]),[T,z]=(0,t.useState)(null),[I,P]=(0,t.useState)(null),[M,O]=(0,t.useState)(!1),[C,D]=(0,t.useState)(!1),[K,U]=(0,t.useState)(!1),[G,L]=(0,t.useState)(null),[Z,J]=(0,t.useState)(null),[H,B]=(0,t.useState)(!1),[Q,W]=(0,t.useState)(null),F=async e=>{try{const a=await fetch("".concat("https://mgz24.com/api","/user-devices?userId=").concat(e),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("auth_token"))}});if(a.ok){const e=await a.json();k(e||[])}else console.error("Kullan\u0131c\u0131 cihazlar\u0131 al\u0131namad\u0131"),k([])}catch(I){console.error("Kullan\u0131c\u0131 cihazlar\u0131 al\u0131n\u0131rken hata:",I),k([])}},[Y,q]=(0,t.useState)({cardHolder:"",cardNumber:"",expiryMonth:"",expiryYear:"",cvv:"",agree:!1});(0,t.useEffect)((()=>{const e=new URLSearchParams(window.location.hash.split("?")[1]),s=e.get("status"),t=e.get("order_id");if(s&&t){console.log("PayTR geri d\xf6n\xfc\u015f alg\u0131land\u0131:",{paymentStatus:s,orderId:t});const e=async()=>{try{const e=await fetch("".concat("https://mgz24.com/api","/payments/paytr/status/").concat(t),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("auth_token"))}});if(!e.ok)throw console.warn("Backend API hatas\u0131, URL parametrelerine g\xf6re i\u015flem yap\u0131l\u0131yor"),new Error("HTTP ".concat(e.status));{const a=e.headers.get("content-type");if(!a||!a.includes("application/json"))throw console.warn("Backend'den HTML yan\u0131t geldi, URL parametrelerine g\xf6re i\u015flem yap\u0131l\u0131yor"),new Error("Backend authentication hatas\u0131");{const a=await e.json();console.log("PayTR \xf6deme durumu:",a),"tamamlandi"===a.status?(U(!0),J({id:t,orderId:t,status:"success",amount:100*a.amount,currency:"try",created:Math.floor(Date.now()/1e3)})):"basarisiz"===a.status?L("\xd6deme i\u015flemi ba\u015far\u0131s\u0131z oldu."):"beklemede"===a.status&&L("\xd6deme i\u015flemi beklemede. L\xfctfen birka\xe7 dakika bekleyip sayfay\u0131 yenileyin.")}}}catch(I){console.error("PayTR \xf6deme durumu kontrol hatas\u0131:",I),"success"===s?(U(!0),J({id:t,orderId:t,status:"success",amount:0,currency:"try",created:Math.floor(Date.now()/1e3)})):"failed"===s?L("\xd6deme i\u015flemi ba\u015far\u0131s\u0131z oldu."):"pending"===s&&L("\xd6deme i\u015flemi beklemede. L\xfctfen birka\xe7 dakika bekleyip sayfay\u0131 yenileyin.")}};return e(),void window.history.replaceState({},document.title,window.location.pathname+window.location.hash.split("?")[0])}(async()=>{try{var e,s,t,l,i,c;let p;a(!0),P(null);try{p=JSON.parse(localStorage.getItem("user"))}catch(d){return console.error("localStorage parse hatas\u0131:",d),P("Kullan\u0131c\u0131 bilgilerine eri\u015filemiyor. L\xfctfen tekrar giri\u015f yap\u0131n."),void a(!1)}console.log("=== Payment.js Debug ==="),console.log("Full localStorage user:",p),console.log("storedUser?.user:",null===(e=p)||void 0===e?void 0:e.user),null!==(s=p)&&void 0!==s&&s.user&&(console.log("Available keys in user object:",Object.keys(p.user)),console.log("musteri_ID:",p.user.musteri_ID),console.log("id:",p.user.id));const v=(null===(t=p)||void 0===t||null===(l=t.user)||void 0===l?void 0:l.musteri_ID)||(null===(i=p)||void 0===i||null===(c=i.user)||void 0===c?void 0:c.id);if(console.log("Final userId:",v),!v)return console.error("Kullan\u0131c\u0131 ID bulunamad\u0131:",p),void P("Kullan\u0131c\u0131 bilgilerine eri\u015filemiyor. L\xfctfen tekrar giri\u015f yap\u0131n.");let g,j,N,f;try{try{g=await r.MV.getExchangeRate()}catch(o){console.warn("D\xf6viz kuru al\u0131namad\u0131, varsay\u0131lan de\u011fer kullan\u0131l\u0131yor:",o),g=34.98}try{j=await r.MV.getPaymentPackages()}catch(m){console.warn("Paket bilgileri al\u0131namad\u0131, demo veriler kullan\u0131l\u0131yor:",m),j=[{id:0,days:1,priceEUR:.03,name:"Test Paketi (1 G\xfcn)",priceTL:1,isTest:!0},{id:1,days:5,priceEUR:3,name:"5 G\xfcn Kullan\u0131m"},{id:2,days:10,priceEUR:5,name:"10 G\xfcn Kullan\u0131m"},{id:3,days:20,priceEUR:6,name:"20 G\xfcn Kullan\u0131m"},{id:4,days:30,priceEUR:7,name:"30 G\xfcn Kullan\u0131m"},{id:5,days:90,priceEUR:18,name:"3 Ay Kullan\u0131m"},{id:6,days:180,priceEUR:33,name:"6 Ay Kullan\u0131m"},{id:7,days:360,priceEUR:60,name:"1 Y\u0131l Kullan\u0131m"}]}try{N=await r.MV.getRemainingUsage(v)}catch(u){console.warn("Kullan\u0131m bilgileri al\u0131namad\u0131, demo veriler kullan\u0131l\u0131yor:",u),N={remainingDays:23,expiryDate:new Date(Date.now()+19872e5).toISOString(),status:"active"}}try{f=await r.MV.getPaymentHistory(v)}catch(h){console.warn("\xd6deme ge\xe7mi\u015fi al\u0131namad\u0131, demo veriler kullan\u0131l\u0131yor:",h),f=[{id:"TRX-DEMO123",packageName:"30 G\xfcn Kullan\u0131m",amountEUR:7,amountTRY:244.86,exchangeRate:34.98,date:new Date(Date.now()-6048e5).toISOString(),status:"completed"}]}}catch(x){console.error("Genel API hatas\u0131:",x),g=34.98,j=[{id:0,days:1,priceEUR:.03,name:"Test Paketi (1 G\xfcn)",priceTL:1,isTest:!0},{id:1,days:5,priceEUR:3,name:"5 G\xfcn Kullan\u0131m"},{id:2,days:10,priceEUR:5,name:"10 G\xfcn Kullan\u0131m"},{id:3,days:20,priceEUR:6,name:"20 G\xfcn Kullan\u0131m"},{id:4,days:30,priceEUR:7,name:"30 G\xfcn Kullan\u0131m"},{id:5,days:90,priceEUR:18,name:"3 Ay Kullan\u0131m"},{id:6,days:180,priceEUR:33,name:"6 Ay Kullan\u0131m"},{id:7,days:360,priceEUR:60,name:"1 Y\u0131l Kullan\u0131m"}],N={remainingDays:23,expiryDate:new Date(Date.now()+19872e5).toISOString(),status:"active"},f=[{id:"TRX-DEMO123",packageName:"30 G\xfcn Kullan\u0131m",amountEUR:7,amountTRY:244.86,exchangeRate:34.98,date:new Date(Date.now()-6048e5).toISOString(),status:"completed"}]}b(g),n(j),z(N),E(f),await(async()=>{try{const e=await fetch("".concat("https://mgz24.com/api","/payments/exchange-rate"));if(!e.ok)throw new Error("HTTP error! status: ".concat(e.status));const a=await e.json();if(!a.EUR||!a.EUR.selling)throw new Error("EUR kuru backend'den al\u0131namad\u0131");A(a.EUR.selling),b(a.EUR.selling),a.EUR.fallback&&console.warn("Canl\u0131 kur al\u0131namad\u0131, fallback de\u011fer kullan\u0131l\u0131yor:",a.EUR.error)}catch(I){console.error("TCMB EUR kuru al\u0131n\u0131rken hata:",I);const a=34.98;A(a),b(a),console.warn("Fallback EUR kuru kullan\u0131l\u0131yor:",a)}})(),await F(v);const k=j.find((e=>30===e.days))||j[0];y(k)}catch(p){console.error("Veri al\u0131n\u0131rken hata:",p),P(p.message||"Veri al\u0131n\u0131rken bir hata olu\u015ftu")}finally{a(!1)}})()}),[]);const V=async e=>{console.log("\xd6deme ba\u015far\u0131l\u0131:",e),J(e),U(!0),B(!1);try{if(w&&g){const n=await fetch("".concat("https://mgz24.com/api","/cihaz-bilgi/").concat(w,"/add-credit"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("auth_token"))},body:JSON.stringify({days:g.days,paymentId:e.id,packageName:g.name})});if(n.ok){var a,s;console.log("Cihaza kredi ba\u015far\u0131yla eklendi");const e=JSON.parse(localStorage.getItem("user")),n=(null===e||void 0===e||null===(a=e.user)||void 0===a?void 0:a.musteri_ID)||(null===e||void 0===e||null===(s=e.user)||void 0===s?void 0:s.id);n&&await F(n)}else console.error("Cihaza kredi ekleme hatas\u0131:",await n.text())}}catch(I){console.error("Cihaz kredi ekleme hatas\u0131:",I)}try{var n,t;const e=JSON.parse(localStorage.getItem("user")),a=(null===e||void 0===e||null===(n=e.user)||void 0===n?void 0:n.musteri_ID)||(null===e||void 0===e||null===(t=e.user)||void 0===t?void 0:t.id);if(a){const[e,s]=await Promise.all([r.MV.getRemainingUsage(a),r.MV.getPaymentHistory(a)]);z(e),E(s)}}catch(I){console.error("Kullan\u0131m s\xfcresi g\xfcncellenirken hata:",I)}},X=e=>{console.error("\xd6deme hatas\u0131:",e),L(e.message||"\xd6deme i\u015flemi ba\u015far\u0131s\u0131z"),B(!1)},_=()=>{if(!Z)return;const e="\n            MGZ24 \xd6DEME MAKBUZu\n            ==================\n            \u0130\u015flem No: ".concat(Z.id,"\n            Tutar: ").concat((Z.amount/100).toFixed(2)," ").concat(Z.currency.toUpperCase(),"\n            Tarih: ").concat(new Date(1e3*Z.created).toLocaleString("tr-TR"),"\n            Durum: Ba\u015far\u0131l\u0131\n            \n            Bu makbuz \xf6deme kan\u0131t\u0131n\u0131zd\u0131r.\n        "),a=new Blob([e],{type:"text/plain"}),s=URL.createObjectURL(a),n=document.createElement("a");n.href=s,n.download="MGZ24_Makbuz_".concat(Z.id,".txt"),n.click(),URL.revokeObjectURL(s)},$=async()=>{if(Z)try{var e,a;(await fetch("/api/send-receipt-email",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({paymentIntentId:Z.id,userEmail:null===(e=JSON.parse(localStorage.getItem("user")))||void 0===e||null===(a=e.user)||void 0===a?void 0:a.email})})).ok?alert("Makbuz e-posta adresinize g\xf6nderildi."):alert("E-posta g\xf6nderilirken hata olu\u015ftu.")}catch(I){console.error("E-posta g\xf6nderme hatas\u0131:",I),alert("E-posta g\xf6nderilirken hata olu\u015ftu.")}},ee=()=>{if(!Z)return;const e=window.open("","_blank");e.document.write('\n            <html>\n                <head>\n                    <title>MGZ24 \xd6deme Makbuzu</title>\n                    <style>\n                        body { font-family: Arial, sans-serif; padding: 20px; }\n                        .header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; }\n                        .details { margin: 20px 0; }\n                        .row { display: flex; justify-content: space-between; margin: 5px 0; }\n                    </style>\n                </head>\n                <body>\n                    <div class="header">\n                        <h2>MGZ24 \xd6DEME MAKBUZU</h2>\n                    </div>\n                    <div class="details">\n                        <div class="row"><span>\u0130\u015flem No:</span><span>'.concat(Z.id,'</span></div>\n                        <div class="row"><span>Tutar:</span><span>').concat((Z.amount/100).toFixed(2)," ").concat(Z.currency.toUpperCase(),'</span></div>\n                        <div class="row"><span>Tarih:</span><span>').concat(new Date(1e3*Z.created).toLocaleString("tr-TR"),'</span></div>\n                        <div class="row"><span>Durum:</span><span>Ba\u015far\u0131l\u0131</span></div>\n                    </div>\n                </body>\n            </html>\n        ')),e.document.close(),e.print()},ae=()=>{U(!1),J(null),B(!1)};return K&&Z?(0,h.jsx)("div",{className:"container-fluid",children:(0,h.jsxs)("div",{className:"row",children:[(0,h.jsx)(c.A,{}),(0,h.jsx)(d.A,{}),(0,h.jsx)("main",{className:"col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white",children:(0,h.jsx)(v,{paymentIntent:Z,packageInfo:Q,onDownloadReceipt:_,onSendEmail:$,onPrintReceipt:ee,onReturnHome:ae})}),(0,h.jsx)(o.A,{})]})}):H&&Q?(0,h.jsx)("div",{className:"container-fluid",children:(0,h.jsxs)("div",{className:"row",children:[(0,h.jsx)(c.A,{}),(0,h.jsx)(d.A,{}),(0,h.jsxs)("main",{className:"col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white",children:[(0,h.jsxs)("div",{className:"pt-3 pb-1 mt-3 mb-3 border-bottom",children:[(0,h.jsxs)("h1",{className:"h4 text-dark",children:["G\xfcvenli \xd6deme - ",Q.name]}),(0,h.jsx)("button",{className:"btn btn-outline-secondary btn-sm",onClick:()=>B(!1),children:"\u2190 Geri D\xf6n"})]}),(0,h.jsx)("div",{className:"row justify-content-center",children:(0,h.jsx)("div",{className:"col-lg-8",children:(0,h.jsx)(x,{amount:g.priceEUR*j,currency:"TRY",packageInfo:g,deviceId:w,onSuccess:V,onError:X,disabled:C})})})]}),(0,h.jsx)(o.A,{})]})}):(0,h.jsx)("div",{className:"container-fluid",children:(0,h.jsxs)("div",{className:"row",children:[(0,h.jsx)(c.A,{}),(0,h.jsx)(d.A,{}),(0,h.jsxs)("main",{className:"col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white",children:[(0,h.jsx)("div",{className:"pt-3 pb-1 mt-3 mb-3 border-bottom",children:(0,h.jsxs)("div",{className:"d-flex justify-content-between align-items-center",children:[(0,h.jsx)("h1",{className:"h4 text-dark mb-0",children:"Kullan\u0131m S\xfcresi Sat\u0131n Al"}),N&&(0,h.jsxs)("div",{className:"d-flex align-items-center text-muted",children:[(0,h.jsx)("span",{className:"me-2 small",children:"G\xfcncel Euro Kuru:"}),(0,h.jsxs)("span",{className:"badge bg-primary",children:[(0,h.jsx)(l.g,{icon:i.hb3,className:"me-1"}),N.toFixed(2)]})]})]})}),e?(0,h.jsx)(m.A,{size:"lg",variant:"primary",message:"Paket bilgileri y\xfckleniyor...",centered:!0}):I?(0,h.jsx)(u.A,{message:I,variant:"danger",title:"Veri Y\xfckleme Hatas\u0131",dismissible:!0,onDismiss:()=>P(""),children:(0,h.jsx)("button",{className:"btn btn-primary btn-sm mt-2",onClick:()=>window.location.reload(),children:"Yeniden Dene"})}):(0,h.jsxs)("div",{className:"row",children:[(0,h.jsx)("div",{className:"col-lg-6 mb-4",children:(0,h.jsxs)("div",{className:"card border-0 shadow-sm",children:[(0,h.jsxs)("div",{className:"card-header bg-primary text-white",children:[(0,h.jsx)(l.g,{icon:i._2z,className:"me-2"}),(0,h.jsx)("span",{className:"fw-bold",children:"Kullan\u0131m Paketi Se\xe7in"})]}),(0,h.jsx)("div",{className:"card-body",children:(0,h.jsx)("div",{className:"row g-3",children:s.map((e=>(0,h.jsx)("div",{className:"col-12",children:(0,h.jsx)("div",{className:"card border ".concat((null===g||void 0===g?void 0:g.id)===e.id?"border-primary bg-primary bg-opacity-10":"border-light"," cursor-pointer"),onClick:()=>y(e),style:{cursor:"pointer",transition:"all 0.2s"},children:(0,h.jsx)("div",{className:"card-body py-3",children:(0,h.jsxs)("div",{className:"d-flex justify-content-between align-items-center",children:[(0,h.jsxs)("div",{children:[(0,h.jsxs)("h6",{className:"card-title mb-1 fw-bold",children:[(0,h.jsx)(l.g,{icon:i.a$,className:"me-2 text-primary"}),e.days," G\xfcn",e.isTest&&(0,h.jsx)("span",{className:"badge bg-warning ms-2",children:"TEST"})]}),(0,h.jsx)("small",{className:"text-muted",children:e.name})]}),(0,h.jsx)("div",{className:"text-end",children:e.isTest?(0,h.jsxs)("div",{className:"fw-bold text-warning",children:[(0,h.jsx)(l.g,{icon:i.hb3,className:"me-1"}),"\u20ba",e.priceTL]}):(0,h.jsxs)(h.Fragment,{children:[(0,h.jsxs)("div",{className:"fw-bold text-primary",children:[(0,h.jsx)(l.g,{icon:i.s5m,className:"me-1"}),"\u20ac",e.priceEUR]}),(0,h.jsxs)("small",{className:"text-muted",children:[(0,h.jsx)(l.g,{icon:i.hb3,className:"me-1"}),"\u20ba",(e.priceEUR*j).toFixed(2)]})]})})]})})})},e.id)))})})]})}),(0,h.jsx)("div",{className:"col-lg-6 mb-4",children:(0,h.jsxs)("div",{className:"card border-0 shadow-sm",children:[(0,h.jsxs)("div",{className:"card-header bg-success text-white",children:[(0,h.jsx)(l.g,{icon:i.YSV,className:"me-2"}),(0,h.jsx)("span",{className:"fw-bold",children:"Cihaz\u0131n\u0131z\u0131 Se\xe7in"})]}),(0,h.jsxs)("div",{className:"card-body",children:[f.length>0?(0,h.jsxs)("div",{className:"mb-3",children:[(0,h.jsx)("label",{className:"form-label small fw-bold",children:"Kredi Eklenecek Cihaz:"}),(0,h.jsxs)("select",{className:"form-select",value:w,onChange:e=>S(e.target.value),children:[(0,h.jsx)("option",{value:"",children:"Cihaz se\xe7iniz..."}),f.map((e=>(0,h.jsxs)("option",{value:e.CihazID,children:[e.CihazID," - ",e.durum," - ",e.kredi_gun]},e.ID)))]})]}):(0,h.jsxs)("div",{className:"alert alert-warning",children:[(0,h.jsx)(l.g,{icon:i.YSV,className:"me-2"}),"Size ait cihaz bulunamad\u0131. L\xfctfen \xf6nce bir cihaz edinin."]}),g&&w&&(0,h.jsx)("div",{className:"card bg-light border-0 mb-3",children:(0,h.jsxs)("div",{className:"card-body",children:[(0,h.jsxs)("h6",{className:"card-title text-success",children:[(0,h.jsx)(l.g,{icon:i.wq_,className:"me-2"}),"\xd6deme \xd6zeti"]}),(0,h.jsx)("hr",{}),(0,h.jsxs)("div",{className:"row",children:[(0,h.jsxs)("div",{className:"col-6",children:[(0,h.jsx)("small",{className:"text-muted",children:"Paket:"}),(0,h.jsxs)("div",{className:"fw-bold",children:[g.days," G\xfcn"]})]}),(0,h.jsxs)("div",{className:"col-6",children:[(0,h.jsx)("small",{className:"text-muted",children:"Cihaz:"}),(0,h.jsx)("div",{className:"fw-bold",children:w})]}),g.isTest?(0,h.jsxs)("div",{className:"col-12 mt-2",children:[(0,h.jsx)("small",{className:"text-muted",children:"Test Tutar\u0131:"}),(0,h.jsxs)("div",{className:"fw-bold text-warning",children:["\u20ba",g.priceTL,(0,h.jsx)("span",{className:"badge bg-warning ms-2",children:"TEST"})]})]}):(0,h.jsxs)(h.Fragment,{children:[(0,h.jsxs)("div",{className:"col-6 mt-2",children:[(0,h.jsx)("small",{className:"text-muted",children:"Euro:"}),(0,h.jsxs)("div",{className:"fw-bold text-primary",children:["\u20ac",g.priceEUR]})]}),(0,h.jsxs)("div",{className:"col-6 mt-2",children:[(0,h.jsx)("small",{className:"text-muted",children:"T\xfcrk Liras\u0131:"}),(0,h.jsxs)("div",{className:"fw-bold text-success",children:["\u20ba",(g.priceEUR*j).toFixed(2)]})]})]})]})]})}),g&&w&&(0,h.jsxs)("button",{className:"btn btn-success w-100 py-3 fw-bold",onClick:()=>B(!0),disabled:C,children:[(0,h.jsx)(l.g,{icon:i.$O8,className:"me-2"}),C?"\u0130\u015flem Yap\u0131l\u0131yor...":"G\xfcvenli \xd6deme Yap"]})]})]})}),(0,h.jsx)("div",{className:"col-12 mb-4",children:(0,h.jsxs)("div",{className:"card border-0 shadow-sm",children:[(0,h.jsxs)("div",{className:"card-header bg-info text-white",children:[(0,h.jsx)(l.g,{icon:i.a$,className:"me-2"}),(0,h.jsx)("span",{className:"fw-bold",children:"Hesap Durumu"}),(0,h.jsxs)("button",{className:"btn btn-sm btn-outline-light ms-auto",onClick:()=>O(!M),children:[(0,h.jsx)(l.g,{icon:i.Int,className:"me-1"}),M?"Gizle":"Ge\xe7mi\u015f"]})]}),(0,h.jsx)("div",{className:"card-body",children:(0,h.jsxs)("div",{className:"row",children:[(0,h.jsx)("div",{className:"col-md-3",children:(0,h.jsxs)("div",{className:"text-center",children:[(0,h.jsx)("div",{className:"display-6 text-primary fw-bold",children:(null===T||void 0===T?void 0:T.remainingDays)||0}),(0,h.jsx)("small",{className:"text-muted",children:"Kalan G\xfcn"})]})}),(0,h.jsx)("div",{className:"col-md-3",children:(0,h.jsxs)("div",{className:"text-center",children:[(0,h.jsx)("div",{className:"display-6 text-success fw-bold",children:f.length}),(0,h.jsx)("small",{className:"text-muted",children:"Aktif Cihaz"})]})}),(0,h.jsxs)("div",{className:"col-md-6",children:[(0,h.jsx)("h6",{className:"text-muted",children:"Son Kullan\u0131m:"}),(0,h.jsx)("p",{className:"mb-0",children:null!==T&&void 0!==T&&T.expiryDate?new Date(T.expiryDate).toLocaleDateString("tr-TR"):"Belirtilmemi\u015f"})]})]})})]})})]}),H&&g&&w&&(0,h.jsx)("div",{className:"modal show d-block",style:{backgroundColor:"rgba(0,0,0,0.5)"},children:(0,h.jsx)("div",{className:"modal-dialog modal-xl",children:(0,h.jsxs)("div",{className:"modal-content",children:[(0,h.jsxs)("div",{className:"modal-header",children:[(0,h.jsx)("h5",{className:"modal-title",children:"PayTR G\xfcvenli \xd6deme"}),(0,h.jsx)("button",{type:"button",className:"btn-close",onClick:()=>B(!1)})]}),(0,h.jsx)("div",{className:"modal-body",children:(0,h.jsx)(p,{amount:g.isTest?g.priceTL:g.priceEUR*j,currency:"TL",packageInfo:g,deviceId:w,onSuccess:V,onError:X,disabled:C})})]})})})]}),(0,h.jsx)(o.A,{})]})})}}}]);
//# sourceMappingURL=731.dadb18f4.chunk.js.map