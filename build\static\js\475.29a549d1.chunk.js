"use strict";(self.webpackChunkmgz24=self.webpackChunkmgz24||[]).push([[475],{421:(e,a,s)=>{s.d(a,{A:()=>r});s(5043);var l=s(9002),t=s(3910),n=s(7929),i=s(579);const r=()=>{const e=(0,l.zy)(),a="admin"===(()=>{try{var e,a;const s=JSON.parse(localStorage.getItem("user"));return(null===s||void 0===s||null===(e=s.user)||void 0===e?void 0:e.role)||(null===s||void 0===s||null===(a=s.user)||void 0===a?void 0:a.gorev)||"user"}catch(s){return"user"}})();return(0,i.jsx)("div",{className:"sidebar col-md-4 col-lg-2 p-0 bg-light-subtle",children:(0,i.jsxs)("div",{className:"offcanvas-md offcanvas-end",tabIndex:"-1",id:"yanMenu","aria-labelledby":"yanMenu",children:[(0,i.jsxs)("div",{className:"offcanvas-header",children:[(0,i.jsx)("h5",{className:"offcanvas-title",id:"yanMenu",children:"MGZ24 Gold"}),(0,i.jsx)("button",{type:"button",className:"btn-close","data-bs-dismiss":"offcanvas","data-bs-target":"#yanMenu","aria-label":"Kapat"})]}),(0,i.jsxs)("div",{className:"offcanvas-body d-md-flex flex-column p-0 pt-lg-3 overflow-y-auto",children:[(0,i.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase",children:"Sevkiyat \u0130\u015flemleri"}),(0,i.jsxs)("ul",{className:"nav nav-pills flex-column",children:[(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/"===e.pathname?"active":""),to:"/",children:[(0,i.jsx)(t.g,{icon:n.msb}),"Aktif Sevkiyatlar"]})}),(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/add"===e.pathname?"active":""),to:"/add",children:[(0,i.jsx)(t.g,{icon:n.E5r}),"Yeni Sevkiyat Olu\u015ftur"]})}),(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/inactive-devices"===e.pathname?"active":""),to:"/inactive-devices",children:[(0,i.jsx)(t.g,{icon:n.fH7}),"\u0130naktif Cihazlar"]})}),(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/history"===e.pathname?"active":""),to:"/history",children:[(0,i.jsx)(t.g,{icon:n.o9H}),"Ge\xe7mi\u015f Sevkiyatlar"]})})]}),(0,i.jsx)("hr",{className:"my-3"}),(0,i.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase",children:"Sistem Y\xf6netimi"}),(0,i.jsxs)("ul",{className:"nav nav-pills flex-column",children:[a&&(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/devices"===e.pathname?"active":""),to:"/devices",children:[(0,i.jsx)(t.g,{icon:n.ArK}),"Cihaz Y\xf6netimi"]})}),(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/notifications"===e.pathname?"active":""),to:"/notifications",children:[(0,i.jsx)(t.g,{icon:n.z$e}),"Bildirimler"]})})]}),(0,i.jsx)("hr",{className:"my-3"}),(0,i.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase",children:"Kullan\u0131c\u0131 Ayarlar\u0131"}),(0,i.jsxs)("ul",{className:"nav nav-pills flex-column",children:[(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/profile"===e.pathname?"active":""),to:"/profile",children:[(0,i.jsx)(t.g,{icon:n.X46}),"Kullan\u0131c\u0131 Profilim"]})}),(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/viewers"===e.pathname?"active":""),to:"/viewers",children:[(0,i.jsx)(t.g,{icon:n.yLE}),"\u0130zleyici \u0130\u015flemleri"]})})]}),(0,i.jsx)("hr",{className:"my-3"}),(0,i.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-2 text-body-secondary text-uppercase",children:"\xd6deme ve Yap\u0131land\u0131rma"}),(0,i.jsxs)("ul",{className:"nav nav-pills flex-column",children:[(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/payment"===e.pathname?"active":""),to:"/payment",children:[(0,i.jsx)(t.g,{icon:n.$O8}),"\xd6deme Yap"]})}),(0,i.jsx)("li",{className:"nav-item",children:(0,i.jsxs)(l.N_,{className:"nav-link d-flex align-items-center gap-2 ".concat("/invoices"===e.pathname?"active":""),to:"/invoices",children:[(0,i.jsx)(t.g,{icon:n.bLf}),"Faturalar\u0131m"]})})]}),(0,i.jsx)("hr",{className:"my-3"}),(0,i.jsx)("h6",{className:"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-body-secondary text-uppercase",children:"Kredi Durumu"})]})]})})}},834:(e,a,s)=>{s.d(a,{A:()=>t});s(5043);var l=s(579);const t=()=>(0,l.jsx)("footer",{className:"py-5 border-top",children:(0,l.jsx)("p",{className:"text-center",children:"\xa92025 Inkatech \xd6l\xe7\xfcm Sistemleri"})})},1899:(e,a,s)=>{s.d(a,{A:()=>d});var l=s(5043),t=s(9002),n=s(3910),i=s(7929);var r=s(4713),c=s(579);const d=()=>{const e=(0,t.Zp)(),[a,s]=(0,l.useState)("Misafir"),[d,o]=(0,l.useState)(!0);(0,l.useEffect)((()=>{(async()=>{try{var e,a,l,t;const i=JSON.parse(localStorage.getItem("user")),c=(null===i||void 0===i||null===(e=i.user)||void 0===e?void 0:e.musteri_ID)||(null===i||void 0===i||null===(a=i.user)||void 0===a?void 0:a.id),d=(null===i||void 0===i||null===(l=i.user)||void 0===l?void 0:l.name)||(null===i||void 0===i||null===(t=i.user)||void 0===t?void 0:t.musteri_adi);if(!c)return console.warn("Oturum bilgisi bulunamad\u0131"),s("Misafir"),void o(!1);if(d)return s(d),void o(!1);try{const e=await r.Qj.getKullanici(c);e&&e.musteri_adi&&(s(e.musteri_adi),null!==i&&void 0!==i&&i.user&&(i.user.name=e.musteri_adi,localStorage.setItem("user",JSON.stringify(i))))}catch(n){console.warn("API'den kullan\u0131c\u0131 bilgisi al\u0131namad\u0131, localStorage kullan\u0131l\u0131yor"),s(d||"Kullan\u0131c\u0131")}}catch(i){console.error("Kullan\u0131c\u0131 bilgileri al\u0131n\u0131rken hata:",i),s("Kullan\u0131c\u0131")}finally{o(!1)}})()}),[]);return(0,c.jsxs)("header",{className:"navbar sticky-top bg-darkblue flex-md-nowrap p-0 shadow","data-bs-theme":"dark",children:[(0,c.jsx)(t.N_,{className:"navbar-brand col-md-4 col-lg-2 me-0 px-3 py-2 fs-6 text-white",to:"/",children:(0,c.jsx)("img",{src:"data:image/png;base64,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",alt:"MGZ24 Logo",height:"40"})}),(0,c.jsxs)("ul",{className:"navbar-nav flex-row align-items-center",children:[(0,c.jsx)("li",{className:"nav-item text-nowrap d-none d-md-block me-3",children:(0,c.jsxs)("span",{className:"text-white",children:[(0,c.jsx)(n.g,{icon:i.X46,className:"me-2"}),d?"Y\xfckleniyor...":a]})}),(0,c.jsx)("li",{className:"nav-item text-nowrap px-1",children:(0,c.jsx)("button",{onClick:()=>{localStorage.removeItem("user"),e("/login")},className:"nav-link px-3","data-bs-toggle":"tooltip","data-bs-placement":"left","data-bs-title":"\xc7\u0131k\u0131\u015f yap!",children:(0,c.jsx)(n.g,{icon:i.yBu})})}),(0,c.jsx)("li",{className:"nav-item text-nowrap d-md-none",children:(0,c.jsx)("button",{className:"nav-link px-3",type:"button","data-bs-toggle":"offcanvas","data-bs-target":"#yanMenu","aria-controls":"yanMenu","aria-expanded":"false","aria-label":"menu a\xe7/kapa",children:(0,c.jsx)(n.g,{icon:i.ckx})})})]})]})}},8475:(e,a,s)=>{s.r(a),s.d(a,{default:()=>m});var l=s(5043),t=s(9722),n=s(1899),i=s(421),r=s(834),c=s(9144),d=s(5492),o=s(579);const m=()=>{const[e,a]=(0,l.useState)([]),[s,m]=(0,l.useState)(!0),[x,h]=(0,l.useState)(null),[u,j]=(0,l.useState)(null),[v,A]=(0,l.useState)(!1),[N,g]=(0,l.useState)("all"),[p,b]=(0,l.useState)("all");(0,l.useEffect)((()=>{(async()=>{try{m(!0),h(null);const e=JSON.parse(localStorage.getItem("user")),s=(null===e||void 0===e?void 0:e.musteri_ID)||1,l=(await t.A.get("/api/payments/history/".concat(s))).data.map((e=>({id:e.id,invoiceNo:"FAT-".concat(e.date.split("-")[0],"-").concat(e.id.toString().padStart(3,"0")),date:e.date,dueDate:new Date(new Date(e.date).getTime()+2592e6).toISOString().split("T")[0],amount:e.amountEUR,tax:.18*e.amountEUR,total:e.amountTRY,status:"completed"===e.status?"paid":"pending",description:e.packageName,serviceType:"MGZ24 Hizmet",paymentDate:"completed"===e.status?e.date:null,paymentMethod:"Kredi Kart\u0131"})));a(l),m(!1)}catch(x){console.error("Fatura verileri al\u0131n\u0131rken hata:",x),h("Fatura verileri y\xfcklenirken hata olu\u015ftu: "+x.message),m(!1)}})()}),[]);const y=e=>{switch(e){case"paid":return"success";case"pending":return"warning";case"overdue":return"danger";default:return"secondary"}},f=e=>{switch(e){case"paid":return"\xd6dendi";case"pending":return"Bekliyor";case"overdue":return"Vadesi Ge\xe7ti";default:return"Bilinmiyor"}},k=()=>e.filter((e=>{const a="all"===N||e.status===N,s="all"===p||e.date.substring(0,7)===p;return a&&s})),z=(()=>{const e=k();return{total:e.reduce(((e,a)=>e+a.total),0),paid:e.filter((e=>"paid"===e.status)).reduce(((e,a)=>e+a.total),0),pending:e.filter((e=>"pending"===e.status)).reduce(((e,a)=>e+a.total),0),overdue:e.filter((e=>"overdue"===e.status)).reduce(((e,a)=>e+a.total),0)}})();return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(n.A,{}),(0,o.jsx)("div",{className:"container-fluid",children:(0,o.jsxs)("div",{className:"row",children:[(0,o.jsx)(i.A,{}),(0,o.jsxs)("main",{className:"col-md-8 ms-sm-auto col-lg-10 px-md-4 border-start border-dark-subtle bg-white",children:[(0,o.jsx)("div",{className:"pt-3 pb-1 mt-3 mb-3 border-bottom",children:(0,o.jsx)("h1",{className:"h4 text-dark",children:"Faturalar\u0131m"})}),s?(0,o.jsx)(c.A,{size:"lg",variant:"primary",message:"Fatura verileri y\xfckleniyor...",centered:!0}):x?(0,o.jsx)(d.A,{message:x,variant:"danger",title:"Veri Y\xfckleme Hatas\u0131",dismissible:!0,onDismiss:()=>h(""),children:(0,o.jsx)("button",{className:"btn btn-primary btn-sm mt-2",onClick:()=>window.location.reload(),children:"Yeniden Dene"})}):(0,o.jsx)("div",{className:"row",children:(0,o.jsxs)("div",{className:"col-12",children:[(0,o.jsxs)("div",{className:"row mb-4",children:[(0,o.jsx)("div",{className:"col-md-3 col-sm-6 mb-3",children:(0,o.jsx)("div",{className:"card border-primary",children:(0,o.jsxs)("div",{className:"card-body text-center",children:[(0,o.jsx)("h5",{className:"card-title text-primary",children:"Toplam Tutar"}),(0,o.jsxs)("h4",{className:"text-primary",children:[z.total.toFixed(2)," \u20ba"]})]})})}),(0,o.jsx)("div",{className:"col-md-3 col-sm-6 mb-3",children:(0,o.jsx)("div",{className:"card border-success",children:(0,o.jsxs)("div",{className:"card-body text-center",children:[(0,o.jsx)("h5",{className:"card-title text-success",children:"\xd6denen"}),(0,o.jsxs)("h4",{className:"text-success",children:[z.paid.toFixed(2)," \u20ba"]})]})})}),(0,o.jsx)("div",{className:"col-md-3 col-sm-6 mb-3",children:(0,o.jsx)("div",{className:"card border-warning",children:(0,o.jsxs)("div",{className:"card-body text-center",children:[(0,o.jsx)("h5",{className:"card-title text-warning",children:"Bekleyen"}),(0,o.jsxs)("h4",{className:"text-warning",children:[z.pending.toFixed(2)," \u20ba"]})]})})}),(0,o.jsx)("div",{className:"col-md-3 col-sm-6 mb-3",children:(0,o.jsx)("div",{className:"card border-danger",children:(0,o.jsxs)("div",{className:"card-body text-center",children:[(0,o.jsx)("h5",{className:"card-title text-danger",children:"Vadesi Ge\xe7en"}),(0,o.jsxs)("h4",{className:"text-danger",children:[z.overdue.toFixed(2)," \u20ba"]})]})})})]}),(0,o.jsxs)("div",{className:"card mb-4",children:[(0,o.jsx)("div",{className:"card-header",children:(0,o.jsx)("h5",{className:"mb-0",children:"Filtreler"})}),(0,o.jsx)("div",{className:"card-body",children:(0,o.jsxs)("div",{className:"row",children:[(0,o.jsxs)("div",{className:"col-md-4",children:[(0,o.jsx)("label",{htmlFor:"statusFilter",className:"form-label",children:"Durum"}),(0,o.jsxs)("select",{id:"statusFilter",className:"form-select",value:N,onChange:e=>g(e.target.value),children:[(0,o.jsx)("option",{value:"all",children:"T\xfcm\xfc"}),(0,o.jsx)("option",{value:"paid",children:"\xd6dendi"}),(0,o.jsx)("option",{value:"pending",children:"Bekliyor"}),(0,o.jsx)("option",{value:"overdue",children:"Vadesi Ge\xe7ti"})]})]}),(0,o.jsxs)("div",{className:"col-md-4",children:[(0,o.jsx)("label",{htmlFor:"monthFilter",className:"form-label",children:"Ay"}),(0,o.jsxs)("select",{id:"monthFilter",className:"form-select",value:p,onChange:e=>b(e.target.value),children:[(0,o.jsx)("option",{value:"all",children:"T\xfcm\xfc"}),(0,o.jsx)("option",{value:"2024-03",children:"Mart 2024"}),(0,o.jsx)("option",{value:"2024-02",children:"\u015eubat 2024"}),(0,o.jsx)("option",{value:"2024-01",children:"Ocak 2024"}),(0,o.jsx)("option",{value:"2023-12",children:"Aral\u0131k 2023"})]})]}),(0,o.jsx)("div",{className:"col-md-4 d-flex align-items-end",children:(0,o.jsx)("button",{className:"btn btn-secondary",onClick:()=>{g("all"),b("all")},children:"Filtreleri Temizle"})})]})})]}),(0,o.jsxs)("div",{className:"card border-tertiary-subtle",children:[(0,o.jsx)("div",{className:"card-header bg-light",children:(0,o.jsxs)("h5",{className:"mb-0",children:[(0,o.jsx)("i",{className:"fas fa-file-invoice me-2"}),"Fatura Listesi",(0,o.jsx)("span",{className:"badge bg-primary ms-2",children:k().length})]})}),(0,o.jsx)("div",{className:"card-body p-0",children:0===k().length?(0,o.jsx)("div",{className:"p-4 text-center",children:(0,o.jsx)(d.A,{message:"Se\xe7ilen kriterlere uygun fatura bulunamad\u0131.",variant:"info",showIcon:!0})}):(0,o.jsx)("div",{className:"table-responsive",children:(0,o.jsxs)("table",{className:"table table-hover table-striped mb-0",children:[(0,o.jsx)("thead",{className:"table-dark",children:(0,o.jsxs)("tr",{children:[(0,o.jsx)("th",{children:"Fatura No"}),(0,o.jsx)("th",{children:"Tarih"}),(0,o.jsx)("th",{children:"Vade Tarihi"}),(0,o.jsx)("th",{children:"Tutar"}),(0,o.jsx)("th",{children:"KDV"}),(0,o.jsx)("th",{children:"Toplam"}),(0,o.jsx)("th",{children:"Durum"}),(0,o.jsx)("th",{children:"Hizmet T\xfcr\xfc"}),(0,o.jsx)("th",{children:"\u0130\u015flemler"})]})}),(0,o.jsx)("tbody",{children:k().map((e=>(0,o.jsxs)("tr",{children:[(0,o.jsx)("td",{children:(0,o.jsx)("strong",{children:e.invoiceNo})}),(0,o.jsx)("td",{children:e.date}),(0,o.jsx)("td",{children:e.dueDate}),(0,o.jsxs)("td",{children:[e.amount.toFixed(2)," \u20ba"]}),(0,o.jsxs)("td",{children:[e.tax.toFixed(2)," \u20ba"]}),(0,o.jsx)("td",{children:(0,o.jsxs)("strong",{children:[e.total.toFixed(2)," \u20ba"]})}),(0,o.jsx)("td",{children:(0,o.jsx)("span",{className:"badge bg-".concat(y(e.status)),children:f(e.status)})}),(0,o.jsx)("td",{children:e.serviceType}),(0,o.jsx)("td",{children:(0,o.jsxs)("div",{className:"btn-group",role:"group",children:[(0,o.jsx)("button",{className:"btn btn-sm btn-outline-primary",onClick:()=>(e=>{j(e),A(!0)})(e),title:"Detaylar\u0131 G\xf6ster",children:(0,o.jsx)("i",{className:"fas fa-eye"})}),(0,o.jsx)("button",{className:"btn btn-sm btn-outline-success",title:"PDF \u0130ndir",children:(0,o.jsx)("i",{className:"fas fa-download"})})]})})]},e.id)))})]})})})]})]})}),v&&u&&(0,o.jsx)("div",{className:"modal fade show d-block",tabIndex:"-1",style:{backgroundColor:"rgba(0,0,0,0.5)"},children:(0,o.jsx)("div",{className:"modal-dialog modal-lg",children:(0,o.jsxs)("div",{className:"modal-content",children:[(0,o.jsxs)("div",{className:"modal-header",children:[(0,o.jsxs)("h5",{className:"modal-title",children:["Fatura Detay\u0131 - ",u.invoiceNo]}),(0,o.jsx)("button",{type:"button",className:"btn-close",onClick:()=>A(!1)})]}),(0,o.jsxs)("div",{className:"modal-body",children:[(0,o.jsxs)("div",{className:"row",children:[(0,o.jsxs)("div",{className:"col-md-6",children:[(0,o.jsx)("h6",{children:"Fatura Bilgileri"}),(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"Fatura No:"})," ",u.invoiceNo]}),(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"Tarih:"})," ",u.date]}),(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"Vade Tarihi:"})," ",u.dueDate]}),(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"Durum:"}),(0,o.jsx)("span",{className:"badge bg-".concat(y(u.status)," ms-2"),children:f(u.status)})]})]}),(0,o.jsxs)("div",{className:"col-md-6",children:[(0,o.jsx)("h6",{children:"\xd6deme Bilgileri"}),"paid"===u.status?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"\xd6deme Tarihi:"})," ",u.paymentDate]}),(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"\xd6deme Y\xf6ntemi:"})," ",u.paymentMethod]})]}):(0,o.jsx)("p",{className:"text-muted",children:"Hen\xfcz \xf6deme yap\u0131lmam\u0131\u015f"})]})]}),(0,o.jsx)("hr",{}),(0,o.jsx)("h6",{children:"Hizmet Detaylar\u0131"}),(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"Hizmet T\xfcr\xfc:"})," ",u.serviceType]}),(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"A\xe7\u0131klama:"})," ",u.description]}),(0,o.jsx)("hr",{}),(0,o.jsxs)("div",{className:"row",children:[(0,o.jsx)("div",{className:"col-md-8",children:(0,o.jsx)("h6",{children:"Tutar Detaylar\u0131"})}),(0,o.jsxs)("div",{className:"col-md-4 text-end",children:[(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"Tutar:"})," ",u.amount.toFixed(2)," \u20ba"]}),(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"KDV (%18):"})," ",u.tax.toFixed(2)," \u20ba"]}),(0,o.jsx)("hr",{}),(0,o.jsxs)("h5",{children:[(0,o.jsx)("strong",{children:"Toplam:"})," ",u.total.toFixed(2)," \u20ba"]})]})]})]}),(0,o.jsxs)("div",{className:"modal-footer",children:[(0,o.jsxs)("button",{type:"button",className:"btn btn-success",children:[(0,o.jsx)("i",{className:"fas fa-download me-2"}),"PDF \u0130ndir"]}),(0,o.jsx)("button",{type:"button",className:"btn btn-secondary",onClick:()=>A(!1),children:"Kapat"})]})]})})})]}),(0,o.jsx)(r.A,{})]})})]})}}}]);
//# sourceMappingURL=475.29a549d1.chunk.js.map